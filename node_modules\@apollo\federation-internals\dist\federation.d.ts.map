{"version": 3, "file": "federation.d.ts", "sourceRoot": "", "sources": ["../src/federation.ts"], "names": [], "mappings": "AAAA,OAAO,EAGL,aAAa,EACb,WAAW,EAEX,SAAS,EACT,mBAAmB,EAGnB,eAAe,EACf,oBAAoB,EACpB,aAAa,EAMb,SAAS,EAET,UAAU,EACV,UAAU,EACV,MAAM,EACN,eAAe,EACf,YAAY,EAEZ,aAAa,EAEb,SAAS,EAYV,MAAM,eAAe,CAAC;AAEvB,OAAO,EAAE,iBAAiB,EAAE,MAAM,sCAAsC,CAAC;AAEzE,OAAO,EACL,OAAO,EACP,YAAY,EACZ,YAAY,EAWb,MAAM,SAAS,CAAC;AAGjB,OAAO,EAAiG,YAAY,EAAE,MAAM,cAAc,CAAC;AAE3I,OAAO,EAEL,mBAAmB,EAMpB,MAAM,SAAS,CAAC;AAuBjB,OAAO,EAAuB,YAAY,IAAI,YAAY,EAAe,MAAM,SAAS,CAAC;AAKzF,OAAO,EAAiB,sBAAsB,EAAE,0BAA0B,EAAgB,MAAM,kBAAkB,CAAC;AAqBnH,eAAO,MAAM,iCAAiC,MAAM,CAAC;AAErD,eAAO,MAAM,gCAAgC,cAAc,CAAC;AA+N5D,wBAAgB,YAAY,CAAC,KAAK,EAAE,MAAM;;;;;;EAYzC;AA+ZD,wBAAgB,iBAAiB,CAAC,QAAQ,EAAE,kBAAkB,GAAG,GAAG,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC,CA2CnG;AAoWD,qBAAa,kBAAkB;IAMjB,QAAQ,CAAC,MAAM,EAAE,MAAM;IALnC,OAAO,CAAC,eAAe,CAAC,CAAiB;IACzC,OAAO,CAAC,iBAAiB,CAAC,CAAqD;IAC/E,OAAO,CAAC,mBAAmB,CAAC,CAAqD;IACjF,OAAO,CAAC,aAAa,CAAC,CAAU;gBAEX,MAAM,EAAE,MAAM;IAEnC,OAAO,CAAC,YAAY;IAOpB,YAAY,IAAI,OAAO;IAQvB,iBAAiB,IAAI,WAAW,GAAG,SAAS;IAI5C,OAAO,CAAC,cAAc;IAOtB,OAAO,CAAC,gBAAgB;IAOxB,OAAO,CAAC,kBAAkB;IAQ1B,WAAW,CAAC,KAAK,EAAE,eAAe,CAAC,aAAa,CAAC,GAAG,OAAO;IAI3D,eAAe,CAAC,KAAK,EAAE,eAAe,CAAC,GAAG,CAAC,GAAG,oBAAoB;IAIlE,wBAAwB,CAAC,KAAK,EAAE,eAAe,CAAC,GAAG,CAAC,GAAG,oBAAoB;IAI3E,oBAAoB,CAAC,KAAK,EAAE,eAAe,CAAC,GAAG,CAAC,GAAG,oBAAoB;IAIvE,mBAAmB,CAAC,KAAK,EAAE,eAAe,CAAC,GAAG,CAAC,GAAG,oBAAoB;IAItE,gCAAgC,CAAC,YAAY,EAAE,YAAY,GAAG,OAAO;IAIrE,gBAAgB,CAAC,KAAK,EAAE,eAAe,CAAC,GAAG,CAAC,GAAG,OAAO;IAItD,qBAAqB,CAAC,IAAI,EAAE,SAAS,GAAG,IAAI,IAAI,UAAU;IAK1D,+BAA+B,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM;IAYrD,0BAA0B,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM;IA4BhD,OAAO,CAAC,4BAA4B;IAQpC,OAAO,CAAC,sBAAsB;IAM9B,OAAO,CAAC,4BAA4B;IASpC,YAAY,IAAI,mBAAmB,CAAC;QAAC,MAAM,EAAE,GAAG,CAAC;QAAC,UAAU,CAAC,EAAE,OAAO,CAAA;KAAC,CAAC;IAIxE,iBAAiB,IAAI,mBAAmB,CAAC;QAAC,IAAI,EAAE,MAAM,CAAC;QAAC,KAAK,CAAC,EAAE,MAAM,CAAA;KAAC,CAAC;IAIxE,gBAAgB,IAAI,mBAAmB,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAI9D,iBAAiB,IAAI,mBAAmB,CAAC;QAAC,MAAM,EAAE,MAAM,CAAA;KAAC,CAAC;IAI1D,iBAAiB,IAAI,mBAAmB,CAAC;QAAC,MAAM,EAAE,GAAG,CAAA;KAAC,CAAC;IAIvD,iBAAiB,IAAI,mBAAmB,CAAC;QAAC,MAAM,EAAE,GAAG,CAAA;KAAC,CAAC;IAIvD,kBAAkB,IAAI,mBAAmB,CAAC,EAAE,CAAC;IAI7C,YAAY,IAAI,mBAAmB,CAAC;QAAC,IAAI,EAAE,MAAM,CAAA;KAAC,CAAC;IAInD,gBAAgB,IAAI,mCAAmC,CAAC;QAAC,IAAI,EAAE,MAAM,CAAA;KAAC,CAAC;IAIvE,qBAAqB,IAAI,mBAAmB,CAAC,EAAE,CAAC;IAIhD,wBAAwB,IAAI,mCAAmC,CAAC,EAAE,CAAC;IAInE,sBAAsB,IAAI,mCAAmC,CAAC,EAAE,CAAC;IAIjE,uBAAuB,IAAI,mCAAmC,CAAC;QAAC,MAAM,EAAE,MAAM,EAAE,CAAA;KAAC,CAAC;IAIlF,eAAe,IAAI,mCAAmC,CAAC;QAAC,MAAM,EAAE,MAAM,EAAE,CAAA;KAAC,CAAC;IAI1E,oBAAoB,IAAI,mCAAmC,CAAC;QAAE,KAAK,EAAE,MAAM,CAAA;KAAE,CAAC;IAI9E,gBAAgB,IAAI,mCAAmC,CAAC;QAAE,IAAI,EAAE,MAAM,CAAA;KAAE,CAAC;IAIzE,aAAa,IAAI,mCAAmC,CAAC,sBAAsB,CAAC;IAI5E,iBAAiB,IAAI,mCAAmC,CAAC,0BAA0B,CAAC;IAIpF,uBAAuB,IAAI,mBAAmB,EAAE;IAgEhD,UAAU,IAAI,SAAS,GAAG,SAAS;IAInC,OAAO,IAAI,UAAU;IAIrB,WAAW,IAAI,UAAU;IAIzB,YAAY,IAAI,UAAU;IAI1B,kBAAkB,IAAI,SAAS,EAAE;CA6BlC;AAED,MAAM,MAAM,qCAAqC,CAAC,gBAAgB,SAAS;IAAC,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;CAAC,IAAI;IACjG,IAAI,EAAE,MAAM,CAAC;IACb,YAAY,EAAE,MAAM,WAAW,CAAC,SAAS,CAAC,GAAG,EAAE,gBAAgB,CAAC,CAAC,CAAC;CACnE,CAAA;AAED,MAAM,MAAM,mCAAmC,CAAC,gBAAgB,SAAS;IAAC,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;CAAC,IAC3F,mBAAmB,CAAC,gBAAgB,CAAC,GACnC,qCAAqC,CAAC,gBAAgB,CAAC,CAAC;AAE5D,wBAAgB,oCAAoC,CAAC,gBAAgB,SAAS;IAAC,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;CAAC,EAChG,UAAU,EAAE,mCAAmC,CAAC,gBAAgB,CAAC,GAChE,UAAU,IAAI,mBAAmB,CAAC,gBAAgB,CAAC,CAErD;AAED,wBAAgB,mBAAmB,CAAC,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,mCAAmC,CAAC,GAAG,CAAC,GAAG,OAAO,CAElH;AAED,qBAAa,mBAAoB,SAAQ,eAAe;IAC1C,OAAO,CAAC,QAAQ,CAAC,oBAAoB;gBAApB,oBAAoB,EAAE,OAAO;IAI1D,kBAAkB,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW;IAUvD,4BAA4B,CAAC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,GAAG,mBAAmB,GAAG,YAAY,EAAE,GAAG,SAAS;IAgBpH,iBAAiB,CAAC,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,GAAG,OAAO;IAe9D,aAAa,CAAC,MAAM,EAAE,MAAM;IAO5B,oCAAoC,CAAC,MAAM,EAAE,MAAM,GAAG,YAAY,EAAE;IAMpE,cAAc,CAAC,MAAM,EAAE,MAAM;IAO7B,YAAY,CAAC,MAAM,EAAE,MAAM,GAAG,YAAY,EAAE;IAqQ5C,eAAe,IAAI,SAAS,iBAAiB,EAAE;IAI/C,iCAAiC,CAAC,MAAM,EAAE,MAAM,EAAE,oBAAoB,EAAE,MAAM,EAAE,KAAK,EAAE,YAAY,GAAG,YAAY;IA6ClH,2BAA2B;CAG5B;AAmBD,wBAAgB,uBAAuB,CAAC,MAAM,EAAE,MAAM,EAAE,SAAS,GAAE,OAAe,QAoCjF;AAID,eAAO,MAAM,kCAAkC,sWAAgU,CAAC;AAEhX,eAAO,MAAM,2CAA2C,sPAA8N,CAAC;AAGvR,eAAO,MAAM,oDAAoD,qPAA6N,CAAC;AAY/R,wBAAgB,sBAAsB,CAAC,QAAQ,EAAE,YAAY,EAAE,OAAO,CAAC,EAAE;IAAE,oBAAoB,CAAC,EAAE,OAAO,CAAC;IAAC,iBAAiB,CAAC,EAAE,OAAO,CAAA;CAAE,GAAG,YAAY,CAgEtJ;AAED,wBAAgB,kBAAkB,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,MAAM,CAQ1D;AAED,wBAAgB,kBAAkB,CAAC,MAAM,EAAE,MAAM,GAAG,kBAAkB,GAAG,SAAS,CAEjF;AAED,wBAAgB,0BAA0B,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAElE;AAED,wBAAgB,iBAAiB,CAAC,KAAK,EAAE,eAAe,CAAC,aAAa,CAAC,GAAG,OAAO,CAKhF;AAED,wBAAgB,YAAY,CAAC,IAAI,EAAE,SAAS,GAAG,OAAO,CAMrD;AAED,wBAAgB,qBAAqB,CAAC,IAAI,EAAE,SAAS,GAAG,OAAO,CAM9D;AAED,wBAAgB,aAAa,CAC3B,IAAI,EAAE,MAAM,EACZ,GAAG,EAAE,MAAM,EACX,MAAM,EAAE,YAAY,GAAG,MAAM,EAC7B,oBAAoB,GAAE,OAAc,GACnC,QAAQ,CAmBV;AAED,wBAAgB,yBAAyB,CAAC,MAAM,CAAC,EAAE,YAAY,GAAG,MAAM,CAIvE;AAmID,wBAAgB,qBAAqB,CAAC,EACpC,UAAU,EACV,SAAS,EACT,aAAa,EACb,QAAQ,EACR,wBAA+B,EAC/B,SAAiB,GAClB,EAAE;IACD,UAAU,EAAE,aAAa,CAAC;IAC1B,SAAS,EAAE,SAAS,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE;QAAC,MAAM,EAAE,GAAG,CAAA;KAAC,CAAC,CAAC;IAC7D,aAAa,CAAC,EAAE,CAAC,IAAI,EAAE,aAAa,EAAE,SAAS,EAAE,MAAM,KAAK,eAAe,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC;IAC7F,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB,wBAAwB,CAAC,EAAE,OAAO,CAAC;IACnC,SAAS,CAAC,EAAE,OAAO,CAAC;CACrB,GAAG,YAAY,CA0Cf;AAED,wBAAgB,mBAAmB,CAAC,EAClC,UAAU,EACV,SAAS,EACT,qCAAqC,EACrC,QAAe,GAChB,EAAE;IACD,UAAU,EAAE,aAAa,CAAC;IAC1B,SAAS,EAAE,SAAS,CAAC,SAAS,GAAG,eAAe,CAAC,aAAa,CAAC,EAAE;QAAC,MAAM,EAAE,GAAG,CAAA;KAAC,CAAC,CAAC;IAChF,qCAAqC,EAAE,OAAO,CAAC;IAC/C,QAAQ,CAAC,EAAE,OAAO,CAAC;CACpB,GAAG,eAAe,CAAC,aAAa,CAAC,EAAE,CAgCnC;AA+BD,MAAM,WAAW,iBAAiB;IAChC,QAAQ,EAAE,YAAY,CAAC;IACvB,IAAI,EAAE,MAAM,CAAC;IACb,GAAG,CAAC,EAAE,MAAM,CAAC;CACd;AACD,wBAAgB,wBAAwB,CAAC,WAAW,EAAE,iBAAiB,EAAE,GAAG,SAAS,GAAG,YAAY,EAAE,CAgBrG;AAKD,qBAAa,SAAS;IACpB,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAsC;IAEhE,GAAG,CAAC,QAAQ,EAAE,QAAQ,GAAG,QAAQ;IASjC,GAAG,CAAC,IAAI,EAAE,MAAM,GAAG,QAAQ,GAAG,SAAS;IAIvC,IAAI,IAAI,MAAM;IAId,KAAK,IAAI,SAAS,MAAM,EAAE;IAI1B,MAAM,IAAI,SAAS,QAAQ,EAAE;IAI5B,CAAC,MAAM,CAAC,QAAQ,CAAC;IAMlB,QAAQ,IAAI,YAAY,EAAE,GAAG,SAAS;IAgBtC,QAAQ,IAAI,MAAM;CAGnB;AAED,eAAO,MAAM,WAAW,6DAAkD,CAAC;AAE3E,eAAO,MAAM,eAAe,6DAG1B,CAAC;AAEH,eAAO,MAAM,cAAc,6DASzB,CAAC;AAEH,eAAO,MAAM,0BAA0B,+DAAmD,CAAC;AAE3F,eAAO,MAAM,gBAAgB,aAAa,CAAC;AAC3C,eAAO,MAAM,iBAAiB,cAAc,CAAC;AAE7C,eAAO,MAAM,2BAA2B,EAAE,SAAS,MAAM,EAA4C,CAAC;AAEtG,qBAAa,QAAQ;IAEjB,QAAQ,CAAC,IAAI,EAAE,MAAM;IACrB,QAAQ,CAAC,GAAG,EAAE,MAAM;IACpB,QAAQ,CAAC,MAAM,EAAE,MAAM;gBAFd,IAAI,EAAE,MAAM,EACZ,GAAG,EAAE,MAAM,EACX,MAAM,EAAE,MAAM;IAOzB,QAAQ,IAAI,kBAAkB;IAM9B,cAAc,IAAI,OAAO;IAKzB,OAAO,CAAC,uBAAuB;IAmC/B,WAAW,IAAI,QAAQ;IAMvB,QAAQ,IAAI,QAAQ;IAiBpB,OAAO,CAAC,kBAAkB;IAS1B,OAAO,CAAC,aAAa;IAerB,OAAO,CAAC,6BAA6B;IAgCrC,QAAQ,CAAC,gBAAgB,GAAE,YAAkC;CAY9D;AAED,MAAM,MAAM,eAAe,GAAG,OAAO,GAAG;IAAE,QAAQ,EAAE,MAAM,CAAA;CAAE,CAAC;AAE7D,wBAAgB,oBAAoB,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,GAAG,eAAe,CAYrF;AAED,wBAAgB,kBAAkB,CAAC,CAAC,EAAE,YAAY,EAAE,YAAY,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,mBAAmB,GAAG,YAAY,CAoBvH;AAyGD,MAAM,MAAM,6BAA6B,GAAG,SAAS,CAAC,eAAe,CAAC,UAAU,GAAG,aAAa,CAAC,EAAE;IAAC,MAAM,EAAE,GAAG,CAAA;CAAC,CAAC,CAAA;AAajH,wBAAgB,iCAAiC,CAC/C,MAAM,EAAE,MAAM,EACd,UAAU,GAAE,CAAC,KAAK,EAAE,eAAe,CAAC,GAAG,CAAC,EAAE,QAAQ,EAAE,6BAA6B,EAAE,OAAO,CAAC,EAAE,6BAA6B,KAAK,IAAe,QAoB/I"}