import { Resolver, Query, Context } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';

import { JwtAuthGuard, RolesGuard } from '@app/security/guards';
import { Roles } from '@app/security/decorators/roles.decorator';
import { UserRole } from '@app/common';
import { RequestWithUser } from '@app/security';

import { PositionService } from '../services/position.service';

@Resolver('Position')
@UseGuards(JwtAuthGuard, RolesGuard)
export class PositionResolver {
  constructor(private readonly positionService: PositionService) {}

  @Query('positions')
  @Roles(UserRole.ADMIN, UserRole.HR_MANAGER, UserRole.MANAGER, UserRole.EMPLOYEE, UserRole.SUPER_ADMIN)
  async getPositions(@Context('req') req: RequestWithUser) {
    return this.positionService.findAll(req.user.tenantId);
  }
}
