"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MermaidGraph = void 0;
const querygraph_1 = require("./querygraph");
class MermaidGraph {
    constructor(graph, options = {}) {
        this.graph = graph;
        this.options = options;
        this.before = [];
        this.after = [];
        this.subgraphs = new Map();
        this.isBuilt = false;
        for (const name of graph.sources.keys()) {
            if (name === this.graph.name || name === querygraph_1.FEDERATED_GRAPH_ROOT_SOURCE) {
                continue;
            }
            this.subgraphs.set(name, []);
        }
    }
    subgraphName(vertex) {
        if (vertex.source === this.graph.name || vertex.source === querygraph_1.FEDERATED_GRAPH_ROOT_SOURCE) {
            return undefined;
        }
        return vertex.source;
    }
    vertexName(vertex) {
        if ((0, querygraph_1.isFederatedGraphRootType)(vertex.type)) {
            return `root-${vertex.type.name.slice(1, vertex.type.name.length - 1)}`;
        }
        const sg = this.subgraphName(vertex);
        const n = sg ? `${vertex.type.name}-${sg}` : `${vertex.type.name}`;
        return vertex.provideId ? `${n}-${vertex.provideId}` : n;
    }
    addVertex(vertex) {
        const sg = this.subgraphName(vertex);
        const addTo = sg ? this.subgraphs.get(sg) : this.before;
        if ((0, querygraph_1.isFederatedGraphRootType)(vertex.type)) {
            addTo.push(`${this.vertexName(vertex)}(["root(${vertex.type.name.slice(1, vertex.type.name.length)})"])`);
        }
        else {
            addTo.push(`${this.vertexName(vertex)}["${vertex.toString()}"]`);
        }
    }
    addEdge(edge) {
        var _a;
        switch (edge.transition.kind) {
            case 'FieldCollection':
                if (edge.transition.definition.name.startsWith('_')) {
                    return false;
                }
                break;
            case 'RootTypeResolution':
                if (!((_a = this.options.includeRootTypeLinks) !== null && _a !== void 0 ? _a : true)) {
                    return false;
                }
                break;
            case 'SubgraphEnteringTransition':
                const rt = edge.tail.type;
                if (rt.fields().filter((f) => !f.name.startsWith('_')).length === 0) {
                    return false;
                }
                break;
        }
        const head = this.vertexName(edge.head);
        const tail = this.vertexName(edge.tail);
        const addTo = edge.head.source !== this.graph.name && edge.head.source === edge.tail.source
            ? this.subgraphs.get(edge.head.source)
            : this.after;
        const label = edge.label();
        if (label.length === 0) {
            addTo.push(`${head} --> ${tail}`);
        }
        else {
            addTo.push(`${head} -->|"${label}"| ${tail}`);
        }
        return true;
    }
    build() {
        if (this.isBuilt) {
            return;
        }
        (0, querygraph_1.simpleTraversal)(this.graph, (v) => this.addVertex(v), (e) => this.addEdge(e));
        this.isBuilt = true;
    }
    toString() {
        this.build();
        const final = ['flowchart TD'];
        this.before.forEach((b) => final.push('  ' + b));
        for (const [name, data] of this.subgraphs.entries()) {
            final.push(`  subgraph ${name}`);
            data.forEach((d) => final.push('    ' + d));
            final.push('  end');
        }
        this.after.forEach((a) => final.push('  ' + a));
        return final.join('\n');
    }
}
exports.MermaidGraph = MermaidGraph;
//# sourceMappingURL=mermaid.js.map