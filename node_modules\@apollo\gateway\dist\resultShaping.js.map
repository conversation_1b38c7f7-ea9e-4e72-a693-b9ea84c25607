{"version": 3, "file": "resultShaping.js", "sourceRoot": "", "sources": ["../src/resultShaping.ts"], "names": [], "mappings": ";;;AAAA,uEAoBsC;AAgCtC,SAAgB,eAAe,CAAC,EAC9B,SAAS,EACT,SAAS,EACT,KAAK,EACL,qBAAqB,GAMtB;IAIC,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;IACrC,CAAC;IAED,MAAM,UAAU,GAAG;QACjB,MAAM,EAAE,SAAS,CAAC,MAAM,EAAE;QAC1B,SAAS,EAAE;YACT,GAAG,SAAS,CAAC,8BAA8B,EAAE;YAE7C,GAAG,SAAS;SACb;QACD,MAAM,EAAE,EAAE;QACV,qBAAqB;KACtB,CAAC;IAEF,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAEjC,MAAM,GAAG,GAAG,iBAAiB,CAAC;QAC5B,KAAK;QACL,YAAY,EAAE,SAAS,CAAC,YAAY;QACpC,MAAM,EAAE,IAAI;QACZ,UAAU;QACV,IAAI,EAAE,EAAE;QACR,UAAU,EAAE,SAAS,CAAC,MAAM,EAAE,CAAC,gBAAgB,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAE;KAC9E,CAAC,CAAC;IAEH,OAAO;QACL,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI;QACtD,MAAM,EAAE,UAAU,CAAC,MAAM;KAC1B,CAAC;AACJ,CAAC;AA5CD,0CA4CC;AAUD,SAAS,UAAU,CAAC,OAAyB,EAAE,UAAsB;IACnE,MAAM,aAAa,GAAG,OAAO,CAAC,mBAAmB,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACxF,MAAM,gBAAgB,GAAG,OAAO,CAAC,mBAAmB,CAAC,UAAU,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9F,OAAO,CAAC,aAAa,IAAI,OAAO,CAAC,aAAa,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC;WACjE,CAAC,gBAAgB,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC;AAC9E,CAAC;AAED,SAAS,OAAO,CAAC,SAAqD,EAAE,SAA8B;IACpG,MAAM,KAAK,GAAG,SAAS,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC;IACvC,IAAI,KAAK,YAAY,+BAAQ,EAAE,CAAC;QAC9B,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAEpC,IAAA,6BAAM,EAAC,KAAK,KAAK,SAAS,IAAI,OAAO,KAAK,KAAK,SAAS,EAAE,GAAG,EAAE,CAAC,oBAAoB,KAAK,iBAAiB,KAAK,OAAO,SAAS,EAAE,CAAC,CAAC;QACnI,OAAO,KAAK,CAAC;IACf,CAAC;SAAM,CAAC;QACN,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAED,IAAK,WAAkC;AAAvC,WAAK,WAAW;IAAG,yCAAE,CAAA;IAAE,iEAAc,CAAA;AAAC,CAAC,EAAlC,WAAW,KAAX,WAAW,QAAuB;AAEvC,SAAS,oBAAoB,CAC3B,MAAc,EACd,aAAwC,EACxC,QAA4B,EAC5B,UAAyB;IAEzB,IAAI,CAAC,aAAa,EAAE,CAAC;QACnB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,QAAQ,EAAE,CAAC;QACb,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACnC,OAAO,CAAC,CAAC,IAAI,IAAI,IAAA,gCAAS,EAAC,aAAa,EAAE,IAAI,CAAC,CAAC;IAClD,CAAC;SAAM,CAAC;QAGN,OAAO,IAAA,gCAAS,EAAC,aAAa,EAAE,UAAU,CAAC,CAAC;IAC9C,CAAC;AACH,CAAC;AAED,SAAS,iBAAiB,CAAC,EACzB,KAAK,EACL,YAAY,EACZ,MAAM,EACN,UAAU,EACV,IAAI,EACJ,UAAU,GAQX;;IACC,KAAK,MAAM,SAAS,IAAI,YAAY,CAAC,UAAU,EAAE,EAAE,CAAC;QAClD,IAAI,UAAU,CAAC,SAAS,CAAC,OAAO,EAAE,UAAU,CAAC,EAAE,CAAC;YAC9C,SAAS;QACX,CAAC;QAED,IAAI,SAAS,CAAC,IAAI,KAAK,gBAAgB,EAAE,CAAC;YACxC,MAAM,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC;YAChC,MAAM,SAAS,GAAG,KAAK,CAAC,UAAU,CAAC,IAAK,CAAC;YACzC,MAAM,YAAY,GAAG,KAAK,CAAC,YAAY,EAAE,CAAC;YAC1C,MAAM,WAAW,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC;YAEzC,IAAI,KAAK,CAAC,UAAU,CAAC,0BAA0B,EAAE,EAAE,CAAC;gBAClD,IAAI,WAAW,KAAK,SAAS,EAAE,CAAC;oBAC9B,MAAM,CAAC,YAAY,CAAC,GAAG,UAAU,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC;gBACrE,CAAC;gBACD,SAAS;YACX,CAAC;YAED,IAAI,UAAU,GAAG,MAAA,KAAK,CAAC,YAAY,CAAC,mCAAI,IAAI,CAAC;YAmB7C,IAAI,KAAK,CAAC,IAAI,KAAK,wCAAiB,EAAE,CAAC;gBAGrC,IAAI,WAAW,KAAK,SAAS,EAAE,CAAC;oBAK9B,IAAI,UAAU,KAAK,IAAI,IAAI,YAAY,KAAK,wCAAiB,EAAE,CAAC;wBAC9D,UAAU,GAAG,MAAA,KAAK,CAAC,wCAAiB,CAAC,mCAAI,IAAI,CAAC;oBAChD,CAAC;oBAID,MAAM,IAAI,GAAG,UAAU,KAAK,IAAI,IAAI,OAAO,UAAU,KAAK,QAAQ;wBAChE,CAAC,CAAC,MAAA,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,mCAAI,UAAU;wBAClD,CAAC,CAAC,UAAU,CAAC;oBAIf,IAAI,CAAC,IAAA,mCAAY,EAAC,IAAI,CAAC,EAAE,CAAC;wBACxB,OAAO,WAAW,CAAC,cAAc,CAAC;oBACpC,CAAC;oBACD,MAAM,CAAC,YAAY,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;gBACnC,CAAC;gBACD,SAAS;YACX,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACxB,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,iBAAiB,CAAC;gBAC/C,WAAW;gBACX,IAAI,EAAE,SAAS;gBACf,UAAU;gBACV,YAAY,EAAE,SAAS,CAAC,YAAY;gBACpC,IAAI;gBACJ,UAAU;gBACV,UAAU;aACX,CAAC,CAAC;YACH,MAAM,CAAC,YAAY,CAAC,GAAG,OAAO,CAAA;YAC9B,IAAI,CAAC,GAAG,EAAE,CAAC;YACX,IAAI,SAAS,EAAE,CAAC;gBACd,OAAO,WAAW,CAAC,cAAc,CAAC;YACpC,CAAC;QACH,CAAC;aAAM,CAAC;YACN,MAAM,QAAQ,GAAG,SAAS,CAAC,OAAO,CAAC;YACnC,MAAM,QAAQ,GAAG,KAAK,CAAC,wCAAiB,CAAC,CAAC;YAC1C,IAAA,6BAAM,EAAC,CAAC,QAAQ,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,GAAG,EAAE,CAAC,wCAAwC,QAAQ,EAAE,CAAC,CAAC;YAC5G,IAAI,oBAAoB,CAAC,UAAU,CAAC,MAAM,EAAE,QAAQ,CAAC,aAAa,EAAE,QAAQ,EAAE,UAAU,CAAC,EAAE,CAAC;gBAC1F,MAAM,GAAG,GAAG,iBAAiB,CAAC;oBAC5B,KAAK;oBACL,YAAY,EAAE,SAAS,CAAC,YAAY;oBACpC,MAAM;oBACN,UAAU;oBACV,IAAI;oBACJ,UAAU,EAAE,MAAA,QAAQ,CAAC,aAAa,mCAAI,UAAU;iBACjD,CAAC,CAAC;gBACH,IAAI,GAAG,KAAK,WAAW,CAAC,cAAc,EAAE,CAAC;oBACvC,OAAO,WAAW,CAAC,cAAc,CAAC;gBACpC,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,WAAW,CAAC,EAAE,CAAC;AACxB,CAAC;AAED,SAAS,0BAA0B,CAAC,IAAkB,EAAE,WAAiB,EAAE,UAAyB;IAClG,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACtC,IAAA,6BAAM,EAAC,OAAO,KAAK,SAAS,EAAE,8CAA8C,CAAC,CAAC;IAC9E,OAAO,OAAO,OAAO,KAAK,QAAQ;QAChC,CAAC,CAAC,SAAS,UAAU,IAAI,OAAO,EAAE;QAClC,CAAC,CAAC,yBAAyB,WAAW,aAAa,OAAO,EAAE,CAAC;AACjE,CAAC;AAOD,SAAS,iBAAiB,CAAC,EACzB,WAAW,EACX,IAAI,EACJ,UAAU,EACV,YAAY,EACZ,IAAI,EACJ,UAAU,EACV,UAAU,GASX;IAUC,IAAA,6BAAM,EAAC,UAAU,KAAK,SAAS,EAAE,2DAA2D,CAAC,CAAC;IAE9F,IAAI,WAAW,KAAK,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;QAQzE,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;IAClC,CAAC;IAED,IAAI,IAAA,oCAAa,EAAC,IAAI,CAAC,EAAE,CAAC;QACxB,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,iBAAiB,CAAC;YAC/C,WAAW;YACX,IAAI,EAAE,IAAI,CAAC,MAAM;YACjB,UAAU;YACV,YAAY;YACZ,IAAI;YACJ,UAAU;YACV,UAAU;SACX,CAAC,CAAC;QACH,IAAI,OAAO,KAAK,IAAI,EAAE,CAAC;YACrB,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,6BAAM,CAAC,eAAe,CAAC,GAAG,CAC/C,uCAAuC,0BAA0B,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,UAAU,CAAC,GAAG,EACnG,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAC3B,CAAC,CAAC;YACL,CAAC;YACD,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC;QACvD,CAAC;QACD,OAAO,EAAE,OAAO,EAAE,CAAC;IACrB,CAAC;IAID,IAAI,UAAU,KAAK,IAAI,EAAE,CAAC;QAGxB,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAC3B,CAAC;IAED,IAAI,IAAA,iCAAU,EAAC,IAAI,CAAC,EAAE,CAAC;QAOrB,IAAA,6BAAM,EAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,GAAG,EAAE,CAAC,0BAA0B,UAAU,uCAAuC,IAAI,EAAE,CAAC,CAAA;QAC1H,IAAA,6BAAM,EAAC,WAAW,KAAK,SAAS,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,GAAG,EAAE,CAAC,0BAA0B,WAAW,uCAAuC,IAAI,EAAE,CAAC,CAAA;QACzJ,MAAM,eAAe,GAAU,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC;QAEtH,IAAA,6BAAM,EAAC,UAAU,CAAC,MAAM,KAAK,eAAe,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,IAAI,UAAU,aAAa,UAAU,CAAC,MAAM,UAAU,eAAe,aAAa,eAAe,CAAC,MAAM,GAAG,CAAC,CAAA;QACvK,IAAI,aAAa,GAAG,KAAK,CAAC;QAC1B,IAAI,SAAS,GAAG,KAAK,CAAC;QACtB,MAAM,OAAO,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,GAAG,EAAE,EAAE;YAC1D,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACf,MAAM,GAAG,GAAG,iBAAiB,CAAC;gBAC5B,WAAW,EAAE,cAAc;gBAC3B,IAAI,EAAE,IAAI,CAAC,MAAM;gBACjB,UAAU,EAAE,UAAU,CAAC,GAAG,CAAC;gBAC3B,YAAY;gBACZ,IAAI;gBACJ,UAAU;gBACV,UAAU;aACX,CAAC,CAAC;YACH,IAAI,CAAC,GAAG,EAAE,CAAC;YAGX,aAAa,KAAb,aAAa,GAAK,CAAC,CAAC,GAAG,CAAC,SAAS,EAAC;YAClC,SAAS,KAAT,SAAS,GAAK,CAAC,CAAC,GAAG,CAAC,SAAS,EAAC;YAC9B,OAAO,GAAG,CAAC,OAAO,CAAC;QACrB,CAAC,CAAC,CAAC;QAGH,OAAO,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE,SAAS,EAAE,CAAA;IAC/D,CAAC;IAED,IAAI,IAAA,sCAAe,EAAC,IAAI,CAAC,EAAE,CAAC;QAC1B,IAAA,6BAAM,EAAC,YAAY,EAAE,GAAG,EAAE,CAAC,kDAAkD,IAAI,EAAE,CAAC,CAAC;QACrF,IAAA,6BAAM,EAAC,OAAO,UAAU,KAAK,QAAQ,EAAE,GAAG,EAAE,CAAC,4BAA4B,UAAU,4CAA4C,IAAI,EAAE,CAAC,CAAA;QACtI,IAAA,6BAAM,EAAC,WAAW,KAAK,SAAS,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE,GAAG,EAAE,CAAC,4BAA4B,UAAU,4CAA4C,IAAI,EAAE,CAAC,CAAA;QAEpK,MAAM,aAAa,GAAG,UAAU,CAAC,wCAAiB,CAAC,CAAC;QACpD,IAAA,6BAAM,EAAC,aAAa,KAAK,SAAS,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE,GAAG,EAAE,CAAC,4BAA4B,aAAa,sBAAsB,IAAI,EAAE,CAAC,CAAA;QACrJ,IAAI,OAAO,GAAG,IAAI,CAAC;QACnB,IAAI,aAAa,EAAE,CAAC;YAGlB,MAAM,YAAY,GAAG,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAC3D,IAAI,CAAC,YAAY,IAAI,CAAC,IAAA,sCAAe,EAAC,YAAY,CAAC,EAAE,CAAC;gBAKpD,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,6BAAM,CAAC,eAAe,CAAC,GAAG,CAC/C,0CAA0C,0BAA0B,CAAC,IAAI,EAAE,IAAI,EAAE,UAAU,CAAC,GAAG,EAC/F,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAC3B,CAAC,CAAC;gBACH,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC;YAC5C,CAAC;YACD,OAAO,GAAG,YAAY,CAAC;QACzB,CAAC;QAED,MAAM,iBAAiB,GAAwB,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC;QAE7G,MAAM,GAAG,GAAG,iBAAiB,CAAC;YAC5B,KAAK,EAAE,UAAU;YACjB,YAAY;YACZ,MAAM,EAAE,iBAAiB;YACzB,UAAU;YACV,IAAI;YACJ,UAAU,EAAE,OAAO;SACpB,CAAC,CAAC;QAGH,MAAM,SAAS,GAAG,GAAG,KAAK,WAAW,CAAC,cAAc,CAAC;QACrD,OAAO,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,iBAAiB,EAAE,SAAS,EAAE,CAAC;IACtE,CAAC;IAKD,IAAA,6BAAM,EAAC,WAAW,KAAK,SAAS,EAAE,GAAG,EAAE,CAAC,2CAA2C,IAAI,aAAa,IAAI,EAAE,CAAC,CAAA;IAE3G,MAAM,YAAY,GAAG,IAAA,uCAAgB,EAAC,UAAU,CAAC,MAAM,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;IAC3E,IAAI,CAAC,YAAY,EAAE,CAAC;QAClB,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,6BAAM,CAAC,eAAe,CAAC,GAAG,CAC/C,2BAA2B,0BAA0B,CAAC,IAAI,EAAE,IAAI,EAAE,UAAU,CAAC,GAAG,EAChF,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAC3B,CAAC,CAAC;IACL,CAAC;IAGD,OAAO,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,YAAY,EAAC,CAAC;AAChF,CAAC"}