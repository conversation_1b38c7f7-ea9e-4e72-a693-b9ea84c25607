import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// Entities
import {
  Position,
  Department,
  Employee,
  User,
  Tenant,
  AuditLog,
} from '@app/database';

// Controllers
import { PositionController } from './position.controller';

// Services
import { PositionService } from './services/position.service';
import { PositionValidationService } from './services/position-validation.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Position,
      Department,
      Employee,
      User,
      Tenant,
      AuditLog,
    ]),
  ],
  controllers: [PositionController],
  providers: [
    PositionService,
    PositionValidationService,
  ],
  exports: [
    PositionService,
    PositionValidationService,
  ],
})
export class PositionModule {}
