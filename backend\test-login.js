const axios = require('axios');

async function testDirectBackend() {
  console.log('Testing direct backend...');
  try {
    const response = await axios.post('http://localhost:4000/api/v1/auth/login', {
      email: '<EMAIL>',
      password: 'Password1234'
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('Direct Backend Success:', response.data);
  } catch (error) {
    console.log('Direct Backend Error:', error.response?.data || error.message);
  }
}

async function testFrontendProxy() {
  console.log('\nTesting frontend proxy...');
  try {
    const response = await axios.post('http://localhost:3002/api/v1/auth/login', {
      email: '<EMAIL>',
      password: 'Password1234'
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('Frontend Proxy Success:', response.data);
  } catch (error) {
    console.log('Frontend Proxy Error:', error.response?.data || error.message);
    console.log('Status:', error.response?.status);
  }
}

async function runTests() {
  await testDirectBackend();
  await testFrontendProxy();
}

runTests();
