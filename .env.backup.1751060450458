# =============================================================================
# PeopleNest HRMS - Environment Configuration Template
# =============================================================================
# Copy this file to .env and update the values according to your environment
# =============================================================================

# Application Configuration
NODE_ENV=development
APP_NAME=PeopleNest HRMS
APP_VERSION=1.0.0
APP_PORT=4000
API_PREFIX=api/v1

# Database Configuration
# PostgreSQL Primary Database
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=peoplenest_dev
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=postgres123
DATABASE_SSL=false
DATABASE_LOGGING=true
DATABASE_SYNCHRONIZE=false
DATABASE_MIGRATIONS_RUN=true

# PostgreSQL Test Database
TEST_DATABASE_NAME=peoplenest_test
TEST_DATABASE_HOST=localhost
TEST_DATABASE_PORT=5432
TEST_DATABASE_USERNAME=postgres
TEST_DATABASE_PASSWORD=postgres123

# MongoDB Configuration
MONGODB_URI=*************************************************************************
MONGODB_DATABASE=peoplenest_docs

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_TTL=3600

# JWT Configuration
JWT_SECRET=OPkObONjhZkSssN56NMuVmZ_66Kecw0TwZVpQkUFLH44KqpCdOrkR4_RsHlxUUnGIAS_5czAVCPHM8sG7mBAVg
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=qmMGff5U9YhxjQqTJ3cT2-4xSSj_Id1It_4a12usOsESIsOFlM-Dk9PCkUpjXLCj_c67Qmz6woQbCpapwvSYfw
JWT_REFRESH_EXPIRES_IN=7d

# Encryption Configuration
ENCRYPTION_KEY=29018730ca123d90b2433a4e5b2fbc00ae1cf61bd4848a8f77d7d080ee425d0e
ENCRYPTION_MASTER_KEY=e4bc8949052f2fc9e35674e9a454b372c4968265c35ba43c7c682b5447accb9e82f53be664cde050eff7fbd007b3bd37b6175dd7f5def1b9ff524d8d447aced5
ENCRYPTION_ALGORITHM=aes-256-gcm
HASH_SALT_ROUNDS=12
INDEX_HASH_SALT=7e6b4993ee9d3d909be264f5d993fe113b8dc0a407fc3b767ef5fe8d4e845b2e

# OAuth Configuration (Auth0)
AUTH0_DOMAIN=your-auth0-domain.auth0.com
AUTH0_CLIENT_ID=your-auth0-client-id
AUTH0_CLIENT_SECRET=your-auth0-client-secret
AUTH0_AUDIENCE=https://api.peoplenest.com

# Email Configuration (SendGrid)
SENDGRID_API_KEY=your-sendgrid-api-key
FROM_EMAIL=<EMAIL>
FROM_NAME=PeopleNest HRMS

# SMS Configuration (Twilio)
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********

# File Storage Configuration (AWS S3 / MinIO)
AWS_ACCESS_KEY_ID=minioadmin
AWS_SECRET_ACCESS_KEY=minioadmin123
AWS_REGION=us-east-1
AWS_S3_BUCKET=peoplenest-documents
S3_ENDPOINT=http://localhost:9000
S3_FORCE_PATH_STYLE=true

# Kafka Configuration
KAFKA_BROKERS=localhost:9092
KAFKA_CLIENT_ID=peoplenest-hrms
KAFKA_GROUP_ID=peoplenest-consumer-group

# Elasticsearch Configuration
ELASTICSEARCH_NODE=http://localhost:9200
ELASTICSEARCH_INDEX_PREFIX=peoplenest

# Monitoring Configuration
PROMETHEUS_ENDPOINT=http://localhost:9090
JAEGER_ENDPOINT=http://localhost:14268/api/traces

# AI Services Configuration
AI_SERVICE_URL=http://localhost:8000
AI_SERVICE_API_KEY=your-ai-service-api-key
OPENAI_API_KEY=your-openai-api-key
HUGGINGFACE_API_KEY=your-huggingface-api-key

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=false

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:3001
CORS_CREDENTIALS=true

# Security Configuration
HELMET_ENABLED=true
CSRF_PROTECTION=true
SESSION_SECRET=peoplenest-session-secret-2024-enterprise-hrms-secure

# Logging Configuration
LOG_LEVEL=debug
LOG_FORMAT=json
LOG_FILE_ENABLED=true
LOG_FILE_PATH=./logs/app.log
LOG_MAX_FILES=5
LOG_MAX_SIZE=10m

# Health Check Configuration
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_TIMEOUT=5000
HEALTH_CHECK_INTERVAL=30000

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=peoplenest-backups

# Multi-tenancy Configuration
MULTI_TENANT_ENABLED=true
DEFAULT_TENANT_ID=default
TENANT_ISOLATION_LEVEL=schema

# Payroll Configuration
PAYROLL_PROCESSING_ENABLED=true
PAYROLL_BATCH_SIZE=1000
PAYROLL_RETRY_ATTEMPTS=3
PAYROLL_NOTIFICATION_ENABLED=true

# Compliance Configuration
GDPR_ENABLED=true
CCPA_ENABLED=true
SOX_ENABLED=true
SOC2_ENABLED=true
AUDIT_LOG_RETENTION_DAYS=2555

# Security Monitoring Configuration
SECURITY_FAILED_LOGIN_THRESHOLD=5
SECURITY_SUSPICIOUS_ACTIVITY_THRESHOLD=10
SECURITY_DATA_ACCESS_THRESHOLD=100
SECURITY_SESSION_ANOMALY_THRESHOLD=3
SECURITY_MONITORING_ENABLED=true
THREAT_DETECTION_ENABLED=true
AUTO_BLOCK_THREATS=true

# Performance Configuration
CACHE_TTL=3600
QUERY_TIMEOUT=30000
CONNECTION_POOL_SIZE=10
MAX_CONCURRENT_REQUESTS=1000

# Development Configuration
DEBUG_MODE=true
HOT_RELOAD=true
SWAGGER_ENABLED=true
GRAPHQL_PLAYGROUND=true
PROFILING_ENABLED=false

# Test Configuration
TEST_TIMEOUT=30000
TEST_COVERAGE_THRESHOLD=80
E2E_TEST_ENABLED=true
LOAD_TEST_ENABLED=false

# Default Admin User Configuration
DEFAULT_ADMIN_USERNAME=awadhesh
DEFAULT_ADMIN_PASSWORD=awadhesh123
DEFAULT_ADMIN_EMAIL=<EMAIL>
DEFAULT_ADMIN_FIRST_NAME=Awadhesh
DEFAULT_ADMIN_LAST_NAME=Admin

# Feature Flags
FEATURE_AI_RESUME_PARSING=true
FEATURE_SENTIMENT_ANALYSIS=true
FEATURE_PREDICTIVE_ANALYTICS=true
FEATURE_MULTI_COUNTRY_PAYROLL=true
FEATURE_ADVANCED_REPORTING=true
FEATURE_MOBILE_APP=true
FEATURE_SLACK_INTEGRATION=true
FEATURE_TEAMS_INTEGRATION=true

# External API Configuration
SLACK_BOT_TOKEN=xoxb-your-slack-bot-token
SLACK_SIGNING_SECRET=your-slack-signing-secret
TEAMS_APP_ID=your-teams-app-id
TEAMS_APP_PASSWORD=your-teams-app-password

# Notification Configuration
NOTIFICATION_CHANNELS=email,sms,slack,teams
NOTIFICATION_RETRY_ATTEMPTS=3
NOTIFICATION_BATCH_SIZE=100

# Analytics Configuration
ANALYTICS_ENABLED=true
ANALYTICS_BATCH_SIZE=1000
ANALYTICS_FLUSH_INTERVAL=60000
CLICKHOUSE_HOST=localhost
CLICKHOUSE_PORT=8123
CLICKHOUSE_DATABASE=peoplenest_analytics
CLICKHOUSE_USERNAME=default
CLICKHOUSE_PASSWORD=
