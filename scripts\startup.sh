#!/bin/bash

# =============================================================================
# PeopleNest HRMS - Startup Script
# =============================================================================
# This script handles the complete initialization and startup of PeopleNest HRMS
# =============================================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if a service is running
check_service() {
    local service_name=$1
    local port=$2
    local max_attempts=30
    local attempt=1

    print_status "Checking if $service_name is running on port $port..."
    
    while [ $attempt -le $max_attempts ]; do
        if nc -z localhost $port 2>/dev/null; then
            print_success "$service_name is running on port $port"
            return 0
        fi
        
        print_status "Attempt $attempt/$max_attempts: Waiting for $service_name..."
        sleep 2
        ((attempt++))
    done
    
    print_error "$service_name is not responding on port $port after $max_attempts attempts"
    return 1
}

# Function to wait for PostgreSQL to be ready
wait_for_postgres() {
    print_status "Waiting for PostgreSQL to be ready..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if PGPASSWORD=postgres psql -h localhost -U postgres -d postgres -c "SELECT 1;" >/dev/null 2>&1; then
            print_success "PostgreSQL is ready"
            return 0
        fi
        
        print_status "Attempt $attempt/$max_attempts: Waiting for PostgreSQL..."
        sleep 2
        ((attempt++))
    done
    
    print_error "PostgreSQL is not ready after $max_attempts attempts"
    return 1
}

# Function to initialize database
init_database() {
    print_status "Initializing PeopleNest database..."
    
    # Create database if it doesn't exist
    PGPASSWORD=postgres psql -h localhost -U postgres -d postgres -c "CREATE DATABASE peoplenest_dev;" 2>/dev/null || true
    
    # Run initialization script
    if [ -f "scripts/init-database.sql" ]; then
        print_status "Running database initialization script..."
        PGPASSWORD=postgres psql -h localhost -U postgres -d peoplenest_dev -f scripts/init-database.sql
        print_success "Database initialization completed"
    else
        print_error "Database initialization script not found"
        return 1
    fi
    
    # Create admin user
    if [ -f "scripts/create-admin-user.sql" ]; then
        print_status "Creating default admin user..."
        PGPASSWORD=postgres psql -h localhost -U postgres -d peoplenest_dev -f scripts/create-admin-user.sql
        print_success "Admin user created successfully"
    else
        print_error "Admin user creation script not found"
        return 1
    fi
}

# Function to check Docker Compose status
check_docker_compose() {
    print_status "Checking Docker Compose services..."
    
    if ! docker-compose ps >/dev/null 2>&1; then
        print_error "Docker Compose is not running. Please start it first with: docker-compose up -d"
        return 1
    fi
    
    # Check individual services
    local services=("postgres:5432" "redis:6379" "mongodb:27017" "kafka:9092" "elasticsearch:9200" "minio:9000")
    
    for service in "${services[@]}"; do
        IFS=':' read -r name port <<< "$service"
        if ! check_service "$name" "$port"; then
            print_warning "$name is not ready yet"
        fi
    done
}

# Function to run database migrations
run_migrations() {
    print_status "Running database migrations..."
    
    if [ -f "package.json" ]; then
        if npm run migration:run >/dev/null 2>&1; then
            print_success "Database migrations completed"
        else
            print_warning "Migration command not found or failed"
        fi
    fi
}

# Function to seed initial data
seed_data() {
    print_status "Seeding initial data..."
    
    if [ -f "package.json" ]; then
        if npm run seed >/dev/null 2>&1; then
            print_success "Data seeding completed"
        else
            print_warning "Seed command not found or failed"
        fi
    fi
}

# Function to start the application
start_application() {
    print_status "Starting PeopleNest HRMS application..."
    
    # Check if we're in development mode
    if [ "${NODE_ENV:-development}" = "development" ]; then
        print_status "Starting in development mode..."
        npm run dev
    else
        print_status "Starting in production mode..."
        npm run start:prod
    fi
}

# Main execution
main() {
    echo "============================================================================="
    echo "                    PeopleNest HRMS - Startup Script"
    echo "============================================================================="
    echo ""
    
    # Check if .env file exists
    if [ ! -f ".env" ]; then
        print_error ".env file not found. Please create it from .env.example"
        exit 1
    fi
    
    # Load environment variables
    export $(grep -v '^#' .env | xargs)
    
    print_status "Starting PeopleNest HRMS initialization..."
    print_status "Environment: ${NODE_ENV:-development}"
    print_status "Application Port: ${APP_PORT:-3000}"
    print_status "Database: ${DATABASE_NAME:-peoplenest_dev}"
    echo ""
    
    # Check Docker Compose services
    check_docker_compose
    echo ""
    
    # Wait for PostgreSQL
    wait_for_postgres
    echo ""
    
    # Initialize database
    init_database
    echo ""
    
    # Run migrations
    run_migrations
    echo ""
    
    # Seed data
    seed_data
    echo ""
    
    print_success "PeopleNest HRMS initialization completed successfully!"
    echo ""
    echo "============================================================================="
    echo "                           SYSTEM READY"
    echo "============================================================================="
    echo ""
    echo "🎉 PeopleNest HRMS is ready to start!"
    echo ""
    echo "📋 Default Admin Credentials:"
    echo "   Username: awadhesh"
    echo "   Password: awadhesh123"
    echo "   Email: <EMAIL>"
    echo ""
    echo "🌐 Application URLs:"
    echo "   Frontend: http://localhost:${APP_PORT:-3000}"
    echo "   API: http://localhost:${APP_PORT:-3000}/api/v1"
    echo "   GraphQL: http://localhost:${APP_PORT:-3000}/graphql"
    echo ""
    echo "🔧 Infrastructure Services:"
    echo "   PostgreSQL: localhost:5432"
    echo "   Redis: localhost:6379"
    echo "   MongoDB: localhost:27017"
    echo "   Elasticsearch: localhost:9200"
    echo "   MinIO: localhost:9000"
    echo "   Kafka: localhost:9092"
    echo "   Prometheus: localhost:9090"
    echo "   Grafana: localhost:3001"
    echo ""
    echo "============================================================================="
    echo ""
    
    # Ask user if they want to start the application
    read -p "Do you want to start the application now? (y/n): " -n 1 -r
    echo ""
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo ""
        start_application
    else
        print_status "You can start the application later with: npm run dev"
        print_status "Or use this script again: ./scripts/startup.sh"
    fi
}

# Check if script is being run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
