import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards, ParseUUIDPipe } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard, RolesGuard, TenantGuard } from '@app/security';
import { Roles, CurrentUser, CurrentTenant } from '@app/common';
import { UserRole } from '@app/common/enums/user-role.enum';
import { EmployeeBenefitService } from '../services/employee-benefit.service';

@ApiTags('Employee Benefits')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard, TenantGuard, RolesGuard)
@Controller('employee-benefits')
export class EmployeeBenefitController {
  constructor(private readonly employeeBenefitService: EmployeeBenefitService) {}

  @Post()
  @Roles(UserRole.ADMIN, UserRole.HR)
  @ApiOperation({ summary: 'Create employee benefit' })
  async create(@Body() createEmployeeBenefitDto: any, @CurrentUser() user: any, @CurrentTenant() tenantId: string) {
    return this.employeeBenefitService.create(createEmployeeBenefitDto, user.id, tenantId);
  }

  @Get()
  @Roles(UserRole.ADMIN, UserRole.HR, UserRole.MANAGER, UserRole.EMPLOYEE)
  @ApiOperation({ summary: 'Get employee benefits' })
  async findAll(@Query() query: any, @CurrentUser() user: any, @CurrentTenant() tenantId: string) {
    return this.employeeBenefitService.findAll(query, user, tenantId);
  }

  @Get(':id')
  @Roles(UserRole.ADMIN, UserRole.HR, UserRole.MANAGER, UserRole.EMPLOYEE)
  @ApiOperation({ summary: 'Get employee benefit by ID' })
  async findOne(@Param('id', ParseUUIDPipe) id: string, @CurrentUser() user: any, @CurrentTenant() tenantId: string) {
    return this.employeeBenefitService.findOne(id, user, tenantId);
  }

  @Put(':id')
  @Roles(UserRole.ADMIN, UserRole.HR)
  @ApiOperation({ summary: 'Update employee benefit' })
  async update(@Param('id', ParseUUIDPipe) id: string, @Body() updateEmployeeBenefitDto: any, @CurrentUser() user: any, @CurrentTenant() tenantId: string) {
    return this.employeeBenefitService.update(id, updateEmployeeBenefitDto, user.id, tenantId);
  }

  @Delete(':id')
  @Roles(UserRole.ADMIN, UserRole.HR)
  @ApiOperation({ summary: 'Delete employee benefit' })
  async remove(@Param('id', ParseUUIDPipe) id: string, @CurrentUser() user: any, @CurrentTenant() tenantId: string) {
    return this.employeeBenefitService.remove(id, user.id, tenantId);
  }
}
