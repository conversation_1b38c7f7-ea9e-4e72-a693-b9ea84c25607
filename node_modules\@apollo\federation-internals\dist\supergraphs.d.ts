import { DocumentNode } from "graphql";
import { CoreFeatures, Schema } from "./definitions";
import { JoinSpecDefinition } from "./specs/joinSpec";
import { ContextSpecDefinition } from "./specs/contextSpec";
import { CostSpecDefinition } from "./specs/costSpec";
import { Subgraphs } from ".";
export declare const DEFAULT_SUPPORTED_SUPERGRAPH_FEATURES: Set<string>;
export declare const ROUTER_SUPPORTED_SUPERGRAPH_FEATURES: Set<string>;
export declare function validateSupergraph(supergraph: Schema): [
    CoreFeatures,
    JoinSpecDefinition,
    ContextSpecDefinition | undefined,
    CostSpecDefinition | undefined
];
export declare function isFed1Supergraph(supergraph: Schema): boolean;
export declare class Supergraph {
    readonly schema: Schema;
    private readonly shouldValidate;
    private readonly containedSubgraphs;
    private _subgraphs?;
    private _subgraphNameToGraphEnumValue?;
    constructor(schema: Schema, supportedFeatures?: Set<string> | null, shouldValidate?: boolean);
    static build(supergraphSdl: string | DocumentNode, options?: {
        supportedFeatures?: Set<string> | null;
        validateSupergraph?: boolean;
    }): Supergraph;
    static buildForTests(supergraphSdl: string | DocumentNode, validateSupergraph?: boolean): Supergraph;
    subgraphsMetadata(): readonly {
        name: string;
        url: string;
    }[];
    subgraphs(): Subgraphs;
    subgraphNameToGraphEnumValue(): Map<string, string>;
    apiSchema(): Schema;
}
//# sourceMappingURL=supergraphs.d.ts.map