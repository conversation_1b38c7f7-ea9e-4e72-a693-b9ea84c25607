import React, { useEffect } from 'react';
import { X, CheckCircle, AlertCircle, AlertTriangle, Info } from 'lucide-react';
import { useAppSelector, useAppDispatch } from '@/store';
import { removeNotification } from '@/store/slices/ui-slice';
import { cn } from '@/lib/utils';

const iconMap = {
  success: CheckCircle,
  error: AlertCircle,
  warning: AlertTriangle,
  info: Info,
};

const colorMap = {
  success: 'bg-green-50 border-green-200 text-green-800',
  error: 'bg-red-50 border-red-200 text-red-800',
  warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
  info: 'bg-blue-50 border-blue-200 text-blue-800',
};

const iconColorMap = {
  success: 'text-green-400',
  error: 'text-red-400',
  warning: 'text-yellow-400',
  info: 'text-blue-400',
};

interface ToastProps {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
  onRemove: (id: string) => void;
}

const Toast: React.FC<ToastProps> = ({
  id,
  type,
  title,
  message,
  duration = 5000,
  onRemove,
}) => {
  const Icon = iconMap[type];

  useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(() => {
        onRemove(id);
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [id, duration, onRemove]);

  return (
    <div
      className={cn(
        'pointer-events-auto w-full max-w-sm overflow-hidden rounded-lg border shadow-lg',
        'transform transition-all duration-300 ease-in-out',
        'animate-in slide-in-from-top-2',
        colorMap[type]
      )}
      role="alert"
    >
      <div className="p-4">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <Icon className={cn('h-5 w-5', iconColorMap[type])} aria-hidden="true" />
          </div>
          <div className="ml-3 w-0 flex-1">
            <p className="text-sm font-medium">{title}</p>
            {message && (
              <p className="mt-1 text-sm opacity-90">{message}</p>
            )}
          </div>
          <div className="ml-4 flex flex-shrink-0">
            <button
              type="button"
              className={cn(
                'inline-flex rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2',
                'hover:opacity-75 transition-opacity',
                type === 'success' && 'text-green-400 focus:ring-green-500',
                type === 'error' && 'text-red-400 focus:ring-red-500',
                type === 'warning' && 'text-yellow-400 focus:ring-yellow-500',
                type === 'info' && 'text-blue-400 focus:ring-blue-500'
              )}
              onClick={() => onRemove(id)}
            >
              <span className="sr-only">Close</span>
              <X className="h-4 w-4" aria-hidden="true" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export const Toaster: React.FC = () => {
  const notifications = useAppSelector((state) => state.ui.notifications);
  const dispatch = useAppDispatch();

  const handleRemove = (id: string) => {
    dispatch(removeNotification(id));
  };

  if (notifications.length === 0) {
    return null;
  }

  return (
    <div
      aria-live="assertive"
      className="pointer-events-none fixed inset-0 z-50 flex items-end px-4 py-6 sm:items-start sm:p-6"
    >
      <div className="flex w-full flex-col items-center space-y-4 sm:items-end">
        {notifications.map((notification) => (
          <Toast
            key={notification.id}
            id={notification.id}
            type={notification.type}
            title={notification.title}
            message={notification.message}
            duration={notification.duration}
            onRemove={handleRemove}
          />
        ))}
      </div>
    </div>
  );
};

// Hook to use toaster
export const useToast = () => {
  const dispatch = useAppDispatch();

  const toast = {
    success: (title: string, message?: string, duration?: number) => {
      dispatch({
        type: 'ui/addNotification',
        payload: { type: 'success', title, message, duration },
      });
    },
    error: (title: string, message?: string, duration?: number) => {
      dispatch({
        type: 'ui/addNotification',
        payload: { type: 'error', title, message, duration },
      });
    },
    warning: (title: string, message?: string, duration?: number) => {
      dispatch({
        type: 'ui/addNotification',
        payload: { type: 'warning', title, message, duration },
      });
    },
    info: (title: string, message?: string, duration?: number) => {
      dispatch({
        type: 'ui/addNotification',
        payload: { type: 'info', title, message, duration },
      });
    },
  };

  return { toast };
};
