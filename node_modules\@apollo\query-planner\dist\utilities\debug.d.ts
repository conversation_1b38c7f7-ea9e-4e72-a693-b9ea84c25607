export declare class DebugLogger {
    private readonly enabled;
    private currentIndentLevel;
    constructor(enabled: boolean);
    log(message: string | (() => string), prefix?: string): this;
    groupedValues<T>(values: T[], printFn: (v: T) => string): this;
    groupedEntries<K, V>(map: Map<K, V>, keyPrintFn: (k: K) => string, valuePrintFn: (v: V) => string): this;
    group(openingMessage?: string | (() => string)): this;
    groupEnd(closingMessage?: string | (() => string)): this;
}
//# sourceMappingURL=debug.d.ts.map