{"version": 3, "file": "schemaUpgrader.d.ts", "sourceRoot": "", "sources": ["../src/schemaUpgrader.ts"], "names": [], "mappings": "AAAA,OAAO,EAEL,YAAY,EAGb,MAAM,SAAS,CAAC;AAkBjB,OAAO,EASL,SAAS,EACV,MAAM,cAAc,CAAC;AACtB,OAAO,EAAmB,QAAQ,EAAE,MAAM,SAAS,CAAC;AAIpD,MAAM,MAAM,aAAa,GAAG,cAAc,GAAG,cAAc,CAAC;AAE5D,KAAK,cAAc,GAAG,QAAQ,CAAC,eAAe,EAAE,aAAa,CAAC,CAAC;AAE/D,MAAM,MAAM,cAAc,GAAG;IAC3B,SAAS,EAAE,SAAS,CAAC;IACrB,OAAO,EAAE,GAAG,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;IACrC,MAAM,CAAC,EAAE,KAAK,CAAC;CAChB,CAAA;AAED,MAAM,MAAM,cAAc,GAAG;IAC3B,SAAS,CAAC,EAAE,KAAK,CAAC;IAClB,OAAO,CAAC,EAAE,KAAK,CAAC;IAChB,MAAM,EAAE,YAAY,EAAE,CAAC;CACxB,CAAA;AAED,MAAM,MAAM,eAAe,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;AAElD,MAAM,MAAM,aAAa,GACvB,8BAA8B,GAC5B,oBAAoB,GACpB,qBAAqB,GACrB,iCAAiC,GACjC,0BAA0B,GAC1B,2BAA2B,GAC3B,iCAAiC,GACjC,uCAAuC,GACvC,sBAAsB,GACtB,qBAAqB,GACrB,qBAAqB,GACrB,yCAAyC,GACzC,6BAA6B,GAC7B,8BAA8B,GAC9B,oBAAoB,CACvB;AAED,qBAAa,8BAA8B;IAG7B,QAAQ,CAAC,KAAK,EAAE,MAAM;IAFlC,QAAQ,CAAC,EAAE,uCAAiD;gBAEvC,KAAK,EAAE,MAAM;IAElC,QAAQ;CAGT;AAED,qBAAa,oBAAoB;IAGnB,QAAQ,CAAC,IAAI,EAAE,MAAM;IAFjC,QAAQ,CAAC,EAAE,2BAAqC;gBAE3B,IAAI,EAAE,MAAM;IAEjC,QAAQ;CAGT;AAED,qBAAa,0BAA0B;IAGzB,QAAQ,CAAC,KAAK,EAAE,MAAM;IAFlC,QAAQ,CAAC,EAAE,kCAA4C;gBAElC,KAAK,EAAE,MAAM;IAElC,QAAQ;CAGT;AAED,qBAAa,2BAA2B;IAG1B,QAAQ,CAAC,IAAI,EAAE,MAAM;IAFjC,QAAQ,CAAC,EAAE,oCAA8C;gBAEpC,IAAI,EAAE,MAAM;IAEjC,QAAQ;CAGT;AAED,qBAAa,qBAAqB;IAGpB,QAAQ,CAAC,KAAK,EAAE,MAAM;IAFlC,QAAQ,CAAC,EAAE,4BAAsC;gBAE5B,KAAK,EAAE,MAAM;IAElC,QAAQ;CAGT;AAED,qBAAa,iCAAiC;IAGhC,QAAQ,CAAC,IAAI,EAAE,MAAM;IAFjC,QAAQ,CAAC,EAAE,2CAAqD;gBAE3C,IAAI,EAAE,MAAM;IAEjC,QAAQ;CAGT;AAED,qBAAa,iCAAiC;IAGhC,QAAQ,CAAC,MAAM,EAAE,MAAM;IAAE,QAAQ,CAAC,OAAO,EAAE,MAAM;IAF7D,QAAQ,CAAC,EAAE,0CAAoD;gBAE1C,MAAM,EAAE,MAAM,EAAW,OAAO,EAAE,MAAM;IAE7D,QAAQ;CAGT;AAED,qBAAa,uCAAuC;IAGtC,QAAQ,CAAC,MAAM,EAAE,MAAM;IAAE,QAAQ,CAAC,QAAQ,EAAE,MAAM;IAAE,QAAQ,CAAC,OAAO,EAAE,MAAM;IAFxF,QAAQ,CAAC,EAAE,iDAA2D;gBAEjD,MAAM,EAAE,MAAM,EAAW,QAAQ,EAAE,MAAM,EAAW,OAAO,EAAE,MAAM;IAExF,QAAQ;CAGT;AAED,qBAAa,sBAAsB;IAGrB,QAAQ,CAAC,KAAK,EAAE,MAAM;IAAE,QAAQ,CAAC,kBAAkB,EAAE,MAAM,EAAE;IAFzE,QAAQ,CAAC,EAAE,6BAAuC;gBAE7B,KAAK,EAAE,MAAM,EAAW,kBAAkB,EAAE,MAAM,EAAE;IAEzE,QAAQ;CAGT;AAED,qBAAa,qBAAqB;IAGpB,QAAQ,CAAC,IAAI,EAAE,MAAM;IAAE,QAAQ,CAAC,kBAAkB,EAAE,MAAM,EAAE;IAFxE,QAAQ,CAAC,EAAE,4BAAsC;gBAE5B,IAAI,EAAE,MAAM,EAAW,kBAAkB,EAAE,MAAM,EAAE;IAExE,QAAQ;CAGT;AAED,qBAAa,qBAAqB;IAGpB,QAAQ,CAAC,IAAI,EAAE,MAAM;IAFjC,QAAQ,CAAC,EAAE,6BAAuC;gBAE7B,IAAI,EAAE,MAAM;IAEjC,QAAQ;CAGT;AAED,qBAAa,yCAAyC;IAGxC,QAAQ,CAAC,KAAK,EAAE,MAAM;IAAE,QAAQ,CAAC,SAAS,EAAE,MAAM;IAF9D,QAAQ,CAAC,EAAE,oDAA8D;gBAEpD,KAAK,EAAE,MAAM,EAAW,SAAS,EAAE,MAAM;IAE9D,QAAQ;CAGT;AAED,qBAAa,6BAA6B;IAG5B,QAAQ,CAAC,KAAK,EAAE,MAAM;IAAE,QAAQ,CAAC,IAAI,EAAE,MAAM;IAFzD,QAAQ,CAAC,EAAE,sCAAgD;gBAEtC,KAAK,EAAE,MAAM,EAAW,IAAI,EAAE,MAAM;IAEzD,QAAQ;CAGT;AAED,qBAAa,8BAA8B;IAG7B,QAAQ,CAAC,OAAO,EAAE,MAAM;IAAE,QAAQ,CAAC,SAAS,EAAE,MAAM;IAAE,QAAQ,CAAC,MAAM,EAAE,MAAM;IAAE,QAAQ,CAAC,KAAK,EAAE,MAAM;IAFjH,QAAQ,CAAC,EAAE,uCAAiD;gBAEvC,OAAO,EAAE,MAAM,EAAW,SAAS,EAAE,MAAM,EAAW,MAAM,EAAE,MAAM,EAAW,KAAK,EAAE,MAAM;IAEjH,QAAQ;CAGT;AAED,qBAAa,oBAAoB;IAGnB,QAAQ,CAAC,WAAW,EAAE,MAAM;IAAE,QAAQ,CAAC,OAAO,EAAE,MAAM;IAFlE,QAAQ,CAAC,EAAE,4BAAsC;gBAE5B,WAAW,EAAE,MAAM,EAAW,OAAO,EAAE,MAAM;IAElE,QAAQ;CAGT;AAED,wBAAgB,2BAA2B,CAAC,MAAM,EAAE,SAAS,GAAG,aAAa,CAyD5E"}