#!/usr/bin/env node

/**
 * Encryption Keys Generator Script
 * Generates cryptographically secure encryption keys for PeopleNest HRMS
 * 
 * Features:
 * - AES-256-GCM compatible encryption keys
 * - Master keys for tenant-specific encryption
 * - Index hash salts for searchable encryption
 * - Automatic environment file updates
 * - Secure backup creation
 */

const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

// Configuration
const ENV_FILES = ['.env', '.env.local', '.env.development', '.env.production', '.env.staging', '.env.test'];
const ENCRYPTION_KEYS = [
  'ENCRYPTION_KEY',
  'ENCRYPTION_MASTER_KEY', 
  'INDEX_HASH_SALT'
];

/**
 * Generate cryptographically secure key
 * @param {number} bytes - Number of bytes for the key
 * @param {string} encoding - Encoding format (hex, base64, base64url)
 * @returns {string} - Generated key
 */
function generateSecureKey(bytes, encoding = 'hex') {
  const buffer = crypto.randomBytes(bytes);
  
  switch (encoding) {
    case 'base64url':
      return buffer.toString('base64').replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
    case 'base64':
      return buffer.toString('base64');
    case 'hex':
    default:
      return buffer.toString('hex');
  }
}

/**
 * Generate all encryption keys
 * @returns {Object} - Object containing all generated keys
 */
function generateEncryptionKeys() {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  
  const keys = {
    // AES-256-GCM requires 32-byte (256-bit) key
    ENCRYPTION_KEY: generateSecureKey(32, 'hex'),
    
    // Master key for tenant encryption - 64 bytes for extra security
    ENCRYPTION_MASTER_KEY: generateSecureKey(64, 'hex'),
    
    // Index hash salt for searchable encryption - 32 bytes
    INDEX_HASH_SALT: generateSecureKey(32, 'hex'),
    
    // Metadata
    generated_at: timestamp,
    algorithm: 'aes-256-gcm',
    key_lengths: {
      ENCRYPTION_KEY: '32 bytes (256 bits)',
      ENCRYPTION_MASTER_KEY: '64 bytes (512 bits)', 
      INDEX_HASH_SALT: '32 bytes (256 bits)'
    },
    entropy_bits: {
      ENCRYPTION_KEY: 256,
      ENCRYPTION_MASTER_KEY: 512,
      INDEX_HASH_SALT: 256,
      total: 1024
    }
  };
  
  console.log('🔐 Generated new encryption keys:');
  console.log(`   ENCRYPTION_KEY: ${keys.ENCRYPTION_KEY.substring(0, 16)}... (32 bytes)`);
  console.log(`   ENCRYPTION_MASTER_KEY: ${keys.ENCRYPTION_MASTER_KEY.substring(0, 16)}... (64 bytes)`);
  console.log(`   INDEX_HASH_SALT: ${keys.INDEX_HASH_SALT.substring(0, 16)}... (32 bytes)`);
  console.log(`   Total entropy: ${keys.entropy_bits.total} bits`);
  console.log(`   Generated at: ${keys.generated_at}`);
  
  return keys;
}

/**
 * Update environment file with new encryption keys
 * @param {string} filePath - Path to environment file
 * @param {Object} keys - Generated encryption keys
 * @returns {boolean} - Success status
 */
function updateEnvFile(filePath, keys) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  File ${filePath} does not exist, skipping...`);
      return false;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    let updated = false;

    // Create backup
    const timestamp = Date.now();
    const backupPath = `${filePath}.backup.${timestamp}`;
    fs.copyFileSync(filePath, backupPath);
    
    // Update ENCRYPTION_KEY
    if (content.match(/^ENCRYPTION_KEY=.*$/m)) {
      content = content.replace(/^ENCRYPTION_KEY=.*$/m, `ENCRYPTION_KEY=${keys.ENCRYPTION_KEY}`);
      updated = true;
    } else {
      // Add if not present
      const encryptionSection = content.match(/^# Encryption Configuration$/m);
      if (encryptionSection) {
        content = content.replace(
          /^# Encryption Configuration$/m,
          `# Encryption Configuration\nENCRYPTION_KEY=${keys.ENCRYPTION_KEY}`
        );
      } else {
        content += `\n# Encryption Configuration\nENCRYPTION_KEY=${keys.ENCRYPTION_KEY}`;
      }
      updated = true;
    }

    // Update ENCRYPTION_MASTER_KEY
    if (content.match(/^ENCRYPTION_MASTER_KEY=.*$/m)) {
      content = content.replace(/^ENCRYPTION_MASTER_KEY=.*$/m, `ENCRYPTION_MASTER_KEY=${keys.ENCRYPTION_MASTER_KEY}`);
      updated = true;
    } else {
      content = content.replace(
        /^ENCRYPTION_KEY=.*$/m,
        `ENCRYPTION_KEY=${keys.ENCRYPTION_KEY}\nENCRYPTION_MASTER_KEY=${keys.ENCRYPTION_MASTER_KEY}`
      );
      updated = true;
    }

    // Update INDEX_HASH_SALT
    if (content.match(/^INDEX_HASH_SALT=.*$/m)) {
      content = content.replace(/^INDEX_HASH_SALT=.*$/m, `INDEX_HASH_SALT=${keys.INDEX_HASH_SALT}`);
      updated = true;
    } else {
      // Add after HASH_SALT_ROUNDS if it exists
      if (content.match(/^HASH_SALT_ROUNDS=.*$/m)) {
        content = content.replace(
          /^HASH_SALT_ROUNDS=.*$/m,
          `HASH_SALT_ROUNDS=12\nINDEX_HASH_SALT=${keys.INDEX_HASH_SALT}`
        );
      } else {
        content = content.replace(
          /^ENCRYPTION_MASTER_KEY=.*$/m,
          `ENCRYPTION_MASTER_KEY=${keys.ENCRYPTION_MASTER_KEY}\nINDEX_HASH_SALT=${keys.INDEX_HASH_SALT}`
        );
      }
      updated = true;
    }

    if (updated) {
      fs.writeFileSync(filePath, content);
      console.log(`✅ Updated ${filePath}`);
      console.log(`📁 Backup created: ${backupPath}`);
      return true;
    }

    return false;
  } catch (error) {
    console.error(`❌ Error updating ${filePath}:`, error.message);
    return false;
  }
}

/**
 * Save encryption keys reference
 * @param {Object} keys - Generated encryption keys
 */
function saveKeysReference(keys) {
  const secretsDir = path.join(process.cwd(), '.secrets');
  const keysFile = path.join(secretsDir, `encryption-keys-${keys.generated_at}.json`);
  
  try {
    // Create .secrets directory if it doesn't exist
    if (!fs.existsSync(secretsDir)) {
      fs.mkdirSync(secretsDir, { recursive: true });
    }

    // Save keys with metadata
    const keysData = {
      ENCRYPTION_KEY: keys.ENCRYPTION_KEY,
      ENCRYPTION_MASTER_KEY: keys.ENCRYPTION_MASTER_KEY,
      INDEX_HASH_SALT: keys.INDEX_HASH_SALT,
      generated_at: keys.generated_at,
      algorithm: keys.algorithm,
      key_lengths: keys.key_lengths,
      entropy_bits: keys.entropy_bits,
      note: "Encryption keys generated automatically. Keep this file secure!",
      usage: {
        ENCRYPTION_KEY: "Primary encryption key for AES-256-GCM data encryption",
        ENCRYPTION_MASTER_KEY: "Master key for tenant-specific key derivation",
        INDEX_HASH_SALT: "Salt for searchable encryption index hashing"
      },
      security_notes: [
        "These keys are used for data encryption and must be kept secure",
        "Changing these keys will make existing encrypted data unreadable",
        "Use proper key rotation procedures in production",
        "Store keys in secure key management systems for production use"
      ]
    };

    fs.writeFileSync(keysFile, JSON.stringify(keysData, null, 2));
    console.log(`💾 Keys reference saved: ${keysFile}`);
    
    // Add .secrets to .gitignore if not already present
    const gitignorePath = path.join(process.cwd(), '.gitignore');
    if (fs.existsSync(gitignorePath)) {
      const gitignoreContent = fs.readFileSync(gitignorePath, 'utf8');
      if (!gitignoreContent.includes('.secrets/')) {
        fs.appendFileSync(gitignorePath, '\n# Encryption Keys\n.secrets/\n');
        console.log('📝 Added .secrets/ to .gitignore');
      }
    }
  } catch (error) {
    console.error('❌ Error saving keys reference:', error.message);
  }
}

/**
 * Main execution function
 */
function main() {
  console.log('🚀 PeopleNest HRMS - Encryption Keys Generator');
  console.log('==============================================');
  console.log('');

  // Generate new encryption keys
  const keys = generateEncryptionKeys();
  console.log('');

  // Update environment files
  let updatedFiles = 0;
  for (const envFile of ENV_FILES) {
    const filePath = path.join(process.cwd(), envFile);
    if (updateEnvFile(filePath, keys)) {
      updatedFiles++;
    }
  }

  console.log('');
  
  if (updatedFiles > 0) {
    console.log(`✅ Successfully updated ${updatedFiles} environment file(s)`);
    
    // Save keys reference
    saveKeysReference(keys);
    
    console.log('');
    console.log('🔒 Security Notes:');
    console.log('   • Encryption keys have been updated with cryptographically secure values');
    console.log('   • Backup files have been created for rollback if needed');
    console.log('   • Keys reference saved in .secrets/ directory (git-ignored)');
    console.log('   • Restart your application to use the new keys');
    console.log('');
    console.log('⚠️  Critical Warning:');
    console.log('   • Changing encryption keys will make existing encrypted data unreadable');
    console.log('   • Ensure you have proper data migration procedures before using in production');
    console.log('   • Never commit the new keys to version control');
    console.log('   • Update your production environment variables separately');
  } else {
    console.log('❌ No environment files were updated');
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  generateEncryptionKeys,
  generateSecureKey,
  updateEnvFile,
  saveKeysReference
};
