import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards, ParseUUIDPipe } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard, RolesGuard, TenantGuard } from '@app/security';
import { Roles, CurrentUser, CurrentTenant } from '@app/common';
import { NotificationService } from '../services/notification.service';

@ApiTags('Notifications')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard, TenantGuard, RolesGuard)
@Controller('notifications')
export class NotificationController {
  constructor(private readonly notificationService: NotificationService) {}

  @Post()
  @Roles('admin', 'hr_manager', 'hr_user', 'manager')
  @ApiOperation({ summary: 'Create notification' })
  async create(@Body() createNotificationDto: any, @CurrentUser() user: any, @CurrentTenant() tenantId: string) {
    return this.notificationService.create(createNotificationDto, user.id, tenantId);
  }

  @Get()
  @Roles('admin', 'hr_manager', 'hr_user', 'manager', 'employee')
  @ApiOperation({ summary: 'Get notifications' })
  async findAll(@Query() query: any, @CurrentUser() user: any, @CurrentTenant() tenantId: string) {
    return this.notificationService.findAll(query, user, tenantId);
  }

  @Get(':id')
  @Roles('admin', 'hr_manager', 'hr_user', 'manager', 'employee')
  @ApiOperation({ summary: 'Get notification by ID' })
  async findOne(@Param('id', ParseUUIDPipe) id: string, @CurrentUser() user: any, @CurrentTenant() tenantId: string) {
    return this.notificationService.findOne(id, user, tenantId);
  }

  @Put(':id/read')
  @Roles('admin', 'hr_manager', 'hr_user', 'manager', 'employee')
  @ApiOperation({ summary: 'Mark notification as read' })
  async markAsRead(@Param('id', ParseUUIDPipe) id: string, @CurrentUser() user: any, @CurrentTenant() tenantId: string) {
    return this.notificationService.markAsRead(id, user.id, tenantId);
  }

  @Put('read-all')
  @Roles('admin', 'hr_manager', 'hr_user', 'manager', 'employee')
  @ApiOperation({ summary: 'Mark all notifications as read' })
  async markAllAsRead(@CurrentUser() user: any, @CurrentTenant() tenantId: string) {
    return this.notificationService.markAllAsRead(user.id, tenantId);
  }

  @Post('send')
  @Roles('admin', 'hr_manager', 'hr_user', 'manager')
  @ApiOperation({ summary: 'Send notification' })
  async sendNotification(@Body() sendNotificationDto: any, @CurrentTenant() tenantId: string) {
    return this.notificationService.sendNotification(
      sendNotificationDto.recipientId,
      sendNotificationDto.templateId,
      sendNotificationDto.data,
      tenantId
    );
  }

  @Delete(':id')
  @Roles('admin', 'hr_manager', 'hr_user', 'manager', 'employee')
  @ApiOperation({ summary: 'Delete notification' })
  async remove(@Param('id', ParseUUIDPipe) id: string, @CurrentUser() user: any, @CurrentTenant() tenantId: string) {
    return this.notificationService.remove(id, user.id, tenantId);
  }
}
