import { OperationElement, Variable } from "@apollo/federation-internals";
export declare function isPathContext(v: any): v is PathContext;
export type OperationConditional = {
    kind: 'include' | 'skip';
    value: boolean | Variable;
};
export declare function extractOperationConditionals(operation: OperationElement): OperationConditional[];
export declare class PathContext {
    readonly conditionals: OperationConditional[];
    constructor(conditionals: OperationConditional[]);
    isEmpty(): boolean;
    withContextOf(operation: OperationElement): PathContext;
    equals(that: PathContext): boolean;
    toString(): string;
}
export declare const emptyContext: PathContext;
//# sourceMappingURL=pathContext.d.ts.map