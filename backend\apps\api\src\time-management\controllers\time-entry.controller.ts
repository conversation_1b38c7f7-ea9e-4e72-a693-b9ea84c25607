import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  ParseUUIDPipe,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
} from '@nestjs/swagger';

import { JwtAuthGuard, RolesGuard, TenantGuard } from '@app/security';
import { Roles, CurrentUser, CurrentTenant } from '@app/common';
import { TimeEntryService } from '../services/time-entry.service';

@ApiTags('Time Entries')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard, TenantGuard, RolesGuard)
@Controller('time-entries')
export class TimeEntryController {
  constructor(private readonly timeEntryService: TimeEntryService) {}

  @Post()
  @Roles('admin', 'hr_manager', 'hr_user', 'manager', 'employee')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ 
    summary: 'Create time entry',
    description: 'Create a new time entry for an employee'
  })
  @ApiResponse({ 
    status: 201, 
    description: 'Time entry created successfully'
  })
  @ApiResponse({ 
    status: 400, 
    description: 'Invalid input data'
  })
  async create(
    @Body() createTimeEntryDto: any,
    @CurrentUser() user: any,
    @CurrentTenant() tenantId: string,
  ) {
    return this.timeEntryService.create(createTimeEntryDto, user.id, tenantId);
  }

  @Get()
  @Roles('admin', 'hr_manager', 'hr_user', 'manager', 'employee')
  @ApiOperation({ 
    summary: 'Get time entries',
    description: 'Retrieve time entries with optional filtering'
  })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'employeeId', required: false, type: String })
  @ApiQuery({ name: 'startDate', required: false, type: String })
  @ApiQuery({ name: 'endDate', required: false, type: String })
  @ApiResponse({ 
    status: 200, 
    description: 'Time entries retrieved successfully'
  })
  async findAll(
    @Query() query: any,
    @CurrentUser() user: any,
    @CurrentTenant() tenantId: string,
  ) {
    return this.timeEntryService.findAll(query, user, tenantId);
  }

  @Get(':id')
  @Roles('admin', 'hr_manager', 'hr_user', 'manager', 'employee')
  @ApiOperation({ 
    summary: 'Get time entry by ID',
    description: 'Retrieve detailed information about a specific time entry'
  })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiResponse({ 
    status: 200, 
    description: 'Time entry found successfully'
  })
  @ApiResponse({ 
    status: 404, 
    description: 'Time entry not found'
  })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() user: any,
    @CurrentTenant() tenantId: string,
  ) {
    return this.timeEntryService.findOne(id, user, tenantId);
  }

  @Put(':id')
  @Roles('admin', 'hr_manager', 'hr_user', 'manager', 'employee')
  @ApiOperation({ 
    summary: 'Update time entry',
    description: 'Update time entry information'
  })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiResponse({ 
    status: 200, 
    description: 'Time entry updated successfully'
  })
  @ApiResponse({ 
    status: 404, 
    description: 'Time entry not found'
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateTimeEntryDto: any,
    @CurrentUser() user: any,
    @CurrentTenant() tenantId: string,
  ) {
    return this.timeEntryService.update(id, updateTimeEntryDto, user.id, tenantId);
  }

  @Delete(':id')
  @Roles('admin', 'hr_manager', 'hr_user', 'manager', 'employee')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ 
    summary: 'Delete time entry',
    description: 'Delete a time entry'
  })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiResponse({ 
    status: 204, 
    description: 'Time entry deleted successfully'
  })
  @ApiResponse({ 
    status: 404, 
    description: 'Time entry not found'
  })
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() user: any,
    @CurrentTenant() tenantId: string,
  ) {
    return this.timeEntryService.remove(id, user.id, tenantId);
  }

  @Post(':id/approve')
  @Roles('admin', 'hr_manager', 'manager')
  @ApiOperation({ 
    summary: 'Approve time entry',
    description: 'Approve a time entry'
  })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiResponse({ 
    status: 200, 
    description: 'Time entry approved successfully'
  })
  async approve(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() user: any,
    @CurrentTenant() tenantId: string,
  ) {
    return this.timeEntryService.approve(id, user.id, tenantId);
  }

  @Post(':id/reject')
  @Roles('admin', 'hr_manager', 'manager')
  @ApiOperation({ 
    summary: 'Reject time entry',
    description: 'Reject a time entry with reason'
  })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiResponse({ 
    status: 200, 
    description: 'Time entry rejected successfully'
  })
  async reject(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() rejectDto: { reason: string },
    @CurrentUser() user: any,
    @CurrentTenant() tenantId: string,
  ) {
    return this.timeEntryService.reject(id, rejectDto.reason, user.id, tenantId);
  }
}
