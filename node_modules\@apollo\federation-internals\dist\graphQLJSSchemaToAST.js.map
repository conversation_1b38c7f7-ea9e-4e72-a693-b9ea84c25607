{"version": 3, "file": "graphQLJSSchemaToAST.js", "sourceRoot": "", "sources": ["../src/graphQLJSSchemaToAST.ts"], "names": [], "mappings": ";;;AAAA,qCAqBiB;AAEjB,+CAAgD;AAEhD,MAAM,oBAAoB,GAAG,CAAE,2BAAiB,CAAC,KAAK,EAAE,2BAAiB,CAAC,QAAQ,EAAE,2BAAiB,CAAC,YAAY,CAAC,CAAC;AASpH,SAAgB,oBAAoB,CAAC,MAAqB;IACxD,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,IAAA,6BAAmB,EAAC,IAAI,CAAC,IAAI,CAAC,IAAA,+BAAqB,EAAC,IAAI,CAAC,CAAC,CAAC;IAC9H,MAAM,UAAU,GAAG,MAAM,CAAC,aAAa,EAAE,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,IAAA,8BAAoB,EAAC,SAAS,CAAC,CAAC,CAAC;IAElG,MAAM,UAAU,GAAG,WAAW,CAAC,oCAAoC,CAAC,MAAM,CAAC,CAAC,CAAC;IAC7E,MAAM,SAAS,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,WAAW,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;IACzF,MAAM,cAAc,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC,CAAC;IAEzF,OAAO;QACL,IAAI,EAAE,cAAI,CAAC,QAAQ;QACnB,WAAW,EAAE,CAAC,GAAG,UAAU,EAAE,GAAG,SAAS,EAAE,GAAG,cAAc,CAAC;KAC9D,CAAA;AACH,CAAC;AAZD,oDAYC;AAED,SAAS,WAAW,CAA2D,EAC7E,UAAU,EACV,UAAU,GAGkB;IAE5B,OAAO,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;AAC/D,CAAC;AAED,SAAS,KAAK,CAAI,CAAW;IAC3B,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;AAC3B,CAAC;AAGD,SAAS,oCAAoC,CAAC,MAAqB;IACjE,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC1D,OAAO;YACL,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC;YACjC,UAAU,EAAE,MAAM,CAAC,iBAAiB;SACrC,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,IAAI,UAAU,GAAqC,SAAS,CAAC;QAC7D,IAAI,sBAAsB,CAAC,MAAM,CAAC,EAAE,CAAC;YACnC,MAAM,cAAc,GAAkC,EAAE,CAAC;YACzD,KAAK,MAAM,SAAS,IAAI,oBAAoB,EAAE,CAAC;gBAC7C,MAAM,IAAI,GAAG,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;gBAC3C,IAAI,IAAI,EAAE,CAAC;oBACT,cAAc,CAAC,IAAI,CAAC;wBAClB,IAAI,EAAE,cAAI,CAAC,yBAAyB;wBACpC,SAAS;wBACT,IAAI,EAAE,EAAE,IAAI,EAAE,cAAI,CAAC,UAAU,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,cAAI,CAAC,IAAI,EAAE,KAAK,EAAG,IAAI,CAAC,IAAI,EAAE,EAAE;qBAC9E,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YACD,UAAU,GAAG;gBACX,IAAI,EAAE,cAAI,CAAC,iBAAiB;gBAC5B,WAAW,EAAE,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC;oBAChC,IAAI,EAAE,cAAI,CAAC,MAAM;oBACjB,KAAK,EAAE,MAAM,CAAC,WAAW;iBAC1B,CAAC,CAAC,CAAC,SAAS;gBACb,cAAc;aACf,CAAA;QACH,CAAC;QACD,OAAO;YACL,UAAU;YACV,UAAU,EAAE,EAAE;SACf,CAAC;IACJ,CAAC;AACH,CAAC;AAED,SAAS,sBAAsB,CAAC,MAAqB;IACnD,OAAO,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,oBAAoB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC1F,CAAC;AAED,SAAS,oBAAoB,CAAC,IAA8B,EAAE,SAA4B;IACxF,OAAO,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,IAAA,6BAAe,EAAC,SAAS,CAAC,CAAC;AAC5D,CAAC;AAED,SAAgB,uBAAuB,CAAC,IAAsB;IAC5D,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACtD,OAAO;YACL,UAAU,EAAE,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;YAC/B,UAAU,EAAE,IAAI,CAAC,iBAAiB;SACnC,CAAC;IACJ,CAAC;SAAM,CAAC;QAEN,OAAO;YACL,UAAU,EAAE,IAAA,eAAK,EAAC,IAAA,mBAAS,EAAC,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAuB;YACvE,UAAU,EAAE,EAAE;SACf,CAAC;IACJ,CAAC;AACH,CAAC;AAbD,0DAaC;AAED,SAAgB,uBAAuB,CAAC,SAA2B;IACjE,IAAI,SAAS,CAAC,OAAO,EAAE,CAAC;QACtB,OAAO,SAAS,CAAC,OAAO,CAAC;IAC3B,CAAC;SAAM,CAAC;QAIN,MAAM,UAAU,GAAG,IAAI,uBAAa,CAAC;YACnC,UAAU,EAAE,CAAC,SAAS,CAAC;YACvB,WAAW,EAAE,IAAI;SAClB,CAAC,CAAC;QACH,MAAM,QAAQ,GAAG,IAAA,eAAK,EAAC,IAAA,qBAAW,EAAC,UAAU,CAAC,CAAC,CAAC;QAChD,OAAO,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,cAAI,CAAC,oBAAoB,CAA4B,CAAC;IAC/G,CAAC;AACH,CAAC;AAdD,0DAcC"}