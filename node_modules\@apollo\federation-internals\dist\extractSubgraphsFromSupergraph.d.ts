import { Schema } from "./definitions";
import { Subgraphs } from "./federation";
export declare function extractSubgraphsNamesAndUrlsFromSupergraph(supergraph: Schema): {
    name: string;
    url: string;
}[];
export declare function extractSubgraphsFromSupergraph(supergraph: Schema, validateExtractedSubgraphs?: boolean): [Subgraphs, Map<string, string>];
//# sourceMappingURL=extractSubgraphsFromSupergraph.d.ts.map