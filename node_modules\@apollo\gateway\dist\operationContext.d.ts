import { DocumentNode, FragmentDefinitionNode, GraphQLSchema, OperationDefinitionNode } from 'graphql';
type FragmentMap = {
    [fragmentName: string]: FragmentDefinitionNode;
};
export type OperationContext = {
    schema: GraphQLSchema;
    operation: OperationDefinitionNode;
    fragments: FragmentMap;
};
interface BuildOperationContextOptions {
    schema: GraphQLSchema;
    operationDocument: DocumentNode;
    operationName?: string;
}
export declare function buildOperationContext({ schema, operationDocument, operationName, }: BuildOperationContextOptions): OperationContext;
export {};
//# sourceMappingURL=operationContext.d.ts.map