import { SupergraphSdlUpdate } from '../../config';
import type { Fetcher } from '@apollo/utils.fetcher';
import type { Logger } from '@apollo/utils.logger';
export declare const SUPERGRAPH_SDL_QUERY = "#graphql\n  query SupergraphSdl($apiKey: String!, $ref: String!, $ifAfterId: ID) {\n    routerConfig(ref: $ref, apiKey: $apiKey, ifAfterId: $ifAfterId) {\n      __typename\n      ... on RouterConfigResult {\n        id\n        supergraphSdl: supergraphSDL\n        minDelaySeconds\n      }\n      ... on FetchError {\n        code\n        message\n      }\n    }\n  }\n";
export declare class UplinkFetcherError extends Error {
    constructor(message: string);
}
export declare function loadSupergraphSdlFromUplinks({ graphRef, apiKey, endpoints, fetcher, compositionId, maxRetries, requestTimeoutMs, roundRobinSeed, logger, }: {
    graphRef: string;
    apiKey: string;
    endpoints: string[];
    fetcher: Fetcher;
    compositionId: string | null;
    maxRetries: number;
    requestTimeoutMs: number;
    roundRobinSeed: number;
    logger: Logger;
}): Promise<SupergraphSdlUpdate | null>;
export declare function loadSupergraphSdlFromStorage({ graphRef, apiKey, endpoint, fetcher, requestTimeoutMs, compositionId, logger, }: {
    graphRef: string;
    apiKey: string;
    endpoint: string;
    fetcher: Fetcher;
    requestTimeoutMs: number;
    compositionId: string | null;
    logger: Logger;
}): Promise<SupergraphSdlUpdate | null>;
//# sourceMappingURL=loadSupergraphSdlFromStorage.d.ts.map