import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectConnection } from '@nestjs/typeorm';
import { Connection } from 'typeorm';

@Injectable()
export class HealthService {
  private readonly logger = new Logger(HealthService.name);

  constructor(
    private readonly configService: ConfigService,
    @InjectConnection() private readonly connection: Connection,
  ) {}

  async getHealthStatus() {
    const startTime = Date.now();
    
    try {
      const checks = await Promise.allSettled([
        this.checkDatabase(),
        this.checkMemory(),
        this.checkDisk(),
      ]);

      const databaseCheck = checks[0];
      const memoryCheck = checks[1];
      const diskCheck = checks[2];

      const allHealthy = checks.every(check => 
        check.status === 'fulfilled' && check.value.status === 'healthy'
      );

      const status = allHealthy ? 'healthy' : 'degraded';
      const responseTime = Date.now() - startTime;

      return {
        status,
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: '1.0.0',
        environment: this.configService.get<string>('NODE_ENV', 'development'),
        responseTime: `${responseTime}ms`,
        checks: {
          database: databaseCheck.status === 'fulfilled' ? databaseCheck.value : { status: 'unhealthy', error: databaseCheck.reason },
          memory: memoryCheck.status === 'fulfilled' ? memoryCheck.value : { status: 'unhealthy', error: memoryCheck.reason },
          disk: diskCheck.status === 'fulfilled' ? diskCheck.value : { status: 'unhealthy', error: diskCheck.reason },
        },
      };
    } catch (error) {
      this.logger.error('Health check failed:', error);
      return {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error.message,
      };
    }
  }

  async getReadinessStatus() {
    try {
      const dbCheck = await this.checkDatabase();
      
      if (dbCheck.status === 'healthy') {
        return {
          status: 'ready',
          timestamp: new Date().toISOString(),
          checks: {
            database: dbCheck,
          },
        };
      } else {
        return {
          status: 'not ready',
          timestamp: new Date().toISOString(),
          checks: {
            database: dbCheck,
          },
        };
      }
    } catch (error) {
      this.logger.error('Readiness check failed:', error);
      return {
        status: 'not ready',
        timestamp: new Date().toISOString(),
        error: error.message,
      };
    }
  }

  async getLivenessStatus() {
    return {
      status: 'alive',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      pid: process.pid,
    };
  }

  private async checkDatabase() {
    try {
      const startTime = Date.now();
      await this.connection.query('SELECT 1');
      const responseTime = Date.now() - startTime;

      return {
        status: 'healthy',
        responseTime: `${responseTime}ms`,
        connection: 'active',
        database: this.configService.get<string>('DATABASE_NAME'),
      };
    } catch (error) {
      this.logger.error('Database health check failed:', error);
      return {
        status: 'unhealthy',
        error: error.message,
        connection: 'failed',
      };
    }
  }

  private checkMemory() {
    try {
      const memoryUsage = process.memoryUsage();
      const totalMemory = memoryUsage.heapTotal;
      const usedMemory = memoryUsage.heapUsed;
      const freeMemory = totalMemory - usedMemory;
      const memoryUsagePercent = (usedMemory / totalMemory) * 100;

      const status = memoryUsagePercent > 90 ? 'unhealthy' : 'healthy';

      return {
        status,
        usage: {
          total: `${Math.round(totalMemory / 1024 / 1024)}MB`,
          used: `${Math.round(usedMemory / 1024 / 1024)}MB`,
          free: `${Math.round(freeMemory / 1024 / 1024)}MB`,
          percentage: `${Math.round(memoryUsagePercent)}%`,
        },
      };
    } catch (error) {
      this.logger.error('Memory health check failed:', error);
      return {
        status: 'unhealthy',
        error: error.message,
      };
    }
  }

  private checkDisk() {
    try {
      // Basic disk space check (simplified)
      return {
        status: 'healthy',
        message: 'Disk space check not implemented',
      };
    } catch (error) {
      this.logger.error('Disk health check failed:', error);
      return {
        status: 'unhealthy',
        error: error.message,
      };
    }
  }
}
