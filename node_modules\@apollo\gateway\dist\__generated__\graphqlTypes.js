"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RouterEntitlementAudience = exports.MessageLevel = exports.FetchErrorCode = void 0;
var FetchErrorCode;
(function (FetchErrorCode) {
    FetchErrorCode["AccessDenied"] = "ACCESS_DENIED";
    FetchErrorCode["AuthenticationFailed"] = "AUTHENTICATION_FAILED";
    FetchErrorCode["NotImplementedOnThisInstance"] = "NOT_IMPLEMENTED_ON_THIS_INSTANCE";
    FetchErrorCode["RetryLater"] = "RETRY_LATER";
    FetchErrorCode["UnknownRef"] = "UNKNOWN_REF";
})(FetchErrorCode || (exports.FetchErrorCode = FetchErrorCode = {}));
var MessageLevel;
(function (MessageLevel) {
    MessageLevel["Error"] = "ERROR";
    MessageLevel["Info"] = "INFO";
    MessageLevel["Warn"] = "WARN";
})(MessageLevel || (exports.MessageLevel = MessageLevel = {}));
var RouterEntitlementAudience;
(function (RouterEntitlementAudience) {
    RouterEntitlementAudience["Cloud"] = "CLOUD";
    RouterEntitlementAudience["SelfHosted"] = "SELF_HOSTED";
})(RouterEntitlementAudience || (exports.RouterEntitlementAudience = RouterEntitlementAudience = {}));
//# sourceMappingURL=graphqlTypes.js.map