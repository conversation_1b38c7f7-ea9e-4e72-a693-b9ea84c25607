import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PositionLevel } from './create-position.dto';

export class PositionResponseDto {
  @ApiProperty({ description: 'Position UUID' })
  id: string;

  @ApiProperty({ description: 'Position title' })
  title: string;

  @ApiProperty({ description: 'Position code' })
  code: string;

  @ApiPropertyOptional({ description: 'Position description' })
  description?: string;

  @ApiProperty({ description: 'Department information' })
  department: {
    id: string;
    name: string;
    code: string;
  };

  @ApiProperty({ description: 'Position level', enum: PositionLevel })
  level: PositionLevel;

  @ApiPropertyOptional({ description: 'Reports to position information' })
  reportsTo?: {
    id: string;
    title: string;
    code: string;
  };

  @ApiPropertyOptional({ description: 'Minimum salary' })
  minSalary?: number;

  @ApiPropertyOptional({ description: 'Maximum salary' })
  maxSalary?: number;

  @ApiPropertyOptional({ description: 'Required skills' })
  skills?: string[];

  @ApiPropertyOptional({ description: 'Required qualifications' })
  qualifications?: string[];

  @ApiPropertyOptional({ description: 'Years of experience required' })
  experienceYears?: number;

  @ApiPropertyOptional({ description: 'Job responsibilities' })
  responsibilities?: string[];

  @ApiProperty({ description: 'Employee count' })
  employeeCount: number;

  @ApiProperty({ description: 'Active status' })
  isActive: boolean;

  @ApiProperty({ description: 'Creation date' })
  createdAt: string;

  @ApiProperty({ description: 'Last update date' })
  updatedAt: string;

  @ApiPropertyOptional({ description: 'Additional metadata' })
  metadata?: Record<string, any>;
}

export class PaginatedPositionResponseDto {
  @ApiProperty({ description: 'Position data', type: [PositionResponseDto] })
  data: PositionResponseDto[];

  @ApiProperty({ description: 'Pagination metadata' })
  meta: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}
