"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.emptyContext = exports.PathContext = exports.extractOperationConditionals = exports.isPathContext = void 0;
const federation_internals_1 = require("@apollo/federation-internals");
const deep_equal_1 = __importDefault(require("deep-equal"));
function isPathContext(v) {
    return v instanceof PathContext;
}
exports.isPathContext = isPathContext;
function extractOperationConditionals(operation) {
    const conditionals = [];
    addExtractedConditional(operation, 'skip', conditionals);
    addExtractedConditional(operation, 'include', conditionals);
    return conditionals;
}
exports.extractOperationConditionals = extractOperationConditionals;
function addExtractedConditional(operation, kind, addTo) {
    const applied = operation.appliedDirectivesOf(kind);
    if (applied.length > 0) {
        (0, federation_internals_1.assert)(applied.length === 1, () => `${kind} shouldn't be repeated on ${operation}`);
        const value = applied[0].arguments()['if'];
        (0, federation_internals_1.assert)(typeof value === 'boolean' || (0, federation_internals_1.isVariable)(value), () => `Invalid value ${value} found as condition of @${kind}`);
        addTo.push({ kind, value });
    }
}
class PathContext {
    constructor(conditionals) {
        this.conditionals = conditionals;
    }
    isEmpty() {
        return this.conditionals.length === 0;
    }
    withContextOf(operation) {
        if (operation.appliedDirectives.length === 0) {
            return this;
        }
        const newConditionals = extractOperationConditionals(operation);
        return newConditionals.length === 0
            ? this
            : new PathContext(newConditionals.concat(this.conditionals));
    }
    equals(that) {
        return (0, deep_equal_1.default)(this.conditionals, that.conditionals);
    }
    toString() {
        return '['
            + this.conditionals.map(({ kind, value }) => `@${kind}(if: ${value})`).join(', ')
            + ']';
    }
}
exports.PathContext = PathContext;
exports.emptyContext = new PathContext([]);
//# sourceMappingURL=pathContext.js.map