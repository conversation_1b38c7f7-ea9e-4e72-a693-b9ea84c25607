"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.computeShareables = void 0;
const _1 = require(".");
function computeShareables(schema) {
    const metadata = (0, _1.federationMetadata)(schema);
    (0, _1.assert)(metadata, 'Schema should be a federation subgraph');
    const keyDirective = metadata.keyDirective();
    const providesDirective = metadata.providesDirective();
    const shareableDirective = metadata.isFed2Schema() ? metadata.shareableDirective() : undefined;
    const shareableFields = new Set();
    const addKeyFields = (type) => {
        for (const key of type.appliedDirectivesOf(keyDirective)) {
            (0, _1.collectTargetFields)({
                parentType: type,
                directive: key,
                includeInterfaceFieldsImplementations: true,
                validate: false,
            }).forEach((f) => shareableFields.add(f.coordinate));
        }
    };
    for (const type of schema.objectTypes()) {
        addKeyFields(type);
        const shareablesOnType = shareableDirective ? type.appliedDirectivesOf(shareableDirective) : [];
        for (const field of type.fields()) {
            const fieldIsShareable = shareableDirective && field.hasAppliedDirective(shareableDirective)
                || (shareablesOnType.length > 0 && shareablesOnType.some((d) => field.ofExtension() === d.ofExtension()));
            if (fieldIsShareable) {
                shareableFields.add(field.coordinate);
            }
            for (const provides of field.appliedDirectivesOf(providesDirective)) {
                const parentType = (0, _1.baseType)(field.type);
                if ((0, _1.isCompositeType)(parentType)) {
                    (0, _1.collectTargetFields)({
                        parentType,
                        directive: provides,
                        includeInterfaceFieldsImplementations: true,
                        validate: false,
                    }).forEach((f) => {
                        if (metadata.isFieldExternal(f)) {
                            shareableFields.add(f.coordinate);
                        }
                    });
                }
            }
        }
    }
    for (const type of schema.interfaceTypes()) {
        addKeyFields(type);
    }
    return (field) => shareableFields.has(field.coordinate);
}
exports.computeShareables = computeShareables;
//# sourceMappingURL=precompute.js.map