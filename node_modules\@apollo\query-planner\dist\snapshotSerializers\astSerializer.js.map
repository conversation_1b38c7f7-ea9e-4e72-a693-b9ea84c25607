{"version": 3, "file": "astSerializer.js", "sourceRoot": "", "sources": ["../../src/snapshotSerializers/astSerializer.ts"], "names": [], "mappings": ";;;AACA,qCAA+F;AAG/F,kBAAe;IACb,IAAI,CAAC,KAAU;QAOb,OAAO,KAAK,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,QAAQ,CAAC;IACjD,CAAC;IAED,SAAS,CACP,KAAc,EACd,MAAc,EACd,WAAmB,EACnB,MAAc,EACd,KAAW,EACX,QAAa;QAEb,MAAM,KAAK,GAAG,IAAA,eAAK,EAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAGxE,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvB,OAAO,EAAE,CAAC;QACZ,CAAC;aAAM,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9B,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;QAED,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YAOtB,MAAM,iBAAiB,GAAG,oBAAoB,CAAC,IAAI,CAAC,CAAC;YACrD,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;YAEnD,MAAM,gBAAgB,GAAG,iBAAiB,GAAG,CAAC,CAAC;YAE/C,OAAO,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,GAAG,YAAY,CAAC;QAC7E,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;IAC/B,CAAC;CACW,CAAC;AAGf,SAAS,oBAAoB,CAAC,IAAY;IACxC,MAAM,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACrC,OAAO,MAAM,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;AAChD,CAAC;AAmBD,SAAgB,wBAAwB,CAAC,IAAa;IACpD,OAAO,IAAA,eAAK,EAAC,IAAI,EAAE;QACjB,cAAc,EAAE,CAAC,YAAY,EAAE,EAAE;YAE/B,IAAI,YAAY,CAAC,YAAY;gBAAE,OAAO,YAAY,CAAC;YAUnD,OAAO;gBACL,IAAI,EAAE,cAAI,CAAC,eAAe;gBAC1B,aAAa,EAAE,YAAY,CAAC,aAAa;oBACvC,CAAC,CAAC;wBACE,IAAI,EAAE,cAAI,CAAC,UAAU;wBACrB,IAAI,EAAE;4BACJ,IAAI,EAAE,cAAI,CAAC,IAAI;4BACf,KAAK,EAAE,YAAY,CAAC,aAAa;yBAClC;qBACF;oBACH,CAAC,CAAC,SAAS;gBACb,YAAY,EAAE;oBACZ,IAAI,EAAE,cAAI,CAAC,aAAa;oBAExB,UAAU,EAAE,eAAe,CACvB,YAAwD,CAAC,UAAU,CACtE;iBACF;aACF,CAAC;QACJ,CAAC;KACF,CAAC,CAAC;AACL,CAAC;AAnCD,4DAmCC;AAED,SAAS,eAAe,CACtB,UAAoC;IAEpC,OAAO,UAAU,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE;QAClC,QAAQ,SAAS,CAAC,IAAI,EAAE,CAAC;YACvB,KAAK,OAAO;gBACV,OAAO;oBACL,IAAI,EAAE,cAAI,CAAC,KAAK;oBAChB,IAAI,EAAE;wBACJ,IAAI,EAAE,cAAI,CAAC,IAAI;wBACf,KAAK,EAAE,SAAS,CAAC,IAAI;qBACtB;oBACD,YAAY,EAAE;wBACZ,IAAI,EAAE,cAAI,CAAC,aAAa;wBACxB,UAAU,EAAE,eAAe,CAAC,SAAS,CAAC,UAAU,IAAI,EAAE,CAAC;qBACxD;iBACF,CAAC;YACJ,KAAK,gBAAgB;gBACnB,OAAO;oBACL,IAAI,EAAE,cAAI,CAAC,eAAe;oBAC1B,YAAY,EAAE;wBACZ,IAAI,EAAE,cAAI,CAAC,aAAa;wBACxB,UAAU,EAAE,eAAe,CAAC,SAAS,CAAC,UAAU,IAAI,EAAE,CAAC;qBACxD;oBACD,aAAa,EAAE,SAAS,CAAC,aAAa;wBACpC,CAAC,CAAC;4BACE,IAAI,EAAE,cAAI,CAAC,UAAU;4BACrB,IAAI,EAAE;gCACJ,IAAI,EAAE,cAAI,CAAC,IAAI;gCACf,KAAK,EAAE,SAAS,CAAC,aAAa;6BAC/B;yBACF;wBACH,CAAC,CAAC,SAAS;iBACd,CAAC;QACN,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC"}