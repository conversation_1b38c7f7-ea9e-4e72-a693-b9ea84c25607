import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { INestApplication } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export function setupSwagger(app: INestApplication): void {
  const configService = app.get(ConfigService);
  const environment = configService.get('NODE_ENV', 'development');
  
  // Only enable Swagger in development and staging
  if (environment === 'production') {
    return;
  }

  const config = new DocumentBuilder()
    .setTitle('PeopleNest HRMS API Gateway')
    .setDescription(`
# PeopleNest HRMS API Gateway

## Overview
The PeopleNest HRMS API Gateway is the central entry point for all client applications to interact with the PeopleNest Human Resource Management System. It provides a unified interface to access various microservices including authentication, employee management, payroll processing, performance management, and AI-powered features.

## Architecture
- **Microservices Architecture**: The system is built using a microservices architecture with dedicated services for different HR domains
- **API Gateway Pattern**: All client requests are routed through this gateway which handles authentication, rate limiting, load balancing, and service discovery
- **GraphQL Federation**: GraphQL queries are federated across multiple services for efficient data fetching
- **Circuit Breaker**: Automatic failure detection and recovery for improved system resilience
- **Service Discovery**: Dynamic service registration and health monitoring

## Authentication
All endpoints (except public ones) require JWT authentication. Include the JWT token in the Authorization header:
\`\`\`
Authorization: Bearer <your-jwt-token>
\`\`\`

## Rate Limiting
API requests are rate-limited to ensure fair usage and system stability:
- **Default**: 100 requests per 15 minutes per user
- **AI Services**: 20 requests per minute per user
- **Payroll**: 50 requests per minute per user

## Error Handling
The API uses standard HTTP status codes and returns consistent error responses:
- **400**: Bad Request - Invalid input parameters
- **401**: Unauthorized - Missing or invalid authentication
- **403**: Forbidden - Insufficient permissions
- **404**: Not Found - Resource not found
- **429**: Too Many Requests - Rate limit exceeded
- **500**: Internal Server Error - Server-side error
- **503**: Service Unavailable - Service temporarily down

## Pagination
List endpoints support pagination with the following query parameters:
- \`page\`: Page number (default: 1)
- \`limit\`: Items per page (default: 20, max: 100)
- \`sortBy\`: Field to sort by
- \`sortOrder\`: Sort direction (asc/desc)

## Filtering and Search
Most list endpoints support filtering and search:
- \`search\`: Full-text search across relevant fields
- \`status\`: Filter by status
- \`department\`: Filter by department
- \`dateFrom\` / \`dateTo\`: Date range filtering

## Multi-tenancy
The system supports multi-tenancy. Include the tenant ID in the header:
\`\`\`
X-Tenant-ID: <your-tenant-id>
\`\`\`

## Monitoring and Health
- **Health Check**: \`GET /_gateway/status\` - Gateway and services health
- **Metrics**: \`GET /_gateway/metrics\` - Performance and usage metrics
- **Circuit Breakers**: \`GET /_gateway/circuit-breakers\` - Circuit breaker status

## Support
For API support and documentation, contact the development team or refer to the internal documentation portal.
    `)
    .setVersion('1.0.0')
    .setContact(
      'PeopleNest Development Team',
      'https://peoplenest.com/support',
      '<EMAIL>'
    )
    .setLicense('Proprietary', 'https://peoplenest.com/license')
    .addServer('http://localhost:3000', 'Development Server')
    .addServer('https://api-staging.peoplenest.com', 'Staging Server')
    .addServer('https://api.peoplenest.com', 'Production Server')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'JWT',
        description: 'Enter JWT token',
        in: 'header',
      },
      'JWT-auth'
    )
    .addApiKey(
      {
        type: 'apiKey',
        name: 'X-API-Key',
        in: 'header',
        description: 'API Key for service-to-service communication',
      },
      'API-Key'
    )
    .addTag('Application', 'Application information and health checks')
    .addTag('Gateway', 'API Gateway management and monitoring')
    .addTag('Authentication', 'User authentication and authorization')
    .addTag('Employees', 'Employee management operations')
    .addTag('Departments', 'Department and organizational structure')
    .addTag('Positions', 'Job positions and roles')
    .addTag('Payroll', 'Payroll processing and management')
    .addTag('Performance', 'Performance management and reviews')
    .addTag('Goals', 'Goal setting and tracking')
    .addTag('Feedback', 'Feedback and 360-degree reviews')
    .addTag('AI Services', 'AI-powered HR features')
    .addTag('Resume Parsing', 'AI resume parsing and analysis')
    .addTag('Sentiment Analysis', 'Employee sentiment analysis')
    .addTag('Predictive Analytics', 'HR predictive analytics')
    .addTag('Notifications', 'Notification and messaging system')
    .addTag('Reports', 'Reporting and analytics')
    .addTag('Admin', 'Administrative operations')
    .addTag('Health', 'System health and monitoring')
    .build();

  const document = SwaggerModule.createDocument(app, config, {
    operationIdFactory: (controllerKey: string, methodKey: string) => methodKey,
    deepScanRoutes: true,
    ignoreGlobalPrefix: false,
  });

  // Customize the document
  document.info.termsOfService = 'https://peoplenest.com/terms';
  
  // Add global parameters
  document.components = document.components || {};
  document.components.parameters = {
    TenantId: {
      name: 'X-Tenant-ID',
      in: 'header',
      description: 'Tenant identifier for multi-tenant operations',
      required: false,
      schema: {
        type: 'string',
        format: 'uuid',
      },
    },
    CorrelationId: {
      name: 'X-Correlation-ID',
      in: 'header',
      description: 'Correlation ID for request tracing',
      required: false,
      schema: {
        type: 'string',
      },
    },
    RequestId: {
      name: 'X-Request-ID',
      in: 'header',
      description: 'Unique request identifier',
      required: false,
      schema: {
        type: 'string',
        format: 'uuid',
      },
    },
  };

  // Add global responses
  document.components.responses = {
    BadRequest: {
      description: 'Bad Request - Invalid input parameters',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              statusCode: { type: 'number', example: 400 },
              message: { type: 'string', example: 'Validation failed' },
              error: { type: 'string', example: 'Bad Request' },
              details: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    field: { type: 'string' },
                    message: { type: 'string' },
                  },
                },
              },
            },
          },
        },
      },
    },
    Unauthorized: {
      description: 'Unauthorized - Missing or invalid authentication',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              statusCode: { type: 'number', example: 401 },
              message: { type: 'string', example: 'Unauthorized' },
              error: { type: 'string', example: 'Unauthorized' },
            },
          },
        },
      },
    },
    Forbidden: {
      description: 'Forbidden - Insufficient permissions',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              statusCode: { type: 'number', example: 403 },
              message: { type: 'string', example: 'Forbidden resource' },
              error: { type: 'string', example: 'Forbidden' },
            },
          },
        },
      },
    },
    NotFound: {
      description: 'Not Found - Resource not found',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              statusCode: { type: 'number', example: 404 },
              message: { type: 'string', example: 'Resource not found' },
              error: { type: 'string', example: 'Not Found' },
            },
          },
        },
      },
    },
    TooManyRequests: {
      description: 'Too Many Requests - Rate limit exceeded',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              statusCode: { type: 'number', example: 429 },
              message: { type: 'string', example: 'Too many requests' },
              error: { type: 'string', example: 'Too Many Requests' },
              retryAfter: { type: 'number', example: 60 },
            },
          },
        },
      },
    },
    InternalServerError: {
      description: 'Internal Server Error',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              statusCode: { type: 'number', example: 500 },
              message: { type: 'string', example: 'Internal server error' },
              error: { type: 'string', example: 'Internal Server Error' },
            },
          },
        },
      },
    },
    ServiceUnavailable: {
      description: 'Service Unavailable - Service temporarily down',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              statusCode: { type: 'number', example: 503 },
              message: { type: 'string', example: 'Service temporarily unavailable' },
              error: { type: 'string', example: 'Service Unavailable' },
              retryAfter: { type: 'number', example: 30 },
            },
          },
        },
      },
    },
  };

  // Add global schemas
  document.components.schemas = {
    ...document.components.schemas,
    PaginationMeta: {
      type: 'object',
      properties: {
        page: { type: 'number', example: 1 },
        limit: { type: 'number', example: 20 },
        total: { type: 'number', example: 100 },
        totalPages: { type: 'number', example: 5 },
        hasNext: { type: 'boolean', example: true },
        hasPrev: { type: 'boolean', example: false },
      },
    },
    PaginatedResponse: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { type: 'object' },
        },
        meta: { $ref: '#/components/schemas/PaginationMeta' },
      },
    },
  };

  const apiPrefix = configService.get('API_PREFIX', 'api/v1');
  
  SwaggerModule.setup(`${apiPrefix}/docs`, app, document, {
    swaggerOptions: {
      persistAuthorization: true,
      displayRequestDuration: true,
      docExpansion: 'none',
      filter: true,
      showRequestHeaders: true,
      tryItOutEnabled: true,
      defaultModelsExpandDepth: 2,
      defaultModelExpandDepth: 2,
    },
    customSiteTitle: 'PeopleNest HRMS API Documentation',
    customfavIcon: '/favicon.ico',
    customCss: `
      .swagger-ui .topbar { display: none; }
      .swagger-ui .info .title { color: #1f2937; }
      .swagger-ui .scheme-container { background: #f9fafb; padding: 10px; border-radius: 4px; }
    `,
  });
}
