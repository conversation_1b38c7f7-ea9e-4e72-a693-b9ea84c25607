import { FetchDataRewrite } from "@apollo/query-planner";
import { GraphQLSchema } from "graphql";
export declare function applyRewrites(schema: GraphQLSchema, rewrites: FetchDataRewrite[] | undefined, value: Record<string, any>): void;
export declare function isObjectOfType(schema: GraphQLSchema, obj: Record<string, any>, typeCondition: string, defaultOnUnknownObjectType?: boolean): boolean;
//# sourceMappingURL=dataRewrites.d.ts.map