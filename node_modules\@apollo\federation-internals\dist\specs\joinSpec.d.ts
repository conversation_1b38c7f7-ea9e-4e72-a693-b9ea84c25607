import { GraphQLError } from 'graphql';
import { CorePurpose, FeatureDefinition, FeatureDefinitions, FeatureVersion } from "./coreSpec";
import { DirectiveDefinition, EnumType, ScalarType, Schema } from "../definitions";
import { Subgraphs } from "../federation";
export declare const joinIdentity = "https://specs.apollo.dev/join";
export type JoinTypeDirectiveArguments = {
    graph: string;
    key?: string;
    extension?: boolean;
    resolvable?: boolean;
    isInterfaceObject?: boolean;
};
export type JoinFieldDirectiveArguments = {
    graph?: string;
    requires?: string;
    provides?: string;
    override?: string;
    type?: string;
    external?: boolean;
    usedOverridden?: boolean;
    overrideLabel?: string;
    contextArguments?: {
        name: string;
        type: string;
        context: string;
        selection: string;
    }[];
};
export type JoinDirectiveArguments = {
    graphs: string[];
    name: string;
    args?: Record<string, any>;
};
export declare class JoinSpecDefinition extends FeatureDefinition {
    constructor(version: FeatureVersion, minimumFederationVersion?: FeatureVersion);
    private isV01;
    addElementsToSchema(schema: Schema): GraphQLError[];
    allElementNames(): string[];
    populateGraphEnum(schema: Schema, subgraphs: Subgraphs): Map<string, string>;
    fieldSetScalar(schema: Schema): ScalarType;
    graphEnum(schema: Schema): EnumType;
    graphDirective(schema: Schema): DirectiveDefinition<{
        name: string;
        url: string;
    }>;
    directiveDirective(schema: Schema): DirectiveDefinition<JoinDirectiveArguments>;
    typeDirective(schema: Schema): DirectiveDefinition<JoinTypeDirectiveArguments>;
    implementsDirective(schema: Schema): DirectiveDefinition<{
        graph: string;
        interface: string;
    }> | undefined;
    fieldDirective(schema: Schema): DirectiveDefinition<JoinFieldDirectiveArguments>;
    unionMemberDirective(schema: Schema): DirectiveDefinition<{
        graph: string;
        member: string;
    }> | undefined;
    enumValueDirective(schema: Schema): DirectiveDefinition<{
        graph: string;
    }> | undefined;
    ownerDirective(schema: Schema): DirectiveDefinition<{
        graph: string;
    }> | undefined;
    get defaultCorePurpose(): CorePurpose | undefined;
}
export declare const JOIN_VERSIONS: FeatureDefinitions<JoinSpecDefinition>;
//# sourceMappingURL=joinSpec.d.ts.map