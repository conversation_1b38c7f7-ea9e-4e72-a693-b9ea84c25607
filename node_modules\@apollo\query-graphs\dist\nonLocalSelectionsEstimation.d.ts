import { Schema, Selection } from '@apollo/federation-internals';
import { QueryGraph, Vertex } from './querygraph';
import { SimultaneousPathsWithLazyIndirectPaths } from './graphPath';
export declare class NonLocalSelectionsMetadata {
    static readonly MAX_NON_LOCAL_SELECTIONS = 100000;
    private readonly typesToIndirectOptions;
    private readonly remainingVerticesToInterfaceObjectOptions;
    private readonly fieldsToEndpoints;
    private readonly inlineFragmentsToEndpoints;
    private readonly verticesToObjectTypeDowncasts;
    private readonly fieldsToRebaseableParentVertices;
    private readonly inlineFragmentsToRebaseableParentVertices;
    constructor(graph: QueryGraph);
    private precomputeNonLocalSelectionMetadata;
    private precomputeNextVertexMetadata;
    private precomputeRebasingMetadata;
    checkNonLocalSelectionsLimitExceededAtRoot(stack: [Selection, SimultaneousPathsWithLazyIndirectPaths[]][], state: NonLocalSelectionsState, supergraphSchema: Schema, inconsistentAbstractTypesRuntimes: Set<string>, overrideConditions: Map<string, boolean>): boolean;
    private checkNonLocalSelectionsLimitExceeded;
    private updateCount;
    private estimateNextVerticesForSelection;
    private mergeNextVerticesInfo;
    private mergeVerticesWithIndirectOptionsInfo;
    private estimateNextVerticesForSelectionWithoutCaching;
    private estimateVerticesWithIndirectOptions;
}
interface NextVerticesCache {
    typesToNextVertices: Map<string, NextVerticesInfo>;
    remainingVerticesToNextVertices: Map<Vertex, NextVerticesInfo>;
}
interface NextVerticesInfo {
    nextVertices: Set<Vertex>;
    nextVerticesHaveReachableCrossSubgraphEdges: boolean;
    nextVerticesWithIndirectOptions: VerticesWithIndirectOptionsInfo;
}
interface VerticesWithIndirectOptionsInfo {
    types: Set<string>;
    remainingVertices: Set<Vertex>;
}
export declare class NonLocalSelectionsState {
    count: number;
    readonly nextVerticesCache: Map<string, NextVerticesCache>;
}
export {};
//# sourceMappingURL=nonLocalSelectionsEstimation.d.ts.map