import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ConflictException,
  ForbiddenException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere, In, Like, Between } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ConfigService } from '@nestjs/config';

import {
  Employee,
  Department,
  Position,
  EmployeeContact,
  EmployeeAddress,
  EmployeeEmergencyContact,
  User,
  Tenant,
} from '@app/database';
import { EmployeeStatus, EmploymentType } from '@app/common/enums/status.enum';
import { AuditService } from '@app/security';

import { CreateEmployeeDto } from '../dto/create-employee.dto';
import { UpdateEmployeeDto } from '../dto/update-employee.dto';
import { EmployeeQueryDto, EmployeeStatsQueryDto } from '../dto/employee-query.dto';
import {
  EmployeeResponseDto,
  EmployeeListResponseDto,
  EmployeeStatsResponseDto,
} from '../dto/employee-response.dto';

import { EmployeeValidationService } from './employee-validation.service';
import { EmployeeNotificationService } from './employee-notification.service';

@Injectable()
export class EmployeeService {
  constructor(
    @InjectRepository(Employee)
    private readonly employeeRepository: Repository<Employee>,
    @InjectRepository(Department)
    private readonly departmentRepository: Repository<Department>,
    @InjectRepository(Position)
    private readonly positionRepository: Repository<Position>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Tenant)
    private readonly tenantRepository: Repository<Tenant>,
    private readonly eventEmitter: EventEmitter2,
    private readonly configService: ConfigService,
    private readonly auditService: AuditService,
    private readonly validationService: EmployeeValidationService,
    private readonly notificationService: EmployeeNotificationService,
  ) {}

  /**
   * Create a new employee
   */
  async create(
    createEmployeeDto: CreateEmployeeDto,
    tenantId: string,
    createdBy: string,
  ): Promise<EmployeeResponseDto> {
    // Validate tenant exists
    const tenant = await this.tenantRepository.findOne({
      where: { id: tenantId },
    });
    if (!tenant) {
      throw new NotFoundException('Tenant not found');
    }

    // Validate department and position exist
    await this.validationService.validateDepartmentAndPosition(
      createEmployeeDto.departmentId,
      createEmployeeDto.positionId,
      tenantId,
    );

    // Validate manager if provided
    if (createEmployeeDto.managerId) {
      await this.validationService.validateManager(
        createEmployeeDto.managerId,
        tenantId,
      );
    }

    // Check for duplicate email
    const existingEmployee = await this.employeeRepository.findOne({
      where: {
        email: createEmployeeDto.email,
        tenantId,
      },
    });
    if (existingEmployee) {
      throw new ConflictException('Employee with this email already exists');
    }

    // Generate employee ID if not provided
    const employeeId = createEmployeeDto.employeeId || 
      await this.generateEmployeeId(tenantId);

    // Check for duplicate employee ID
    const existingEmployeeId = await this.employeeRepository.findOne({
      where: {
        employeeId,
        tenantId,
      },
    });
    if (existingEmployeeId) {
      throw new ConflictException('Employee ID already exists');
    }

    // Create employee entity with proper property mapping (only DTO properties)
    const employee = this.employeeRepository.create({
      employeeId,
      tenantId,
      createdBy,
      updatedBy: createdBy,
      // Map DTO properties to Entity properties
      email: createEmployeeDto.email,
      firstName: createEmployeeDto.firstName,
      middleName: createEmployeeDto.middleName,
      lastName: createEmployeeDto.lastName,
      preferredName: createEmployeeDto.preferredName,
      dateOfBirth: createEmployeeDto.dateOfBirth ? new Date(createEmployeeDto.dateOfBirth) : undefined,
      gender: createEmployeeDto.gender,
      maritalStatus: createEmployeeDto.maritalStatus,
      nationality: createEmployeeDto.nationality,
      phoneNumber: createEmployeeDto.phoneNumber,
      personalPhoneNumber: createEmployeeDto.personalPhoneNumber,
      status: createEmployeeDto.status,
      employmentType: createEmployeeDto.employmentType,
      dateOfJoining: createEmployeeDto.dateOfJoining ? new Date(createEmployeeDto.dateOfJoining) : new Date(),
      probationEndDate: createEmployeeDto.probationEndDate ? new Date(createEmployeeDto.probationEndDate) : undefined,
      departmentId: createEmployeeDto.departmentId,
      positionId: createEmployeeDto.positionId,
      managerId: createEmployeeDto.managerId,
      baseSalary: createEmployeeDto.baseSalary,
      salaryCurrency: createEmployeeDto.salaryCurrency,
      salaryFrequency: createEmployeeDto.salaryFrequency,
      workLocation: createEmployeeDto.workLocation,
      timeZone: createEmployeeDto.timeZone,
      userId: createEmployeeDto.userId,
      notes: createEmployeeDto.notes,
      // Skip nested objects that need separate handling
      // contacts, addresses, emergencyContacts, workSchedule will be handled separately
    });

    // Save employee
    const savedEmployee = await this.employeeRepository.save(employee) as Employee;

    // Create related entities if provided
    if (createEmployeeDto.contacts?.length) {
      await this.createEmployeeContacts(savedEmployee.id, createEmployeeDto.contacts);
    }

    if (createEmployeeDto.addresses?.length) {
      await this.createEmployeeAddresses(savedEmployee.id, createEmployeeDto.addresses);
    }

    if (createEmployeeDto.emergencyContacts?.length) {
      await this.createEmployeeEmergencyContacts(
        savedEmployee.id,
        createEmployeeDto.emergencyContacts,
      );
    }

    // Emit employee created event
    this.eventEmitter.emit('employee.created', {
      employee: savedEmployee,
      tenantId,
      createdBy,
    });

    // Log audit trail
    await this.auditService.logUserAction({
      userId: createdBy,
      action: 'EMPLOYEE_CREATED',
      resource: 'Employee',
      resourceId: savedEmployee.id,
      success: true,
      metadata: {
        employeeId: savedEmployee.employeeId,
        email: savedEmployee.email,
        name: `${savedEmployee.firstName} ${savedEmployee.lastName}`,
        tenantId,
      },
    });

    // Send welcome notification
    await this.notificationService.sendWelcomeNotification(savedEmployee);

    return this.findById(savedEmployee.id, tenantId);
  }

  /**
   * Find employee by ID
   */
  async findById(
    id: string,
    tenantId: string,
    include?: string[],
  ): Promise<EmployeeResponseDto> {
    const relations = this.buildRelations(include);

    const employee = await this.employeeRepository.findOne({
      where: { id, tenantId },
      relations,
    });

    if (!employee) {
      throw new NotFoundException('Employee not found');
    }

    return this.transformToResponseDto(employee);
  }

  /**
   * Find employee by employee ID
   */
  async findByEmployeeId(
    employeeId: string,
    tenantId: string,
    include?: string[],
  ): Promise<EmployeeResponseDto> {
    const relations = this.buildRelations(include);

    const employee = await this.employeeRepository.findOne({
      where: { employeeId, tenantId },
      relations,
    });

    if (!employee) {
      throw new NotFoundException('Employee not found');
    }

    return this.transformToResponseDto(employee);
  }

  /**
   * Find employees with pagination and filtering
   */
  async findAll(
    query: EmployeeQueryDto,
    tenantId: string,
  ): Promise<EmployeeListResponseDto> {
    const {
      page = 1,
      limit = 20,
      search,
      status,
      employmentType,
      departmentId,
      positionId,
      managerId,
      gender,
      maritalStatus,
      workLocation,
      joiningDateFrom,
      joiningDateTo,
      minSalary,
      maxSalary,
      skills,
      activeOnly,
      probationOnly,
      sortBy = 'firstName',
      sortOrder = 'ASC',
      include,
    } = query;

    const relations = this.buildRelations(include?.split(','));
    const queryBuilder = this.employeeRepository
      .createQueryBuilder('employee')
      .leftJoinAndSelect('employee.department', 'department')
      .leftJoinAndSelect('employee.position', 'position')
      .leftJoinAndSelect('employee.manager', 'manager')
      .where('employee.tenantId = :tenantId', { tenantId });

    // Apply filters
    if (search) {
      queryBuilder.andWhere(
        '(employee.firstName ILIKE :search OR employee.lastName ILIKE :search OR employee.email ILIKE :search OR employee.employeeId ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    if (status) {
      queryBuilder.andWhere('employee.status = :status', { status });
    }

    if (employmentType) {
      queryBuilder.andWhere('employee.employmentType = :employmentType', {
        employmentType,
      });
    }

    if (departmentId) {
      queryBuilder.andWhere('employee.departmentId = :departmentId', {
        departmentId,
      });
    }

    if (positionId) {
      queryBuilder.andWhere('employee.positionId = :positionId', {
        positionId,
      });
    }

    if (managerId) {
      queryBuilder.andWhere('employee.managerId = :managerId', { managerId });
    }

    if (gender) {
      queryBuilder.andWhere('employee.gender = :gender', { gender });
    }

    if (maritalStatus) {
      queryBuilder.andWhere('employee.maritalStatus = :maritalStatus', {
        maritalStatus,
      });
    }

    if (workLocation) {
      queryBuilder.andWhere('employee.workLocation ILIKE :workLocation', {
        workLocation: `%${workLocation}%`,
      });
    }

    if (joiningDateFrom) {
      queryBuilder.andWhere('employee.dateOfJoining >= :joiningDateFrom', {
        joiningDateFrom,
      });
    }

    if (joiningDateTo) {
      queryBuilder.andWhere('employee.dateOfJoining <= :joiningDateTo', {
        joiningDateTo,
      });
    }

    if (minSalary) {
      queryBuilder.andWhere('employee.baseSalary >= :minSalary', { minSalary });
    }

    if (maxSalary) {
      queryBuilder.andWhere('employee.baseSalary <= :maxSalary', { maxSalary });
    }

    if (activeOnly) {
      queryBuilder.andWhere('employee.status = :activeStatus', {
        activeStatus: EmployeeStatus.ACTIVE,
      });
    }

    if (probationOnly) {
      queryBuilder.andWhere('employee.probationEndDate > :now', {
        now: new Date(),
      });
    }

    // Apply sorting
    const sortField = this.getSortField(sortBy);
    queryBuilder.orderBy(sortField, sortOrder);

    // Apply pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    const [employees, total] = await queryBuilder.getManyAndCount();

    const data = employees.map(employee => this.transformToResponseDto(employee));

    return {
      data,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
      hasNext: page * limit < total,
      hasPrev: page > 1,
    };
  }

  /**
   * Update employee
   */
  async update(
    id: string,
    updateEmployeeDto: UpdateEmployeeDto,
    tenantId: string,
    updatedBy: string,
  ): Promise<EmployeeResponseDto> {
    const employee = await this.employeeRepository.findOne({
      where: { id, tenantId },
    });

    if (!employee) {
      throw new NotFoundException('Employee not found');
    }

    // Validate department and position if being updated
    if (updateEmployeeDto.departmentId || updateEmployeeDto.positionId) {
      await this.validationService.validateDepartmentAndPosition(
        updateEmployeeDto.departmentId || employee.departmentId,
        updateEmployeeDto.positionId || employee.positionId,
        tenantId,
      );
    }

    // Validate manager if being updated
    if (updateEmployeeDto.managerId) {
      await this.validationService.validateManager(
        updateEmployeeDto.managerId,
        tenantId,
      );
    }

    // Store original values for audit
    const originalValues = { ...employee };

    // Update employee
    Object.assign(employee, updateEmployeeDto, {
      updatedBy,
      updatedAt: new Date(),
    });

    const savedEmployee = await this.employeeRepository.save(employee);

    // Emit employee updated event
    this.eventEmitter.emit('employee.updated', {
      employee: savedEmployee,
      originalValues,
      tenantId,
      updatedBy,
    });

    // Log audit trail
    await this.auditService.logUserAction({
      userId: updatedBy,
      action: 'EMPLOYEE_UPDATED',
      resource: 'Employee',
      resourceId: savedEmployee.id,
      success: true,
      metadata: {
        employeeId: savedEmployee.employeeId,
        changes: this.getChangedFields(originalValues, savedEmployee),
        tenantId,
      },
    });

    return this.findById(savedEmployee.id, tenantId);
  }

  /**
   * Soft delete employee
   */
  async remove(
    id: string,
    tenantId: string,
    deletedBy: string,
    reason?: string,
  ): Promise<void> {
    const employee = await this.employeeRepository.findOne({
      where: { id, tenantId },
    });

    if (!employee) {
      throw new NotFoundException('Employee not found');
    }

    // Soft delete
    employee.deletedAt = new Date();
    employee.deletedBy = deletedBy;
    employee.status = EmployeeStatus.TERMINATED;

    await this.employeeRepository.save(employee);

    // Emit employee deleted event
    this.eventEmitter.emit('employee.deleted', {
      employee,
      tenantId,
      deletedBy,
      reason,
    });

    // Log audit trail
    await this.auditService.logUserAction({
      userId: deletedBy,
      action: 'EMPLOYEE_DELETED',
      resource: 'Employee',
      resourceId: employee.id,
      success: true,
      metadata: {
        employeeId: employee.employeeId,
        reason,
        tenantId,
      },
    });
  }

  /**
   * Get employee statistics
   */
  async getStats(
    query: EmployeeStatsQueryDto,
    tenantId: string,
  ): Promise<EmployeeStatsResponseDto> {
    const { departmentId, dateFrom, dateTo } = query;

    const queryBuilder = this.employeeRepository
      .createQueryBuilder('employee')
      .where('employee.tenantId = :tenantId', { tenantId });

    if (departmentId) {
      queryBuilder.andWhere('employee.departmentId = :departmentId', {
        departmentId,
      });
    }

    if (dateFrom) {
      queryBuilder.andWhere('employee.dateOfJoining >= :dateFrom', {
        dateFrom,
      });
    }

    if (dateTo) {
      queryBuilder.andWhere('employee.dateOfJoining <= :dateTo', { dateTo });
    }

    const employees = await queryBuilder.getMany();

    const now = new Date();
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const thisYear = new Date(now.getFullYear(), 0, 1);

    const stats = {
      totalEmployees: employees.length,
      activeEmployees: employees.filter(e => e.status === EmployeeStatus.ACTIVE).length,
      inactiveEmployees: employees.filter(e => e.status !== EmployeeStatus.ACTIVE).length,
      employeesOnProbation: employees.filter(e =>
        e.probationEndDate && new Date(e.probationEndDate) > now
      ).length,
      newHiresThisMonth: employees.filter(e =>
        new Date(e.dateOfJoining) >= thisMonth
      ).length,
      newHiresThisYear: employees.filter(e =>
        new Date(e.dateOfJoining) >= thisYear
      ).length,
      terminationsThisMonth: employees.filter(e =>
        e.dateOfLeaving && new Date(e.dateOfLeaving) >= thisMonth
      ).length,
      terminationsThisYear: employees.filter(e =>
        e.dateOfLeaving && new Date(e.dateOfLeaving) >= thisYear
      ).length,
      averageYearsOfService: this.calculateAverageYearsOfService(employees),
      employmentTypeBreakdown: this.getEmploymentTypeBreakdown(employees),
      departmentBreakdown: await this.getDepartmentBreakdown(employees, tenantId),
      genderBreakdown: this.getGenderBreakdown(employees),
      ageGroupBreakdown: this.getAgeGroupBreakdown(employees),
      generatedAt: new Date(),
    };

    return stats;
  }

  /**
   * Generate unique employee ID
   */
  private async generateEmployeeId(tenantId: string): Promise<string> {
    const tenant = await this.tenantRepository.findOne({
      where: { id: tenantId },
    });

    const prefix = tenant?.settings?.employeeIdPrefix || 'EMP';
    const year = new Date().getFullYear().toString().slice(-2);

    // Get the last employee ID for this tenant
    const lastEmployee = await this.employeeRepository
      .createQueryBuilder('employee')
      .where('employee.tenantId = :tenantId', { tenantId })
      .andWhere('employee.employeeId LIKE :pattern', { pattern: `${prefix}${year}%` })
      .orderBy('employee.employeeId', 'DESC')
      .getOne();

    let sequence = 1;
    if (lastEmployee) {
      const lastSequence = parseInt(lastEmployee.employeeId.slice(-4));
      sequence = lastSequence + 1;
    }

    return `${prefix}${year}${sequence.toString().padStart(4, '0')}`;
  }

  /**
   * Create employee contacts
   */
  private async createEmployeeContacts(
    employeeId: string,
    contacts: any[],
  ): Promise<void> {
    const contactEntities = contacts.map(contact => ({
      ...contact,
      employeeId,
    }));

    await this.employeeRepository.manager
      .getRepository(EmployeeContact)
      .save(contactEntities);
  }

  /**
   * Create employee addresses
   */
  private async createEmployeeAddresses(
    employeeId: string,
    addresses: any[],
  ): Promise<void> {
    const addressEntities = addresses.map(address => ({
      ...address,
      employeeId,
    }));

    await this.employeeRepository.manager
      .getRepository(EmployeeAddress)
      .save(addressEntities);
  }

  /**
   * Create employee emergency contacts
   */
  private async createEmployeeEmergencyContacts(
    employeeId: string,
    emergencyContacts: any[],
  ): Promise<void> {
    const emergencyContactEntities = emergencyContacts.map(contact => ({
      ...contact,
      employeeId,
    }));

    await this.employeeRepository.manager
      .getRepository(EmployeeEmergencyContact)
      .save(emergencyContactEntities);
  }

  /**
   * Build relations array for queries
   */
  private buildRelations(include?: string[]): string[] {
    const relations = ['department', 'position'];

    if (include?.includes('manager')) {
      relations.push('manager');
    }

    if (include?.includes('contacts')) {
      relations.push('contacts');
    }

    if (include?.includes('addresses')) {
      relations.push('addresses');
    }

    if (include?.includes('emergencyContacts')) {
      relations.push('emergencyContacts');
    }

    if (include?.includes('education')) {
      relations.push('education');
    }

    if (include?.includes('experience')) {
      relations.push('experience');
    }

    if (include?.includes('skills')) {
      relations.push('skills');
    }

    if (include?.includes('certifications')) {
      relations.push('certifications');
    }

    return relations;
  }

  /**
   * Get sort field for query
   */
  private getSortField(sortBy: string): string {
    const sortFields = {
      firstName: 'employee.firstName',
      lastName: 'employee.lastName',
      email: 'employee.email',
      employeeId: 'employee.employeeId',
      dateOfJoining: 'employee.dateOfJoining',
      status: 'employee.status',
      department: 'department.name',
      position: 'position.title',
    };

    return (sortFields as any)[sortBy] || 'employee.firstName';
  }

  /**
   * Transform employee entity to response DTO
   */
  private transformToResponseDto(employee: Employee): EmployeeResponseDto {
    const dto = new EmployeeResponseDto();
    Object.assign(dto, employee);
    return dto;
  }

  /**
   * Get changed fields for audit
   */
  private getChangedFields(original: Employee, updated: Employee): Record<string, any> {
    const changes: Record<string, any> = {};

    const fieldsToCheck = [
      'firstName', 'lastName', 'email', 'status', 'employmentType',
      'departmentId', 'positionId', 'managerId', 'baseSalary', 'workLocation',
    ];

    fieldsToCheck.forEach(field => {
      const originalValue = (original as any)[field];
      const updatedValue = (updated as any)[field];
      if (originalValue !== updatedValue) {
        changes[field] = {
          from: originalValue,
          to: updatedValue,
        };
      }
    });

    return changes;
  }

  /**
   * Calculate average years of service
   */
  private calculateAverageYearsOfService(employees: Employee[]): number {
    if (employees.length === 0) return 0;

    const totalYears = employees.reduce((sum, employee) => {
      const today = new Date();
      const joinDate = new Date(employee.dateOfJoining);
      const years = (today.getTime() - joinDate.getTime()) / (1000 * 60 * 60 * 24 * 365.25);
      return sum + years;
    }, 0);

    return Math.round((totalYears / employees.length) * 100) / 100;
  }

  /**
   * Get employment type breakdown
   */
  private getEmploymentTypeBreakdown(employees: Employee[]): Record<string, number> {
    const breakdown: Record<string, number> = {};

    employees.forEach(employee => {
      const type = employee.employmentType || 'Unknown';
      breakdown[type] = (breakdown[type] || 0) + 1;
    });

    return breakdown;
  }

  /**
   * Get department breakdown
   */
  private async getDepartmentBreakdown(
    employees: Employee[],
    tenantId: string,
  ): Promise<Record<string, number>> {
    const breakdown: Record<string, number> = {};
    const departmentIds = [...new Set(employees.map(e => e.departmentId))];

    const departments = await this.departmentRepository.find({
      where: { id: In(departmentIds), tenantId },
    });

    const departmentMap = new Map(departments.map(d => [d.id, d.name]));

    employees.forEach(employee => {
      const departmentName = departmentMap.get(employee.departmentId) || 'Unknown';
      breakdown[departmentName] = (breakdown[departmentName] || 0) + 1;
    });

    return breakdown;
  }

  /**
   * Get gender breakdown
   */
  private getGenderBreakdown(employees: Employee[]): Record<string, number> {
    const breakdown: Record<string, number> = {};

    employees.forEach(employee => {
      const gender = employee.gender || 'Not Specified';
      breakdown[gender] = (breakdown[gender] || 0) + 1;
    });

    return breakdown;
  }

  /**
   * Get age group breakdown
   */
  private getAgeGroupBreakdown(employees: Employee[]): Record<string, number> {
    const breakdown: Record<string, number> = {
      '18-25': 0,
      '26-35': 0,
      '36-45': 0,
      '46-55': 0,
      '56-65': 0,
      '65+': 0,
      'Unknown': 0,
    };

    employees.forEach(employee => {
      if (!employee.dateOfBirth) {
        breakdown['Unknown']++;
        return;
      }

      const today = new Date();
      const birthDate = new Date(employee.dateOfBirth);
      let age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();

      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }

      if (age >= 18 && age <= 25) breakdown['18-25']++;
      else if (age >= 26 && age <= 35) breakdown['26-35']++;
      else if (age >= 36 && age <= 45) breakdown['36-45']++;
      else if (age >= 46 && age <= 55) breakdown['46-55']++;
      else if (age >= 56 && age <= 65) breakdown['56-65']++;
      else if (age > 65) breakdown['65+']++;
      else breakdown['Unknown']++;
    });

    return breakdown;
  }
}
