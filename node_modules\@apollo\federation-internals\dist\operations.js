"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.hasSelectionWithPredicate = exports.operationToDocument = exports.parseOperationAST = exports.parseSelectionSet = exports.parseOperation = exports.operationFromDocument = exports.FragmentSpreadSelection = exports.FragmentSelection = exports.FieldSelection = exports.selectionOfElement = exports.selectionSetOfElement = exports.selectionSetOf = exports.allFieldDefinitionsInSelectionSet = exports.MutableSelectionSet = exports.SelectionSetUpdates = exports.SelectionSet = exports.ContainsResult = exports.NamedFragments = exports.NamedFragmentDefinition = exports.Operation = exports.concatOperationPaths = exports.conditionalDirectivesInOperationPath = exports.sameOperationPaths = exports.operationPathToStringPath = exports.FragmentElement = exports.Field = exports.DEFAULT_MIN_USAGES_TO_OPTIMIZE = void 0;
const graphql_1 = require("graphql");
const definitions_1 = require("./definitions");
const federation_1 = require("./federation");
const error_1 = require("./error");
const types_1 = require("./types");
const utils_1 = require("./utils");
const values_1 = require("./values");
const uuid_1 = require("uuid");
exports.DEFAULT_MIN_USAGES_TO_OPTIMIZE = 2;
function validate(condition, message, sourceAST) {
    if (!condition) {
        throw error_1.ERRORS.INVALID_GRAPHQL.err(message(), { nodes: sourceAST });
    }
}
function haveSameDirectives(op1, op2) {
    return (0, definitions_1.sameDirectiveApplications)(op1.appliedDirectives, op2.appliedDirectives);
}
class AbstractOperationElement extends definitions_1.DirectiveTargetElement {
    constructor(schema, directives) {
        super(schema, directives);
    }
    collectVariables(collector) {
        this.collectVariablesInElement(collector);
        this.collectVariablesInAppliedDirectives(collector);
    }
    rebaseOnOrError(parentType) {
        return this.rebaseOn({ parentType, errorIfCannotRebase: true });
    }
    addAttachment(key, value) {
        if (!this.attachments) {
            this.attachments = new Map();
        }
        this.attachments.set(key, value);
    }
    getAttachment(key) {
        var _a;
        return (_a = this.attachments) === null || _a === void 0 ? void 0 : _a.get(key);
    }
    copyAttachmentsTo(elt) {
        if (this.attachments) {
            for (const [k, v] of this.attachments.entries()) {
                elt.addAttachment(k, v);
            }
        }
    }
    keyForDirectives() {
        return this.appliedDirectives.map((d) => keyForDirective(d)).join(' ');
    }
}
class Field extends AbstractOperationElement {
    constructor(definition, args, directives, alias) {
        super(definition.schema(), directives);
        this.definition = definition;
        this.args = args;
        this.alias = alias;
        this.kind = 'Field';
    }
    collectVariablesInElement(collector) {
        if (this.args) {
            collector.collectInArguments(this.args);
        }
    }
    get name() {
        return this.definition.name;
    }
    argumentValue(name) {
        return this.args ? this.args[name] : undefined;
    }
    responseName() {
        return this.alias ? this.alias : this.name;
    }
    key() {
        return this.responseName() + this.keyForDirectives();
    }
    asPathElement() {
        return this.responseName();
    }
    get parentType() {
        return this.definition.parent;
    }
    isLeafField() {
        return (0, definitions_1.isLeafType)(this.baseType());
    }
    baseType() {
        return (0, definitions_1.baseType)(this.definition.type);
    }
    copy() {
        const newField = new Field(this.definition, this.args, this.appliedDirectives, this.alias);
        this.copyAttachmentsTo(newField);
        return newField;
    }
    withUpdatedArguments(newArgs) {
        const newField = new Field(this.definition, { ...this.args, ...newArgs }, this.appliedDirectives, this.alias);
        this.copyAttachmentsTo(newField);
        return newField;
    }
    withUpdatedDefinition(newDefinition) {
        const newField = new Field(newDefinition, this.args, this.appliedDirectives, this.alias);
        this.copyAttachmentsTo(newField);
        return newField;
    }
    withUpdatedAlias(newAlias) {
        const newField = new Field(this.definition, this.args, this.appliedDirectives, newAlias);
        this.copyAttachmentsTo(newField);
        return newField;
    }
    withUpdatedDirectives(newDirectives) {
        const newField = new Field(this.definition, this.args, newDirectives, this.alias);
        this.copyAttachmentsTo(newField);
        return newField;
    }
    argumentsToNodes() {
        if (!this.args) {
            return undefined;
        }
        const entries = Object.entries(this.args);
        if (entries.length === 0) {
            return undefined;
        }
        return entries.map(([n, v]) => {
            return {
                kind: graphql_1.Kind.ARGUMENT,
                name: { kind: graphql_1.Kind.NAME, value: n },
                value: (0, values_1.valueToAST)(v, this.definition.argument(n).type),
            };
        });
    }
    selects(definition, assumeValid = false, variableDefinitions, contextualArguments) {
        (0, utils_1.assert)(assumeValid || variableDefinitions, 'Must provide variable definitions if validation is needed');
        if (definition === this.definition) {
            return true;
        }
        if (this.name !== definition.name) {
            return false;
        }
        for (const argDef of definition.arguments()) {
            const appliedValue = this.argumentValue(argDef.name);
            if (appliedValue === undefined) {
                if (argDef.defaultValue === undefined && !(0, definitions_1.isNullableType)(argDef.type) && (!contextualArguments || !(contextualArguments === null || contextualArguments === void 0 ? void 0 : contextualArguments.includes(argDef.name)))) {
                    return false;
                }
            }
            else {
                if (!assumeValid && !(0, values_1.isValidValue)(appliedValue, argDef, variableDefinitions)) {
                    return false;
                }
            }
        }
        if (!assumeValid && this.args) {
            for (const [name, value] of Object.entries(this.args)) {
                if (value !== null && definition.argument(name) === undefined) {
                    return false;
                }
            }
        }
        return true;
    }
    validate(variableDefinitions, validateContextualArgs) {
        var _a;
        validate(this.name === this.definition.name, () => `Field name "${this.name}" cannot select field "${this.definition.coordinate}: name mismatch"`);
        for (const argDef of this.definition.arguments()) {
            const appliedValue = this.argumentValue(argDef.name);
            let isContextualArg = false;
            const schema = this.definition.schema();
            const fromContextDirective = (_a = (0, federation_1.federationMetadata)(schema)) === null || _a === void 0 ? void 0 : _a.fromContextDirective();
            if (fromContextDirective && (0, federation_1.isFederationDirectiveDefinedInSchema)(fromContextDirective)) {
                isContextualArg = argDef.appliedDirectivesOf(fromContextDirective).length > 0;
            }
            if (appliedValue === undefined) {
                validate((isContextualArg && !validateContextualArgs) || argDef.defaultValue !== undefined || (0, definitions_1.isNullableType)(argDef.type), () => `Missing mandatory value for argument "${argDef.name}" of field "${this.definition.coordinate}" in selection "${this}"`);
            }
            else {
                validate((isContextualArg && !validateContextualArgs) || (0, values_1.isValidValue)(appliedValue, argDef, variableDefinitions), () => `Invalid value ${(0, values_1.valueToString)(appliedValue)} for argument "${argDef.coordinate}" of type ${argDef.type}`);
            }
        }
        if (this.args) {
            for (const [name, value] of Object.entries(this.args)) {
                validate(value === null || this.definition.argument(name) !== undefined, () => `Unknown argument "${name}" in field application of "${this.name}"`);
            }
        }
    }
    rebaseOn({ parentType, errorIfCannotRebase }) {
        const fieldParent = this.definition.parent;
        if (parentType === fieldParent) {
            return this;
        }
        if (this.name === definitions_1.typenameFieldName) {
            if ((0, definitions_1.possibleRuntimeTypes)(parentType).some((runtimeType) => (0, federation_1.isInterfaceObjectType)(runtimeType))) {
                validate(!errorIfCannotRebase, () => `Cannot add selection of field "${this.definition.coordinate}" to selection set of parent type "${parentType}" that is potentially an interface object type at runtime`);
                return undefined;
            }
            else {
                return this.withUpdatedDefinition(parentType.typenameField());
            }
        }
        const fieldDef = parentType.field(this.name);
        const canRebase = this.canRebaseOn(parentType) && fieldDef;
        if (!canRebase) {
            validate(!errorIfCannotRebase, () => `Cannot add selection of field "${this.definition.coordinate}" to selection set of parent type "${parentType}"`);
            return undefined;
        }
        return this.withUpdatedDefinition(fieldDef);
    }
    canRebaseOn(parentType) {
        const fieldParentType = this.definition.parent;
        return parentType.name === fieldParentType.name
            || (0, definitions_1.isInterfaceType)(fieldParentType)
            || (0, federation_1.isInterfaceObjectType)(fieldParentType);
    }
    typeIfAddedTo(parentType) {
        var _a, _b, _c;
        const fieldParentType = this.definition.parent;
        if (parentType == fieldParentType) {
            return this.definition.type;
        }
        if (this.name === definitions_1.typenameFieldName) {
            return (_a = parentType.typenameField()) === null || _a === void 0 ? void 0 : _a.type;
        }
        const returnType = this.canRebaseOn(parentType)
            ? (_b = parentType.field(this.name)) === null || _b === void 0 ? void 0 : _b.type
            : undefined;
        const fromContextDirective = (_c = (0, federation_1.federationMetadata)(parentType.schema())) === null || _c === void 0 ? void 0 : _c.fromContextDirective();
        if (fromContextDirective && (0, federation_1.isFederationDirectiveDefinedInSchema)(fromContextDirective)) {
            const fieldInParent = parentType.field(this.name);
            if (fieldInParent && fieldInParent.arguments()
                .some(arg => arg.appliedDirectivesOf(fromContextDirective).length > 0 && (!this.args || this.args[arg.name] === undefined))) {
                return undefined;
            }
        }
        return returnType;
    }
    hasDefer() {
        return false;
    }
    deferDirectiveArgs() {
        return undefined;
    }
    withoutDefer() {
        return this;
    }
    equals(that) {
        if (this === that) {
            return true;
        }
        return that.kind === 'Field'
            && this.name === that.name
            && this.alias === that.alias
            && (this.args ? that.args && (0, values_1.argumentsEquals)(this.args, that.args) : !that.args)
            && haveSameDirectives(this, that);
    }
    toString() {
        const alias = this.alias ? this.alias + ': ' : '';
        const entries = this.args ? Object.entries(this.args) : [];
        const args = entries.length === 0
            ? ''
            : '(' + entries.map(([n, v]) => { var _a; return `${n}: ${(0, values_1.valueToString)(v, (_a = this.definition.argument(n)) === null || _a === void 0 ? void 0 : _a.type)}`; }).join(', ') + ')';
        return alias + this.name + args + this.appliedDirectivesToString();
    }
}
exports.Field = Field;
function keyForDirective(directive, directivesNeverEqualToThemselves = ['defer']) {
    if (directivesNeverEqualToThemselves.includes(directive.name)) {
        return (0, uuid_1.v1)();
    }
    const entries = Object.entries(directive.arguments()).filter(([_, v]) => v !== undefined);
    entries.sort(([n1], [n2]) => n1.localeCompare(n2));
    const args = entries.length == 0 ? '' : '(' + entries.map(([n, v]) => `${n}: ${(0, values_1.valueToString)(v, directive.argumentType(n))}`).join(', ') + ')';
    return `@${directive.name}${args}`;
}
class FragmentElement extends AbstractOperationElement {
    constructor(sourceType, typeCondition, directives) {
        super(sourceType.schema(), directives);
        this.sourceType = sourceType;
        this.kind = 'FragmentElement';
        this.typeCondition = typeCondition !== undefined && typeof typeCondition === 'string'
            ? this.schema().type(typeCondition)
            : typeCondition;
    }
    collectVariablesInElement(_) {
    }
    get parentType() {
        return this.sourceType;
    }
    key() {
        if (!this.computedKey) {
            this.computedKey = '...' + (this.typeCondition ? ' on ' + this.typeCondition.name : '') + this.keyForDirectives();
        }
        return this.computedKey;
    }
    castedType() {
        return this.typeCondition ? this.typeCondition : this.sourceType;
    }
    asPathElement() {
        const condition = this.typeCondition;
        return condition ? `... on ${condition}` : undefined;
    }
    withUpdatedSourceType(newSourceType) {
        return this.withUpdatedTypes(newSourceType, this.typeCondition);
    }
    withUpdatedCondition(newCondition) {
        return this.withUpdatedTypes(this.sourceType, newCondition);
    }
    withUpdatedTypes(newSourceType, newCondition) {
        const newFragment = new FragmentElement(newSourceType, newCondition === null || newCondition === void 0 ? void 0 : newCondition.name, this.appliedDirectives);
        this.copyAttachmentsTo(newFragment);
        return newFragment;
    }
    withUpdatedDirectives(newDirectives) {
        const newFragment = new FragmentElement(this.sourceType, this.typeCondition, newDirectives);
        this.copyAttachmentsTo(newFragment);
        return newFragment;
    }
    rebaseOn({ parentType, errorIfCannotRebase }) {
        const fragmentParent = this.parentType;
        const typeCondition = this.typeCondition;
        if (parentType === fragmentParent) {
            return this;
        }
        const { canRebase, rebasedCondition } = this.canRebaseOn(parentType);
        if (!canRebase) {
            validate(!errorIfCannotRebase, () => `Cannot add fragment of condition "${typeCondition}" (runtimes: [${(0, definitions_1.possibleRuntimeTypes)(typeCondition)}]) to parent type "${parentType}" (runtimes: ${(0, definitions_1.possibleRuntimeTypes)(parentType)})`);
            return undefined;
        }
        return this.withUpdatedTypes(parentType, rebasedCondition);
    }
    canRebaseOn(parentType) {
        if (!this.typeCondition) {
            return { canRebase: true, rebasedCondition: undefined };
        }
        const rebasedCondition = parentType.schema().type(this.typeCondition.name);
        if (!rebasedCondition || !(0, definitions_1.isCompositeType)(rebasedCondition) || !(0, definitions_1.runtimeTypesIntersects)(parentType, rebasedCondition)) {
            return { canRebase: false };
        }
        return { canRebase: true, rebasedCondition };
    }
    castedTypeIfAddedTo(parentType) {
        if (parentType == this.parentType) {
            return this.castedType();
        }
        const { canRebase, rebasedCondition } = this.canRebaseOn(parentType);
        return canRebase ? (rebasedCondition ? rebasedCondition : parentType) : undefined;
    }
    hasDefer() {
        return this.hasAppliedDirective('defer');
    }
    hasStream() {
        return this.hasAppliedDirective('stream');
    }
    deferDirectiveArgs() {
        var _a;
        return (_a = this.appliedDirectivesOf(this.schema().deferDirective())[0]) === null || _a === void 0 ? void 0 : _a.arguments();
    }
    withoutDefer() {
        const deferName = this.schema().deferDirective().name;
        const updatedDirectives = this.appliedDirectives.filter((d) => d.name !== deferName);
        if (!this.typeCondition && updatedDirectives.length === 0) {
            return undefined;
        }
        if (updatedDirectives.length === this.appliedDirectives.length) {
            return this;
        }
        const updated = new FragmentElement(this.sourceType, this.typeCondition, updatedDirectives);
        this.copyAttachmentsTo(updated);
        return updated;
    }
    withNormalizedDefer(normalizer) {
        const deferArgs = this.deferDirectiveArgs();
        if (!deferArgs) {
            return this;
        }
        let newDeferArgs = undefined;
        let conditionVariable = undefined;
        if (deferArgs.if !== undefined) {
            if (typeof deferArgs.if === 'boolean') {
                if (deferArgs.if) {
                    newDeferArgs = {
                        ...deferArgs,
                        if: undefined,
                    };
                }
                else {
                    return this.withoutDefer();
                }
            }
            else {
                conditionVariable = deferArgs.if;
            }
        }
        let label = deferArgs.label;
        if (!label) {
            label = normalizer.newLabel();
            if (newDeferArgs) {
                newDeferArgs.label = label;
            }
            else {
                newDeferArgs = {
                    ...deferArgs,
                    label,
                };
            }
        }
        if (conditionVariable) {
            normalizer.registerCondition(label, conditionVariable);
        }
        if (!newDeferArgs) {
            return this;
        }
        const deferDirective = this.schema().deferDirective();
        const updatedDirectives = this.appliedDirectives
            .filter((d) => d.name !== deferDirective.name)
            .concat(new definitions_1.Directive(deferDirective.name, newDeferArgs));
        const updated = new FragmentElement(this.sourceType, this.typeCondition, updatedDirectives);
        this.copyAttachmentsTo(updated);
        return updated;
    }
    equals(that) {
        var _a, _b;
        if (this === that) {
            return true;
        }
        return that.kind === 'FragmentElement'
            && ((_a = this.typeCondition) === null || _a === void 0 ? void 0 : _a.name) === ((_b = that.typeCondition) === null || _b === void 0 ? void 0 : _b.name)
            && haveSameDirectives(this, that);
    }
    toString() {
        return '...' + (this.typeCondition ? ' on ' + this.typeCondition : '') + this.appliedDirectivesToString();
    }
}
exports.FragmentElement = FragmentElement;
function operationPathToStringPath(path) {
    return path
        .filter((p) => !(p.kind === 'FragmentElement' && !p.typeCondition))
        .map((p) => { var _a; return p.kind === 'Field' ? p.responseName() : `... on ${(_a = p.typeCondition) === null || _a === void 0 ? void 0 : _a.coordinate}`; });
}
exports.operationPathToStringPath = operationPathToStringPath;
function sameOperationPaths(p1, p2) {
    if (p1 === p2) {
        return true;
    }
    if (p1.length !== p2.length) {
        return false;
    }
    for (let i = 0; i < p1.length; i++) {
        if (!p1[i].equals(p2[i])) {
            return false;
        }
    }
    return true;
}
exports.sameOperationPaths = sameOperationPaths;
function conditionalDirectivesInOperationPath(path) {
    return path.map((e) => e.appliedDirectives).flat().filter((d) => (0, definitions_1.isConditionalDirective)(d));
}
exports.conditionalDirectivesInOperationPath = conditionalDirectivesInOperationPath;
function concatOperationPaths(head, tail) {
    if (head.length === 0) {
        return tail;
    }
    if (tail.length === 0) {
        return head;
    }
    const lastOfHead = head[head.length - 1];
    const conditionals = conditionalDirectivesInOperationPath(head);
    let firstOfTail = tail[0];
    while (firstOfTail && isUselessFollowupElement(lastOfHead, firstOfTail, conditionals)) {
        tail = tail.slice(1);
        firstOfTail = tail[0];
    }
    return head.concat(tail);
}
exports.concatOperationPaths = concatOperationPaths;
function isUselessFollowupElement(first, followup, conditionals) {
    const typeOfFirst = first.kind === 'Field'
        ? first.baseType()
        : first.typeCondition;
    return !!typeOfFirst
        && followup.kind === 'FragmentElement'
        && !!followup.typeCondition
        && (followup.appliedDirectives.length === 0 || (0, definitions_1.isDirectiveApplicationsSubset)(conditionals, followup.appliedDirectives))
        && (0, types_1.isSubtype)(followup.typeCondition, typeOfFirst);
}
function computeFragmentsDependents(fragments) {
    const reverseDeps = new utils_1.SetMultiMap();
    for (const fragment of fragments.definitions()) {
        for (const dependency of fragment.fragmentUsages().keys()) {
            reverseDeps.add(dependency, fragment.name);
        }
    }
    return reverseDeps;
}
function clearKeptFragments(usages, fragments, minUsagesToOptimize) {
    let toCheck = Array.from(usages.entries()).filter(([_, count]) => count >= minUsagesToOptimize).map(([name, _]) => name);
    while (toCheck.length > 0) {
        const newToCheck = [];
        for (const name of toCheck) {
            usages.delete(name);
            const ownUsages = fragments.get(name).fragmentUsages();
            for (const [otherName, otherCount] of ownUsages.entries()) {
                const prevCount = usages.get(otherName);
                if (prevCount !== undefined) {
                    const newCount = prevCount + otherCount;
                    usages.set(otherName, newCount);
                    if (prevCount < minUsagesToOptimize && newCount >= minUsagesToOptimize) {
                        newToCheck.push(otherName);
                    }
                }
            }
        }
        toCheck = newToCheck;
    }
}
function computeFragmentsToKeep(selectionSet, fragments, minUsagesToOptimize) {
    const usages = new Map();
    selectionSet.collectUsedFragmentNames(usages);
    if (usages.size === 0) {
        return null;
    }
    for (const fragment of fragments.definitions()) {
        if (usages.get(fragment.name) === undefined) {
            usages.set(fragment.name, 0);
        }
    }
    const reverseDependencies = computeFragmentsDependents(fragments);
    const toExpand = new Set;
    let shouldContinue = true;
    while (shouldContinue) {
        shouldContinue = false;
        clearKeptFragments(usages, fragments, minUsagesToOptimize);
        for (const name of (0, utils_1.mapKeys)(usages)) {
            const count = usages.get(name);
            if (count === 0) {
                continue;
            }
            if (count >= minUsagesToOptimize) {
                shouldContinue = true;
                break;
            }
            const fragmentsUsingName = reverseDependencies.get(name);
            if (!fragmentsUsingName || [...fragmentsUsingName].every((fragName) => toExpand.has(fragName) || !usages.get(fragName))) {
                toExpand.add(name);
                usages.delete(name);
                shouldContinue = true;
                const nameUsages = fragments.get(name).fragmentUsages();
                for (const [otherName, otherCount] of nameUsages.entries()) {
                    const prev = usages.get(otherName);
                    if (prev !== undefined) {
                        usages.set(otherName, prev + count * otherCount);
                    }
                }
            }
        }
    }
    for (const name of usages.keys()) {
        toExpand.add(name);
    }
    return toExpand.size === 0 ? fragments : fragments.filter((f) => !toExpand.has(f.name));
}
class Operation extends definitions_1.DirectiveTargetElement {
    constructor(schema, rootKind, selectionSet, variableDefinitions, fragments, name, directives = []) {
        super(schema, directives);
        this.rootKind = rootKind;
        this.selectionSet = selectionSet;
        this.variableDefinitions = variableDefinitions;
        this.fragments = fragments;
        this.name = name;
    }
    withUpdatedSelectionSet(newSelectionSet) {
        if (this.selectionSet === newSelectionSet) {
            return this;
        }
        return new Operation(this.schema(), this.rootKind, newSelectionSet, this.variableDefinitions, this.fragments, this.name, this.appliedDirectives);
    }
    collectUndefinedVariablesFromFragments(fragments) {
        const collector = new definitions_1.VariableCollector();
        for (const namedFragment of fragments.definitions()) {
            namedFragment.selectionSet.usedVariables().forEach(v => {
                if (!this.variableDefinitions.definition(v)) {
                    collector.add(v);
                }
            });
        }
        return collector.variables();
    }
    withUpdatedSelectionSetAndFragments(newSelectionSet, newFragments, allAvailableVariables) {
        if (this.selectionSet === newSelectionSet && newFragments === this.fragments) {
            return this;
        }
        let newVariableDefinitions = this.variableDefinitions;
        if (allAvailableVariables && newFragments) {
            const undefinedVariables = this.collectUndefinedVariablesFromFragments(newFragments);
            if (undefinedVariables.length > 0) {
                newVariableDefinitions = new definitions_1.VariableDefinitions();
                newVariableDefinitions.addAll(this.variableDefinitions);
                newVariableDefinitions.addAll(allAvailableVariables.filter(undefinedVariables));
            }
        }
        return new Operation(this.schema(), this.rootKind, newSelectionSet, newVariableDefinitions, newFragments, this.name, this.appliedDirectives);
    }
    optimize(fragments, minUsagesToOptimize = exports.DEFAULT_MIN_USAGES_TO_OPTIMIZE, allAvailableVariables) {
        (0, utils_1.assert)(minUsagesToOptimize >= 1, `Expected 'minUsagesToOptimize' to be at least 1, but got ${minUsagesToOptimize}`);
        if (!fragments || fragments.isEmpty()) {
            return this;
        }
        let optimizedSelection = this.selectionSet.optimize(fragments);
        if (optimizedSelection === this.selectionSet) {
            return this;
        }
        let finalFragments = computeFragmentsToKeep(optimizedSelection, fragments, minUsagesToOptimize);
        if (finalFragments !== null && (finalFragments === null || finalFragments === void 0 ? void 0 : finalFragments.size) !== fragments.size) {
            optimizedSelection = optimizedSelection.expandFragments(finalFragments);
            optimizedSelection = optimizedSelection.normalize({ parentType: optimizedSelection.parentType });
            if (finalFragments) {
                let beforeRemoval;
                do {
                    beforeRemoval = finalFragments;
                    const usages = new Map();
                    optimizedSelection.collectUsedFragmentNames(usages);
                    finalFragments.collectUsedFragmentNames(usages);
                    finalFragments = finalFragments.filter((f) => { var _a; return ((_a = usages.get(f.name)) !== null && _a !== void 0 ? _a : 0) > 0; });
                } while (finalFragments && finalFragments.size < beforeRemoval.size);
            }
        }
        return this.withUpdatedSelectionSetAndFragments(optimizedSelection, finalFragments !== null && finalFragments !== void 0 ? finalFragments : undefined, allAvailableVariables);
    }
    generateQueryFragments() {
        const [minimizedSelectionSet, fragments] = this.selectionSet.minimizeSelectionSet();
        return new Operation(this.schema(), this.rootKind, minimizedSelectionSet, this.variableDefinitions, fragments, this.name, this.appliedDirectives);
    }
    expandAllFragments() {
        const expanded = this.selectionSet.expandFragments();
        return this.withUpdatedSelectionSetAndFragments(expanded.normalize({ parentType: expanded.parentType }), undefined);
    }
    normalize() {
        return this.withUpdatedSelectionSet(this.selectionSet.normalize({ parentType: this.selectionSet.parentType }));
    }
    withoutDefer(labelsToRemove) {
        return this.withUpdatedSelectionSet(this.selectionSet.withoutDefer(labelsToRemove));
    }
    withNormalizedDefer() {
        const normalizer = new DeferNormalizer();
        const { hasDefers, hasNonLabelledOrConditionalDefers } = normalizer.init(this.selectionSet);
        let updatedOperation = this;
        if (hasNonLabelledOrConditionalDefers) {
            updatedOperation = this.withUpdatedSelectionSet(this.selectionSet.withNormalizedDefer(normalizer));
        }
        return {
            operation: updatedOperation,
            hasDefers,
            assignedDeferLabels: normalizer.assignedLabels,
            deferConditions: normalizer.deferConditions,
        };
    }
    collectDefaultedVariableValues() {
        const defaultedVariableValues = {};
        for (const { variable, defaultValue } of this.variableDefinitions.definitions()) {
            if (defaultValue !== undefined) {
                defaultedVariableValues[variable.name] = defaultValue;
            }
        }
        return defaultedVariableValues;
    }
    toString(expandFragments = false, prettyPrint = true) {
        return this.selectionSet.toOperationString(this.rootKind, this.variableDefinitions, this.fragments, this.name, this.appliedDirectives, expandFragments, prettyPrint);
    }
}
exports.Operation = Operation;
class NamedFragmentDefinition extends definitions_1.DirectiveTargetElement {
    constructor(schema, name, typeCondition, directives) {
        super(schema, directives);
        this.name = name;
        this.typeCondition = typeCondition;
        this.expandedSelectionSetsAtTypesCache = new Map();
    }
    setSelectionSet(selectionSet) {
        (0, utils_1.assert)(!this._selectionSet, 'Attempting to set the selection set of a fragment definition already built');
        (0, utils_1.assert)(selectionSet.parentType === this.typeCondition, `Fragment selection set parent is ${selectionSet.parentType} differs from the fragment condition type ${this.typeCondition}`);
        this._selectionSet = selectionSet;
        return this;
    }
    get selectionSet() {
        (0, utils_1.assert)(this._selectionSet, () => `Trying to access fragment definition ${this.name} before it is fully built`);
        return this._selectionSet;
    }
    withUpdatedSelectionSet(newSelectionSet) {
        return new NamedFragmentDefinition(this.schema(), this.name, this.typeCondition).setSelectionSet(newSelectionSet);
    }
    fragmentUsages() {
        if (!this._fragmentUsages) {
            this._fragmentUsages = new Map();
            this.selectionSet.collectUsedFragmentNames(this._fragmentUsages);
        }
        return this._fragmentUsages;
    }
    collectUsedFragmentNames(collector) {
        const usages = this.fragmentUsages();
        for (const [name, count] of usages.entries()) {
            const prevCount = collector.get(name);
            collector.set(name, prevCount ? prevCount + count : count);
        }
    }
    collectVariables(collector) {
        this.selectionSet.collectVariables(collector);
        this.collectVariablesInAppliedDirectives(collector);
    }
    toFragmentDefinitionNode() {
        return {
            kind: graphql_1.Kind.FRAGMENT_DEFINITION,
            name: {
                kind: graphql_1.Kind.NAME,
                value: this.name
            },
            typeCondition: {
                kind: graphql_1.Kind.NAMED_TYPE,
                name: {
                    kind: graphql_1.Kind.NAME,
                    value: this.typeCondition.name
                }
            },
            selectionSet: this.selectionSet.toSelectionSetNode()
        };
    }
    canApplyDirectlyAtType(type) {
        if ((0, types_1.sameType)(type, this.typeCondition)) {
            return true;
        }
        if (!(0, definitions_1.isAbstractType)(this.typeCondition)) {
            return false;
        }
        const conditionRuntimes = (0, definitions_1.possibleRuntimeTypes)(this.typeCondition);
        const typeRuntimes = (0, definitions_1.possibleRuntimeTypes)(type);
        if (conditionRuntimes.length < typeRuntimes.length
            || !typeRuntimes.every((t1) => conditionRuntimes.some((t2) => (0, types_1.sameType)(t1, t2)))) {
            return false;
        }
        return (0, definitions_1.isObjectType)(type) || (0, definitions_1.isUnionType)(this.typeCondition);
    }
    expandedSelectionSet() {
        if (!this._expandedSelectionSet) {
            this._expandedSelectionSet = this.selectionSet.expandFragments();
        }
        return this._expandedSelectionSet;
    }
    expandedSelectionSetAtType(type) {
        let cached = this.expandedSelectionSetsAtTypesCache.get(type.name);
        if (!cached) {
            cached = this.computeExpandedSelectionSetAtType(type);
            this.expandedSelectionSetsAtTypesCache.set(type.name, cached);
        }
        return cached;
    }
    computeExpandedSelectionSetAtType(type) {
        const expandedSelectionSet = this.expandedSelectionSet();
        const selectionSet = expandedSelectionSet.normalize({ parentType: type });
        if (!(0, definitions_1.isObjectType)(this.typeCondition)) {
            const validator = FieldsConflictValidator.build(expandedSelectionSet);
            return { selectionSet, validator };
        }
        const trimmed = expandedSelectionSet.minus(selectionSet);
        const validator = trimmed.isEmpty() ? undefined : FieldsConflictValidator.build(trimmed);
        return { selectionSet, validator };
    }
    includes(otherFragment) {
        if (this.name === otherFragment) {
            return false;
        }
        if (!this._includedFragmentNames) {
            this._includedFragmentNames = this.computeIncludedFragmentNames();
        }
        return this._includedFragmentNames.has(otherFragment);
    }
    computeIncludedFragmentNames() {
        const included = new Set();
        for (const selection of this.selectionSet.selections()) {
            if (selection instanceof FragmentSpreadSelection) {
                included.add(selection.namedFragment.name);
            }
        }
        return included;
    }
    toString(indent) {
        return `fragment ${this.name} on ${this.typeCondition}${this.appliedDirectivesToString()} ${this.selectionSet.toString(false, true, indent)}`;
    }
}
exports.NamedFragmentDefinition = NamedFragmentDefinition;
class NamedFragments {
    constructor() {
        this.fragments = new utils_1.MapWithCachedArrays();
    }
    isEmpty() {
        return this.size === 0;
    }
    get size() {
        return this.fragments.size;
    }
    names() {
        return this.fragments.keys();
    }
    add(fragment) {
        if (this.fragments.has(fragment.name)) {
            throw error_1.ERRORS.INVALID_GRAPHQL.err(`Duplicate fragment name '${fragment}'`);
        }
        this.fragments.set(fragment.name, fragment);
    }
    addIfNotExist(fragment) {
        if (!this.fragments.has(fragment.name)) {
            this.fragments.set(fragment.name, fragment);
        }
    }
    maybeApplyingDirectlyAtType(type) {
        return this.fragments.values().filter(f => f.canApplyDirectlyAtType(type));
    }
    get(name) {
        return this.fragments.get(name);
    }
    has(name) {
        return this.fragments.has(name);
    }
    definitions() {
        return this.fragments.values();
    }
    collectUsedFragmentNames(collector) {
        for (const fragment of this.definitions()) {
            fragment.collectUsedFragmentNames(collector);
        }
    }
    map(mapper) {
        const mapped = new NamedFragments();
        for (const def of this.fragments.values()) {
            mapped.fragments.set(def.name, mapper(def));
        }
        return mapped;
    }
    mapInDependencyOrder(mapper) {
        const fragmentsMap = new Map();
        for (const fragment of this.definitions()) {
            fragmentsMap.set(fragment.name, {
                fragment,
                dependsOn: Array.from(fragment.fragmentUsages().keys()),
            });
        }
        const removedFragments = new Set();
        const mappedFragments = new NamedFragments();
        while (fragmentsMap.size > 0) {
            for (const [name, info] of fragmentsMap) {
                if (info.dependsOn.every((n) => mappedFragments.has(n) || removedFragments.has(n))) {
                    const mapped = mapper(info.fragment, mappedFragments);
                    fragmentsMap.delete(name);
                    if (!mapped) {
                        removedFragments.add(name);
                    }
                    else {
                        mappedFragments.add(mapped);
                    }
                    break;
                }
            }
        }
        return mappedFragments.isEmpty() ? undefined : mappedFragments;
    }
    mapToExpandedSelectionSets(mapper) {
        return this.mapInDependencyOrder((fragment, newFragments) => {
            const mappedSelectionSet = mapper(fragment.selectionSet.expandFragments().normalize({ parentType: fragment.typeCondition }));
            if (!mappedSelectionSet) {
                return undefined;
            }
            const reoptimizedSelectionSet = mappedSelectionSet.optimize(newFragments);
            return fragment.withUpdatedSelectionSet(reoptimizedSelectionSet);
        });
    }
    rebaseOn(schema) {
        return this.mapInDependencyOrder((fragment, newFragments) => {
            const rebasedType = schema.type(fragment.selectionSet.parentType.name);
            if (!rebasedType || !(0, definitions_1.isCompositeType)(rebasedType)) {
                return undefined;
            }
            let rebasedSelection = fragment.selectionSet.rebaseOn({ parentType: rebasedType, fragments: newFragments, errorIfCannotRebase: false });
            rebasedSelection = rebasedSelection.normalize({ parentType: rebasedType });
            return rebasedSelection.isWorthUsing()
                ? new NamedFragmentDefinition(schema, fragment.name, rebasedType).setSelectionSet(rebasedSelection)
                : undefined;
        });
    }
    filter(predicate) {
        return this.mapInDependencyOrder((fragment, newFragments) => {
            if (predicate(fragment)) {
                const updatedSelectionSet = fragment.selectionSet.expandFragments(newFragments);
                return updatedSelectionSet === fragment.selectionSet
                    ? fragment
                    : fragment.withUpdatedSelectionSet(updatedSelectionSet.normalize({ parentType: updatedSelectionSet.parentType }));
            }
            else {
                return undefined;
            }
        });
    }
    validate(variableDefinitions) {
        for (const fragment of this.fragments.values()) {
            fragment.selectionSet.validate(variableDefinitions);
        }
    }
    toFragmentDefinitionNodes() {
        return this.definitions().map(f => f.toFragmentDefinitionNode());
    }
    toString(indent) {
        return this.definitions().map(f => f.toString(indent)).join('\n\n');
    }
}
exports.NamedFragments = NamedFragments;
class DeferNormalizer {
    constructor() {
        this.index = 0;
        this.assignedLabels = new Set();
        this.deferConditions = new utils_1.SetMultiMap();
        this.usedLabels = new Set();
    }
    init(selectionSet) {
        let hasNonLabelledOrConditionalDefers = false;
        let hasDefers = false;
        const stack = selectionSet.selections().concat();
        while (stack.length > 0) {
            const selection = stack.pop();
            if (selection.kind === 'FragmentSelection') {
                const deferArgs = selection.element.deferDirectiveArgs();
                if (deferArgs) {
                    hasDefers = true;
                    if (!deferArgs.label || deferArgs.if !== undefined) {
                        hasNonLabelledOrConditionalDefers = true;
                    }
                    if (deferArgs.label) {
                        this.usedLabels.add(deferArgs.label);
                    }
                }
            }
            if (selection.selectionSet) {
                selection.selectionSet.selections().forEach((s) => stack.push(s));
            }
        }
        return { hasDefers, hasNonLabelledOrConditionalDefers };
    }
    nextLabel() {
        return `qp__${this.index++}`;
    }
    newLabel() {
        let candidate = this.nextLabel();
        while (this.usedLabels.has(candidate)) {
            candidate = this.nextLabel();
        }
        this.assignedLabels.add(candidate);
        return candidate;
    }
    registerCondition(label, condition) {
        this.deferConditions.add(condition.name, label);
    }
}
var ContainsResult;
(function (ContainsResult) {
    ContainsResult[ContainsResult["NOT_CONTAINED"] = 0] = "NOT_CONTAINED";
    ContainsResult[ContainsResult["STRICTLY_CONTAINED"] = 1] = "STRICTLY_CONTAINED";
    ContainsResult[ContainsResult["EQUAL"] = 2] = "EQUAL";
})(ContainsResult || (exports.ContainsResult = ContainsResult = {}));
class SelectionSet {
    constructor(parentType, keyedSelections = new Map()) {
        this.parentType = parentType;
        this._keyedSelections = keyedSelections;
        this._selections = (0, utils_1.mapValues)(keyedSelections);
    }
    minimizeSelectionSet(namedFragments = new NamedFragments(), seenSelections = new Map()) {
        const minimizedSelectionSet = this.lazyMap((selection) => {
            var _a;
            if (selection.kind === 'FragmentSelection' && selection.element.typeCondition && selection.element.appliedDirectives.length === 0
                && selection.selectionSet && selection.selectionSet.isWorthUsing()) {
                const mockHashCode = `on${selection.element.typeCondition}` + selection.selectionSet.selections().length;
                const equivalentSelectionSetCandidates = seenSelections.get(mockHashCode);
                if (equivalentSelectionSetCandidates) {
                    const match = equivalentSelectionSetCandidates.find(([candidateSet]) => candidateSet.equals(selection.selectionSet));
                    if (match) {
                        return new FragmentSpreadSelection(this.parentType, namedFragments, match[1], []);
                    }
                }
                const [minimizedSelectionSet] = selection.selectionSet.minimizeSelectionSet(namedFragments, seenSelections);
                const updatedEquivalentSelectionSetCandidates = seenSelections.get(mockHashCode);
                const fragmentDefinition = new NamedFragmentDefinition(this.parentType.schema(), `_generated_${mockHashCode}_${(_a = updatedEquivalentSelectionSetCandidates === null || updatedEquivalentSelectionSetCandidates === void 0 ? void 0 : updatedEquivalentSelectionSetCandidates.length) !== null && _a !== void 0 ? _a : 0}`, selection.element.typeCondition).setSelectionSet(minimizedSelectionSet);
                namedFragments.add(fragmentDefinition);
                if (updatedEquivalentSelectionSetCandidates) {
                    updatedEquivalentSelectionSetCandidates.push([selection.selectionSet, fragmentDefinition]);
                }
                else {
                    seenSelections.set(mockHashCode, [[selection.selectionSet, fragmentDefinition]]);
                }
                return new FragmentSpreadSelection(this.parentType, namedFragments, fragmentDefinition, []);
            }
            if (selection.selectionSet) {
                selection = selection.withUpdatedSelectionSet(selection.selectionSet.minimizeSelectionSet(namedFragments, seenSelections)[0]);
            }
            return selection;
        });
        return [minimizedSelectionSet, namedFragments];
    }
    selectionsInReverseOrder() {
        const length = this._selections.length;
        const reversed = new Array(length);
        for (let i = 0; i < length; i++) {
            reversed[i] = this._selections[length - i - 1];
        }
        return reversed;
    }
    selections() {
        return this._selections;
    }
    hasTopLevelTypenameField() {
        return this._keyedSelections.has(definitions_1.typenameFieldName);
    }
    withoutTopLevelTypenameField() {
        if (!this.hasTopLevelTypenameField) {
            return this;
        }
        const newKeyedSelections = new Map();
        for (const [key, selection] of this._keyedSelections) {
            if (key !== definitions_1.typenameFieldName) {
                newKeyedSelections.set(key, selection);
            }
        }
        return new SelectionSet(this.parentType, newKeyedSelections);
    }
    fieldsInSet() {
        const fields = new Array();
        for (const selection of this.selections()) {
            if (selection.kind === 'FieldSelection') {
                fields.push({ path: [], field: selection });
            }
            else {
                const condition = selection.element.typeCondition;
                const header = condition ? [`... on ${condition}`] : [];
                for (const { path, field } of selection.selectionSet.fieldsInSet()) {
                    fields.push({ path: header.concat(path), field });
                }
            }
        }
        return fields;
    }
    fieldsByResponseName() {
        const byResponseName = new utils_1.MultiMap();
        this.collectFieldsByResponseName(byResponseName);
        return byResponseName;
    }
    collectFieldsByResponseName(collector) {
        for (const selection of this.selections()) {
            if (selection.kind === 'FieldSelection') {
                collector.add(selection.element.responseName(), selection);
            }
            else {
                selection.selectionSet.collectFieldsByResponseName(collector);
            }
        }
    }
    usedVariables() {
        const collector = new definitions_1.VariableCollector();
        this.collectVariables(collector);
        return collector.variables();
    }
    collectVariables(collector) {
        for (const selection of this.selections()) {
            selection.collectVariables(collector);
        }
    }
    collectUsedFragmentNames(collector) {
        for (const selection of this.selections()) {
            selection.collectUsedFragmentNames(collector);
        }
    }
    optimize(fragments) {
        if (!fragments || fragments.isEmpty()) {
            return this;
        }
        const wrapped = new InlineFragmentSelection(new FragmentElement(this.parentType, this.parentType), this);
        const validator = FieldsConflictMultiBranchValidator.ofInitial(FieldsConflictValidator.build(this));
        const optimized = wrapped.optimize(fragments, validator);
        return optimized instanceof FragmentSpreadSelection
            ? selectionSetOf(this.parentType, optimized)
            : optimized.selectionSet;
    }
    optimizeSelections(fragments, validator) {
        return this.lazyMap((selection) => selection.optimize(fragments, validator));
    }
    expandFragments(updatedFragments) {
        return this.lazyMap((selection) => selection.expandFragments(updatedFragments));
    }
    normalize({ parentType, recursive }) {
        return this.lazyMap((selection) => selection.normalize({ parentType, recursive }), { parentType });
    }
    lazyMap(mapper, options) {
        var _a;
        const selections = this.selections();
        let updatedSelections = undefined;
        for (let i = 0; i < selections.length; i++) {
            const selection = selections[i];
            const updated = mapper(selection);
            if (updated !== selection && !updatedSelections) {
                updatedSelections = new SelectionSetUpdates();
                for (let j = 0; j < i; j++) {
                    updatedSelections.add(selections[j]);
                }
            }
            if (!!updated && updatedSelections) {
                updatedSelections.add(updated);
            }
        }
        if (!updatedSelections) {
            return this;
        }
        return updatedSelections.toSelectionSet((_a = options === null || options === void 0 ? void 0 : options.parentType) !== null && _a !== void 0 ? _a : this.parentType);
    }
    withoutDefer(labelsToRemove) {
        return this.lazyMap((selection) => selection.withoutDefer(labelsToRemove));
    }
    withNormalizedDefer(normalizer) {
        return this.lazyMap((selection) => selection.withNormalizedDefer(normalizer));
    }
    hasDefer() {
        return this.selections().some((s) => s.hasDefer());
    }
    filter(predicate) {
        return this.lazyMap((selection) => predicate(selection) ? selection : undefined);
    }
    filterRecursiveDepthFirst(predicate) {
        return this.lazyMap((selection) => selection.filterRecursiveDepthFirst(predicate));
    }
    withoutEmptyBranches() {
        const updated = this.filterRecursiveDepthFirst((selection) => { var _a; return ((_a = selection.selectionSet) === null || _a === void 0 ? void 0 : _a.isEmpty()) !== true; });
        return updated.isEmpty() ? undefined : updated;
    }
    rebaseOn({ parentType, fragments, errorIfCannotRebase, }) {
        if (this.parentType === parentType) {
            return this;
        }
        const newSelections = new Map();
        for (const selection of this.selections()) {
            const rebasedSelection = selection.rebaseOn({ parentType, fragments, errorIfCannotRebase });
            if (rebasedSelection) {
                newSelections.set(selection.key(), rebasedSelection);
            }
        }
        return new SelectionSet(parentType, newSelections);
    }
    equals(that) {
        if (this === that) {
            return true;
        }
        if (this._selections.length !== that._selections.length) {
            return false;
        }
        for (const [key, thisSelection] of this._keyedSelections) {
            const thatSelection = that._keyedSelections.get(key);
            if (!thatSelection || !thisSelection.equals(thatSelection)) {
                return false;
            }
        }
        return true;
    }
    contains(that, options) {
        var _a;
        const ignoreMissingTypename = (_a = options === null || options === void 0 ? void 0 : options.ignoreMissingTypename) !== null && _a !== void 0 ? _a : false;
        if (that._selections.length > this._selections.length) {
            if (!ignoreMissingTypename || that._selections.length > this._selections.length + 1 || this.hasTopLevelTypenameField() || !that.hasTopLevelTypenameField()) {
                return ContainsResult.NOT_CONTAINED;
            }
        }
        let isEqual = true;
        let didIgnoreTypename = false;
        for (const [key, thatSelection] of that._keyedSelections) {
            if (key === definitions_1.typenameFieldName && ignoreMissingTypename) {
                if (!this._keyedSelections.has(definitions_1.typenameFieldName)) {
                    didIgnoreTypename = true;
                }
                continue;
            }
            const thisSelection = this._keyedSelections.get(key);
            const selectionResult = thisSelection === null || thisSelection === void 0 ? void 0 : thisSelection.contains(thatSelection, options);
            if (selectionResult === undefined || selectionResult === ContainsResult.NOT_CONTAINED) {
                return ContainsResult.NOT_CONTAINED;
            }
            isEqual && (isEqual = selectionResult === ContainsResult.EQUAL);
        }
        return isEqual && that._selections.length === (this._selections.length + (didIgnoreTypename ? 1 : 0))
            ? ContainsResult.EQUAL
            : ContainsResult.STRICTLY_CONTAINED;
    }
    containsTopLevelField(field) {
        const selection = this._keyedSelections.get(field.key());
        return !!selection && selection.element.equals(field);
    }
    minus(that) {
        const updated = new SelectionSetUpdates();
        for (const [key, thisSelection] of this._keyedSelections) {
            const thatSelection = that._keyedSelections.get(key);
            if (thatSelection) {
                const remainder = thisSelection.minus(thatSelection);
                if (remainder) {
                    updated.add(remainder);
                }
            }
            else {
                updated.add(thisSelection);
            }
        }
        return updated.toSelectionSet(this.parentType);
    }
    intersectionWith(that) {
        if (this.isEmpty()) {
            return this;
        }
        if (that.isEmpty()) {
            return that;
        }
        const intersection = new SelectionSetUpdates();
        for (const [key, thisSelection] of this._keyedSelections) {
            const thatSelection = that._keyedSelections.get(key);
            if (thatSelection) {
                const selection = thisSelection.intersectionWith(thatSelection);
                if (selection) {
                    intersection.add(selection);
                }
            }
        }
        return intersection.toSelectionSet(this.parentType);
    }
    canRebaseOn(parentTypeToTest) {
        return this.selections().every((selection) => selection.canAddTo(parentTypeToTest));
    }
    validate(variableDefinitions, validateContextualArgs = false) {
        validate(!this.isEmpty(), () => `Invalid empty selection set`);
        for (const selection of this.selections()) {
            selection.validate(variableDefinitions, validateContextualArgs);
        }
    }
    isEmpty() {
        return this._selections.length === 0;
    }
    toSelectionSetNode() {
        if (this.isEmpty()) {
            return {
                kind: graphql_1.Kind.SELECTION_SET,
                selections: [{
                        kind: graphql_1.Kind.FIELD,
                        name: {
                            kind: graphql_1.Kind.NAME,
                            value: '...',
                        },
                    }]
            };
        }
        return {
            kind: graphql_1.Kind.SELECTION_SET,
            selections: Array.from(this.selectionsInPrintOrder(), s => s.toSelectionNode())
        };
    }
    selectionsInPrintOrder() {
        const isPlainTypenameSelection = (s) => s.kind === 'FieldSelection' && s.isPlainTypenameField();
        const typenameSelection = this._selections.find((s) => isPlainTypenameSelection(s));
        if (typenameSelection) {
            return [typenameSelection].concat(this.selections().filter(s => !isPlainTypenameSelection(s)));
        }
        else {
            return this._selections;
        }
    }
    toOperationPaths() {
        return this.toOperationPathsInternal([]);
    }
    toOperationPathsInternal(parentPaths) {
        return this.selections().flatMap((selection) => {
            const updatedPaths = parentPaths.map(path => path.concat(selection.element));
            return selection.selectionSet
                ? selection.selectionSet.toOperationPathsInternal(updatedPaths)
                : updatedPaths;
        });
    }
    forEachElement(callback) {
        var _a;
        const stack = this.selectionsInReverseOrder().concat();
        while (stack.length > 0) {
            const selection = stack.pop();
            callback(selection.element);
            (_a = selection.selectionSet) === null || _a === void 0 ? void 0 : _a.selectionsInReverseOrder().forEach((s) => stack.push(s));
        }
    }
    some(predicate) {
        for (const selection of this.selections()) {
            if (predicate(selection.element) || (selection.selectionSet && selection.selectionSet.some(predicate))) {
                return true;
            }
        }
        return false;
    }
    toOperationString(rootKind, variableDefinitions, fragments, operationName, directives, expandFragments = false, prettyPrint = true) {
        const indent = prettyPrint ? '' : undefined;
        const fragmentsDefinitions = !expandFragments && fragments && !fragments.isEmpty()
            ? fragments.toString(indent) + "\n\n"
            : "";
        if (rootKind == "query" && !operationName && variableDefinitions.isEmpty()) {
            return fragmentsDefinitions + this.toString(expandFragments, true, indent);
        }
        const nameAndVariables = operationName
            ? " " + (operationName + (variableDefinitions.isEmpty() ? "" : variableDefinitions.toString()))
            : (variableDefinitions.isEmpty() ? "" : " " + variableDefinitions.toString());
        const directives_str = (0, definitions_1.directivesToString)(directives);
        return fragmentsDefinitions + rootKind + nameAndVariables + directives_str + " " + this.toString(expandFragments, true, indent);
    }
    toString(expandFragments = true, includeExternalBrackets = true, indent) {
        if (this.isEmpty()) {
            return '{}';
        }
        if (indent === undefined) {
            const selectionsToString = this.selections().map(s => s.toString(expandFragments)).join(' ');
            return includeExternalBrackets ? '{ ' + selectionsToString + ' }' : selectionsToString;
        }
        else {
            const selectionIndent = includeExternalBrackets ? indent + "  " : indent;
            const selectionsToString = this.selections().map(s => s.toString(expandFragments, selectionIndent)).join('\n');
            return includeExternalBrackets
                ? '{\n' + selectionsToString + '\n' + indent + '}'
                : selectionsToString;
        }
    }
    isWorthUsing() {
        const selections = this.selections();
        if (selections.length === 0) {
            return false;
        }
        if (selections.length === 1) {
            const s = selections[0];
            return !(s.kind === 'FieldSelection' && s.element.isLeafField());
        }
        return true;
    }
}
exports.SelectionSet = SelectionSet;
class SelectionSetUpdates {
    constructor() {
        this.keyedUpdates = new utils_1.MultiMap;
    }
    isEmpty() {
        return this.keyedUpdates.size === 0;
    }
    add(selections) {
        addToKeyedUpdates(this.keyedUpdates, selections);
        return this;
    }
    addAtPath(path, selections) {
        if (path.length === 0) {
            if (selections) {
                addToKeyedUpdates(this.keyedUpdates, selections);
            }
        }
        else {
            if (path.length === 1 && !selections) {
                const element = path[0];
                if (element.kind === 'Field' && element.isLeafField()) {
                    const selection = selectionOfElement(element);
                    this.keyedUpdates.add(selection.key(), selection);
                    return this;
                }
            }
            this.keyedUpdates.add(path[0].key(), { path, selections });
        }
        return this;
    }
    clone() {
        const cloned = new SelectionSetUpdates();
        for (const [key, values] of this.keyedUpdates.entries()) {
            cloned.keyedUpdates.set(key, Array.from(values));
        }
        return cloned;
    }
    clear() {
        this.keyedUpdates.clear();
    }
    toSelectionSet(parentType, fragments) {
        return makeSelectionSet(parentType, this.keyedUpdates, fragments);
    }
    toString() {
        return '{\n'
            + [...this.keyedUpdates.entries()].map(([k, updates]) => {
                const updStr = updates.map((upd) => upd instanceof AbstractSelection
                    ? upd.toString()
                    : `${upd.path} -> ${upd.selections}`);
                return ` - ${k}: ${updStr}`;
            }).join('\n')
            + '\n\}';
    }
}
exports.SelectionSetUpdates = SelectionSetUpdates;
function addToKeyedUpdates(keyedUpdates, selections) {
    if (selections instanceof AbstractSelection) {
        addOneToKeyedUpdates(keyedUpdates, selections);
    }
    else {
        const toAdd = selections instanceof SelectionSet ? selections.selections() : selections;
        for (const selection of toAdd) {
            addOneToKeyedUpdates(keyedUpdates, selection);
        }
    }
}
function addOneToKeyedUpdates(keyedUpdates, selection) {
    if (selection instanceof FragmentSpreadSelection) {
        keyedUpdates.set(selection.key(), [selection]);
    }
    else {
        keyedUpdates.add(selection.key(), selection);
    }
}
function maybeRebaseOnSchema(toRebase, schema) {
    if (toRebase.schema() === schema) {
        return toRebase;
    }
    const rebased = schema.type(toRebase.name);
    (0, utils_1.assert)(rebased && (0, definitions_1.isCompositeType)(rebased), () => `Expected ${toRebase} to exists and be composite in the rebased schema, but got ${rebased === null || rebased === void 0 ? void 0 : rebased.kind}`);
    return rebased;
}
function isUnecessaryFragment(parentType, fragment) {
    return fragment.element.appliedDirectives.length === 0
        && (!fragment.element.typeCondition || (0, types_1.isSubtype)(maybeRebaseOnSchema(fragment.element.typeCondition, parentType.schema()), parentType));
}
function withUnecessaryFragmentsRemoved(parentType, selections) {
    if (selections instanceof AbstractSelection) {
        if (selections.kind !== 'FragmentSelection' || !isUnecessaryFragment(parentType, selections)) {
            return selections;
        }
        return withUnecessaryFragmentsRemoved(parentType, selections.selectionSet);
    }
    const toCheck = selections instanceof SelectionSet ? selections.selections() : selections;
    const filtered = [];
    for (const selection of toCheck) {
        if (selection.kind === 'FragmentSelection' && isUnecessaryFragment(parentType, selection)) {
            const subSelections = withUnecessaryFragmentsRemoved(parentType, selection.selectionSet);
            if (subSelections instanceof AbstractSelection) {
                filtered.push(subSelections);
            }
            else {
                for (const subSelection of subSelections) {
                    filtered.push(subSelection);
                }
            }
        }
        else {
            filtered.push(selection);
        }
    }
    return filtered;
}
function makeSelection(parentType, updates, fragments) {
    (0, utils_1.assert)(updates.length > 0, 'Should not be called without any updates');
    const first = updates[0];
    if (updates.length === 1 && first instanceof AbstractSelection) {
        return first.rebaseOnOrError({ parentType, fragments });
    }
    const element = updateElement(first).rebaseOnOrError(parentType);
    const subSelectionParentType = element.kind === 'Field' ? element.baseType() : element.castedType();
    if (!(0, definitions_1.isCompositeType)(subSelectionParentType)) {
        return selectionOfElement(element);
    }
    const subSelectionKeyedUpdates = new utils_1.MultiMap();
    for (const update of updates) {
        if (update instanceof AbstractSelection) {
            if (update.selectionSet) {
                addToKeyedUpdates(subSelectionKeyedUpdates, update.selectionSet);
            }
        }
        else {
            addSubpathToKeyUpdates(subSelectionKeyedUpdates, subSelectionParentType, update);
        }
    }
    return selectionOfElement(element, makeSelectionSet(subSelectionParentType, subSelectionKeyedUpdates, fragments));
}
function updateElement(update) {
    return update instanceof AbstractSelection ? update.element : update.path[0];
}
function addSubpathToKeyUpdates(keyedUpdates, subSelectionParentType, pathUpdate) {
    if (pathUpdate.path.length === 1) {
        if (!pathUpdate.selections) {
            return;
        }
        addToKeyedUpdates(keyedUpdates, withUnecessaryFragmentsRemoved(subSelectionParentType, pathUpdate.selections));
    }
    else {
        keyedUpdates.add(pathUpdate.path[1].key(), { path: pathUpdate.path.slice(1), selections: pathUpdate.selections });
    }
}
function makeSelectionSet(parentType, keyedUpdates, fragments) {
    const selections = new Map();
    for (const [key, updates] of keyedUpdates.entries()) {
        selections.set(key, makeSelection(parentType, updates, fragments));
    }
    return new SelectionSet(parentType, selections);
}
class MutableSelectionSet {
    constructor(parentType, _updates, memoizer) {
        this.parentType = parentType;
        this._updates = _updates;
        this.memoizer = memoizer;
    }
    static empty(parentType) {
        return this.emptyWithMemoized(parentType, () => ({}));
    }
    static emptyWithMemoized(parentType, memoizer) {
        return new MutableSelectionSet(parentType, new SelectionSetUpdates(), memoizer);
    }
    static of(selectionSet) {
        return this.ofWithMemoized(selectionSet, () => ({}));
    }
    static ofWithMemoized(selectionSet, memoizer) {
        const s = new MutableSelectionSet(selectionSet.parentType, new SelectionSetUpdates(), memoizer);
        s._updates.add(selectionSet);
        s.computed = selectionSet;
        return s;
    }
    isEmpty() {
        return this._updates.isEmpty();
    }
    get() {
        if (!this.computed) {
            this.computed = this._updates.toSelectionSet(this.parentType);
            this._updates.clear();
            this._updates.add(this.computed);
        }
        return this.computed;
    }
    updates() {
        this.computed = undefined;
        this._memoized = undefined;
        return this._updates;
    }
    clone() {
        const cloned = new MutableSelectionSet(this.parentType, this._updates.clone(), this.memoizer);
        cloned.computed = this.computed;
        cloned._memoized = this._memoized;
        return cloned;
    }
    rebaseOn(parentType) {
        const rebased = new MutableSelectionSet(parentType, new SelectionSetUpdates(), this.memoizer);
        rebased._updates.add(this.get());
        return rebased;
    }
    memoized() {
        if (!this._memoized) {
            this._memoized = this.memoizer(this.get());
        }
        return this._memoized;
    }
    toString() {
        return this.get().toString();
    }
}
exports.MutableSelectionSet = MutableSelectionSet;
function allFieldDefinitionsInSelectionSet(selection) {
    const stack = Array.from(selection.selections());
    const allFields = [];
    while (stack.length > 0) {
        const selection = stack.pop();
        if (selection.kind === 'FieldSelection') {
            allFields.push(selection.element.definition);
        }
        if (selection.selectionSet) {
            stack.push(...selection.selectionSet.selections());
        }
    }
    return allFields;
}
exports.allFieldDefinitionsInSelectionSet = allFieldDefinitionsInSelectionSet;
function selectionSetOf(parentType, selection) {
    const map = new Map();
    map.set(selection.key(), selection);
    return new SelectionSet(parentType, map);
}
exports.selectionSetOf = selectionSetOf;
function selectionSetOfElement(element, subSelection) {
    return selectionSetOf(element.parentType, selectionOfElement(element, subSelection));
}
exports.selectionSetOfElement = selectionSetOfElement;
function selectionOfElement(element, subSelection) {
    return element.kind === 'Field' ? new FieldSelection(element, subSelection) : new InlineFragmentSelection(element, subSelection);
}
exports.selectionOfElement = selectionOfElement;
class AbstractSelection {
    constructor(element) {
        this.element = element;
    }
    rebaseOnOrError({ parentType, fragments }) {
        return this.rebaseOn({ parentType, fragments, errorIfCannotRebase: true });
    }
    get parentType() {
        return this.element.parentType;
    }
    isTypenameField() {
        return false;
    }
    collectVariables(collector) {
        var _a;
        this.element.collectVariables(collector);
        (_a = this.selectionSet) === null || _a === void 0 ? void 0 : _a.collectVariables(collector);
    }
    collectUsedFragmentNames(collector) {
        var _a;
        (_a = this.selectionSet) === null || _a === void 0 ? void 0 : _a.collectUsedFragmentNames(collector);
    }
    withUpdatedSelectionSet(selectionSet) {
        return this.withUpdatedComponents(this.element, selectionSet);
    }
    withUpdatedElement(element) {
        return this.withUpdatedComponents(element, this.selectionSet);
    }
    mapToSelectionSet(mapper) {
        if (!this.selectionSet) {
            return this.us();
        }
        const updatedSelectionSet = mapper(this.selectionSet);
        return updatedSelectionSet === this.selectionSet
            ? this.us()
            : this.withUpdatedSelectionSet(updatedSelectionSet);
    }
    isFragmentSpread() {
        return false;
    }
    minus(that) {
        if (this.selectionSet && that.selectionSet) {
            const updatedSubSelectionSet = this.selectionSet.minus(that.selectionSet);
            if (!updatedSubSelectionSet.isEmpty()) {
                return this.withUpdatedSelectionSet(updatedSubSelectionSet);
            }
        }
        return undefined;
    }
    intersectionWith(that) {
        if (this.selectionSet && that.selectionSet) {
            const subSelectionSetIntersection = this.selectionSet.intersectionWith(that.selectionSet);
            if (subSelectionSetIntersection.isEmpty()) {
                return undefined;
            }
            else {
                return this.withUpdatedSelectionSet(subSelectionSetIntersection);
            }
        }
        else {
            return this.us();
        }
    }
    tryOptimizeSubselectionWithFragments({ parentType, subSelection, fragments, validator, canUseFullMatchingFragment, }) {
        const candidates = fragments.maybeApplyingDirectlyAtType(parentType);
        if (candidates.length === 0) {
            return subSelection;
        }
        const applyingFragments = [];
        for (const candidate of candidates) {
            const atType = candidate.expandedSelectionSetAtType(parentType);
            if (atType.selectionSet.isEmpty() || (atType.selectionSet.selections().length === 1 && atType.selectionSet.selections()[0].isTypenameField())) {
                continue;
            }
            const res = subSelection.contains(atType.selectionSet, { ignoreMissingTypename: true });
            if (res === ContainsResult.EQUAL) {
                if (canUseFullMatchingFragment(candidate)) {
                    if (!validator.checkCanReuseFragmentAndTrackIt(atType)) {
                        continue;
                    }
                    return candidate;
                }
                if (candidate.appliedDirectives.length === 0) {
                    applyingFragments.push({ fragment: candidate, atType });
                }
            }
            else if (res === ContainsResult.STRICTLY_CONTAINED && candidate.appliedDirectives.length === 0) {
                applyingFragments.push({ fragment: candidate, atType });
            }
        }
        if (applyingFragments.length === 0) {
            return subSelection;
        }
        const filteredApplyingFragments = applyingFragments.filter(({ fragment }) => !applyingFragments.some((o) => o.fragment.includes(fragment.name)));
        let notCoveredByFragments = subSelection;
        const optimized = new SelectionSetUpdates();
        for (const { fragment, atType } of filteredApplyingFragments) {
            if (!validator.checkCanReuseFragmentAndTrackIt(atType)) {
                continue;
            }
            const notCovered = subSelection.minus(atType.selectionSet);
            notCoveredByFragments = notCoveredByFragments.intersectionWith(notCovered);
            optimized.add(new FragmentSpreadSelection(parentType, fragments, fragment, []));
        }
        return optimized.add(notCoveredByFragments).toSelectionSet(parentType, fragments);
    }
}
class FieldsConflictMultiBranchValidator {
    constructor(validators) {
        this.validators = validators;
    }
    static ofInitial(validator) {
        return new FieldsConflictMultiBranchValidator([validator]);
    }
    forField(field) {
        const forAllBranches = this.validators.flatMap((vs) => vs.forField(field));
        (0, utils_1.assert)(forAllBranches.length > 0, `Shoud have found at least one validator for ${field}`);
        return new FieldsConflictMultiBranchValidator(forAllBranches);
    }
    checkCanReuseFragmentAndTrackIt(fragment) {
        const validator = fragment.validator;
        if (!validator) {
            return true;
        }
        if (!this.validators.every((v) => v.doMergeWith(validator))) {
            return false;
        }
        if (this.usedSpreadTrimmedPartAtLevel) {
            if (!this.usedSpreadTrimmedPartAtLevel.every((t) => validator.doMergeWith(t))) {
                return false;
            }
        }
        else {
            this.usedSpreadTrimmedPartAtLevel = [];
        }
        this.usedSpreadTrimmedPartAtLevel.push(validator);
        return true;
    }
}
class FieldsConflictValidator {
    constructor(byResponseName) {
        this.byResponseName = byResponseName;
    }
    static build(s) {
        return FieldsConflictValidator.forLevel(s.fieldsInSet());
    }
    static forLevel(level) {
        var _a;
        const atLevel = new Map();
        for (const { field } of level) {
            const responseName = field.element.responseName();
            let atResponseName = atLevel.get(responseName);
            if (!atResponseName) {
                atResponseName = new Map();
                atLevel.set(responseName, atResponseName);
            }
            if (field.selectionSet) {
                const forField = (_a = atResponseName.get(field.element)) !== null && _a !== void 0 ? _a : [];
                atResponseName.set(field.element, forField.concat(field.selectionSet.fieldsInSet()));
            }
            else {
                atResponseName.set(field.element, null);
            }
        }
        const byResponseName = new Map();
        for (const [name, level] of atLevel.entries()) {
            const atResponseName = new Map();
            for (const [field, collectedFields] of level) {
                const validator = collectedFields ? FieldsConflictValidator.forLevel(collectedFields) : null;
                atResponseName.set(field, validator);
            }
            byResponseName.set(name, atResponseName);
        }
        return new FieldsConflictValidator(byResponseName);
    }
    forField(field) {
        const byResponseName = this.byResponseName.get(field.responseName());
        if (!byResponseName) {
            return [];
        }
        return (0, utils_1.mapValues)(byResponseName).filter((v) => !!v);
    }
    doMergeWith(that) {
        var _a, _b;
        for (const [responseName, thisFields] of this.byResponseName.entries()) {
            const thatFields = that.byResponseName.get(responseName);
            if (!thatFields) {
                continue;
            }
            for (const [thisField, thisValidator] of thisFields.entries()) {
                for (const [thatField, thatValidator] of thatFields.entries()) {
                    if (!(0, types_1.typesCanBeMerged)(thisField.definition.type, thatField.definition.type)) {
                        return false;
                    }
                    const p1 = thisField.parentType;
                    const p2 = thatField.parentType;
                    if ((0, types_1.sameType)(p1, p2) || !(0, definitions_1.isObjectType)(p1) || !(0, definitions_1.isObjectType)(p2)) {
                        if (thisField.name !== thatField.name
                            || !(0, values_1.argumentsEquals)((_a = thisField.args) !== null && _a !== void 0 ? _a : {}, (_b = thatField.args) !== null && _b !== void 0 ? _b : {})
                            || (thisValidator && thatValidator && !thisValidator.doMergeWith(thatValidator))) {
                            return false;
                        }
                    }
                    else {
                        if (thisValidator && thatValidator && !thisValidator.hasSameResponseShapeThan(thatValidator)) {
                            return false;
                        }
                    }
                }
            }
        }
        return true;
    }
    hasSameResponseShapeThan(that) {
        for (const [responseName, thisFields] of this.byResponseName.entries()) {
            const thatFields = that.byResponseName.get(responseName);
            if (!thatFields) {
                continue;
            }
            for (const [thisField, thisValidator] of thisFields.entries()) {
                for (const [thatField, thatValidator] of thatFields.entries()) {
                    if (!(0, types_1.typesCanBeMerged)(thisField.definition.type, thatField.definition.type)
                        || (thisValidator && thatValidator && !thisValidator.hasSameResponseShapeThan(thatValidator))) {
                        return false;
                    }
                }
            }
        }
        return true;
    }
    toString(indent = '') {
        return '{\n'
            + [...this.byResponseName.entries()].map(([name, byFields]) => {
                const innerIndent = indent + '  ';
                return `${innerIndent}${name}: [\n`
                    + [...byFields.entries()]
                        .map(([field, next]) => `${innerIndent}  ${field.parentType}.${field}${next ? next.toString(innerIndent + '  ') : ''}`)
                        .join('\n')
                    + `\n${innerIndent}]`;
            }).join('\n')
            + `\n${indent}}`;
    }
}
class FieldSelection extends AbstractSelection {
    constructor(field, _selectionSet) {
        super(field);
        this._selectionSet = _selectionSet;
        this.kind = 'FieldSelection';
    }
    get selectionSet() {
        return this._selectionSet;
    }
    us() {
        return this;
    }
    isTypenameField() {
        return this.element.definition.name === definitions_1.typenameFieldName;
    }
    isPlainTypenameField() {
        return this.element.definition.name === definitions_1.typenameFieldName
            && this.element.appliedDirectives.length == 0
            && !this.element.alias;
    }
    withAttachment(key, value) {
        const updatedField = this.element.copy();
        updatedField.addAttachment(key, value);
        return this.withUpdatedElement(updatedField);
    }
    withUpdatedComponents(field, selectionSet) {
        if (this.element === field && this.selectionSet === selectionSet) {
            return this;
        }
        return new FieldSelection(field, selectionSet);
    }
    key() {
        return this.element.key();
    }
    optimize(fragments, validator) {
        const fieldBaseType = (0, definitions_1.baseType)(this.element.definition.type);
        if (!(0, definitions_1.isCompositeType)(fieldBaseType) || !this.selectionSet) {
            return this;
        }
        const fieldValidator = validator.forField(this.element);
        const optimized = this.tryOptimizeSubselectionWithFragments({
            parentType: fieldBaseType,
            subSelection: this.selectionSet,
            fragments,
            validator: fieldValidator,
            canUseFullMatchingFragment: (fragment) => fragment.appliedDirectives.length === 0,
        });
        let optimizedSelection;
        if (optimized instanceof NamedFragmentDefinition) {
            optimizedSelection = selectionSetOf(fieldBaseType, new FragmentSpreadSelection(fieldBaseType, fragments, optimized, []));
        }
        else {
            optimizedSelection = optimized;
        }
        optimizedSelection = optimizedSelection.optimizeSelections(fragments, fieldValidator);
        return this.selectionSet === optimizedSelection
            ? this
            : this.withUpdatedSelectionSet(optimizedSelection);
    }
    filterRecursiveDepthFirst(predicate) {
        if (!this.selectionSet) {
            return predicate(this) ? this : undefined;
        }
        const updatedSelectionSet = this.selectionSet.filterRecursiveDepthFirst(predicate);
        const thisWithFilteredSelectionSet = this.selectionSet === updatedSelectionSet
            ? this
            : new FieldSelection(this.element, updatedSelectionSet);
        return predicate(thisWithFilteredSelectionSet) ? thisWithFilteredSelectionSet : undefined;
    }
    validate(variableDefinitions, validateContextualArgs) {
        var _a;
        this.element.validate(variableDefinitions, validateContextualArgs);
        validate(this.element.isLeafField() || (this.selectionSet && !this.selectionSet.isEmpty()), () => `Invalid empty selection set for field "${this.element.definition.coordinate}" of non-leaf type ${this.element.definition.type}`, this.element.definition.sourceAST);
        (_a = this.selectionSet) === null || _a === void 0 ? void 0 : _a.validate(variableDefinitions);
    }
    rebaseOn({ parentType, fragments, errorIfCannotRebase, }) {
        if (this.element.parentType === parentType) {
            return this;
        }
        const rebasedElement = this.element.rebaseOn({ parentType, errorIfCannotRebase });
        if (!rebasedElement) {
            return undefined;
        }
        if (!this.selectionSet) {
            return this.withUpdatedElement(rebasedElement);
        }
        const rebasedBase = rebasedElement.baseType();
        if (rebasedBase === this.selectionSet.parentType) {
            return this.withUpdatedElement(rebasedElement);
        }
        validate((0, definitions_1.isCompositeType)(rebasedBase), () => `Cannot rebase field selection ${this} on ${parentType}: rebased field base return type ${rebasedBase} is not composite`);
        const rebasedSelectionSet = this.selectionSet.rebaseOn({ parentType: rebasedBase, fragments, errorIfCannotRebase });
        return rebasedSelectionSet.isEmpty() ? undefined : this.withUpdatedComponents(rebasedElement, rebasedSelectionSet);
    }
    canAddTo(parentType) {
        if (this.element.parentType === parentType) {
            return true;
        }
        const type = this.element.typeIfAddedTo(parentType);
        if (!type) {
            return false;
        }
        const base = (0, definitions_1.baseType)(type);
        if (this.selectionSet && this.selectionSet.parentType !== base) {
            (0, utils_1.assert)((0, definitions_1.isCompositeType)(base), () => `${this.element} should have a selection set as it's type is not a composite`);
            return this.selectionSet.selections().every((s) => s.canAddTo(base));
        }
        return true;
    }
    toSelectionNode() {
        var _a;
        const alias = this.element.alias ? { kind: graphql_1.Kind.NAME, value: this.element.alias, } : undefined;
        return {
            kind: graphql_1.Kind.FIELD,
            name: {
                kind: graphql_1.Kind.NAME,
                value: this.element.name,
            },
            alias,
            arguments: this.element.argumentsToNodes(),
            directives: this.element.appliedDirectivesToDirectiveNodes(),
            selectionSet: (_a = this.selectionSet) === null || _a === void 0 ? void 0 : _a.toSelectionSetNode()
        };
    }
    withoutDefer(labelsToRemove) {
        return this.mapToSelectionSet((s) => s.withoutDefer(labelsToRemove));
    }
    withNormalizedDefer(normalizer) {
        return this.mapToSelectionSet((s) => s.withNormalizedDefer(normalizer));
    }
    hasDefer() {
        var _a;
        return !!((_a = this.selectionSet) === null || _a === void 0 ? void 0 : _a.hasDefer());
    }
    normalize({ parentType, recursive }) {
        const definition = parentType === this.parentType
            ? this.element.definition
            : parentType.field(this.element.name);
        (0, utils_1.assert)(definition, `Cannot normalize ${this.element} at ${parentType} which does not have that field`);
        const element = this.element.definition === definition ? this.element : this.element.withUpdatedDefinition(definition);
        if (!this.selectionSet) {
            return this.withUpdatedElement(element);
        }
        const base = element.baseType();
        (0, utils_1.assert)((0, definitions_1.isCompositeType)(base), () => `Field ${element} should not have a sub-selection`);
        const normalizedSubSelection = (recursive !== null && recursive !== void 0 ? recursive : true) ? this.selectionSet.normalize({ parentType: base }) : this.selectionSet;
        if (normalizedSubSelection === null || normalizedSubSelection === void 0 ? void 0 : normalizedSubSelection.isEmpty()) {
            return this.withUpdatedComponents(element, selectionSetOfElement(new Field(base.typenameField(), undefined, [new definitions_1.Directive('include', { 'if': false })])));
        }
        else {
            return this.withUpdatedComponents(element, normalizedSubSelection);
        }
    }
    expandFragments(updatedFragments) {
        return this.mapToSelectionSet((s) => s.expandFragments(updatedFragments));
    }
    equals(that) {
        if (this === that) {
            return true;
        }
        if (!(that instanceof FieldSelection) || !this.element.equals(that.element)) {
            return false;
        }
        if (!this.selectionSet) {
            return !that.selectionSet;
        }
        return !!that.selectionSet && this.selectionSet.equals(that.selectionSet);
    }
    contains(that, options) {
        if (!(that instanceof FieldSelection) || !this.element.equals(that.element)) {
            return ContainsResult.NOT_CONTAINED;
        }
        if (!this.selectionSet) {
            (0, utils_1.assert)(!that.selectionSet, '`this` and `that` have the same element, so if one does not have a sub-selection, neither should the other one');
            return ContainsResult.EQUAL;
        }
        (0, utils_1.assert)(that.selectionSet, '`this` and `that` have the same element, so if one has sub-selection, the other one should too');
        return this.selectionSet.contains(that.selectionSet, options);
    }
    toString(expandFragments = true, indent) {
        return (indent !== null && indent !== void 0 ? indent : '') + this.element + (this.selectionSet ? ' ' + this.selectionSet.toString(expandFragments, true, indent) : '');
    }
}
exports.FieldSelection = FieldSelection;
class FragmentSelection extends AbstractSelection {
    constructor() {
        super(...arguments);
        this.kind = 'FragmentSelection';
    }
    us() {
        return this;
    }
    validateDeferAndStream() {
        if (this.element.hasDefer() || this.element.hasStream()) {
            const schemaDef = this.element.schema().schemaDefinition;
            const parentType = this.parentType;
            validate(schemaDef.rootType('mutation') !== parentType && schemaDef.rootType('subscription') !== parentType, () => { var _a; return `The @defer and @stream directives cannot be used on ${(_a = schemaDef.roots().filter((t) => t.type === parentType).pop()) === null || _a === void 0 ? void 0 : _a.rootKind} root type "${parentType}"`; });
        }
    }
    filterRecursiveDepthFirst(predicate) {
        const updatedSelectionSet = this.selectionSet.filterRecursiveDepthFirst(predicate);
        const thisWithFilteredSelectionSet = updatedSelectionSet === this.selectionSet
            ? this
            : new InlineFragmentSelection(this.element, updatedSelectionSet);
        return predicate(thisWithFilteredSelectionSet) ? thisWithFilteredSelectionSet : undefined;
    }
    hasDefer() {
        return this.element.hasDefer() || this.selectionSet.hasDefer();
    }
    normalize({ parentType, recursive }) {
        const thisCondition = this.element.typeCondition;
        if (thisCondition && parentType !== this.parentType) {
            const conditionRuntimes = (0, definitions_1.possibleRuntimeTypes)(thisCondition);
            const typeRuntimes = (0, definitions_1.possibleRuntimeTypes)(parentType);
            if (!conditionRuntimes.some((t) => typeRuntimes.includes(t))) {
                return undefined;
            }
        }
        return this.normalizeKnowingItIntersects({ parentType, recursive });
    }
}
exports.FragmentSelection = FragmentSelection;
class InlineFragmentSelection extends FragmentSelection {
    constructor(fragment, _selectionSet) {
        super(fragment);
        this._selectionSet = _selectionSet;
    }
    get selectionSet() {
        return this._selectionSet;
    }
    key() {
        return this.element.key();
    }
    withUpdatedComponents(fragment, selectionSet) {
        if (fragment === this.element && selectionSet === this.selectionSet) {
            return this;
        }
        return new InlineFragmentSelection(fragment, selectionSet);
    }
    validate(variableDefinitions) {
        this.validateDeferAndStream();
        validate(!this.selectionSet.isEmpty(), () => `Invalid empty selection set for fragment "${this.element}"`);
        this.selectionSet.validate(variableDefinitions);
    }
    rebaseOn({ parentType, fragments, errorIfCannotRebase, }) {
        if (this.parentType === parentType) {
            return this;
        }
        const rebasedFragment = this.element.rebaseOn({ parentType, errorIfCannotRebase });
        if (!rebasedFragment) {
            return undefined;
        }
        const rebasedCastedType = rebasedFragment.castedType();
        if (rebasedCastedType === this.selectionSet.parentType) {
            return this.withUpdatedElement(rebasedFragment);
        }
        const rebasedSelectionSet = this.selectionSet.rebaseOn({ parentType: rebasedCastedType, fragments, errorIfCannotRebase });
        return rebasedSelectionSet.isEmpty() ? undefined : this.withUpdatedComponents(rebasedFragment, rebasedSelectionSet);
    }
    canAddTo(parentType) {
        if (this.element.parentType === parentType) {
            return true;
        }
        const type = this.element.castedTypeIfAddedTo(parentType);
        if (!type) {
            return false;
        }
        if (this.selectionSet.parentType !== type) {
            return this.selectionSet.selections().every((s) => s.canAddTo(type));
        }
        return true;
    }
    toSelectionNode() {
        const typeCondition = this.element.typeCondition;
        return {
            kind: graphql_1.Kind.INLINE_FRAGMENT,
            typeCondition: typeCondition
                ? {
                    kind: graphql_1.Kind.NAMED_TYPE,
                    name: {
                        kind: graphql_1.Kind.NAME,
                        value: typeCondition.name,
                    },
                }
                : undefined,
            directives: this.element.appliedDirectivesToDirectiveNodes(),
            selectionSet: this.selectionSet.toSelectionSetNode()
        };
    }
    optimize(fragments, validator) {
        let optimizedSelection = this.selectionSet;
        const typeCondition = this.element.typeCondition;
        if (typeCondition) {
            const optimized = this.tryOptimizeSubselectionWithFragments({
                parentType: typeCondition,
                subSelection: optimizedSelection,
                fragments,
                validator,
                canUseFullMatchingFragment: (fragment) => {
                    return fragment.appliedDirectives.length === 0
                        || ((0, types_1.sameType)(typeCondition, fragment.typeCondition)
                            && fragment.appliedDirectives.every((d) => this.element.appliedDirectives.some((s) => (0, definitions_1.sameDirectiveApplication)(d, s))));
                },
            });
            if (optimized instanceof NamedFragmentDefinition) {
                if ((0, types_1.sameType)(typeCondition, optimized.typeCondition)) {
                    let spreadDirectives = this.element.appliedDirectives;
                    if (optimized.appliedDirectives) {
                        spreadDirectives = spreadDirectives.filter((s) => !optimized.appliedDirectives.some((d) => (0, definitions_1.sameDirectiveApplication)(d, s)));
                    }
                    return new FragmentSpreadSelection(this.parentType, fragments, optimized, spreadDirectives);
                }
                else {
                    optimizedSelection = selectionSetOf(typeCondition, new FragmentSpreadSelection(typeCondition, fragments, optimized, []));
                }
            }
            else {
                optimizedSelection = optimized;
            }
        }
        optimizedSelection = optimizedSelection.optimizeSelections(fragments, validator);
        return this.selectionSet === optimizedSelection
            ? this
            : new InlineFragmentSelection(this.element, optimizedSelection);
    }
    withoutDefer(labelsToRemove) {
        const newSelection = this.selectionSet.withoutDefer(labelsToRemove);
        const deferArgs = this.element.deferDirectiveArgs();
        const hasDeferToRemove = deferArgs && (!labelsToRemove || (deferArgs.label && labelsToRemove.has(deferArgs.label)));
        if (newSelection === this.selectionSet && !hasDeferToRemove) {
            return this;
        }
        const newElement = hasDeferToRemove ? this.element.withoutDefer() : this.element;
        if (!newElement) {
            return newSelection;
        }
        return this.withUpdatedComponents(newElement, newSelection);
    }
    withNormalizedDefer(normalizer) {
        const newElement = this.element.withNormalizedDefer(normalizer);
        const newSelection = this.selectionSet.withNormalizedDefer(normalizer);
        if (!newElement) {
            return newSelection;
        }
        return newElement === this.element && newSelection === this.selectionSet
            ? this
            : this.withUpdatedComponents(newElement, newSelection);
    }
    normalizeKnowingItIntersects({ parentType, recursive }) {
        var _a;
        const thisCondition = this.element.typeCondition;
        if (this.element.appliedDirectives.length === 0) {
            if (!thisCondition || parentType === this.element.typeCondition || (0, definitions_1.isObjectType)(parentType)) {
                const normalized = this.selectionSet.normalize({ parentType, recursive });
                return normalized.isEmpty() ? undefined : normalized;
            }
        }
        let normalizedSelectionSet;
        if (recursive !== null && recursive !== void 0 ? recursive : true) {
            normalizedSelectionSet = this.selectionSet.normalize({ parentType: thisCondition !== null && thisCondition !== void 0 ? thisCondition : parentType });
            if (normalizedSelectionSet.isEmpty()) {
                if (this.element.appliedDirectives.length === 0) {
                    return undefined;
                }
                else {
                    return this.withUpdatedComponents(this.element.rebaseOnOrError(parentType), selectionSetOfElement(new Field(((_a = this.element.typeCondition) !== null && _a !== void 0 ? _a : parentType).typenameField(), undefined, [new definitions_1.Directive('include', { 'if': false })])));
                }
            }
        }
        else {
            normalizedSelectionSet = this.selectionSet;
        }
        if (this.element.appliedDirectives.length === 0 && (0, definitions_1.isAbstractType)(thisCondition)) {
            (0, utils_1.assert)(!(0, definitions_1.isObjectType)(parentType), () => `Should not have got here if ${parentType} is an object type`);
            const currentRuntimes = (0, definitions_1.possibleRuntimeTypes)(parentType);
            const liftableSelections = [];
            for (const selection of normalizedSelectionSet.selections()) {
                if (selection.kind === 'FragmentSelection'
                    && selection.element.typeCondition
                    && (0, definitions_1.isObjectType)(selection.element.typeCondition)
                    && currentRuntimes.includes(selection.element.typeCondition)) {
                    liftableSelections.push(selection);
                }
            }
            if (liftableSelections.length === normalizedSelectionSet.selections().length) {
                return normalizedSelectionSet;
            }
            if (liftableSelections.length > 0) {
                const newSet = new SelectionSetUpdates();
                newSet.add(liftableSelections);
                newSet.add(this.withUpdatedSelectionSet(normalizedSelectionSet.filter((s) => !liftableSelections.includes(s))));
                return newSet.toSelectionSet(parentType);
            }
        }
        return this.parentType === parentType && this.selectionSet === normalizedSelectionSet
            ? this
            : this.withUpdatedComponents(this.element.rebaseOnOrError(parentType), normalizedSelectionSet);
    }
    expandFragments(updatedFragments) {
        return this.mapToSelectionSet((s) => s.expandFragments(updatedFragments));
    }
    equals(that) {
        if (this === that) {
            return true;
        }
        return (that instanceof FragmentSelection)
            && this.element.equals(that.element)
            && this.selectionSet.equals(that.selectionSet);
    }
    contains(that, options) {
        if (!(that instanceof FragmentSelection) || !this.element.equals(that.element)) {
            return ContainsResult.NOT_CONTAINED;
        }
        return this.selectionSet.contains(that.selectionSet, options);
    }
    toString(expandFragments = true, indent) {
        return (indent !== null && indent !== void 0 ? indent : '') + this.element + ' ' + this.selectionSet.toString(expandFragments, true, indent);
    }
}
class FragmentSpreadSelection extends FragmentSelection {
    constructor(sourceType, fragments, namedFragment, spreadDirectives) {
        super(new FragmentElement(sourceType, namedFragment.typeCondition, namedFragment.appliedDirectives.concat(spreadDirectives)));
        this.fragments = fragments;
        this.namedFragment = namedFragment;
        this.spreadDirectives = spreadDirectives;
    }
    isFragmentSpread() {
        return true;
    }
    get selectionSet() {
        return this.namedFragment.selectionSet;
    }
    key() {
        if (!this.computedKey) {
            this.computedKey = '...' + this.namedFragment.name + (0, definitions_1.directivesToString)(this.spreadDirectives);
        }
        return this.computedKey;
    }
    withUpdatedComponents(_fragment, _selectionSet) {
        (0, utils_1.assert)(false, `Unsupported`);
    }
    normalizeKnowingItIntersects({ parentType }) {
        (0, utils_1.assert)(parentType.schema() === this.parentType.schema(), 'Should not try to normalize using a type from another schema');
        return this.rebaseOnOrError({ parentType, fragments: this.fragments });
    }
    validate() {
        this.validateDeferAndStream();
        validate((0, definitions_1.runtimeTypesIntersects)(this.parentType, this.namedFragment.typeCondition), () => `Fragment "${this.namedFragment.name}" cannot be spread inside type ${this.parentType} as the runtime types do not intersect ${this.namedFragment.typeCondition}`);
    }
    toSelectionNode() {
        const directiveNodes = (0, definitions_1.directivesToDirectiveNodes)(this.spreadDirectives);
        return {
            kind: graphql_1.Kind.FRAGMENT_SPREAD,
            name: { kind: graphql_1.Kind.NAME, value: this.namedFragment.name },
            directives: directiveNodes,
        };
    }
    optimize(_1, _2) {
        return this;
    }
    rebaseOn({ parentType, fragments, errorIfCannotRebase, }) {
        if (this.parentType === parentType) {
            return this;
        }
        const rebaseOnSameSchema = this.parentType.schema() === parentType.schema();
        (0, utils_1.assert)(fragments || rebaseOnSameSchema, `Must provide fragments is rebasing on other schema`);
        const newFragments = fragments !== null && fragments !== void 0 ? fragments : this.fragments;
        const namedFragment = newFragments.get(this.namedFragment.name);
        if (!namedFragment) {
            validate(!errorIfCannotRebase, () => `Cannot rebase ${this.toString(false)} if it isn't part of the provided fragments`);
            return undefined;
        }
        if (!rebaseOnSameSchema && !(0, definitions_1.runtimeTypesIntersects)(parentType, namedFragment.typeCondition)) {
            const expanded = this.namedFragment.selectionSet.rebaseOn({ parentType, fragments, errorIfCannotRebase });
            return expanded.isEmpty() ? undefined : new InlineFragmentSelection(new FragmentElement(parentType), expanded);
        }
        return new FragmentSpreadSelection(parentType, newFragments, namedFragment, this.spreadDirectives);
    }
    canAddTo(_) {
        return true;
    }
    expandFragments(updatedFragments) {
        if (updatedFragments === null || updatedFragments === void 0 ? void 0 : updatedFragments.has(this.namedFragment.name)) {
            return this;
        }
        const expandedSubSelections = this.selectionSet.expandFragments(updatedFragments);
        return (0, types_1.sameType)(this.parentType, this.namedFragment.typeCondition) && this.element.appliedDirectives.length === 0
            ? expandedSubSelections.selections()
            : new InlineFragmentSelection(this.element, expandedSubSelections);
    }
    collectUsedFragmentNames(collector) {
        const usageCount = collector.get(this.namedFragment.name);
        collector.set(this.namedFragment.name, usageCount === undefined ? 1 : usageCount + 1);
    }
    withoutDefer(_labelsToRemove) {
        (0, utils_1.assert)(false, 'Unsupported, see `Operation.withAllDeferLabelled`');
    }
    withNormalizedDefer(_normalizer) {
        (0, utils_1.assert)(false, 'Unsupported, see `Operation.withAllDeferLabelled`');
    }
    minus(that) {
        (0, utils_1.assert)(this.equals(that), () => `Invalid operation for ${this.toString(false)} and ${that.toString(false)}`);
        return undefined;
    }
    equals(that) {
        if (this === that) {
            return true;
        }
        return (that instanceof FragmentSpreadSelection)
            && this.namedFragment.name === that.namedFragment.name
            && (0, definitions_1.sameDirectiveApplications)(this.spreadDirectives, that.spreadDirectives);
    }
    contains(that, options) {
        if (this.equals(that)) {
            return ContainsResult.EQUAL;
        }
        if (!(that instanceof FragmentSelection) || !this.element.equals(that.element)) {
            return ContainsResult.NOT_CONTAINED;
        }
        return this.selectionSet.contains(that.selectionSet, options);
    }
    toString(expandFragments = true, indent) {
        if (expandFragments) {
            return (indent !== null && indent !== void 0 ? indent : '') + this.element + ' ' + this.selectionSet.toString(true, true, indent);
        }
        else {
            return (indent !== null && indent !== void 0 ? indent : '') + '...' + this.namedFragment.name + (0, definitions_1.directivesToString)(this.spreadDirectives);
        }
    }
}
exports.FragmentSpreadSelection = FragmentSpreadSelection;
function selectionSetOfNode(parentType, node, variableDefinitions, fragments, fieldAccessor = (type, name) => type.field(name)) {
    if (node.selections.length === 1) {
        return selectionSetOf(parentType, selectionOfNode(parentType, node.selections[0], variableDefinitions, fragments, fieldAccessor));
    }
    const selections = new SelectionSetUpdates();
    for (const selectionNode of node.selections) {
        selections.add(selectionOfNode(parentType, selectionNode, variableDefinitions, fragments, fieldAccessor));
    }
    return selections.toSelectionSet(parentType, fragments);
}
function directiveOfNode(schema, node) {
    const directiveDef = schema.directive(node.name.value);
    validate(directiveDef, () => `Unknown directive "@${node.name.value}"`);
    return new definitions_1.Directive(directiveDef.name, (0, values_1.argumentsFromAST)(directiveDef.coordinate, node.arguments, directiveDef));
}
function directivesOfNodes(schema, nodes) {
    var _a;
    return (_a = nodes === null || nodes === void 0 ? void 0 : nodes.map((n) => directiveOfNode(schema, n))) !== null && _a !== void 0 ? _a : [];
}
function selectionOfNode(parentType, node, variableDefinitions, fragments, fieldAccessor = (type, name) => type.field(name)) {
    var _a, _b;
    let selection;
    const directives = directivesOfNodes(parentType.schema(), node.directives);
    switch (node.kind) {
        case graphql_1.Kind.FIELD:
            const definition = fieldAccessor(parentType, node.name.value);
            validate(definition, () => `Cannot query field "${node.name.value}" on type "${parentType}".`, parentType.sourceAST);
            const type = (0, definitions_1.baseType)(definition.type);
            const selectionSet = node.selectionSet
                ? selectionSetOfNode(type, node.selectionSet, variableDefinitions, fragments, fieldAccessor)
                : undefined;
            selection = new FieldSelection(new Field(definition, (0, values_1.argumentsFromAST)(definition.coordinate, node.arguments, definition), directives, (_a = node.alias) === null || _a === void 0 ? void 0 : _a.value), selectionSet);
            break;
        case graphql_1.Kind.INLINE_FRAGMENT:
            const element = new FragmentElement(parentType, (_b = node.typeCondition) === null || _b === void 0 ? void 0 : _b.name.value, directives);
            selection = new InlineFragmentSelection(element, selectionSetOfNode(element.typeCondition ? element.typeCondition : element.parentType, node.selectionSet, variableDefinitions, fragments, fieldAccessor));
            break;
        case graphql_1.Kind.FRAGMENT_SPREAD:
            const fragmentName = node.name.value;
            validate(fragments, () => `Cannot find fragment name "${fragmentName}" (no fragments were provided)`);
            const fragment = fragments.get(fragmentName);
            validate(fragment, () => `Cannot find fragment name "${fragmentName}" (provided fragments are: [${fragments.names().join(', ')}])`);
            selection = new FragmentSpreadSelection(parentType, fragments, fragment, directives);
            break;
    }
    return selection;
}
function operationFromDocument(schema, document, options) {
    let operation;
    let operation_directives;
    const operationName = options === null || options === void 0 ? void 0 : options.operationName;
    const fragments = new NamedFragments();
    document.definitions.forEach(definition => {
        switch (definition.kind) {
            case graphql_1.Kind.OPERATION_DEFINITION:
                validate(!operation || operationName, () => 'Must provide operation name if query contains multiple operations.');
                if (!operationName || (definition.name && definition.name.value === operationName)) {
                    operation = definition;
                    operation_directives = directivesOfNodes(schema, definition.directives);
                }
                break;
            case graphql_1.Kind.FRAGMENT_DEFINITION:
                const name = definition.name.value;
                const typeName = definition.typeCondition.name.value;
                const typeCondition = schema.type(typeName);
                if (!typeCondition) {
                    throw error_1.ERRORS.INVALID_GRAPHQL.err(`Unknown type "${typeName}" for fragment "${name}"`, { nodes: definition });
                }
                if (!(0, definitions_1.isCompositeType)(typeCondition)) {
                    throw error_1.ERRORS.INVALID_GRAPHQL.err(`Invalid fragment "${name}" on non-composite type "${typeName}"`, { nodes: definition });
                }
                fragments.add(new NamedFragmentDefinition(schema, name, typeCondition, directivesOfNodes(schema, definition.directives)));
                break;
        }
    });
    validate(operation, () => operationName ? `Unknown operation named "${operationName}"` : 'No operation found in provided document.');
    const variableDefinitions = operation.variableDefinitions
        ? (0, definitions_1.variableDefinitionsFromAST)(schema, operation.variableDefinitions)
        : new definitions_1.VariableDefinitions();
    document.definitions.forEach(definition => {
        switch (definition.kind) {
            case graphql_1.Kind.FRAGMENT_DEFINITION:
                const fragment = fragments.get(definition.name.value);
                fragment.setSelectionSet(selectionSetOfNode(fragment.typeCondition, definition.selectionSet, variableDefinitions, fragments));
                break;
        }
    });
    fragments.validate(variableDefinitions);
    return operationFromAST({ schema, operation, operation_directives, variableDefinitions, fragments, validateInput: options === null || options === void 0 ? void 0 : options.validate });
}
exports.operationFromDocument = operationFromDocument;
function operationFromAST({ schema, operation, operation_directives, variableDefinitions, fragments, validateInput, }) {
    var _a;
    const rootType = schema.schemaDefinition.root(operation.operation);
    validate(rootType, () => `The schema has no "${operation.operation}" root type defined`);
    const fragmentsIfAny = fragments.isEmpty() ? undefined : fragments;
    return new Operation(schema, operation.operation, parseSelectionSet({
        parentType: rootType.type,
        source: operation.selectionSet,
        variableDefinitions,
        fragments: fragmentsIfAny,
        validate: validateInput,
    }), variableDefinitions, fragmentsIfAny, (_a = operation.name) === null || _a === void 0 ? void 0 : _a.value, operation_directives);
}
function parseOperation(schema, operation, options) {
    return operationFromDocument(schema, (0, graphql_1.parse)(operation), options);
}
exports.parseOperation = parseOperation;
function parseSelectionSet({ parentType, source, variableDefinitions = new definitions_1.VariableDefinitions(), fragments, fieldAccessor, validate = true, }) {
    const node = typeof source === 'string'
        ? parseOperationAST(source.trim().startsWith('{') ? source : `{${source}}`).selectionSet
        : source;
    const selectionSet = selectionSetOfNode(parentType, node, variableDefinitions !== null && variableDefinitions !== void 0 ? variableDefinitions : new definitions_1.VariableDefinitions(), fragments, fieldAccessor);
    if (validate)
        selectionSet.validate(variableDefinitions);
    return selectionSet;
}
exports.parseSelectionSet = parseSelectionSet;
function parseOperationAST(source) {
    const parsed = (0, graphql_1.parse)(source);
    validate(parsed.definitions.length === 1, () => 'Selections should contain a single definitions, found ' + parsed.definitions.length);
    const def = parsed.definitions[0];
    validate(def.kind === graphql_1.Kind.OPERATION_DEFINITION, () => 'Expected an operation definition but got a ' + def.kind);
    return def;
}
exports.parseOperationAST = parseOperationAST;
function operationToDocument(operation) {
    var _a;
    const operationAST = {
        kind: graphql_1.Kind.OPERATION_DEFINITION,
        operation: operation.rootKind,
        name: operation.name ? { kind: graphql_1.Kind.NAME, value: operation.name } : undefined,
        selectionSet: operation.selectionSet.toSelectionSetNode(),
        variableDefinitions: operation.variableDefinitions.toVariableDefinitionNodes(),
        directives: (0, definitions_1.directivesToDirectiveNodes)(operation.appliedDirectives),
    };
    const fragmentASTs = operation.fragments
        ? (_a = operation.fragments) === null || _a === void 0 ? void 0 : _a.toFragmentDefinitionNodes()
        : [];
    return {
        kind: graphql_1.Kind.DOCUMENT,
        definitions: [operationAST].concat(fragmentASTs),
    };
}
exports.operationToDocument = operationToDocument;
function hasSelectionWithPredicate(selectionSet, predicate) {
    for (const selection of selectionSet.selections()) {
        if (predicate(selection)) {
            return true;
        }
        if (selection.selectionSet) {
            if (hasSelectionWithPredicate(selection.selectionSet, predicate)) {
                return true;
            }
        }
    }
    return false;
}
exports.hasSelectionWithPredicate = hasSelectionWithPredicate;
//# sourceMappingURL=operations.js.map