import { GraphQLDataSource, GraphQLDataSourceProcessOptions } from './types';
import { ResponsePath } from '@apollo/query-planner';
import { Request as NodeFetchRequest } from 'node-fetch';
import { <PERSON>tch<PERSON>, FetcherResponse } from '@apollo/utils.fetcher';
import { GraphQLError } from 'graphql';
import { GatewayGraphQLRequest, GatewayGraphQLRequestContext, GatewayGraphQLResponse } from '@apollo/server-gateway-interface';
export declare class RemoteGraphQLDataSource<TContext extends Record<string, any> = Record<string, any>> implements GraphQLDataSource<TContext> {
    fetcher: Fetcher;
    constructor(config?: Partial<RemoteGraphQLDataSource<TContext>> & object & ThisType<RemoteGraphQLDataSource<TContext>>);
    url: string;
    apq: boolean;
    honorSubgraphCacheControlHeader: boolean;
    process(options: GraphQLDataSourceProcessOptions<TContext>): Promise<GatewayGraphQLResponse>;
    private sendRequest;
    willSendRequest?(options: GraphQLDataSourceProcessOptions<TContext>): void | Promise<void>;
    private respond;
    didReceiveResponse?(requestContext: Required<Pick<GatewayGraphQLRequestContext<TContext>, 'request' | 'response' | 'context'>> & {
        pathInIncomingRequest?: ResponsePath;
    }): GatewayGraphQLResponse | Promise<GatewayGraphQLResponse>;
    didEncounterError(error: Error, _fetchRequest: NodeFetchRequest, _fetchResponse?: FetcherResponse, _context?: TContext, _request?: GatewayGraphQLRequest): void;
    parseBody(fetchResponse: FetcherResponse, _fetchRequest?: NodeFetchRequest, _context?: TContext): Promise<object | string>;
    errorFromResponse(response: FetcherResponse): Promise<GraphQLError>;
}
//# sourceMappingURL=RemoteGraphQLDataSource.d.ts.map