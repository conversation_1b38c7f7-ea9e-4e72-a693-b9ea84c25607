"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MismatchReporter = void 0;
const federation_internals_1 = require("@apollo/federation-internals");
const hints_1 = require("../hints");
class MismatchReporter {
    constructor(names, pushError, pushHint) {
        this.names = names;
        this.pushError = pushError;
        this.pushHint = pushHint;
    }
    reportMismatchError(code, message, mismatchedElement, subgraphElements, mismatchAccessor) {
        this.reportMismatch(mismatchedElement, subgraphElements, mismatchAccessor, (elt, names) => `${elt} in ${names}`, (elt, names) => `${elt} in ${names}`, (distribution, nodes) => {
            this.pushError(code.err(message + (0, federation_internals_1.joinStrings)(distribution, ' and ', ' but '), { nodes }));
        }, elt => !elt);
    }
    reportMismatchErrorWithoutSupergraph(code, message, subgraphElements, mismatchAccessor) {
        this.reportMismatch(undefined, subgraphElements, mismatchAccessor, () => '', (elt, names) => `${elt} in ${names}`, (distribution, nodes) => {
            this.pushError(code.err(message + (0, federation_internals_1.joinStrings)(distribution, ' and ', ' but '), { nodes }));
        }, elt => !elt);
    }
    reportMismatchErrorWithSpecifics({ code, message, mismatchedElement, subgraphElements, mismatchAccessor, supergraphElementPrinter, otherElementsPrinter, ignorePredicate, includeMissingSources = false, extraNodes, }) {
        this.reportMismatch(mismatchedElement, subgraphElements, mismatchAccessor, supergraphElementPrinter, otherElementsPrinter, (distribution, nodes) => {
            this.pushError(code.err(message + distribution[0] + (0, federation_internals_1.joinStrings)(distribution.slice(1), ' and '), { nodes: nodes.concat(extraNodes !== null && extraNodes !== void 0 ? extraNodes : []) }));
        }, ignorePredicate, includeMissingSources);
    }
    reportMismatchHint({ code, message, supergraphElement, subgraphElements, targetedElement, elementToString, supergraphElementPrinter, otherElementsPrinter, ignorePredicate, includeMissingSources = false, noEndOfMessageDot = false, }) {
        this.reportMismatch(supergraphElement, subgraphElements, elementToString, supergraphElementPrinter, otherElementsPrinter, (distribution, astNodes) => {
            this.pushHint(new hints_1.CompositionHint(code, message + distribution[0] + (0, federation_internals_1.joinStrings)(distribution.slice(1), ' and ') + (noEndOfMessageDot ? '' : '.'), targetedElement !== null && targetedElement !== void 0 ? targetedElement : ((supergraphElement instanceof federation_internals_1.NamedSchemaElement) ? supergraphElement : undefined), astNodes));
        }, ignorePredicate, includeMissingSources);
    }
    reportMismatch(supergraphElement, subgraphElements, mismatchAccessor, supergraphElementPrinter, otherElementsPrinter, reporter, ignorePredicate, includeMissingSources = false) {
        var _a;
        const distributionMap = new federation_internals_1.MultiMap();
        const astNodes = [];
        const processSubgraphElt = (name, subgraphElt) => {
            if (ignorePredicate && ignorePredicate(subgraphElt)) {
                return;
            }
            const elt = mismatchAccessor(subgraphElt, false);
            distributionMap.add(elt !== null && elt !== void 0 ? elt : '', name);
            if (subgraphElt.sourceAST) {
                astNodes.push((0, federation_internals_1.addSubgraphToASTNode)(subgraphElt.sourceAST, name));
            }
        };
        if (includeMissingSources) {
            for (const [i, name] of this.names.entries()) {
                const subgraphElt = subgraphElements.get(i);
                if (!subgraphElt) {
                    distributionMap.add('', name);
                    continue;
                }
                processSubgraphElt(name, subgraphElt);
            }
        }
        else {
            for (const [i, subgraphElt] of subgraphElements.entries()) {
                if (!subgraphElt) {
                    continue;
                }
                processSubgraphElt(this.names[i], subgraphElt);
            }
        }
        const supergraphMismatch = (_a = (supergraphElement && mismatchAccessor(supergraphElement, true))) !== null && _a !== void 0 ? _a : '';
        (0, federation_internals_1.assert)(distributionMap.size > 1, () => `Should not have been called for ${supergraphElement}`);
        const distribution = [];
        const subgraphsLikeSupergraph = distributionMap.get(supergraphMismatch);
        distribution.push(supergraphElementPrinter(supergraphMismatch, subgraphsLikeSupergraph ? (0, federation_internals_1.printSubgraphNames)(subgraphsLikeSupergraph) : undefined));
        for (const [v, names] of distributionMap.entries()) {
            if (v === supergraphMismatch) {
                continue;
            }
            distribution.push(otherElementsPrinter(v, (0, federation_internals_1.printSubgraphNames)(names)));
        }
        reporter(distribution, astNodes);
    }
}
exports.MismatchReporter = MismatchReporter;
//# sourceMappingURL=reporter.js.map