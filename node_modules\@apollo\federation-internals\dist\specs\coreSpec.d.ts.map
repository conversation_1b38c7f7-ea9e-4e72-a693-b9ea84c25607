{"version": 3, "file": "coreSpec.d.ts", "sourceRoot": "", "sources": ["../../src/specs/coreSpec.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAqB,YAAY,EAAE,eAAe,EAAE,MAAM,SAAS,CAAC;AAEpF,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,mBAAmB,EAAE,QAAQ,EAAwF,SAAS,EAAe,UAAU,EAAE,MAAM,EAAE,gBAAgB,EAA6B,MAAM,gBAAgB,CAAC;AAOtQ,OAAO,EAAmH,iCAAiC,EAAE,sBAAsB,EAAE,iBAAiB,EAAE,MAAM,kCAAkC,CAAC;AAEjP,eAAO,MAAM,YAAY,kCAAkC,CAAC;AAC5D,eAAO,MAAM,YAAY,kCAAkC,CAAC;AAE5D,eAAO,MAAM,wBAAwB,SAAS,CAAC;AAE/C,eAAO,MAAM,kBAAkB,WAAY,YAAY,EAAE,iBAAuE,CAAC;AAOjI,eAAO,MAAM,YAAY,8BAGxB,CAAC;AAEF,MAAM,MAAM,WAAW,GAAG,OAAO,YAAY,CAAC,MAAM,CAAC,CAAC;AAStD,8BAAsB,iBAAiB;IAOC,QAAQ,CAAC,wBAAwB,CAAC;IANxE,QAAQ,CAAC,GAAG,EAAE,UAAU,CAAC;IAEzB,OAAO,CAAC,QAAQ,CAAC,eAAe,CAA6D;IAC7F,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAwD;gBAGvE,GAAG,EAAE,UAAU,GAAG,MAAM,EAAW,wBAAwB,CAAC,4BAAgB;IAIxF,SAAS,CAAC,iBAAiB,CAAC,IAAI,EAAE,sBAAsB;IAIxD,SAAS,CAAC,YAAY,CAAC,IAAI,EAAE,iBAAiB;IAI9C,SAAS,CAAC,kBAAkB,CAAC,UAAU,EAAE,iBAAiB;IAS1D,cAAc,IAAI,SAAS,sBAAsB,EAAE;IAInD,aAAa,CAAC,IAAI,EAAE,MAAM,GAAG,sBAAsB,GAAG,SAAS;IAI/D,SAAS,IAAI,SAAS,iBAAiB,EAAE;IAIzC,QAAQ,CAAC,IAAI,EAAE,MAAM,GAAG,iBAAiB,GAAG,SAAS;IAIrD,IAAI,QAAQ,IAAI,MAAM,CAErB;IAED,IAAI,OAAO,IAAI,cAAc,CAE5B;IAED,UAAU,CAAC,IAAI,EAAE,SAAS,GAAG,OAAO;IAKpC,eAAe,CAAC,SAAS,EAAE,mBAAmB,GAAG,OAAO;IAKxD,mBAAmB,CAAC,MAAM,EAAE,MAAM,GAAG,YAAY,EAAE;IAenD,eAAe,IAAI,MAAM,EAAE;IAK3B,SAAS,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS;IAK1D,SAAS,CAAC,qBAAqB,CAAC,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS;IAK1F,SAAS,CAAC,gBAAgB,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS;IAKhF,SAAS,CAAC,aAAa,CAAC,gBAAgB,SAAS;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;KAAE,EAAE,MAAM,EAAE,MAAM,GAAG,mBAAmB,CAAC,gBAAgB,CAAC,GAAG,SAAS;IAK3I,SAAS,CAAC,SAAS,CAAC,gBAAgB,SAAS;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;KAAE,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,GAAG,mBAAmB,CAAC,gBAAgB,CAAC,GAAG,SAAS;IAK5J,SAAS,CAAC,IAAI,CAAC,CAAC,SAAS,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,GAAG,CAAC,GAAG,SAAS;IAKvF,SAAS,CAAC,gBAAgB,CAAC,MAAM,EAAE,MAAM,GAAG,mBAAmB;IAI/D,SAAS,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,GAAG,mBAAmB;IAIzE,SAAS,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,GAAG,UAAU;IAIjE,SAAS,CAAC,WAAW,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,GAAG,QAAQ;IAI7D,SAAS,CAAC,eAAe,CAAC,MAAM,EAAE,MAAM,GAAG,WAAW,GAAG,SAAS;IAQlE,IAAI,kBAAkB,IAAI,WAAW,GAAG,SAAS,CAEhD;IAED,wBAAwB,CAAC,sBAAsB,EAAE,MAAM,GAAG,iCAAiC,GAAG,SAAS;IAKvG,QAAQ,IAAI,MAAM;CAGnB;AAGD,MAAM,MAAM,iBAAiB,GAAG;IAC9B,GAAG,EAAE,SAAS,CAAC;IACf,OAAO,EAAE,MAAM,CAAC;IAChB,EAAE,CAAC,EAAE,MAAM,CAAC;IACZ,GAAG,CAAC,EAAE,MAAM,CAAC;IACb,MAAM,EAAE,SAAS,CAAC;CACnB,CAAA;AAED,MAAM,MAAM,iBAAiB,GAAG;IAC9B,GAAG,EAAE,MAAM,CAAC;IACZ,OAAO,EAAE,SAAS,CAAC;IACnB,EAAE,CAAC,EAAE,MAAM,CAAC;IACZ,GAAG,CAAC,EAAE,MAAM,CAAC;IACb,MAAM,CAAC,EAAE,CAAC,MAAM,GAAG,UAAU,CAAC,EAAE,CAAC;CAClC,CAAA;AAED,MAAM,MAAM,uBAAuB,GAAG,iBAAiB,GAAG,iBAAiB,CAAC;AAE5E,MAAM,MAAM,UAAU,GAAG;IACvB,IAAI,EAAE,MAAM,CAAC;IACb,EAAE,CAAC,EAAE,MAAM,CAAC;CACb,CAAC;AAEF,wBAAgB,yBAAyB,CAAC,GAAG,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,CAAC,gBAAgB,EAAE,uBAAuB,CAAC,GAAG,UAAU,EAAE,CAyFxI;AAoBD,wBAAgB,8BAA8B,CAAC,SAAS,EAAE,SAAS,CAAC,gBAAgB,EAAE,GAAG,CAAC,GAAG,SAAS,IAAI,SAAS,CAAC,gBAAgB,EAAE,uBAAuB,CAAC,CA4B7J;AAkBD,qBAAa,kBAAmB,SAAQ,iBAAiB;IACvD,OAAO,CAAC,QAAQ,CAAC,uBAAuB,CAAyB;gBAErD,OAAO,EAAE,cAAc,EAAE,wBAAwB,CAAC,EAAE,cAAc,EAAE,QAAQ,GAAE,MAAqB,EAAE,IAAI,GAAE,MAAiC;IAWxJ,OAAO,CAAC,sCAAsC;IA0B9C,mBAAmB,CAAC,CAAC,EAAE,MAAM,GAAG,YAAY,EAAE;IAO9C,WAAW,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,MAAM,GAAG,YAAY,EAAE;IAmD3D,sBAAsB,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE,CAAC,EAAE,MAAM,EAAE,OAAO,GAAE,UAAU,EAAO,GAAG,YAAY,EAAE;IAgC/F,eAAe,IAAI,MAAM,EAAE;IAW3B,OAAO,CAAC,eAAe;IAIvB,OAAO,CAAC,aAAa;IAIrB,OAAO,CAAC,cAAc;IAWtB,aAAa,CAAC,MAAM,EAAE,MAAM,GAAG,mBAAmB,CAAC,uBAAuB,CAAC;IAM3E,WAAW,CAAC,MAAM,EAAE,MAAM,GAAG,cAAc;IAK3C,oBAAoB,CAClB,MAAM,EAAE,MAAM,EACd,OAAO,EAAE,iBAAiB,EAC1B,EAAE,CAAC,EAAE,MAAM,EACX,OAAO,CAAC,EAAE,WAAW,EACrB,OAAO,CAAC,EAAE,UAAU,EAAE,GACrB,YAAY,EAAE;IA4BjB,iBAAiB,CAAC,IAAI,EAAE,uBAAuB,GAAG,UAAU;IAI5D,UAAU,IAAI,SAAS,GAAG,KAAK;CAGhC;AAED,qBAAa,kBAAkB,CAAC,CAAC,SAAS,iBAAiB,GAAG,iBAAiB;IAKjE,QAAQ,CAAC,QAAQ,EAAE,MAAM;IAFrC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAW;gBAEnB,QAAQ,EAAE,MAAM;IAGrC,GAAG,CAAC,UAAU,EAAE,CAAC,GAAG,kBAAkB,CAAC,CAAC,CAAC;IAgBzC,IAAI,CAAC,SAAS,EAAE,cAAc,GAAG,CAAC,GAAG,SAAS;IAI9C,QAAQ,IAAI,cAAc,EAAE;IAI5B,MAAM,IAAI,CAAC;IAKX,yBAAyB,CAAC,UAAU,EAAE,cAAc,GAAG,CAAC;CAgBzD;AAKD,qBAAa,cAAc;aACG,KAAK,EAAE,MAAM;aAAkB,KAAK,EAAE,MAAM;gBAA5C,KAAK,EAAE,MAAM,EAAkB,KAAK,EAAE,MAAM;WAY1D,KAAK,CAAC,KAAK,EAAE,MAAM,GAAG,cAAc;WAkBpC,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,cAAc,CAAC,GAAG,cAAc,GAAG,SAAS;IAuB1E,SAAS,CAAC,QAAQ,EAAE,cAAc,GAAG,OAAO;IAenD,IAAW,MAAM,WAGhB;IAUM,SAAS,CAAC,KAAK,EAAE,cAAc,GAAG,MAAM;IAgBxC,EAAE,CAAC,KAAK,EAAE,cAAc,GAAG,OAAO;IAIlC,GAAG,CAAC,KAAK,EAAE,cAAc,GAAG,OAAO;IAInC,EAAE,CAAC,KAAK,EAAE,cAAc,GAAG,OAAO;IAIlC,GAAG,CAAC,KAAK,EAAE,cAAc,GAAG,OAAO;IAanC,mBAAmB,CAAC,OAAO,EAAE,cAAc;IAS3C,QAAQ;IAUR,MAAM,CAAC,KAAK,EAAE,cAAc;IAInC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAoB;CAC9C;AAGD,qBAAa,UAAU;aAEH,QAAQ,EAAE,MAAM;aAChB,IAAI,EAAE,MAAM;aACZ,OAAO,EAAE,cAAc;aACvB,OAAO,CAAC;gBAHR,QAAQ,EAAE,MAAM,EAChB,IAAI,EAAE,MAAM,EACZ,OAAO,EAAE,cAAc,EACvB,OAAO,CAAC,oBAAQ;WAGpB,UAAU,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,OAAO,GAAG,UAAU,GAAG,SAAS;WAQjE,KAAK,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,OAAO,GAAG,UAAU;WAyBhD,MAAM,CAAC,IAAI,EAAE,eAAe,GAAG,UAAU;IAUhD,SAAS,CAAC,SAAS,EAAE,UAAU,GAAG,OAAO;IAKzC,MAAM,CAAC,KAAK,EAAE,UAAU;IAK/B,IAAI,GAAG,WAIN;IAED,IAAI,WAAW,wBAEd;IAED,IAAI,WAAW,uBAEd;IAED,IAAI,IAAI,IAAI,UAAU,CAGrB;IAED,QAAQ;CAGT;AAED,wBAAgB,mBAAmB,CAAC,UAAU,EAAE,UAAU,GAAG,kBAAkB,GAAG,SAAS,CAI1F;AAED,eAAO,MAAM,aAAa,wCAE8E,CAAC;AAEzG,eAAO,MAAM,aAAa,wCACwD,CAAC;AAKnF,wBAAgB,qBAAqB,CAAC,MAAM,EAAE,MAAM,QA0EnD"}