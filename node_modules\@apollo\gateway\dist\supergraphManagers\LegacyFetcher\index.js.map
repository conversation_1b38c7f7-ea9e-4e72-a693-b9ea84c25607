{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/supergraphManagers/LegacyFetcher/index.ts"], "names": [], "mappings": ";;;;;;AAOA,qEAA6C;AAC7C,yCAQsB;AAMtB,qDAAsD;AAgBtD,MAAa,aAAa;IAUxB,YAAY,OAA6B;QALjC,aAAQ,GAA0B,IAAI,CAAC;QAM7C,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC;QACtB,IAAI,CAAC,KAAK,GAAG,EAAE,KAAK,EAAE,aAAa,EAAE,CAAC;QACtC,IAAI,CAAC,wBAAwB,EAAE,CAAC;IAClC,CAAC;IAEO,wBAAwB;;QAC9B,IAAI,kCAAkC,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;YACpE,MAAA,IAAI,CAAC,MAAM,CAAC,MAAM,0CAAE,IAAI,CACtB,4MAA4M,CAC7M,CAAC;QACJ,CAAC;QAED,IAAI,uCAAuC,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;YACzE,MAAA,IAAI,CAAC,MAAM,CAAC,MAAM,0CAAE,IAAI,CACtB,iNAAiN,CAClN,CAAC;QACJ,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,UAAU,CAAC,EACtB,MAAM,EACN,WAAW,EACX,aAAa,GACY;QACzB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QAEnC,IAAI,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAC;YACpC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QACjC,CAAC;QAED,IAAI,oBAAoB,GAAkB,IAAI,CAAC;QAC/C,IAAI,CAAC;YACH,oBAAoB,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC1D,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;YACzB,MAAM,CAAC,CAAC;QACV,CAAC;QAGD,IAAI,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;YACjC,IAAI,CAAC,YAAY,EAAE,CAAC;QACtB,CAAC;QAED,OAAO;YAIL,aAAa,EAAE,oBAAqB;YACpC,OAAO,EAAE,KAAK,IAAI,EAAE;gBAClB,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;oBACnC,MAAM,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;gBAClC,CAAC;gBACD,IAAI,CAAC,KAAK,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;gBAClC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;oBAClB,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBAC5B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;gBACvB,CAAC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,mBAAmB;;QAC/B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,wBAAwB,CACvD,IAAI,CAAC,MAAM,CAAC,aAAa,CAC1B,CAAC;QAEF,IAAI,IAAA,8BAAqB,EAAC,MAAM,CAAC,EAAE,CAAC;YAElC,IAAI,IAAI,CAAC,aAAa,KAAK,MAAM,CAAC,EAAE;gBAAE,OAAO,IAAI,CAAC;YAElD,MAAM,CAAA,MAAA,IAAI,CAAC,WAAW,qDAAG,MAAM,CAAC,aAAa,CAAC,CAAA,CAAC;YAC/C,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,EAAE,CAAC;YAC/B,OAAO,MAAM,CAAC,aAAa,CAAC;QAC9B,CAAC;aAAM,IAAI,IAAA,kCAAyB,EAAC,MAAM,CAAC,EAAE,CAAC;YAC7C,MAAM,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;YACvD,IAAI,CAAC,aAAa;gBAAE,OAAO,IAAI,CAAC;YAChC,MAAM,CAAA,MAAA,IAAI,CAAC,WAAW,qDAAG,aAAa,CAAC,CAAA,CAAC;YACxC,OAAO,aAAa,CAAC;QACvB,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CACb,2EAA2E,CAC5E,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,mBAAmB,CAAC,MAA+B;;QACzD,IACE,CAAC,MAAM,CAAC,kBAAkB;YAC1B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,kBAAkB,CAAC;gBACrC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,kBAAkB,CAAC,EAC3C,CAAC;YACD,MAAA,IAAI,CAAC,MAAM,CAAC,MAAM,0CAAE,KAAK,CACvB,oDAAoD,CACrD,CAAC;YACF,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC5B,MAAA,IAAI,CAAC,MAAM,CAAC,MAAM,0CAAE,IAAI,CAAC,qCAAqC,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC,kBAAkB,CAAC;QAEpD,MAAM,aAAa,GAAG,IAAI,CAAC,+BAA+B,CACxD,MAAM,CAAC,kBAAkB,CAC1B,CAAC;QAEF,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CACb,uEAAuE,CACxE,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,OAAO,aAAa,CAAC;QACvB,CAAC;IACH,CAAC;IAEO,+BAA+B,CAAC,WAAgC;;QACtE,MAAA,IAAI,CAAC,MAAM,CAAC,MAAM,0CAAE,KAAK,CACvB,yCAAyC,WAAW;aACjD,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,KAAK,GAAG,IAAI,OAAO,KAAK,IAAI,EAAE,CAAC;aACtD,IAAI,CAAC,IAAI,CAAC,EAAE,CAChB,CAAC;QAEF,MAAM,iBAAiB,GAAG,IAAA,6BAAe,EAAC,WAAW,CAAC,CAAC;QAEvD,IAAI,iBAAiB,CAAC,MAAM,EAAE,CAAC;YAC7B,MAAM,EAAE,MAAM,EAAE,GAAG,iBAAiB,CAAC;YACrC,MAAM,KAAK,CACT,qFAAqF;gBACnF,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CACjD,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,MAAM,EAAE,aAAa,EAAE,GAAG,iBAAiB,CAAC;YAC5C,KAAK,MAAM,OAAO,IAAI,WAAW,EAAE,CAAC;gBAClC,MAAA,IAAI,CAAC,aAAa,qDAAG,OAAO,CAAC,CAAC;YAChC,CAAC;YAED,MAAA,IAAI,CAAC,MAAM,CAAC,MAAM,0CAAE,KAAK,CAAC,uCAAuC,CAAC,CAAC;YAEnE,OAAO,aAAa,CAAC;QACvB,CAAC;IACH,CAAC;IAEO,YAAY;QAClB,IAAI,CAAC,KAAK,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;QAClC,IAAI,CAAC,IAAI,EAAE,CAAC;IACd,CAAC;IAEO,IAAI;QACV,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC,KAAK,IAAI,EAAE;;YACpC,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;gBACnC,MAAM,cAAc,GAAG,IAAA,oBAAU,GAAE,CAAC;gBAEpC,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,cAAc,CAAC;gBAC3C,IAAI,CAAC;oBACH,MAAM,qBAAqB,GAAG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;oBAC/D,IAAI,qBAAqB,EAAE,CAAC;wBAC1B,MAAA,IAAI,CAAC,MAAM,qDAAG,qBAAqB,CAAC,CAAC;oBACvC,CAAC;gBACH,CAAC;gBAAC,OAAO,CAAC,EAAE,CAAC;oBACX,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;gBAC3B,CAAC;gBACD,cAAc,CAAC,OAAO,EAAE,CAAC;YAC3B,CAAC;YAED,IAAI,CAAC,IAAI,EAAE,CAAC;QACd,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,gBAAiB,CAAC,CAAC;IACpC,CAAC;IAEO,gBAAgB,CAAC,CAAM;;QAC7B,MAAA,IAAI,CAAC,MAAM,CAAC,MAAM,0CAAE,KAAK,CACvB,sEAAsE;YACpE,CAAC,MAAA,CAAC,CAAC,OAAO,mCAAI,CAAC,CAAC,CACnB,CAAC;IACJ,CAAC;CACF;AA3LD,sCA2LC"}