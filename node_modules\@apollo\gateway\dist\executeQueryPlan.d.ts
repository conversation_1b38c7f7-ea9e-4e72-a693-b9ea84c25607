import { GraphQLFieldResolver, GraphQLSchema } from 'graphql';
import { GraphQLDataSource } from './datasources/types';
import { OperationContext } from './operationContext';
import { QueryPlan, ResponsePath } from '@apollo/query-planner';
import { OpenTelemetryConfig } from "./utilities/opentelemetry";
import { Schema } from '@apollo/federation-internals';
import { GatewayGraphQLRequestContext, GatewayExecutionResult } from '@apollo/server-gateway-interface';
export type ServiceMap = {
    [serviceName: string]: GraphQLDataSource;
};
type ResultMap = Record<string, any>;
export declare function executeQueryPlan(queryPlan: QueryPlan, serviceMap: ServiceMap, requestContext: GatewayGraphQLRequestContext, operationContext: OperationContext, supergraphSchema: GraphQLSchema, apiSchema: Schema, telemetryConfig?: OpenTelemetryConfig): Promise<GatewayExecutionResult>;
export declare function generateHydratedPaths(parent: ResponsePath, path: ResponsePath, data: ResultMap | null, result: ResponsePath[]): void;
export declare const defaultFieldResolverWithAliasSupport: GraphQLFieldResolver<any, any>;
export {};
//# sourceMappingURL=executeQueryPlan.d.ts.map