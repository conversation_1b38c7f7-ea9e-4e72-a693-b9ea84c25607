{"version": 3, "file": "argumentCompositionStrategies.js", "sourceRoot": "", "sources": ["../src/argumentCompositionStrategies.ts"], "names": [], "mappings": ";;;AAAA,+CAAyF;AACzF,mCAAmC;AACnC,qCAAuC;AAUvC,SAAS,iBAAiB,CAAC,KAAsC;IAC/D,OAAO,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE;QACtB,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;QAChC,OAAO,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAA,gBAAQ,EAAC,CAAC,EAAE,IAAI,CAAC,CAAC;YAC7C,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE;YACjB,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,WAAW,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;IACxE,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,sBAAsB;IAC7B,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,IAAA,2BAAa,EAAC,IAAI,CAAC,IAAI,IAAA,wBAAU,EAAC,IAAI,CAAC,MAAM,CAAC;QAChE,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE;QACjB,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,qCAAqC,EAAE,CAAA;AAC3E,CAAC;AAED,SAAS,eAAe;IACtB,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,IAAA,wBAAU,EAAC,IAAI,CAAC,IAAI,CAAC,IAAA,2BAAa,EAAC,IAAI,CAAC,IAAI,IAAA,wBAAU,EAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACtF,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE;QACjB,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,wBAAwB,EAAE,CAAC;AAC/D,CAAC;AAOD,SAAS,mBAAmB,CAC1B,WAA+B;IAE/B,OAAO,CAAC,MAAgC,EAAE,EAAE;QAC1C,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,SAAS,CAAQ,CAAC;QACjF,OAAO,aAAa,CAAC,MAAM,GAAG,CAAC;YAC7B,CAAC,CAAC,WAAW,CAAC,aAAa,CAAC;YAC5B,CAAC,CAAC,SAAS,CAAC;IAChB,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,WAAW,CAAC,MAAa;IAChC,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;QACjC,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,EAAO,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAO,EAAE,EAAE,CAAC,IAAA,oBAAW,EAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QACxF,OAAO,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IAC/B,CAAC,EAAE,EAAE,CAAC,CAAC;AACT,CAAC;AAEY,QAAA,+BAA+B,GAAG;IAC7C,GAAG,EAAE;QACH,IAAI,EAAE,KAAK;QACX,eAAe,EAAE,iBAAiB,CAAC,CAAC,MAAc,EAAE,EAAE,CAAC,CAAC,IAAI,yBAAW,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;QAC3F,WAAW,EAAE,CAAC,MAAgB,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC;KACvD;IACD,GAAG,EAAE;QACH,IAAI,EAAE,KAAK;QACX,eAAe,EAAE,iBAAiB,CAAC,CAAC,MAAc,EAAE,EAAE,CAAC,CAAC,IAAI,yBAAW,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;QAC3F,WAAW,EAAE,CAAC,MAAgB,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC;KACvD;IASD,YAAY,EAAE;QACZ,IAAI,EAAE,cAAc;QACpB,eAAe,EAAE,sBAAsB,EAAE;QACzC,WAAW,EAAE,CAAC,MAAa,EAAE,EAAE;;YAAC,OAAA,MAAA,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;gBAC1D,IAAI,GAAG,KAAK,SAAS,EAAE,CAAC;oBACtB,OAAO,IAAI,CAAC;gBACd,CAAC;qBAAM,CAAC;oBACN,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,EAAO,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAO,EAAE,EAAE,CAAC,IAAA,oBAAW,EAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;gBAC9E,CAAC;YACH,CAAC,EAAE,SAAS,CAAC,mCAAI,EAAE,CAAA;SAAA;KACpB;IACD,KAAK,EAAE;QACL,IAAI,EAAE,OAAO;QACb,eAAe,EAAE,sBAAsB,EAAE;QACzC,WAAW,EAAE,WAAW;KACzB;IACD,YAAY,EAAE;QACZ,IAAI,EAAE,cAAc;QACpB,eAAe,EAAE,iBAAiB,CAAC,CAAC,MAAc,EAAE,EAAE,CAAC;YACrD,MAAM,CAAC,WAAW,EAAE;YACpB,IAAI,yBAAW,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;SACtC,CAAC;QACF,WAAW,EAAE,mBAAmB,CAC9B,CAAC,MAAiB,EAAE,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAC9C;KACF;IACD,YAAY,EAAE;QACZ,IAAI,EAAE,cAAc;QACpB,eAAe,EAAE,iBAAiB,CAAC,CAAC,MAAc,EAAE,EAAE,CAAC;YACrD,MAAM,CAAC,OAAO,EAAE;YAChB,IAAI,yBAAW,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;SAClC,CAAC;QACF,WAAW,EAAE,mBAAmB,CAC9B,CAAC,MAAgB,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,CAC1C;KACF;IACD,cAAc,EAAE;QACd,IAAI,EAAE,gBAAgB;QACtB,eAAe,EAAE,eAAe,EAAE;QAClC,WAAW,EAAE,mBAAmB,CAAC,WAAW,CAAC;KAC9C;CACF,CAAA"}