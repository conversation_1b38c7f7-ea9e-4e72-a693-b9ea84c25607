# @apollo/federation-internals

This is an **internal** package for core Federation components. This package may ship breaking changes at any time and does not make the same efforts to avoid breaking changes as our other Federation packages.

If you are looking to create a subgraph in JavaScript, use [Apollo Server with @apollo/subgraph](https://www.apollographql.com/docs/apollo-server/using-federation/apollo-subgraph-setup).

If you are looking to run a supergraph, use [Apollo Router or Apollo Gateway](https://www.apollographql.com/docs/federation/building-supergraphs/router).

If you want to run composition locally, use the [Rover command `supergraph compose`](https://www.apollographql.com/docs/rover/commands/supergraphs#composing-a-supergraph-schema)

