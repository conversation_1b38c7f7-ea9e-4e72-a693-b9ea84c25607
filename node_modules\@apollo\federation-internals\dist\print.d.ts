import { Directive, DirectiveDefinition, EnumValue, FieldDefinition, InputFieldDefinition, InterfaceImplementation, NamedType, Schema, SchemaRootKind, UnionMember } from "./definitions";
export type PrintOptions = {
    indentString: string;
    definitionsOrder: ('schema' | 'types' | 'directives')[];
    rootTypesOrder: SchemaRootKind[];
    typeCompareFn?: (t1: NamedType, t2: NamedType) => number;
    implementedInterfaceCompareFn?: (t1: InterfaceImplementation<any>, t2: InterfaceImplementation<any>) => number;
    fieldCompareFn?: (t1: FieldDefinition<any>, t2: FieldDefinition<any>) => number;
    unionMemberCompareFn?: (t1: UnionMember, t2: UnionMember) => number;
    enumValueCompareFn?: (t1: EnumValue, t2: EnumValue) => number;
    inputObjectFieldCompareFn?: (t1: InputFieldDefinition, t2: InputFieldDefinition) => number;
    directiveCompareFn?: (d1: DirectiveDefinition, d2: DirectiveDefinition) => number;
    mergeTypesAndExtensions: boolean;
    showAllBuiltIns: boolean;
    noDescriptions: boolean;
    directiveDefinitionFilter?: (d: DirectiveDefinition) => boolean;
    typeFilter: (t: NamedType) => boolean;
    fieldFilter: (f: FieldDefinition<any>) => boolean;
    directiveApplicationFilter: (d: Directive) => boolean;
};
export declare const defaultPrintOptions: PrintOptions;
export declare function orderPrintedDefinitions(options: PrintOptions): PrintOptions;
export declare function shallowOrderPrintedDefinitions(options: PrintOptions): PrintOptions;
export declare function printSchema(schema: Schema, options?: PrintOptions): string;
export declare function printType(type: NamedType, options?: PrintOptions): string;
export declare function printTypeDefinitionAndExtensions(type: NamedType, options?: PrintOptions): string[];
export declare function printDirectiveDefinition(directive: DirectiveDefinition, options?: PrintOptions): string;
//# sourceMappingURL=print.d.ts.map