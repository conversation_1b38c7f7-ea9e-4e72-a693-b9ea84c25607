import { Resolver, Query, Context } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';

import { JwtAuthGuard, RolesGuard } from '@app/security/guards';
import { Roles } from '@app/security/decorators/roles.decorator';
import { UserRole } from '@app/common';
import { RequestWithUser } from '@app/security';

import { DepartmentService } from '../services/department.service';

@Resolver('Department')
@UseGuards(JwtAuthGuard, RolesGuard)
export class DepartmentResolver {
  constructor(private readonly departmentService: DepartmentService) {}

  @Query('departments')
  @Roles(UserRole.ADMIN, UserRole.HR_MANAGER, UserRole.MANAGER, UserRole.EMPLOYEE, UserRole.SUPER_ADMIN)
  async getDepartments(@Context('req') req: RequestWithUser) {
    return this.departmentService.findAll(req.user.tenantId);
  }
}
