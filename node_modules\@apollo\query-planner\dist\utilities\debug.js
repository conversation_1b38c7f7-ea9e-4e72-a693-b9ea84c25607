"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DebugLogger = void 0;
const chalk_1 = __importDefault(require("chalk"));
function indentString(indentLevel) {
    let str = "";
    for (let i = 0; i < indentLevel; i++) {
        str += chalk_1.default.blackBright("⎸ ");
    }
    return str;
}
class DebugLogger {
    constructor(enabled) {
        this.enabled = enabled;
        this.currentIndentLevel = 0;
    }
    log(message, prefix = chalk_1.default.yellow('• ')) {
        if (!this.enabled)
            return this;
        if (typeof message !== 'string') {
            message = message();
        }
        const indentStr = indentString(this.currentIndentLevel);
        console.log(indentStr + prefix + message);
        return this;
    }
    groupedValues(values, printFn) {
        if (!this.enabled)
            return this;
        this.group();
        const indentStr = indentString(this.currentIndentLevel);
        for (const value of values) {
            console.log(indentStr + '- ' + printFn(value));
        }
        return this.groupEnd();
    }
    groupedEntries(map, keyPrintFn, valuePrintFn) {
        if (!this.enabled)
            return this;
        this.group();
        const indentStr = indentString(this.currentIndentLevel);
        for (const [k, v] of map.entries()) {
            console.log(indentStr + '- ' + keyPrintFn(k) + ': ' + valuePrintFn(v));
        }
        return this.groupEnd();
    }
    group(openingMessage) {
        if (this.enabled) {
            if (openingMessage) {
                this.log(openingMessage, chalk_1.default.blue('‣ '));
            }
            this.currentIndentLevel++;
        }
        return this;
    }
    groupEnd(closingMessage) {
        if (!this.enabled || this.currentIndentLevel == 0) {
            return this;
        }
        this.currentIndentLevel--;
        if (closingMessage) {
            this.log(closingMessage, chalk_1.default.green('⇒ '));
        }
        return this;
    }
}
exports.DebugLogger = DebugLogger;
//# sourceMappingURL=debug.js.map