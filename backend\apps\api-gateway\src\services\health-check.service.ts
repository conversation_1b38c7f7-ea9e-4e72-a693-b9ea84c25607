import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { InjectConnection } from '@nestjs/typeorm';
import { InjectConnection as InjectMongoConnection } from '@nestjs/mongoose';
import { Connection } from 'typeorm';
import { Connection as MongoConnection } from 'mongoose';
import { firstValueFrom, timeout, catchError, of } from 'rxjs';
import { Cron, CronExpression } from '@nestjs/schedule';
import { EventEmitter2 } from '@nestjs/event-emitter';

export interface HealthCheckResult {
  service: string;
  status: 'healthy' | 'unhealthy' | 'degraded';
  responseTime: number;
  timestamp: Date;
  details?: Record<string, any>;
  error?: string;
}

export interface SystemHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: Date;
  services: Record<string, HealthCheckResult>;
  summary: {
    total: number;
    healthy: number;
    degraded: number;
    unhealthy: number;
  };
  uptime: number;
  version: string;
}

@Injectable()
export class HealthCheckService {
  private readonly logger = new Logger(HealthCheckService.name);
  private readonly healthResults = new Map<string, HealthCheckResult>();
  private readonly startTime = Date.now();

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly eventEmitter: EventEmitter2,
    @InjectConnection() private readonly postgresConnection: Connection,
    @InjectMongoConnection() private readonly mongoConnection: MongoConnection,
  ) {}

  /**
   * Perform comprehensive health check
   */
  async performHealthCheck(): Promise<SystemHealth> {
    const startTime = Date.now();
    this.logger.debug('Starting comprehensive health check');

    // Run all health checks in parallel
    const healthChecks = await Promise.allSettled([
      this.checkDatabase(),
      this.checkMongoDB(),
      this.checkRedis(),
      this.checkGraphQLServices(),
      this.checkExternalServices(),
      this.checkSystemResources(),
    ]);

    // Process results
    const services: Record<string, HealthCheckResult> = {};
    healthChecks.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value) {
        const healthResult = result.value;
        services[healthResult.service] = healthResult;
        this.healthResults.set(healthResult.service, healthResult);
      }
    });

    // Calculate overall status
    const serviceStatuses = Object.values(services);
    const summary = {
      total: serviceStatuses.length,
      healthy: serviceStatuses.filter(s => s.status === 'healthy').length,
      degraded: serviceStatuses.filter(s => s.status === 'degraded').length,
      unhealthy: serviceStatuses.filter(s => s.status === 'unhealthy').length,
    };

    let overallStatus: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
    if (summary.unhealthy > 0) {
      overallStatus = 'unhealthy';
    } else if (summary.degraded > 0) {
      overallStatus = 'degraded';
    }

    const systemHealth: SystemHealth = {
      status: overallStatus,
      timestamp: new Date(),
      services,
      summary,
      uptime: Date.now() - this.startTime,
      version: this.configService.get('APP_VERSION', '1.0.0'),
    };

    const duration = Date.now() - startTime;
    this.logger.debug(`Health check completed in ${duration}ms - Status: ${overallStatus}`);

    // Emit health check event
    this.eventEmitter.emit('health.check.completed', systemHealth);

    return systemHealth;
  }

  /**
   * Check PostgreSQL database health
   */
  private async checkDatabase(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    try {
      // Test connection with a simple query
      await this.postgresConnection.query('SELECT 1');
      
      const responseTime = Date.now() - startTime;
      
      return {
        service: 'postgresql',
        status: responseTime < 1000 ? 'healthy' : 'degraded',
        responseTime,
        timestamp: new Date(),
        details: {
          isConnected: this.postgresConnection.isConnected,
          driver: this.postgresConnection.driver.type,
        },
      };
    } catch (error) {
      return {
        service: 'postgresql',
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        timestamp: new Date(),
        error: (error as Error).message,
      };
    }
  }

  /**
   * Check MongoDB health
   */
  private async checkMongoDB(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    try {
      // Test connection with ping
      await this.mongoConnection.db.admin().ping();
      
      const responseTime = Date.now() - startTime;
      
      return {
        service: 'mongodb',
        status: responseTime < 1000 ? 'healthy' : 'degraded',
        responseTime,
        timestamp: new Date(),
        details: {
          readyState: this.mongoConnection.readyState,
          host: this.mongoConnection.host,
          port: this.mongoConnection.port,
        },
      };
    } catch (error) {
      return {
        service: 'mongodb',
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        timestamp: new Date(),
        error: (error as Error).message,
      };
    }
  }

  /**
   * Check Redis health
   */
  private async checkRedis(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    try {
      // This would require Redis client injection
      // For now, return a mock healthy status
      const responseTime = Date.now() - startTime;
      
      return {
        service: 'redis',
        status: 'healthy',
        responseTime,
        timestamp: new Date(),
        details: {
          connected: true,
        },
      };
    } catch (error) {
      return {
        service: 'redis',
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        timestamp: new Date(),
        error: (error as Error).message,
      };
    }
  }

  /**
   * Check GraphQL services health
   */
  private async checkGraphQLServices(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    const services = [
      { name: 'auth', url: this.configService.get('AUTH_SERVICE_GRAPHQL_URL', 'http://localhost:3001/graphql') },
      { name: 'employee', url: this.configService.get('EMPLOYEE_SERVICE_GRAPHQL_URL', 'http://localhost:3002/graphql') },
      { name: 'payroll', url: this.configService.get('PAYROLL_SERVICE_GRAPHQL_URL', 'http://localhost:3003/graphql') },
      { name: 'performance', url: this.configService.get('PERFORMANCE_SERVICE_GRAPHQL_URL', 'http://localhost:3004/graphql') },
      { name: 'ai', url: this.configService.get('AI_SERVICE_GRAPHQL_URL', 'http://localhost:3005/graphql') },
    ];

    const results = await Promise.allSettled(
      services.map(async (service) => {
        try {
          const response = await firstValueFrom(
            this.httpService.post(service.url, {
              query: '{ __typename }',
            }).pipe(
              timeout(5000),
              catchError(() => of({ status: 500 }))
            )
          );

          return {
            name: service.name,
            healthy: response.status === 200,
            responseTime: Date.now() - startTime,
          };
        } catch (error) {
          return {
            name: service.name,
            healthy: false,
            responseTime: Date.now() - startTime,
            error: (error as Error).message,
          };
        }
      })
    );

    const serviceResults = results.map(result => 
      result.status === 'fulfilled' ? result.value : { name: 'unknown', healthy: false, responseTime: 0 }
    );

    const healthyServices = serviceResults.filter(s => s.healthy).length;
    const totalServices = serviceResults.length;
    const responseTime = Date.now() - startTime;

    let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
    if (healthyServices === 0) {
      status = 'unhealthy';
    } else if (healthyServices < totalServices) {
      status = 'degraded';
    }

    return {
      service: 'graphql-services',
      status,
      responseTime,
      timestamp: new Date(),
      details: {
        totalServices,
        healthyServices,
        services: serviceResults,
      },
    };
  }

  /**
   * Check external services health
   */
  private async checkExternalServices(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    // This would check external APIs, payment gateways, etc.
    // For now, return a mock healthy status
    const responseTime = Date.now() - startTime;
    
    return {
      service: 'external-services',
      status: 'healthy',
      responseTime,
      timestamp: new Date(),
      details: {
        paymentGateway: 'healthy',
        emailService: 'healthy',
        smsService: 'healthy',
      },
    };
  }

  /**
   * Check system resources
   */
  private async checkSystemResources(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    try {
      const memoryUsage = process.memoryUsage();
      const cpuUsage = process.cpuUsage();
      
      // Calculate memory usage percentage (assuming 1GB limit)
      const memoryLimit = 1024 * 1024 * 1024; // 1GB
      const memoryPercent = memoryUsage.heapUsed / memoryLimit;
      
      let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
      if (memoryPercent > 0.9) {
        status = 'unhealthy';
      } else if (memoryPercent > 0.7) {
        status = 'degraded';
      }
      
      const responseTime = Date.now() - startTime;
      
      return {
        service: 'system-resources',
        status,
        responseTime,
        timestamp: new Date(),
        details: {
          memory: {
            used: memoryUsage.heapUsed,
            total: memoryUsage.heapTotal,
            external: memoryUsage.external,
            percentage: Math.round(memoryPercent * 100),
          },
          cpu: {
            user: cpuUsage.user,
            system: cpuUsage.system,
          },
          uptime: process.uptime(),
          nodeVersion: process.version,
        },
      };
    } catch (error) {
      return {
        service: 'system-resources',
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        timestamp: new Date(),
        error: (error as Error).message,
      };
    }
  }

  /**
   * Get cached health results
   */
  getCachedHealthResults(): Record<string, HealthCheckResult> {
    return Object.fromEntries(this.healthResults);
  }

  /**
   * Get specific service health
   */
  getServiceHealth(serviceName: string): HealthCheckResult | undefined {
    return this.healthResults.get(serviceName);
  }

  /**
   * Periodic health check
   */
  @Cron(CronExpression.EVERY_30_SECONDS)
  async periodicHealthCheck(): Promise<void> {
    try {
      await this.performHealthCheck();
    } catch (error) {
      this.logger.error('Periodic health check failed:', error);
    }
  }

  /**
   * Get health check summary
   */
  getHealthSummary(): {
    overallStatus: string;
    lastCheck: Date | null;
    servicesCount: number;
    healthyCount: number;
    uptime: number;
  } {
    const results = Array.from(this.healthResults.values());
    const healthyCount = results.filter(r => r.status === 'healthy').length;
    const lastCheck = results.length > 0 ? 
      new Date(Math.max(...results.map(r => r.timestamp.getTime()))) : null;

    let overallStatus = 'healthy';
    if (results.some(r => r.status === 'unhealthy')) {
      overallStatus = 'unhealthy';
    } else if (results.some(r => r.status === 'degraded')) {
      overallStatus = 'degraded';
    }

    return {
      overallStatus,
      lastCheck,
      servicesCount: results.length,
      healthyCount,
      uptime: Date.now() - this.startTime,
    };
  }
}
