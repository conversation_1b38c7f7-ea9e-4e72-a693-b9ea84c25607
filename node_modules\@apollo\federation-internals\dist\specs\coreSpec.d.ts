import { ASTNode, Graph<PERSON>Error, StringValueNode } from "graphql";
import { CoreFeature, Directive, DirectiveDefinition, EnumType, NamedType, ScalarType, Schema, SchemaDefinition } from "../definitions";
import { DirectiveCompositionSpecification, DirectiveSpecification, TypeSpecification } from "../directiveAndTypeSpecification";
export declare const coreIdentity = "https://specs.apollo.dev/core";
export declare const linkIdentity = "https://specs.apollo.dev/link";
export declare const linkDirectiveDefaultName = "link";
export declare const ErrCoreCheckFailed: (causes: GraphQLError[]) => GraphQLError;
export declare const corePurposes: ("SECURITY" | "EXECUTION")[];
export type CorePurpose = typeof corePurposes[number];
export declare abstract class FeatureDefinition {
    readonly minimumFederationVersion?: FeatureVersion | undefined;
    readonly url: FeatureUrl;
    private readonly _directiveSpecs;
    private readonly _typeSpecs;
    constructor(url: FeatureUrl | string, minimumFederationVersion?: FeatureVersion | undefined);
    protected registerDirective(spec: DirectiveSpecification): void;
    protected registerType(spec: TypeSpecification): void;
    protected registerSubFeature(subFeature: FeatureDefinition): void;
    directiveSpecs(): readonly DirectiveSpecification[];
    directiveSpec(name: string): DirectiveSpecification | undefined;
    typeSpecs(): readonly TypeSpecification[];
    typeSpec(name: string): TypeSpecification | undefined;
    get identity(): string;
    get version(): FeatureVersion;
    isSpecType(type: NamedType): boolean;
    isSpecDirective(directive: DirectiveDefinition): boolean;
    addElementsToSchema(schema: Schema): GraphQLError[];
    allElementNames(): string[];
    protected nameInSchema(schema: Schema): string | undefined;
    protected directiveNameInSchema(schema: Schema, directiveName: string): string | undefined;
    protected typeNameInSchema(schema: Schema, typeName: string): string | undefined;
    protected rootDirective<TApplicationArgs extends {
        [key: string]: any;
    }>(schema: Schema): DirectiveDefinition<TApplicationArgs> | undefined;
    protected directive<TApplicationArgs extends {
        [key: string]: any;
    }>(schema: Schema, elementName: string): DirectiveDefinition<TApplicationArgs> | undefined;
    protected type<T extends NamedType>(schema: Schema, elementName: string): T | undefined;
    protected addRootDirective(schema: Schema): DirectiveDefinition;
    protected addDirective(schema: Schema, name: string): DirectiveDefinition;
    protected addScalarType(schema: Schema, name: string): ScalarType;
    protected addEnumType(schema: Schema, name: string): EnumType;
    protected featureInSchema(schema: Schema): CoreFeature | undefined;
    get defaultCorePurpose(): CorePurpose | undefined;
    compositionSpecification(directiveNameInFeature: string): DirectiveCompositionSpecification | undefined;
    toString(): string;
}
export type CoreDirectiveArgs = {
    url: undefined;
    feature: string;
    as?: string;
    for?: string;
    import: undefined;
};
export type LinkDirectiveArgs = {
    url: string;
    feature: undefined;
    as?: string;
    for?: string;
    import?: (string | CoreImport)[];
};
export type CoreOrLinkDirectiveArgs = CoreDirectiveArgs | LinkDirectiveArgs;
export type CoreImport = {
    name: string;
    as?: string;
};
export declare function extractCoreFeatureImports(url: FeatureUrl, directive: Directive<SchemaDefinition, CoreOrLinkDirectiveArgs>): CoreImport[];
export declare function isCoreSpecDirectiveApplication(directive: Directive<SchemaDefinition, any>): directive is Directive<SchemaDefinition, CoreOrLinkDirectiveArgs>;
export declare class CoreSpecDefinition extends FeatureDefinition {
    private readonly directiveDefinitionSpec;
    constructor(version: FeatureVersion, minimumFederationVersion?: FeatureVersion, identity?: string, name?: string);
    private createDefinitionArgumentSpecifications;
    addElementsToSchema(_: Schema): GraphQLError[];
    addToSchema(schema: Schema, alias?: string): GraphQLError[];
    addDefinitionsToSchema(schema: Schema, as?: string, imports?: CoreImport[]): GraphQLError[];
    allElementNames(): string[];
    private supportPurposes;
    private supportImport;
    private extractFeature;
    coreDirective(schema: Schema): DirectiveDefinition<CoreOrLinkDirectiveArgs>;
    coreVersion(schema: Schema): FeatureVersion;
    applyFeatureToSchema(schema: Schema, feature: FeatureDefinition, as?: string, purpose?: CorePurpose, imports?: CoreImport[]): GraphQLError[];
    extractFeatureUrl(args: CoreOrLinkDirectiveArgs): FeatureUrl;
    urlArgName(): 'feature' | 'url';
}
export declare class FeatureDefinitions<T extends FeatureDefinition = FeatureDefinition> {
    readonly identity: string;
    private readonly _definitions;
    constructor(identity: string);
    add(definition: T): FeatureDefinitions<T>;
    find(requested: FeatureVersion): T | undefined;
    versions(): FeatureVersion[];
    latest(): T;
    getMinimumRequiredVersion(fedVersion: FeatureVersion): T;
}
export declare class FeatureVersion {
    readonly major: number;
    readonly minor: number;
    constructor(major: number, minor: number);
    static parse(input: string): FeatureVersion;
    static max(versions: Iterable<FeatureVersion>): FeatureVersion | undefined;
    satisfies(required: FeatureVersion): boolean;
    get series(): string;
    compareTo(other: FeatureVersion): number;
    lt(other: FeatureVersion): boolean;
    lte(other: FeatureVersion): boolean;
    gt(other: FeatureVersion): boolean;
    gte(other: FeatureVersion): boolean;
    strictlyGreaterThan(version: FeatureVersion): boolean;
    toString(): string;
    equals(other: FeatureVersion): boolean;
    private static VERSION_RE;
}
export declare class FeatureUrl {
    readonly identity: string;
    readonly name: string;
    readonly version: FeatureVersion;
    readonly element?: string | undefined;
    constructor(identity: string, name: string, version: FeatureVersion, element?: string | undefined);
    static maybeParse(input: string, node?: ASTNode): FeatureUrl | undefined;
    static parse(input: string, node?: ASTNode): FeatureUrl;
    static decode(node: StringValueNode): FeatureUrl;
    satisfies(requested: FeatureUrl): boolean;
    equals(other: FeatureUrl): boolean;
    get url(): string;
    get isDirective(): boolean | undefined;
    get elementName(): string | undefined;
    get base(): FeatureUrl;
    toString(): string;
}
export declare function findCoreSpecVersion(featureUrl: FeatureUrl): CoreSpecDefinition | undefined;
export declare const CORE_VERSIONS: FeatureDefinitions<CoreSpecDefinition>;
export declare const LINK_VERSIONS: FeatureDefinitions<CoreSpecDefinition>;
export declare function removeAllCoreFeatures(schema: Schema): void;
//# sourceMappingURL=coreSpec.d.ts.map