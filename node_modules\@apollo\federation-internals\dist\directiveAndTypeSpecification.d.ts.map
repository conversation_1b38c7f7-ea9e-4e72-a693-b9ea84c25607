{"version": 3, "file": "directiveAndTypeSpecification.d.ts", "sourceRoot": "", "sources": ["../src/directiveAndTypeSpecification.ts"], "names": [], "mappings": "AAAA,OAAO,EAAW,iBAAiB,EAAE,YAAY,EAAE,MAAM,SAAS,CAAC;AACnE,OAAO,EAEL,WAAW,EAGX,SAAS,EAST,UAAU,EAEV,MAAM,EAEP,MAAM,eAAe,CAAC;AAKvB,OAAO,EAAE,2BAA2B,EAAE,MAAM,iCAAiC,CAAC;AAC9E,OAAO,EAAE,iBAAiB,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AACrE,OAAO,EAAE,QAAQ,EAAE,MAAM,GAAG,CAAC;AAE7B,MAAM,MAAM,sBAAsB,GAAG;IACnC,IAAI,EAAE,MAAM,CAAC;IACb,UAAU,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,WAAW,EAAE,SAAS,CAAC,EAAE,OAAO,KAAK,YAAY,EAAE,CAAC;IAC3F,WAAW,CAAC,EAAE,iCAAiC,CAAC;CACjD,CAAA;AAED,MAAM,MAAM,iCAAiC,GAAG;IAC9C,uBAAuB,EAAE,CAAC,iBAAiB,EAAE,cAAc,KAAK,iBAAiB,CAAC;IAClF,eAAe,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,KAAK,cAAc,GAAG,YAAY,CAAC;IAC1F,uBAAuB,CAAC,EAAE,wBAAwB,CAAC;CACpD,CAAA;AAED,MAAM,MAAM,wBAAwB,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,CAAC;IAAC,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;CAAC,CAAC,KAAK,QAAQ,CAAC;IAAC,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;CAAC,CAAC,CAAC;AAEpI,MAAM,MAAM,cAAc,GAAG;IAC3B,KAAK,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,GAAG,CAAC;IAC/C,QAAQ,EAAE,MAAM,MAAM,CAAC;CACxB,CAAA;AAED,MAAM,MAAM,iBAAiB,GAAG;IAC9B,IAAI,EAAE,MAAM,CAAC;IACb,UAAU,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,WAAW,EAAE,SAAS,CAAC,EAAE,OAAO,KAAK,YAAY,EAAE,CAAC;CAC5F,CAAA;AAED,MAAM,MAAM,qBAAqB,GAAG;IAClC,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,WAAW,KAAK,SAAS,GAAG,YAAY,EAAE,CAAC;IAC5E,YAAY,CAAC,EAAE,GAAG,CAAC;CACpB,CAAA;AAED,MAAM,MAAM,8BAA8B,GAAG,qBAAqB,GAAG;IACnE,mBAAmB,CAAC,EAAE,2BAA2B,CAAC;CACnD,CAAA;AAED,MAAM,MAAM,kBAAkB,GAAG;IAC/B,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE,UAAU,CAAC;IACjB,IAAI,CAAC,EAAE,6BAA6B,EAAE,CAAC;CACxC,CAAA;AAED,KAAK,6BAA6B,GAAG;IACnC,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE,SAAS,CAAC;IAChB,YAAY,CAAC,EAAE,GAAG,CAAC;CACpB,CAAA;AAED,wBAAgB,4BAA4B,CAAC,EAC3C,IAAI,EACJ,SAAS,EACT,UAAkB,EAClB,IAAS,EACT,QAAgB,EAChB,uBAAmC,EACnC,uBAAmC,GACpC,EAAE;IACD,IAAI,EAAE,MAAM,CAAC;IACb,SAAS,EAAE,iBAAiB,EAAE,CAAC;IAC/B,UAAU,CAAC,EAAE,OAAO,CAAC;IACrB,IAAI,CAAC,EAAE,8BAA8B,EAAE,CAAC;IACxC,QAAQ,CAAC,EAAE,OAAO,CAAC;IACnB,uBAAuB,CAAC,EAAE,CAAC,UAAU,EAAE,cAAc,KAAK,iBAAiB,CAAC;IAC5E,uBAAuB,CAAC,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE;QAAC,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;KAAC,KAAK;QAAC,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;KAAC,CAAC;CACpG,GAAG,sBAAsB,CAoFzB;AAED,wBAAgB,6BAA6B,CAAC,EAAE,IAAI,EAAE,EAAE;IAAE,IAAI,EAAE,MAAM,CAAA;CAAE,GAAG,iBAAiB,CAc3F;AAED,wBAAgB,6BAA6B,CAAC,EAC5C,IAAI,EACJ,SAAS,GACV,EAAE;IACD,IAAI,EAAE,MAAM,CAAC;IACb,SAAS,EAAE,CAAC,MAAM,EAAE,MAAM,KAAK,kBAAkB,EAAE,CAAC;CACrD,GAAG,iBAAiB,CAqDpB;AAED,wBAAgB,4BAA4B,CAAC,EAC3C,IAAI,EACJ,UAAU,GACX,EAAE;IACD,IAAI,EAAE,MAAM,CAAC;IACb,UAAU,EAAE,CAAC,MAAM,EAAE,MAAM,KAAK,MAAM,EAAE,CAAC;CAC1C,GAAG,iBAAiB,CAyCpB;AAED,wBAAgB,2BAA2B,CAAC,EAC1C,IAAI,EACJ,MAAM,GACP,EAAE;IACD,IAAI,EAAE,MAAM,CAAC;IACb,MAAM,EAAE;QAAE,IAAI,EAAE,MAAM,CAAC;QAAC,WAAW,CAAC,EAAE,MAAM,CAAA;KAAE,EAAE,CAAC;CAClD,GAAG,iBAAiB,CA8BpB"}