import type { Logger } from '@apollo/utils.logger';
import type { HeadersInit } from 'node-fetch';
import { ServiceEndpointDefinition } from '../..';
import { SupergraphManager, SupergraphSdlHookOptions } from '../../config';
export interface IntrospectAndComposeOptions {
    subgraphs: ServiceEndpointDefinition[];
    introspectionHeaders?: HeadersInit | ((service: ServiceEndpointDefinition) => Promise<HeadersInit> | HeadersInit);
    pollIntervalInMs?: number;
    logger?: Logger;
    subgraphHealthCheck?: boolean;
}
export declare class IntrospectAndCompose implements SupergraphManager {
    private config;
    private update?;
    private healthCheck?;
    private subgraphs?;
    private serviceSdlCache;
    private timerRef;
    private state;
    constructor(options: IntrospectAndComposeOptions);
    initialize({ update, getDataSource, healthCheck }: SupergraphSdlHookOptions): Promise<{
        supergraphSdl: string;
        cleanup: () => Promise<void>;
    }>;
    private updateSupergraphSdl;
    private createSupergraphFromSubgraphList;
    private beginPolling;
    private poll;
    private logUpdateFailure;
}
//# sourceMappingURL=index.d.ts.map