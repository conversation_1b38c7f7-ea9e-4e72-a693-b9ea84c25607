import { Controller, Get, Param, UseGuards, Request, ParseUUIDPipe } from '@nestjs/common';
import { ApiTags, ApiBearerAuth } from '@nestjs/swagger';

import { JwtAuthGuard, RolesGuard } from '@app/security/guards';
import { Roles } from '@app/security/decorators/roles.decorator';
import { UserRole } from '@app/common';
import { RequestWithUser } from '@app/security';

import { EmployeeDocumentService } from '../services/employee-document.service';

@ApiTags('Employee Documents')
@Controller('employee-documents')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
export class EmployeeDocumentController {
  constructor(private readonly documentService: EmployeeDocumentService) {}

  @Get('employee/:employeeId')
  @Roles(UserRole.ADMIN, UserRole.HR_MANAGER, UserRole.MANAGER, UserRole.EMPLOYEE, UserRole.SUPER_ADMIN)
  async findByEmployeeId(
    @Param('employeeId', ParseUUIDPipe) employeeId: string,
    @Request() req: RequestWithUser,
  ) {
    return this.documentService.findByEmployeeId(employeeId, req.user.tenantId);
  }
}
