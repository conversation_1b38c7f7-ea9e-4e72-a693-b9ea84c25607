"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CONNECT_VERSIONS = exports.ConnectSpecDefinition = exports.connectIdentity = void 0;
const graphql_1 = require("graphql");
const coreSpec_1 = require("./coreSpec");
const definitions_1 = require("../definitions");
const knownCoreFeatures_1 = require("../knownCoreFeatures");
const directiveAndTypeSpecification_1 = require("../directiveAndTypeSpecification");
exports.connectIdentity = 'https://specs.apollo.dev/connect';
const CONNECT = 'connect';
const SOURCE = 'source';
const URL_PATH_TEMPLATE = 'URLPathTemplate';
const JSON_SELECTION = 'JSONSelection';
const CONNECT_HTTP = 'ConnectHTTP';
const CONNECT_BATCH = 'ConnectBatch';
const CONNECTOR_ERRORS = "ConnectorErrors";
const SOURCE_HTTP = "SourceHTTP";
const HTTP_HEADER_MAPPING = 'HTTPHeaderMapping';
class ConnectSpecDefinition extends coreSpec_1.FeatureDefinition {
    constructor(version, minimumFederationVersion) {
        super(new coreSpec_1.FeatureUrl(exports.connectIdentity, CONNECT, version), minimumFederationVersion);
        this.minimumFederationVersion = minimumFederationVersion;
        this.registerDirective((0, directiveAndTypeSpecification_1.createDirectiveSpecification)({
            name: CONNECT,
            locations: [graphql_1.DirectiveLocation.FIELD_DEFINITION],
            repeatable: true,
            composes: false,
        }));
        this.registerDirective((0, directiveAndTypeSpecification_1.createDirectiveSpecification)({
            name: SOURCE,
            locations: [graphql_1.DirectiveLocation.SCHEMA],
            repeatable: true,
            composes: false,
        }));
        this.registerType((0, directiveAndTypeSpecification_1.createScalarTypeSpecification)({ name: URL_PATH_TEMPLATE }));
        this.registerType((0, directiveAndTypeSpecification_1.createScalarTypeSpecification)({ name: JSON_SELECTION }));
        this.registerType({ name: CONNECT_HTTP, checkOrAdd: () => [] });
        this.registerType({ name: SOURCE_HTTP, checkOrAdd: () => [] });
        this.registerType({ name: HTTP_HEADER_MAPPING, checkOrAdd: () => [] });
    }
    addElementsToSchema(schema) {
        const URLPathTemplate = this.addScalarType(schema, URL_PATH_TEMPLATE);
        const JSONSelection = this.addScalarType(schema, JSON_SELECTION);
        const connect = this.addDirective(schema, CONNECT).addLocations(graphql_1.DirectiveLocation.FIELD_DEFINITION, graphql_1.DirectiveLocation.OBJECT);
        connect.repeatable = true;
        connect.addArgument(SOURCE, schema.stringType());
        const HTTPHeaderMapping = schema.addType(new definitions_1.InputObjectType(this.typeNameInSchema(schema, HTTP_HEADER_MAPPING)));
        HTTPHeaderMapping.addField(new definitions_1.InputFieldDefinition('name')).type =
            new definitions_1.NonNullType(schema.stringType());
        HTTPHeaderMapping.addField(new definitions_1.InputFieldDefinition('from')).type =
            schema.stringType();
        HTTPHeaderMapping.addField(new definitions_1.InputFieldDefinition('value')).type =
            schema.stringType();
        const ConnectHTTP = schema.addType(new definitions_1.InputObjectType(this.typeNameInSchema(schema, CONNECT_HTTP)));
        ConnectHTTP.addField(new definitions_1.InputFieldDefinition('GET')).type =
            URLPathTemplate;
        ConnectHTTP.addField(new definitions_1.InputFieldDefinition('POST')).type =
            URLPathTemplate;
        ConnectHTTP.addField(new definitions_1.InputFieldDefinition('PUT')).type =
            URLPathTemplate;
        ConnectHTTP.addField(new definitions_1.InputFieldDefinition('PATCH')).type =
            URLPathTemplate;
        ConnectHTTP.addField(new definitions_1.InputFieldDefinition('DELETE')).type =
            URLPathTemplate;
        ConnectHTTP.addField(new definitions_1.InputFieldDefinition('body')).type = JSONSelection;
        ConnectHTTP.addField(new definitions_1.InputFieldDefinition('headers')).type =
            new definitions_1.ListType(new definitions_1.NonNullType(HTTPHeaderMapping));
        ConnectHTTP.addField(new definitions_1.InputFieldDefinition('path')).type = JSONSelection;
        ConnectHTTP.addField(new definitions_1.InputFieldDefinition('queryParams')).type =
            JSONSelection;
        connect.addArgument('http', new definitions_1.NonNullType(ConnectHTTP));
        const ConnectBatch = schema.addType(new definitions_1.InputObjectType(this.typeNameInSchema(schema, CONNECT_BATCH)));
        ConnectBatch.addField(new definitions_1.InputFieldDefinition('maxSize')).type = schema.intType();
        connect.addArgument('batch', ConnectBatch);
        const ConnectorErrors = schema.addType(new definitions_1.InputObjectType(this.typeNameInSchema(schema, CONNECTOR_ERRORS)));
        ConnectorErrors.addField(new definitions_1.InputFieldDefinition('message')).type = JSONSelection;
        ConnectorErrors.addField(new definitions_1.InputFieldDefinition('extensions')).type = JSONSelection;
        connect.addArgument('errors', ConnectorErrors);
        connect.addArgument('selection', new definitions_1.NonNullType(JSONSelection));
        connect.addArgument('entity', schema.booleanType(), false);
        const source = this.addDirective(schema, SOURCE).addLocations(graphql_1.DirectiveLocation.SCHEMA);
        source.repeatable = true;
        source.addArgument('name', new definitions_1.NonNullType(schema.stringType()));
        const SourceHTTP = schema.addType(new definitions_1.InputObjectType(this.typeNameInSchema(schema, SOURCE_HTTP)));
        SourceHTTP.addField(new definitions_1.InputFieldDefinition('baseURL')).type =
            new definitions_1.NonNullType(schema.stringType());
        SourceHTTP.addField(new definitions_1.InputFieldDefinition('headers')).type =
            new definitions_1.ListType(new definitions_1.NonNullType(HTTPHeaderMapping));
        SourceHTTP.addField(new definitions_1.InputFieldDefinition('path')).type = JSONSelection;
        SourceHTTP.addField(new definitions_1.InputFieldDefinition('queryParams')).type = JSONSelection;
        source.addArgument('http', new definitions_1.NonNullType(SourceHTTP));
        source.addArgument('errors', ConnectorErrors);
        return [];
    }
    get defaultCorePurpose() {
        return 'EXECUTION';
    }
}
exports.ConnectSpecDefinition = ConnectSpecDefinition;
exports.CONNECT_VERSIONS = new coreSpec_1.FeatureDefinitions(exports.connectIdentity)
    .add(new ConnectSpecDefinition(new coreSpec_1.FeatureVersion(0, 1), new coreSpec_1.FeatureVersion(2, 10)))
    .add(new ConnectSpecDefinition(new coreSpec_1.FeatureVersion(0, 2), new coreSpec_1.FeatureVersion(2, 10)));
(0, knownCoreFeatures_1.registerKnownFeature)(exports.CONNECT_VERSIONS);
//# sourceMappingURL=connectSpec.js.map