import { <PERSON>Map, NamedT<PERSON>, Schema, SchemaRootKind, Type, SelectionSet, MapWithCachedArrays, Supergraph } from '@apollo/federation-internals';
import { Transition } from './transition';
import { NonLocalSelectionsMetadata } from './nonLocalSelectionsEstimation';
export declare const FEDERATED_GRAPH_ROOT_SOURCE = "_";
export declare function federatedGraphRootTypeName(rootKind: SchemaRootKind): string;
export declare function isFederatedGraphRootType(type: NamedType): boolean;
export declare class Vertex {
    readonly index: number;
    readonly type: NamedType;
    readonly source: string;
    hasReachableCrossSubgraphEdges: boolean;
    provideId: number | undefined;
    constructor(index: number, type: NamedType, source: string);
    toString(): string;
}
export declare class RootVertex extends Vertex {
    readonly rootKind: SchemaRootKind;
    constructor(rootKind: SchemaRootKind, index: number, type: NamedType, source: string);
    toString(): string;
}
export declare function isRootVertex(vertex: Vertex): vertex is RootVertex;
export interface OverrideCondition {
    label: string;
    condition: boolean;
}
export declare function checkOverrideCondition(overrideCondition: OverrideCondition, conditionsToCheck: Map<string, boolean>): boolean;
export type ContextCondition = {
    context: string;
    subgraphName: string;
    namedParameter: string;
    selection: string;
    typesWithContextSet: Set<string>;
    argType: Type;
    coordinate: string;
};
export declare class Edge {
    readonly index: number;
    readonly head: Vertex;
    readonly tail: Vertex;
    readonly transition: Transition;
    overrideCondition?: OverrideCondition | undefined;
    private _conditions?;
    requiredContexts: ContextCondition[];
    constructor(index: number, head: Vertex, tail: Vertex, transition: Transition, conditions?: SelectionSet, overrideCondition?: OverrideCondition | undefined, requiredContexts?: ContextCondition[]);
    get conditions(): SelectionSet | undefined;
    isEdgeForField(name: string): boolean;
    matchesSupergraphTransition(otherTransition: Transition): boolean;
    changesSubgraph(): boolean;
    label(): string;
    withNewHead(newHead: Vertex): Edge;
    addToConditions(newConditions: SelectionSet): void;
    addToContextConditions(contextConditions: ContextCondition[]): void;
    isKeyOrRootTypeEdgeToSelf(): boolean;
    satisfiesOverrideConditions(conditionsToCheck: Map<string, boolean>): boolean;
    toString(): string;
}
export declare class QueryGraph {
    readonly name: string;
    readonly vertices: Vertex[];
    private readonly _outEdges;
    private readonly typesToVertices;
    private readonly rootVertices;
    readonly sources: ReadonlyMap<string, Schema>;
    readonly subgraphToArgs: Map<string, string[]>;
    readonly subgraphToArgIndices: Map<string, Map<string, string>>;
    readonly schema: Schema;
    readonly nonTrivialFollowupEdges: (edge: Edge) => readonly Edge[];
    readonly nonLocalSelectionsMetadata: NonLocalSelectionsMetadata | null;
    constructor(name: string, vertices: Vertex[], _outEdges: Edge[][], typesToVertices: MultiMap<string, number>, rootVertices: MapWithCachedArrays<SchemaRootKind, RootVertex>, sources: ReadonlyMap<string, Schema>, subgraphToArgs: Map<string, string[]>, subgraphToArgIndices: Map<string, Map<string, string>>, schema: Schema, isFederatedAndForQueryPlanning?: boolean);
    verticesCount(): number;
    edgesCount(): number;
    rootKinds(): readonly SchemaRootKind[];
    roots(): readonly RootVertex[];
    root(kind: SchemaRootKind): RootVertex | undefined;
    outEdges(vertex: Vertex, includeKeyAndRootTypeEdgesToSelf?: boolean): readonly Edge[];
    outEdgesCount(vertex: Vertex): number;
    outEdge(vertex: Vertex, edgeIndex: number): Edge | undefined;
    allVertices(): Iterable<Vertex>;
    allEdges(): Iterable<Edge>;
    isTerminal(vertex: Vertex): boolean;
    verticesForType(typeName: string): Vertex[];
}
export declare class QueryGraphState<VertexState, EdgeState = undefined> {
    private readonly verticesStates;
    private readonly adjacenciesStates;
    setVertexState(vertex: Vertex, state: VertexState): void;
    removeVertexState(vertex: Vertex): void;
    getVertexState(vertex: Vertex): VertexState | undefined;
    setEdgeState(edge: Edge, state: EdgeState): void;
    removeEdgeState(edge: Edge): void;
    getEdgeState(edge: Edge): EdgeState | undefined;
    toDebugString(vertexMapper: (s: VertexState) => string, edgeMapper: (e: EdgeState) => string): string;
}
export declare function buildQueryGraph(name: string, schema: Schema, overrideLabelsByCoordinate?: Map<string, string>): QueryGraph;
export declare function buildSupergraphAPIQueryGraph(supergraph: Supergraph): QueryGraph;
export declare function buildFederatedQueryGraph(supergraph: Supergraph, forQueryPlanning: boolean): QueryGraph;
export declare function simpleTraversal(graph: QueryGraph, onVertex: (v: Vertex) => void, onEdges: (e: Edge) => boolean): void;
//# sourceMappingURL=querygraph.d.ts.map