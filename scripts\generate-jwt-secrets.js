#!/usr/bin/env node

/**
 * JWT Secret Generator Script
 * Generates secure JWT secrets and updates environment files
 */

const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

// Configuration
const ENV_FILES = [
  '.env',
  '.env.local',
  '.env.development',
  '.env.production',
  '.env.staging',
  '.env.test'
];

const JWT_SECRET_KEYS = [
  'JWT_SECRET',
  'JWT_REFRESH_SECRET'
];

/**
 * Generate a cryptographically secure random string
 * @param {number} length - Length of the secret
 * @returns {string} - Base64 encoded secret
 */
function generateSecureSecret(length = 64) {
  return crypto.randomBytes(length).toString('base64url');
}

/**
 * Generate JWT secrets with metadata
 * @returns {Object} - Object containing generated secrets
 */
function generateJWTSecrets() {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const secrets = {
    JWT_SECRET: generateSecureSecret(64),
    JWT_REFRESH_SECRET: generateSecureSecret(64),
    generated_at: timestamp,
    entropy_bits: 512 // 64 bytes * 8 bits/byte = 512 bits
  };
  
  console.log('🔐 Generated new JWT secrets:');
  console.log(`   JWT_SECRET: ${secrets.JWT_SECRET.substring(0, 20)}...`);
  console.log(`   JWT_REFRESH_SECRET: ${secrets.JWT_REFRESH_SECRET.substring(0, 20)}...`);
  console.log(`   Entropy: ${secrets.entropy_bits} bits`);
  console.log(`   Generated at: ${secrets.generated_at}`);
  
  return secrets;
}

/**
 * Update environment file with new JWT secrets
 * @param {string} filePath - Path to the environment file
 * @param {Object} secrets - Generated secrets object
 * @returns {boolean} - Success status
 */
function updateEnvFile(filePath, secrets) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  File ${filePath} does not exist, skipping...`);
      return false;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    let updated = false;

    // Update JWT_SECRET
    const jwtSecretRegex = /^JWT_SECRET=.*$/m;
    if (jwtSecretRegex.test(content)) {
      content = content.replace(jwtSecretRegex, `JWT_SECRET=${secrets.JWT_SECRET}`);
      updated = true;
    } else {
      // Add JWT_SECRET if not found
      content += `\nJWT_SECRET=${secrets.JWT_SECRET}`;
      updated = true;
    }

    // Update JWT_REFRESH_SECRET
    const jwtRefreshSecretRegex = /^JWT_REFRESH_SECRET=.*$/m;
    if (jwtRefreshSecretRegex.test(content)) {
      content = content.replace(jwtRefreshSecretRegex, `JWT_REFRESH_SECRET=${secrets.JWT_REFRESH_SECRET}`);
      updated = true;
    } else {
      // Add JWT_REFRESH_SECRET if not found
      content += `\nJWT_REFRESH_SECRET=${secrets.JWT_REFRESH_SECRET}`;
      updated = true;
    }

    if (updated) {
      // Create backup
      const backupPath = `${filePath}.backup.${Date.now()}`;
      fs.copyFileSync(filePath, backupPath);
      
      // Write updated content
      fs.writeFileSync(filePath, content);
      console.log(`✅ Updated ${filePath}`);
      console.log(`📁 Backup created: ${backupPath}`);
      return true;
    }

    return false;
  } catch (error) {
    console.error(`❌ Error updating ${filePath}:`, error.message);
    return false;
  }
}

/**
 * Save secrets to a secure file for reference
 * @param {Object} secrets - Generated secrets object
 */
function saveSecretsReference(secrets) {
  const secretsDir = path.join(process.cwd(), '.secrets');
  const secretsFile = path.join(secretsDir, `jwt-secrets-${secrets.generated_at}.json`);
  
  try {
    // Create .secrets directory if it doesn't exist
    if (!fs.existsSync(secretsDir)) {
      fs.mkdirSync(secretsDir, { mode: 0o700 }); // Owner read/write/execute only
    }

    // Save secrets with metadata
    const secretsData = {
      ...secrets,
      note: 'JWT secrets generated automatically. Keep this file secure!',
      usage: {
        JWT_SECRET: 'Used for signing access tokens',
        JWT_REFRESH_SECRET: 'Used for signing refresh tokens'
      }
    };

    fs.writeFileSync(secretsFile, JSON.stringify(secretsData, null, 2), { mode: 0o600 });
    console.log(`💾 Secrets reference saved: ${secretsFile}`);
    
    // Add .secrets to .gitignore if not already present
    const gitignorePath = path.join(process.cwd(), '.gitignore');
    if (fs.existsSync(gitignorePath)) {
      const gitignoreContent = fs.readFileSync(gitignorePath, 'utf8');
      if (!gitignoreContent.includes('.secrets/')) {
        fs.appendFileSync(gitignorePath, '\n# JWT Secrets\n.secrets/\n');
        console.log('📝 Added .secrets/ to .gitignore');
      }
    }
  } catch (error) {
    console.error('❌ Error saving secrets reference:', error.message);
  }
}

/**
 * Main function
 */
function main() {
  console.log('🚀 PeopleNest HRMS - JWT Secret Generator');
  console.log('==========================================\n');

  // Generate new secrets
  const secrets = generateJWTSecrets();
  console.log('');

  // Update environment files
  let updatedFiles = 0;
  ENV_FILES.forEach(envFile => {
    const filePath = path.join(process.cwd(), envFile);
    if (updateEnvFile(filePath, secrets)) {
      updatedFiles++;
    }
  });

  console.log('');
  
  if (updatedFiles > 0) {
    console.log(`✅ Successfully updated ${updatedFiles} environment file(s)`);
    
    // Save secrets reference
    saveSecretsReference(secrets);
    
    console.log('\n🔒 Security Notes:');
    console.log('   • JWT secrets have been updated with cryptographically secure values');
    console.log('   • Backup files have been created for rollback if needed');
    console.log('   • Secrets reference saved in .secrets/ directory (git-ignored)');
    console.log('   • Restart your application to use the new secrets');
    console.log('\n⚠️  Important:');
    console.log('   • Do not commit the new secrets to version control');
    console.log('   • Update your production environment variables separately');
    console.log('   • Existing JWT tokens will be invalidated');
  } else {
    console.log('❌ No environment files were updated');
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  generateSecureSecret,
  generateJWTSecrets,
  updateEnvFile
};
