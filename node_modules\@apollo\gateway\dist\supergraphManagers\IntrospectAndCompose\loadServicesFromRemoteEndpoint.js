"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.loadServicesFromRemoteEndpoint = void 0;
const graphql_1 = require("graphql");
const node_fetch_1 = require("node-fetch");
const types_1 = require("../../datasources/types");
const __1 = require("../..");
async function loadServicesFromRemoteEndpoint({ serviceList, getServiceIntrospectionHeaders, serviceSdlCache, }) {
    if (!serviceList || !serviceList.length) {
        throw new Error('Tried to load services from remote endpoints but none provided');
    }
    let isNewSchema = false;
    const promiseOfServiceList = serviceList.map(async ({ name, url, dataSource }) => {
        if (!url) {
            throw new Error(`Tried to load schema for '${name}' but no 'url' was specified.`);
        }
        const request = {
            query: __1.SERVICE_DEFINITION_QUERY,
            http: {
                url,
                method: 'POST',
                headers: new node_fetch_1.Headers(await getServiceIntrospectionHeaders({ name, url })),
            },
        };
        return dataSource
            .process({
            kind: types_1.GraphQLDataSourceRequestKind.LOADING_SCHEMA,
            request,
            context: {},
        })
            .then(({ data, errors }) => {
            if (data && !errors) {
                const typeDefs = data._service.sdl;
                const previousDefinition = serviceSdlCache.get(name);
                if (previousDefinition !== typeDefs) {
                    isNewSchema = true;
                }
                serviceSdlCache.set(name, typeDefs);
                return {
                    name,
                    url,
                    typeDefs: (0, graphql_1.parse)(typeDefs),
                };
            }
            throw new Error(errors === null || errors === void 0 ? void 0 : errors.map((e) => e.message).join('\n'));
        })
            .catch((err) => {
            const errorMessage = `Couldn't load service definitions for "${name}" at ${url}` +
                (err && err.message ? ': ' + err.message || err : '');
            throw new Error(errorMessage);
        });
    });
    const serviceDefinitions = await Promise.all(promiseOfServiceList);
    return { serviceDefinitions, isNewSchema };
}
exports.loadServicesFromRemoteEndpoint = loadServicesFromRemoteEndpoint;
//# sourceMappingURL=loadServicesFromRemoteEndpoint.js.map