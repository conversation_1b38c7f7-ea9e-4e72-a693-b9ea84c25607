import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  ParseUUIDPipe,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
} from '@nestjs/swagger';

import { JwtAuthGuard, RolesGuard, TenantGuard } from '@app/security';
import { Roles, CurrentUser, CurrentTenant } from '@app/common';
import { LeaveService } from '../services/leave.service';

@ApiTags('Leave Management')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard, TenantGuard, RolesGuard)
@Controller('leave')
export class LeaveController {
  constructor(private readonly leaveService: LeaveService) {}

  @Post()
  @Roles('admin', 'hr_manager', 'hr_user', 'manager', 'employee')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ 
    summary: 'Create leave request',
    description: 'Create a new leave request'
  })
  async create(
    @Body() createLeaveDto: any,
    @CurrentUser() user: any,
    @CurrentTenant() tenantId: string,
  ) {
    return this.leaveService.create(createLeaveDto, user.id, tenantId);
  }

  @Get()
  @Roles('admin', 'hr_manager', 'hr_user', 'manager', 'employee')
  @ApiOperation({ 
    summary: 'Get leave requests',
    description: 'Retrieve leave requests with optional filtering'
  })
  async findAll(
    @Query() query: any,
    @CurrentUser() user: any,
    @CurrentTenant() tenantId: string,
  ) {
    return this.leaveService.findAll(query, user, tenantId);
  }

  @Get(':id')
  @Roles('admin', 'hr_manager', 'hr_user', 'manager', 'employee')
  @ApiOperation({ 
    summary: 'Get leave request by ID',
    description: 'Retrieve detailed information about a specific leave request'
  })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() user: any,
    @CurrentTenant() tenantId: string,
  ) {
    return this.leaveService.findOne(id, user, tenantId);
  }

  @Put(':id')
  @Roles('admin', 'hr_manager', 'hr_user', 'manager', 'employee')
  @ApiOperation({ 
    summary: 'Update leave request',
    description: 'Update leave request information'
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateLeaveDto: any,
    @CurrentUser() user: any,
    @CurrentTenant() tenantId: string,
  ) {
    return this.leaveService.update(id, updateLeaveDto, user.id, tenantId);
  }

  @Delete(':id')
  @Roles('admin', 'hr_manager', 'hr_user', 'manager', 'employee')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ 
    summary: 'Delete leave request',
    description: 'Delete a leave request'
  })
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() user: any,
    @CurrentTenant() tenantId: string,
  ) {
    return this.leaveService.remove(id, user.id, tenantId);
  }

  @Post(':id/approve')
  @Roles('admin', 'hr_manager', 'manager')
  @ApiOperation({ 
    summary: 'Approve leave request',
    description: 'Approve a leave request'
  })
  async approve(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() user: any,
    @CurrentTenant() tenantId: string,
  ) {
    return this.leaveService.approve(id, user.id, tenantId);
  }

  @Post(':id/reject')
  @Roles('admin', 'hr_manager', 'manager')
  @ApiOperation({ 
    summary: 'Reject leave request',
    description: 'Reject a leave request with reason'
  })
  async reject(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() rejectDto: { reason: string },
    @CurrentUser() user: any,
    @CurrentTenant() tenantId: string,
  ) {
    return this.leaveService.reject(id, rejectDto.reason, user.id, tenantId);
  }
}
