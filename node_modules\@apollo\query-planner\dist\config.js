"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateQueryPlannerConfig = exports.enforceQueryPlannerConfigDefaults = void 0;
const utils_keyvaluecache_1 = require("@apollo/utils.keyvaluecache");
function enforceQueryPlannerConfigDefaults(config) {
    return {
        exposeDocumentNodeInFetchNode: false,
        reuseQueryFragments: true,
        generateQueryFragments: false,
        cache: new utils_keyvaluecache_1.InMemoryLRUCache({ maxSize: Math.pow(2, 20) * 50 }),
        ...config,
        incrementalDelivery: {
            enableDefer: false,
            ...config === null || config === void 0 ? void 0 : config.incrementalDelivery,
        },
        debug: {
            bypassPlannerForSingleSubgraph: false,
            maxEvaluatedPlans: 10000,
            pathsLimit: null,
            ...config === null || config === void 0 ? void 0 : config.debug,
        },
        typeConditionedFetching: (config === null || config === void 0 ? void 0 : config.typeConditionedFetching) || false,
    };
}
exports.enforceQueryPlannerConfigDefaults = enforceQueryPlannerConfigDefaults;
function validateQueryPlannerConfig(config) {
    if (config.debug.maxEvaluatedPlans < 1) {
        throw new Error(`Invalid value for query planning configuration "debug.maxEvaluatedPlans"; expected a number >= 1 but got ${config.debug.maxEvaluatedPlans}`);
    }
}
exports.validateQueryPlannerConfig = validateQueryPlannerConfig;
//# sourceMappingURL=config.js.map