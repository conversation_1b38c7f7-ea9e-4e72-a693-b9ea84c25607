{"version": 3, "file": "validate.js", "sourceRoot": "", "sources": ["../src/validate.ts"], "names": [], "mappings": ";;;AAAA,+CAkBuB;AACvB,qCAAiF;AACjF,qCAAgF;AAChF,mDAA8E;AAC9E,mCAA8C;AAC9C,mCAAiC;AAKjC,SAAgB,cAAc,CAAC,MAAc;IAC3C,OAAO,IAAI,SAAS,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;AAC1C,CAAC;AAFD,wCAEC;AAED,MAAM,gCAAgC;IAOpC,YAA6B,OAAgE;QAAhE,YAAO,GAAP,OAAO,CAAyD;QAN5E,iBAAY,GAAG,IAAI,GAAG,EAAU,CAAC;QAEjC,cAAS,GAA2B,EAAE,CAAC;QAEvC,6BAAwB,GAAG,IAAI,GAAG,EAAkB,CAAC;IAGtE,CAAC;IAED,YAAY,CAAC,IAAqB;QAChC,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YACrC,OAAO;QACT,CAAC;QAED,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjC,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAEpE,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;YAClC,IAAI,IAAA,2BAAa,EAAC,KAAK,CAAC,IAAK,CAAC,IAAI,IAAA,+BAAiB,EAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;gBACvE,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC;gBACpC,MAAM,UAAU,GAAG,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBAErE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC3B,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;oBAC7B,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;gBAC/B,CAAC;qBAAM,CAAC;oBACN,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;oBACnD,MAAM,OAAO,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBACrE,IAAI,CAAC,OAAO,CACV,kCAAkC,SAAS,CAAC,IAAI,yDAAyD,OAAO,IAAI,EACpH,EAAE,KAAK,EAAE,IAAA,wBAAU,EAAC,GAAG,SAAS,CAAC,EAAE,CACpC,CAAC;gBACJ,CAAC;gBACD,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;YACvB,CAAC;QACH,CAAC;QACD,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAClD,CAAC;CACF;AAED,MAAM,SAAS;IAKb,YAAqB,MAAc;QAAd,WAAM,GAAN,MAAM,CAAQ;QAJlB,mBAAc,GAAG,IAAI,iCAAmB,EAAE,CAAC;QACpD,oBAAe,GAAY,KAAK,CAAC;QACxB,WAAM,GAAmB,EAAE,CAAC;IAEP,CAAC;IAEvC,QAAQ;QACN,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,CAAC;YAEvC,IAAI,CAAC,sCAAsB,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;gBAChD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YAC1B,CAAC;YACD,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;gBAClB,KAAK,YAAY,CAAC;gBAClB,KAAK,eAAe;oBAClB,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,CAAC;oBACzC,MAAM;gBACR,KAAK,iBAAiB;oBACpB,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;oBACnC,MAAM;gBACR,KAAK,WAAW;oBACd,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;oBAC7B,MAAM;gBACR,KAAK,UAAU;oBACb,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;oBAC5B,MAAM;YACV,CAAC;QACH,CAAC;QAED,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,EAAE,CAAC;YACpD,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;YAC7B,KAAK,MAAM,GAAG,IAAI,SAAS,CAAC,SAAS,EAAE,EAAE,CAAC;gBACxC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;YACxB,CAAC;YACD,KAAK,MAAM,WAAW,IAAI,SAAS,CAAC,YAAY,EAAE,EAAE,CAAC;gBACnD,IAAI,CAAC,4BAA4B,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;YAC3D,CAAC;QACH,CAAC;QAOD,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YAC1B,MAAM,aAAa,GAAG,IAAI,gCAAgC,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;YACpG,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,CAAC;gBACvC,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;oBAClB,KAAK,YAAY,CAAC;oBAClB,KAAK,eAAe;wBAClB,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,CAAC;wBACzC,MAAM;oBACR,KAAK,iBAAiB;wBACpB,aAAa,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;wBACjC,MAAM;gBACV,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAEO,QAAQ,CAAC,OAAe,EAAE,OAA4B;QAC5D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,cAAM,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;IACjE,CAAC;IAEO,eAAe,CAAC,GAA6D;QAGnF,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACd,IAAI,CAAC,QAAQ,CAAC,WAAW,GAAG,CAAC,UAAU,2BAA2B,EAAE,EAAE,KAAK,EAAE,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC;YAC9F,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;QAC/B,CAAC;QACD,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC;IACpB,CAAC;IAEO,YAAY,CAAC,GAAyC;QAC5D,IAAI,IAAA,mCAAmB,EAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;YAClC,IAAI,CAAC,QAAQ,CACX,SAAS,GAAG,CAAC,IAAI,yEAAyE,EAC1F,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,CAC9C,CAAC;YACF,OAAO;QACT,CAAC;QACD,IAAI,CAAC;YACH,IAAA,oBAAU,EAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACvB,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,EAAE,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAC1E,CAAC;IACH,CAAC;IAEO,6BAA6B,CAAC,IAAgC;QACpE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC;YACtB,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,CAAC,IAAI,kCAAkC,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QAChG,CAAC;QACD,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;YAClC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YACzB,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YAC5B,KAAK,MAAM,GAAG,IAAI,KAAK,CAAC,SAAS,EAAE,EAAE,CAAC;gBACpC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;YACxB,CAAC;QACH,CAAC;IACH,CAAC;IAEO,6BAA6B,CAAC,IAAgC;QACpE,IAAI,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YACxC,IAAI,CAAC,QAAQ,CACX,QAAQ,IAAI,wEAAwE,EACpF,EAAE,KAAK,EAAE,IAAA,wBAAU,EAAC,IAAI,EAAE,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAE,CAAC,EAAE,CACtE,CAAC;QACJ,CAAC;QAED,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC;YACpC,KAAK,MAAM,QAAQ,IAAI,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC;gBACpC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;gBACxC,IAAI,CAAC,KAAK,EAAE,CAAC;oBACX,IAAI,CAAC,QAAQ,CACX,mBAAmB,QAAQ,CAAC,UAAU,iBAAiB,IAAI,uBAAuB,EAClF,EAAE,KAAK,EAAE,IAAA,wBAAU,EAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,CACtC,CAAC;oBACF,SAAS;gBACX,CAAC;gBAID,IAAI,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAA,iBAAS,EAAC,QAAQ,CAAC,IAAK,EAAE,KAAK,CAAC,IAAK,CAAC,EAAE,CAAC;oBAC9E,IAAI,CAAC,QAAQ,CACX,mBAAmB,QAAQ,CAAC,UAAU,iBAAiB,QAAQ,CAAC,IAAI,QAAQ,KAAK,CAAC,UAAU,YAAY,KAAK,CAAC,IAAI,2BAA2B,EAC7I,EAAE,KAAK,EAAE,IAAA,wBAAU,EAAC,QAAQ,EAAE,KAAK,CAAC,EAAE,CACvC,CAAC;gBACJ,CAAC;gBAED,KAAK,MAAM,MAAM,IAAI,QAAQ,CAAC,SAAS,EAAE,EAAE,CAAC;oBAC1C,MAAM,GAAG,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;oBACxC,IAAI,CAAC,GAAG,EAAE,CAAC;wBACT,IAAI,CAAC,QAAQ,CACX,4BAA4B,MAAM,CAAC,UAAU,iBAAiB,KAAK,CAAC,UAAU,uBAAuB,EACrG,EAAE,KAAK,EAAE,IAAA,wBAAU,EAAC,MAAM,EAAE,KAAK,CAAC,EAAE,CACrC,CAAC;wBACF,SAAS;oBACX,CAAC;oBAED,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,IAAA,gBAAQ,EAAC,MAAM,CAAC,IAAK,EAAE,GAAG,CAAC,IAAK,CAAC,EAAE,CAAC;wBACvE,IAAI,CAAC,QAAQ,CACX,4BAA4B,MAAM,CAAC,UAAU,iBAAiB,MAAM,CAAC,IAAI,QAAQ,GAAG,CAAC,UAAU,YAAY,GAAG,CAAC,IAAI,GAAG,EACtH,EAAE,KAAK,EAAE,IAAA,wBAAU,EAAC,MAAM,EAAE,GAAG,CAAC,EAAE,CACnC,CAAC;oBACJ,CAAC;gBACH,CAAC;gBAED,KAAK,MAAM,GAAG,IAAI,KAAK,CAAC,SAAS,EAAE,EAAE,CAAC;oBAEpC,IAAI,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;wBAChC,SAAS;oBACX,CAAC;oBACD,IAAI,GAAG,CAAC,UAAU,EAAE,EAAE,CAAC;wBACrB,IAAI,CAAC,QAAQ,CACX,SAAS,KAAK,CAAC,UAAU,+BAA+B,GAAG,CAAC,IAAI,6CAA6C,QAAQ,CAAC,UAAU,GAAG,EACnI,EAAE,KAAK,EAAE,IAAA,wBAAU,EAAC,GAAG,EAAE,QAAQ,CAAC,EAAE,CACrC,CAAC;oBACJ,CAAC;gBACH,CAAC;YACH,CAAC;YAGD,KAAK,MAAM,QAAQ,IAAI,GAAG,CAAC,UAAU,EAAE,EAAE,CAAC;gBACxC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACxC,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;wBACtB,IAAI,CAAC,QAAQ,CACX,QAAQ,IAAI,qBAAqB,GAAG,gDAAgD,EACpF,EAAE,KAAK,EAAE,IAAA,wBAAU,EAAC,IAAI,EAAE,GAAG,CAAC,EAAE,CACjC,CAAC;oBACJ,CAAC;yBAAM,CAAC;wBACN,IAAI,CAAC,QAAQ,CACX,QAAQ,IAAI,mBAAmB,QAAQ,iCAAiC,GAAG,GAAG,EAC9E,EAAE,KAAK,EAAE,IAAA,wBAAU,EAAC,IAAI,EAAE,GAAG,EAAE,QAAQ,CAAC,EAAE,CAC3C,CAAC;oBACJ,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAEO,uBAAuB,CAAC,IAAqB;QACnD,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC;YACtB,IAAI,CAAC,QAAQ,CAAC,qBAAqB,IAAI,CAAC,IAAI,kCAAkC,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QAC7G,CAAC;QACD,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;YAClC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YACzB,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,CAAC;gBACjC,SAAS;YACX,CAAC;YACD,IAAI,KAAK,CAAC,UAAU,EAAE,IAAI,KAAK,CAAC,YAAY,EAAE,EAAE,CAAC;gBAC/C,IAAI,CAAC,QAAQ,CACX,wBAAwB,KAAK,CAAC,UAAU,wBAAwB,EAChE,EAAE,KAAK,EAAE,IAAA,wBAAU,EAAC,KAAK,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CACzE,CAAC;YACJ,CAAC;YACD,IAAI,KAAK,CAAC,YAAY,KAAK,SAAS,IAAI,CAAC,IAAA,qBAAY,EAAC,KAAK,CAAC,YAAY,EAAE,KAAK,EAAE,IAAI,iCAAmB,EAAE,CAAC,EAAE,CAAC;gBAC5G,IAAI,CAAC,QAAQ,CACX,+BAA+B,IAAA,sBAAa,EAAC,KAAK,CAAC,YAAY,CAAC,8BAA8B,KAAK,CAAC,UAAU,YAAY,KAAK,CAAC,IAAI,GAAG,EACvI,EAAE,KAAK,EAAE,IAAA,wBAAU,EAAC,KAAK,CAAC,EAAE,CAC7B,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAEO,WAAW,CAAC,GAA4B;QAC9C,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,CAAC;YAC/B,OAAO;QACT,CAAC;QACD,IAAI,GAAG,CAAC,UAAU,EAAE,IAAI,GAAG,CAAC,YAAY,EAAE,EAAE,CAAC;YAC3C,IAAI,CAAC,QAAQ,CACX,qBAAqB,GAAG,CAAC,UAAU,wBAAwB,EAC3D,EAAE,KAAK,EAAE,IAAA,wBAAU,EAAC,GAAG,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CACrE,CAAC;QACJ,CAAC;QACD,IAAI,GAAG,CAAC,YAAY,KAAK,SAAS,IAAI,CAAC,IAAA,qBAAY,EAAC,GAAG,CAAC,YAAY,EAAE,GAAG,EAAE,IAAI,iCAAmB,EAAE,CAAC,EAAE,CAAC;YAEtG,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,IAAA,0BAAY,EAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnI,IAAI,CAAC,aAAa,IAAI,CAAC,IAAA,gCAAuB,EAAC,GAAG,CAAC,YAAY,EAAE,aAAa,EAAE,GAAG,CAAC,YAAY,EAAE,IAAI,iCAAmB,EAAE,CAAC,EAAE,CAAC;gBAC7H,IAAI,CAAC,QAAQ,CACX,+BAA+B,IAAA,sBAAa,EAAC,GAAG,CAAC,YAAY,CAAC,2BAA2B,GAAG,CAAC,UAAU,YAAY,GAAG,CAAC,IAAI,GAAG,EAC9H,EAAE,KAAK,EAAE,IAAA,wBAAU,EAAC,GAAG,CAAC,EAAE,CAC3B,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAEO,iBAAiB,CAAC,IAAe;QACvC,IAAI,IAAI,CAAC,YAAY,EAAE,KAAK,CAAC,EAAE,CAAC;YAC9B,IAAI,CAAC,QAAQ,CAAC,cAAc,IAAI,CAAC,UAAU,wCAAwC,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QAClH,CAAC;IACH,CAAC;IAEO,gBAAgB,CAAC,IAAc;QACrC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC7B,IAAI,CAAC,QAAQ,CAAC,aAAa,IAAI,CAAC,UAAU,kCAAkC,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QAC3G,CAAC;QACD,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YACzB,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;gBAC7E,IAAI,CAAC,QAAQ,CACX,aAAa,IAAI,CAAC,UAAU,0BAA0B,KAAK,GAAG,EAC9D,EAAE,KAAK,EAAE,KAAK,CAAC,SAAS,EAAE,CAC3B,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAEO,4BAA4B,CAAC,UAA+B,EAAE,WAAsB;QAI1F,KAAK,MAAM,QAAQ,IAAI,UAAU,CAAC,SAAS,EAAE,EAAE,CAAC;YAC9C,MAAM,KAAK,GAAG,WAAW,CAAC,SAAS,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACrD,IAAI,CAAC,KAAK,EAAE,CAAC;gBAEX,SAAS;YACX,CAAC;YAID,IAAI,QAAQ,CAAC,IAAI,IAAI,CAAC,IAAA,qBAAY,EAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC;gBACzE,MAAM,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;gBAElC,MAAM,UAAU,GAAG,MAAM,YAAY,gCAAkB;oBACrD,CAAC,CAAC,MAAM,CAAC,UAAU;oBACnB,CAAC,CAAC,QAAQ,CAAC;gBACb,IAAI,CAAC,QAAQ,CACX,sBAAsB,QAAQ,CAAC,UAAU,cAAc,QAAQ,CAAC,IAAI,wBAAwB,UAAU,CAAC,UAAU,SAAS,UAAU,IAAI,EACxI,EAAE,KAAK,EAAE,IAAA,wBAAU,EAAC,WAAW,EAAE,QAAQ,CAAC,EAAE,CAC7C,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;CACF"}