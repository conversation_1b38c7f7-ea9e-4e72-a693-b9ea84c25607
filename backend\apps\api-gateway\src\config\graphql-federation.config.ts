import { ConfigService } from '@nestjs/config';
import { ApolloGatewayDriverConfig } from '@nestjs/apollo';
import { IntrospectAndCompose, RemoteGraphQLDataSource, ServiceEndpointDefinition } from '@apollo/gateway';
import { Logger } from '@nestjs/common';

export class AuthenticatedDataSource extends RemoteGraphQLDataSource {
  private readonly logger = new Logger(AuthenticatedDataSource.name);

  willSendRequest({ request, context }: any) {
    // Forward authentication headers to subgraphs
    if (context.req?.headers?.authorization) {
      request.http.headers.set('authorization', context.req.headers.authorization);
    }

    // Forward tenant ID
    if (context.req?.headers?.['x-tenant-id']) {
      request.http.headers.set('x-tenant-id', context.req.headers['x-tenant-id']);
    }

    // Forward correlation ID for tracing
    if (context.req?.headers?.['x-correlation-id']) {
      request.http.headers.set('x-correlation-id', context.req.headers['x-correlation-id']);
    }

    // Add service-specific headers
    request.http.headers.set('x-gateway-source', 'api-gateway');
    request.http.headers.set('x-gateway-version', '1.0.0');

    this.logger.debug(`Forwarding request to ${this.url} with headers: ${JSON.stringify(Object.fromEntries(request.http.headers))}`);
  }

  didReceiveResponse({ response, request, context }: any) {
    // Log response for debugging
    this.logger.debug(`Received response from ${this.url}: ${response.http.status}`);
    
    // Handle errors
    if (response.http.status >= 400) {
      this.logger.warn(`Error response from ${this.url}: ${response.http.status}`);
    }

    return response;
  }

  didEncounterError(error: any, request: any) {
    this.logger.error(`GraphQL error from ${this.url}:`, error);
    return error;
  }
}

export function createGraphQLFederationConfig(configService: ConfigService): ApolloGatewayDriverConfig {
  const environment = configService.get('NODE_ENV', 'development');
  const isProduction = environment === 'production';

  // Service URLs configuration
  const serviceUrls = {
    auth: configService.get('AUTH_SERVICE_GRAPHQL_URL', 'http://localhost:3001/graphql'),
    employee: configService.get('EMPLOYEE_SERVICE_GRAPHQL_URL', 'http://localhost:3002/graphql'),
    payroll: configService.get('PAYROLL_SERVICE_GRAPHQL_URL', 'http://localhost:3003/graphql'),
    performance: configService.get('PERFORMANCE_SERVICE_GRAPHQL_URL', 'http://localhost:3004/graphql'),
    ai: configService.get('AI_SERVICE_GRAPHQL_URL', 'http://localhost:3005/graphql'),
    notification: configService.get('NOTIFICATION_SERVICE_GRAPHQL_URL', 'http://localhost:3006/graphql'),
  };

  const subgraphs = Object.entries(serviceUrls).map(([name, url]) => ({
    name,
    url,
  }));

  return {
    gateway: {
      supergraphSdl: new IntrospectAndCompose({
        subgraphs,
        introspectionHeaders: {
          'User-Agent': 'PeopleNest-Gateway/1.0.0',
        },
        pollIntervalInMs: configService.get<number>('GRAPHQL_POLL_INTERVAL', 30000),
      }),
      buildService(definition: ServiceEndpointDefinition) {
        return new AuthenticatedDataSource({ url: definition.url! }) as any;
      },

    },

    // Enable GraphQL Playground in development
    // playground: !isProduction, // Not available in ApolloGatewayDriverConfig

    // CORS configuration - handled at application level
    // cors: {
    //   origin: configService.get('CORS_ORIGIN', 'http://localhost:3000').split(','),
    //   credentials: true,
    // },

    // Context creation - handled by Apollo Server directly
    // context: ({ req, res }: { req: any; res: any }) => {
    //     // Add request start time for performance tracking
    //     (req as any).startTime = Date.now();

    //     return {
    //       req,
    //       res,
    //       user: req.user,
    //       tenant: req.headers['x-tenant-id'],
    //       correlationId: req.headers['x-correlation-id'] || `gql-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    //     };
    //   },

    // Format errors - handled by Apollo Server directly
    // formatError: (error: any) => {
    //   const logger = new Logger('GraphQLGateway');

    //   // Log the error
    //   logger.error('GraphQL Error:', {
    //     message: error.message,
    //     path: error.path,
    //     locations: error.locations,
    //     extensions: error.extensions,
    //   });

    //   // Don't expose internal errors in production
    //   if (isProduction && error.extensions?.code === 'INTERNAL_ERROR') {
    //     return new Error('Internal server error');
    //   }

    //   return {
    //     message: error.message,
    //     locations: error.locations,
    //     path: error.path,
    //     extensions: {
    //       code: error.extensions?.code || 'UNKNOWN_ERROR',
    //       timestamp: new Date().toISOString(),
    //       ...(isProduction ? {} : { stack: error.stack }),
    //     },
    //   };
    // },

    // Format response - commented out as formatResponse is not available in ApolloGatewayDriverConfig
    // formatResponse: (response: any, requestContext: any) => {
    //   const logger = new Logger('GraphQLGateway');

    //   // Add performance metrics
    //   const duration = Date.now() - (requestContext.request as any).startTime;

    //   return {
    //     ...response,
    //     extensions: {
    //       ...response.extensions,
    //       performance: {
    //         duration,
    //         timestamp: new Date().toISOString(),
    //       },
    //       gateway: {
    //         version: '1.0.0',
    //         correlationId: requestContext.context.correlationId,
    //       },
    //     },
    //   };
    // },

    // Query complexity analysis - moved to Apollo Server config
    // plugins configuration removed for compatibility
    // Cache control configuration removed for compatibility

    // Subscriptions configuration removed for compatibility

    // Upload configuration removed for compatibility
  };
}

/**
 * Health check for GraphQL services
 */
export async function checkGraphQLServicesHealth(configService: ConfigService): Promise<Record<string, boolean>> {
  const serviceUrls = {
    auth: configService.get('AUTH_SERVICE_GRAPHQL_URL', 'http://localhost:3001/graphql'),
    employee: configService.get('EMPLOYEE_SERVICE_GRAPHQL_URL', 'http://localhost:3002/graphql'),
    payroll: configService.get('PAYROLL_SERVICE_GRAPHQL_URL', 'http://localhost:3003/graphql'),
    performance: configService.get('PERFORMANCE_SERVICE_GRAPHQL_URL', 'http://localhost:3004/graphql'),
    ai: configService.get('AI_SERVICE_GRAPHQL_URL', 'http://localhost:3005/graphql'),
    notification: configService.get('NOTIFICATION_SERVICE_GRAPHQL_URL', 'http://localhost:3006/graphql'),
  };

  const healthStatus: Record<string, boolean> = {};

  for (const [serviceName, url] of Object.entries(serviceUrls)) {
    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: '{ __typename }',
        }),
      });

      healthStatus[serviceName] = response.ok;
    } catch (error) {
      healthStatus[serviceName] = false;
    }
  }

  return healthStatus;
}

/**
 * Get GraphQL federation metrics
 */
export function getGraphQLMetrics(): {
  totalQueries: number;
  totalMutations: number;
  totalSubscriptions: number;
  averageResponseTime: number;
  errorRate: number;
} {
  // This would be implemented with proper metrics collection
  // For now, return mock data
  return {
    totalQueries: 0,
    totalMutations: 0,
    totalSubscriptions: 0,
    averageResponseTime: 0,
    errorRate: 0,
  };
}
