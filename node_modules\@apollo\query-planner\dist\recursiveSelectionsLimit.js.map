{"version": 3, "file": "recursiveSelectionsLimit.js", "sourceRoot": "", "sources": ["../src/recursiveSelectionsLimit.ts"], "names": [], "mappings": ";;;AAAA,uEAKsC;AAEtC,MAAM,wBAAwB,GAAG,QAAU,CAAC;AAW5C,SAAS,wBAAwB,CAC/B,SAAoB,EACpB,aAAkC,EAClC,YAA0B,EAC1B,KAAa;IAEb,KAAK,MAAM,SAAS,IAAI,YAAY,CAAC,UAAU,EAAE,EAAE,CAAC;QAElD,KAAK,EAAE,CAAC;QACR,IAAI,KAAK,GAAG,wBAAwB,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,QAAQ,SAAS,CAAC,IAAI,EAAE,CAAC;YACvB,KAAK,gBAAgB,CAAC,CAAC,CAAC;gBACtB,IAAI,SAAS,CAAC,YAAY,EAAE,CAAC;oBAC3B,MAAM,MAAM,GAAG,wBAAwB,CACrC,SAAS,EACT,aAAa,EACb,SAAS,CAAC,YAAY,EACtB,KAAK,CACN,CAAC;oBACF,IAAI,MAAM,KAAK,IAAI;wBAAE,OAAO,IAAI,CAAC;oBACjC,KAAK,GAAG,MAAM,CAAC;gBACjB,CAAC;gBACD,MAAM;YACR,CAAC;YACD,KAAK,mBAAmB,CAAC,CAAC,CAAC;gBACzB,IAAI,SAAS,YAAY,8CAAuB,EAAE,CAAC;oBACjD,MAAM,IAAI,GAAG,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC;oBAC1C,MAAM,MAAM,GAAG,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;oBAEvC,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;wBACzB,KAAK,GAAG,KAAK,GAAG,MAAM,CAAC;wBACvB,IAAI,KAAK,GAAG,wBAAwB,EAAE,CAAC;4BACrC,OAAO,IAAI,CAAC;wBACd,CAAC;oBACH,CAAC;yBAAM,CAAC;wBACN,MAAM,QAAQ,GAAG,KAAK,CAAC;wBACvB,MAAM,MAAM,GAAG,wBAAwB,CACrC,SAAS,EACT,aAAa,EACb,SAAS,CAAC,YAAY,EACtB,KAAK,CACN,CAAC;wBACF,IAAI,MAAM,KAAK,IAAI;4BAAE,OAAO,IAAI,CAAC;wBACjC,KAAK,GAAG,MAAM,CAAC;wBACf,aAAa,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,GAAG,QAAQ,CAAC,CAAC;oBAC5C,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,MAAM,MAAM,GAAG,wBAAwB,CACrC,SAAS,EACT,aAAa,EACb,SAAS,CAAC,YAAY,EACtB,KAAK,CACN,CAAC;oBACF,IAAI,MAAM,KAAK,IAAI;wBAAE,OAAO,IAAI,CAAC;oBACjC,KAAK,GAAG,MAAM,CAAC;gBACjB,CAAC;gBACD,MAAM;YACR,CAAC;YACD;gBACE,IAAA,wCAAiB,EAAC,SAAS,CAAC,CAAC;QACjC,CAAC;IACH,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAgB,2BAA2B,CACzC,SAAoB;IAEpB,MAAM,aAAa,GAAG,IAAI,GAAG,EAAkB,CAAC;IAChD,MAAM,MAAM,GAAG,wBAAwB,CACrC,SAAS,EACT,aAAa,EACb,SAAS,CAAC,YAAY,EACtB,CAAC,CAAC,CAAC;IACL,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;QACpB,MAAM,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAC;IAC7E,CAAC;AACH,CAAC;AAZD,kEAYC;AAAA,CAAC"}