# Encryption Keys Generator Script for PowerShell
# Generates secure encryption keys and updates environment files

param(
    [switch]$Force,
    [string]$EnvFile = ".env"
)

# Configuration
$ENV_FILES = @(".env", ".env.local", ".env.development", ".env.production", ".env.staging", ".env.test")
$ENCRYPTION_KEYS = @("ENCRYPTION_KEY", "ENCRYPTION_MASTER_KEY", "INDEX_HASH_SALT")

function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Generate-SecureKey {
    param([int]$Bytes = 32)
    
    # Generate cryptographically secure random bytes
    $keyBytes = New-Object byte[] $Bytes
    $rng = [System.Security.Cryptography.RNGCryptoServiceProvider]::Create()
    $rng.GetBytes($keyBytes)
    $rng.Dispose()
    
    # Convert to hexadecimal
    $hex = [BitConverter]::ToString($keyBytes).Replace('-', '').ToLower()
    
    return $hex
}

function Generate-EncryptionKeys {
    $timestamp = (Get-Date).ToString("yyyy-MM-ddTHH-mm-ss-fffZ")
    $keys = @{
        # AES-256-GCM requires 32-byte (256-bit) key
        ENCRYPTION_KEY = Generate-SecureKey -Bytes 32
        
        # Master key for tenant encryption - 64 bytes for extra security
        ENCRYPTION_MASTER_KEY = Generate-SecureKey -Bytes 64
        
        # Index hash salt for searchable encryption - 32 bytes
        INDEX_HASH_SALT = Generate-SecureKey -Bytes 32
        
        # Metadata
        generated_at = $timestamp
        algorithm = "aes-256-gcm"
        key_lengths = @{
            ENCRYPTION_KEY = "32 bytes (256 bits)"
            ENCRYPTION_MASTER_KEY = "64 bytes (512 bits)"
            INDEX_HASH_SALT = "32 bytes (256 bits)"
        }
        entropy_bits = @{
            ENCRYPTION_KEY = 256
            ENCRYPTION_MASTER_KEY = 512
            INDEX_HASH_SALT = 256
            total = 1024
        }
    }
    
    Write-ColorOutput "🔐 Generated new encryption keys:" "Green"
    Write-ColorOutput "   ENCRYPTION_KEY: $($keys.ENCRYPTION_KEY.Substring(0, 16))... (32 bytes)" "Yellow"
    Write-ColorOutput "   ENCRYPTION_MASTER_KEY: $($keys.ENCRYPTION_MASTER_KEY.Substring(0, 16))... (64 bytes)" "Yellow"
    Write-ColorOutput "   INDEX_HASH_SALT: $($keys.INDEX_HASH_SALT.Substring(0, 16))... (32 bytes)" "Yellow"
    Write-ColorOutput "   Total entropy: $($keys.entropy_bits.total) bits" "Cyan"
    Write-ColorOutput "   Generated at: $($keys.generated_at)" "Cyan"
    
    return $keys
}

function Update-EnvFile {
    param(
        [string]$FilePath,
        [hashtable]$Keys
    )
    
    try {
        if (-not (Test-Path $FilePath)) {
            Write-ColorOutput "⚠️  File $FilePath does not exist, skipping..." "Yellow"
            return $false
        }

        $content = Get-Content $FilePath -Raw
        $updated = $false

        # Create backup
        $backupPath = "$FilePath.backup.$(Get-Date -Format 'yyyyMMddHHmmss')"
        Copy-Item $FilePath $backupPath
        
        # Update ENCRYPTION_KEY
        if ($content -match "^ENCRYPTION_KEY=.*$") {
            $content = $content -replace "^ENCRYPTION_KEY=.*$", "ENCRYPTION_KEY=$($Keys.ENCRYPTION_KEY)"
            $updated = $true
        } else {
            # Add if not present
            if ($content -match "^# Encryption Configuration$") {
                $content = $content -replace "^# Encryption Configuration$", "# Encryption Configuration`nENCRYPTION_KEY=$($Keys.ENCRYPTION_KEY)"
            } else {
                $content += "`n# Encryption Configuration`nENCRYPTION_KEY=$($Keys.ENCRYPTION_KEY)"
            }
            $updated = $true
        }

        # Update ENCRYPTION_MASTER_KEY
        if ($content -match "^ENCRYPTION_MASTER_KEY=.*$") {
            $content = $content -replace "^ENCRYPTION_MASTER_KEY=.*$", "ENCRYPTION_MASTER_KEY=$($Keys.ENCRYPTION_MASTER_KEY)"
            $updated = $true
        } else {
            $content = $content -replace "^ENCRYPTION_KEY=.*$", "ENCRYPTION_KEY=$($Keys.ENCRYPTION_KEY)`nENCRYPTION_MASTER_KEY=$($Keys.ENCRYPTION_MASTER_KEY)"
            $updated = $true
        }

        # Update INDEX_HASH_SALT
        if ($content -match "^INDEX_HASH_SALT=.*$") {
            $content = $content -replace "^INDEX_HASH_SALT=.*$", "INDEX_HASH_SALT=$($Keys.INDEX_HASH_SALT)"
            $updated = $true
        } else {
            # Add after HASH_SALT_ROUNDS if it exists
            if ($content -match "^HASH_SALT_ROUNDS=.*$") {
                $content = $content -replace "^HASH_SALT_ROUNDS=.*$", "HASH_SALT_ROUNDS=12`nINDEX_HASH_SALT=$($Keys.INDEX_HASH_SALT)"
            } else {
                $content = $content -replace "^ENCRYPTION_MASTER_KEY=.*$", "ENCRYPTION_MASTER_KEY=$($Keys.ENCRYPTION_MASTER_KEY)`nINDEX_HASH_SALT=$($Keys.INDEX_HASH_SALT)"
            }
            $updated = $true
        }

        if ($updated) {
            Set-Content -Path $FilePath -Value $content -NoNewline
            Write-ColorOutput "✅ Updated $FilePath" "Green"
            Write-ColorOutput "📁 Backup created: $backupPath" "Cyan"
            return $true
        }

        return $false
    }
    catch {
        Write-ColorOutput "❌ Error updating $FilePath : $($_.Exception.Message)" "Red"
        return $false
    }
}

function Save-KeysReference {
    param([hashtable]$Keys)
    
    $secretsDir = Join-Path $PWD ".secrets"
    $keysFile = Join-Path $secretsDir "encryption-keys-$($Keys.generated_at).json"
    
    try {
        # Create .secrets directory if it doesn't exist
        if (-not (Test-Path $secretsDir)) {
            New-Item -ItemType Directory -Path $secretsDir -Force | Out-Null
        }

        # Save keys with metadata
        $keysData = @{
            ENCRYPTION_KEY = $Keys.ENCRYPTION_KEY
            ENCRYPTION_MASTER_KEY = $Keys.ENCRYPTION_MASTER_KEY
            INDEX_HASH_SALT = $Keys.INDEX_HASH_SALT
            generated_at = $Keys.generated_at
            algorithm = $Keys.algorithm
            key_lengths = $Keys.key_lengths
            entropy_bits = $Keys.entropy_bits
            note = "Encryption keys generated automatically. Keep this file secure!"
            usage = @{
                ENCRYPTION_KEY = "Primary encryption key for AES-256-GCM data encryption"
                ENCRYPTION_MASTER_KEY = "Master key for tenant-specific key derivation"
                INDEX_HASH_SALT = "Salt for searchable encryption index hashing"
            }
            security_notes = @(
                "These keys are used for data encryption and must be kept secure",
                "Changing these keys will make existing encrypted data unreadable",
                "Use proper key rotation procedures in production",
                "Store keys in secure key management systems for production use"
            )
        }

        $keysData | ConvertTo-Json -Depth 4 | Set-Content $keysFile
        Write-ColorOutput "💾 Keys reference saved: $keysFile" "Green"
        
        # Add .secrets to .gitignore if not already present
        $gitignorePath = Join-Path $PWD ".gitignore"
        if (Test-Path $gitignorePath) {
            $gitignoreContent = Get-Content $gitignorePath -Raw
            if ($gitignoreContent -notmatch "\.secrets/") {
                Add-Content $gitignorePath "`n# Encryption Keys`n.secrets/"
                Write-ColorOutput "📝 Added .secrets/ to .gitignore" "Green"
            }
        }
    }
    catch {
        Write-ColorOutput "❌ Error saving keys reference: $($_.Exception.Message)" "Red"
    }
}

# Main execution
function Main {
    Write-ColorOutput "🚀 PeopleNest HRMS - Encryption Keys Generator" "Magenta"
    Write-ColorOutput "==============================================" "Magenta"
    Write-Host ""

    # Generate new encryption keys
    $keys = Generate-EncryptionKeys
    Write-Host ""

    # Update environment files
    $updatedFiles = 0
    foreach ($envFile in $ENV_FILES) {
        $filePath = Join-Path $PWD $envFile
        if (Update-EnvFile -FilePath $filePath -Keys $keys) {
            $updatedFiles++
        }
    }

    Write-Host ""
    
    if ($updatedFiles -gt 0) {
        Write-ColorOutput "✅ Successfully updated $updatedFiles environment file(s)" "Green"
        
        # Save keys reference
        Save-KeysReference -Keys $keys
        
        Write-Host ""
        Write-ColorOutput "🔒 Security Notes:" "Yellow"
        Write-ColorOutput "   • Encryption keys have been updated with cryptographically secure values" "White"
        Write-ColorOutput "   • Backup files have been created for rollback if needed" "White"
        Write-ColorOutput "   • Keys reference saved in .secrets/ directory (git-ignored)" "White"
        Write-ColorOutput "   • Restart your application to use the new keys" "White"
        Write-Host ""
        Write-ColorOutput "⚠️  Critical Warning:" "Red"
        Write-ColorOutput "   • Changing encryption keys will make existing encrypted data unreadable" "White"
        Write-ColorOutput "   • Ensure you have proper data migration procedures before using in production" "White"
        Write-ColorOutput "   • Never commit the new keys to version control" "White"
        Write-ColorOutput "   • Update your production environment variables separately" "White"
    } else {
        Write-ColorOutput "❌ No environment files were updated" "Red"
    }
}

# Run the script
Main
