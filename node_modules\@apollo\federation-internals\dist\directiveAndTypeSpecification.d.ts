import { DirectiveLocation, GraphQLError } from "graphql";
import { CoreFeature, InputType, OutputType, Schema } from "./definitions";
import { ArgumentCompositionStrategy } from "./argumentCompositionStrategies";
import { FeatureDefinition, FeatureVersion } from "./specs/coreSpec";
import { Subgraph } from '.';
export type DirectiveSpecification = {
    name: string;
    checkOrAdd: (schema: Schema, feature?: CoreFeature, asBuiltIn?: boolean) => GraphQLError[];
    composition?: DirectiveCompositionSpecification;
};
export type DirectiveCompositionSpecification = {
    supergraphSpecification: (federationVersion: FeatureVersion) => FeatureDefinition;
    argumentsMerger?: (schema: Schema, feature: CoreFeature) => ArgumentMerger | GraphQLError;
    staticArgumentTransform?: StaticArgumentsTransform;
};
export type StaticArgumentsTransform = (subgraph: Subgraph, args: Readonly<{
    [key: string]: any;
}>) => Readonly<{
    [key: string]: any;
}>;
export type ArgumentMerger = {
    merge: (argName: string, values: any[]) => any;
    toString: () => string;
};
export type TypeSpecification = {
    name: string;
    checkOrAdd: (schema: Schema, feature?: CoreFeature, asBuiltIn?: boolean) => GraphQLError[];
};
export type ArgumentSpecification = {
    name: string;
    type: (schema: Schema, feature?: CoreFeature) => InputType | GraphQLError[];
    defaultValue?: any;
};
export type DirectiveArgumentSpecification = ArgumentSpecification & {
    compositionStrategy?: ArgumentCompositionStrategy;
};
export type FieldSpecification = {
    name: string;
    type: OutputType;
    args?: ResolvedArgumentSpecification[];
};
type ResolvedArgumentSpecification = {
    name: string;
    type: InputType;
    defaultValue?: any;
};
export declare function createDirectiveSpecification({ name, locations, repeatable, args, composes, supergraphSpecification, staticArgumentTransform, }: {
    name: string;
    locations: DirectiveLocation[];
    repeatable?: boolean;
    args?: DirectiveArgumentSpecification[];
    composes?: boolean;
    supergraphSpecification?: (fedVersion: FeatureVersion) => FeatureDefinition;
    staticArgumentTransform?: (subgraph: Subgraph, args: {
        [key: string]: any;
    }) => {
        [key: string]: any;
    };
}): DirectiveSpecification;
export declare function createScalarTypeSpecification({ name }: {
    name: string;
}): TypeSpecification;
export declare function createObjectTypeSpecification({ name, fieldsFct, }: {
    name: string;
    fieldsFct: (schema: Schema) => FieldSpecification[];
}): TypeSpecification;
export declare function createUnionTypeSpecification({ name, membersFct, }: {
    name: string;
    membersFct: (schema: Schema) => string[];
}): TypeSpecification;
export declare function createEnumTypeSpecification({ name, values, }: {
    name: string;
    values: {
        name: string;
        description?: string;
    }[];
}): TypeSpecification;
export {};
//# sourceMappingURL=directiveAndTypeSpecification.d.ts.map