{"version": 3, "file": "KnownTypeNamesInFederationRule.js", "sourceRoot": "", "sources": ["../../src/validation/KnownTypeNamesInFederationRule.ts"], "names": [], "mappings": ";;;AAAA,qCAA2N;AAE3N,gDAA4D;AAK5D,SAAgB,8BAA8B,CAC5C,OAAiD;IAEjD,MAAM,MAAM,GAAG,OAAO,CAAC,SAAS,EAAE,CAAC;IACnC,MAAM,gBAAgB,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAE5E,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACzC,KAAK,MAAM,GAAG,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC,WAAW,EAAE,CAAC;QAEpD,IAAI,IAAA,8BAAoB,EAAC,GAAG,CAAC,IAAI,IAAA,6BAAmB,EAAC,GAAG,CAAC,EAAE,CAAC;YAC1D,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;QACtC,CAAC;IACH,CAAC;IAED,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,MAAM,CACpD,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAC1B,CAAC;IAEF,OAAO;QACL,SAAS,CAAC,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,SAAS;;YACvC,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;YACjC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC3D,MAAM,cAAc,GAAG,MAAA,SAAS,CAAC,CAAC,CAAC,mCAAI,MAAM,CAAC;gBAC9C,MAAM,KAAK,GAAG,cAAc,IAAI,IAAI,IAAI,SAAS,CAAC,cAAc,CAAC,CAAC;gBAClE,IAAI,KAAK,IAAI,kBAAkB,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC1C,OAAO;gBACT,CAAC;gBAED,MAAM,cAAc,GAAG,IAAA,4BAAc,EACnC,QAAQ,EACR,KAAK,CAAC,CAAC,CAAC,iBAAiB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CACxD,CAAC;gBACF,OAAO,CAAC,WAAW,CACjB,IAAI,sBAAY,CACd,iBAAiB,QAAQ,IAAI,GAAG,IAAA,wBAAU,EAAC,cAAc,CAAC,EAC1D,EAAE,KAAK,EAAE,IAAI,EAAE,CAChB,CACF,CAAC;YACJ,CAAC;QACH,CAAC;KACF,CAAC;AACJ,CAAC;AAzCD,wEAyCC;AAED,MAAM,iBAAiB,GAAG,CAAC,GAAG,8BAAoB,EAAE,GAAG,4BAAkB,CAAC,CAAC,GAAG,CAC5E,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CACpB,CAAC;AAEF,SAAS,kBAAkB,CAAC,QAAgB;IAC1C,OAAO,iBAAiB,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;AACpD,CAAC;AAED,SAAS,SAAS,CAAC,KAAmC;IACpD,OAAO,CACL,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;QACrB,CAAC,IAAA,oCAA0B,EAAC,KAAgB,CAAC,IAAI,IAAA,mCAAyB,EAAC,KAAgB,CAAC,CAAC,CAC9F,CAAC;AACJ,CAAC"}