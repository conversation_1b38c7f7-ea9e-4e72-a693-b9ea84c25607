export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends {
    [key: string]: unknown;
}> = {
    [K in keyof T]: T[K];
};
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & {
    [SubKey in K]?: Maybe<T[SubKey]>;
};
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & {
    [SubKey in K]: Maybe<T[SubKey]>;
};
export type Scalars = {
    ID: string;
    String: string;
    Boolean: boolean;
    Int: number;
    Float: number;
    Long: any;
    Timestamp: any;
};
export type FetchError = {
    __typename?: 'FetchError';
    code: FetchErrorCode;
    message: Scalars['String'];
    minDelaySeconds: Scalars['Float'];
};
export declare enum FetchErrorCode {
    AccessDenied = "ACCESS_DENIED",
    AuthenticationFailed = "AUTHENTICATION_FAILED",
    NotImplementedOnThisInstance = "NOT_IMPLEMENTED_ON_THIS_INSTANCE",
    RetryLater = "RETRY_LATER",
    UnknownRef = "UNKNOWN_REF"
}
export type Message = {
    __typename?: 'Message';
    body: Scalars['String'];
    level: MessageLevel;
};
export declare enum MessageLevel {
    Error = "ERROR",
    Info = "INFO",
    Warn = "WARN"
}
export type PersistedQueriesChunk = {
    __typename?: 'PersistedQueriesChunk';
    id: Scalars['ID'];
    urls: Array<Scalars['String']>;
};
export type PersistedQueriesResponse = FetchError | PersistedQueriesResult | Unchanged;
export type PersistedQueriesResult = {
    __typename?: 'PersistedQueriesResult';
    chunks?: Maybe<Array<PersistedQueriesChunk>>;
    id: Scalars['ID'];
    minDelaySeconds: Scalars['Float'];
};
export type Query = {
    __typename?: 'Query';
    persistedQueries: PersistedQueriesResponse;
    routerConfig: RouterConfigResponse;
    routerEntitlements: RouterEntitlementsResponse;
};
export type QueryPersistedQueriesArgs = {
    apiKey: Scalars['String'];
    ifAfterId?: InputMaybe<Scalars['ID']>;
    ref: Scalars['String'];
};
export type QueryRouterConfigArgs = {
    apiKey: Scalars['String'];
    ifAfterId?: InputMaybe<Scalars['ID']>;
    ref: Scalars['String'];
};
export type QueryRouterEntitlementsArgs = {
    apiKey: Scalars['String'];
    ifAfterId?: InputMaybe<Scalars['ID']>;
    ref: Scalars['String'];
};
export type RateLimit = {
    __typename?: 'RateLimit';
    count: Scalars['Long'];
    durationMs: Scalars['Long'];
};
export type RouterConfigResponse = FetchError | RouterConfigResult | Unchanged;
export type RouterConfigResult = {
    __typename?: 'RouterConfigResult';
    id: Scalars['ID'];
    messages: Array<Message>;
    minDelaySeconds: Scalars['Float'];
    supergraphSDL: Scalars['String'];
};
export type RouterEntitlement = {
    __typename?: 'RouterEntitlement';
    audience: Array<RouterEntitlementAudience>;
    haltAt?: Maybe<Scalars['Timestamp']>;
    jwt: Scalars['String'];
    subject: Scalars['String'];
    throughputLimit?: Maybe<RateLimit>;
    warnAt?: Maybe<Scalars['Timestamp']>;
};
export declare enum RouterEntitlementAudience {
    Cloud = "CLOUD",
    SelfHosted = "SELF_HOSTED"
}
export type RouterEntitlementsResponse = FetchError | RouterEntitlementsResult | Unchanged;
export type RouterEntitlementsResult = {
    __typename?: 'RouterEntitlementsResult';
    entitlement?: Maybe<RouterEntitlement>;
    id: Scalars['ID'];
    minDelaySeconds: Scalars['Float'];
};
export type Unchanged = {
    __typename?: 'Unchanged';
    id: Scalars['ID'];
    minDelaySeconds: Scalars['Float'];
};
export type SupergraphSdlQueryVariables = Exact<{
    apiKey: Scalars['String'];
    ref: Scalars['String'];
    ifAfterId?: InputMaybe<Scalars['ID']>;
}>;
export type SupergraphSdlQuery = {
    __typename?: 'Query';
    routerConfig: {
        __typename: 'FetchError';
        code: FetchErrorCode;
        message: string;
    } | {
        __typename: 'RouterConfigResult';
        id: string;
        minDelaySeconds: number;
        supergraphSdl: string;
    } | {
        __typename: 'Unchanged';
    };
};
//# sourceMappingURL=graphqlTypes.d.ts.map