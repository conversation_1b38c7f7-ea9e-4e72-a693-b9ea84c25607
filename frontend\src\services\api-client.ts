import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

// API Configuration
const API_BASE_URL = '/api/v1';
const REQUEST_TIMEOUT = 30000; // 30 seconds

// Create axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: REQUEST_TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle common errors
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // Handle 401 errors (unauthorized)
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        // Try to refresh token
        const refreshToken = localStorage.getItem('refreshToken');
        if (refreshToken) {
          const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {
            refreshToken,
          });

          const { token, refreshToken: newRefreshToken } = response.data;
          localStorage.setItem('token', token);
          if (newRefreshToken) {
            localStorage.setItem('refreshToken', newRefreshToken);
          }

          // Retry original request with new token
          originalRequest.headers.Authorization = `Bearer ${token}`;
          return apiClient(originalRequest);
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        localStorage.removeItem('token');
        localStorage.removeItem('refreshToken');
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }

    // Handle other errors
    if (error.response?.status === 403) {
      console.error('Access forbidden:', error.response.data);
    } else if (error.response?.status >= 500) {
      console.error('Server error:', error.response.data);
    }

    return Promise.reject(error);
  }
);

// API methods
export const api = {
  // Generic methods
  get: <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> =>
    apiClient.get(url, config),
  
  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> =>
    apiClient.post(url, data, config),
  
  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> =>
    apiClient.put(url, data, config),
  
  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> =>
    apiClient.patch(url, data, config),
  
  delete: <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> =>
    apiClient.delete(url, config),

  // Auth methods
  auth: {
    login: (credentials: { email: string; password: string }) =>
      apiClient.post('/auth/login', credentials),
    
    logout: () =>
      apiClient.post('/auth/logout'),
    
    refresh: (refreshToken: string) =>
      apiClient.post('/auth/refresh', { refreshToken }),
    
    profile: () =>
      apiClient.get('/auth/profile'),
  },

  // Employee methods
  employees: {
    getAll: (params?: any) =>
      apiClient.get('/employees', { params }),
    
    getById: (id: string) =>
      apiClient.get(`/employees/${id}`),
    
    create: (data: any) =>
      apiClient.post('/employees', data),
    
    update: (id: string, data: any) =>
      apiClient.put(`/employees/${id}`, data),
    
    delete: (id: string) =>
      apiClient.delete(`/employees/${id}`),
  },

  // Department methods
  departments: {
    getAll: () =>
      apiClient.get('/departments'),
    
    getById: (id: string) =>
      apiClient.get(`/departments/${id}`),
    
    create: (data: any) =>
      apiClient.post('/departments', data),
    
    update: (id: string, data: any) =>
      apiClient.put(`/departments/${id}`, data),
    
    delete: (id: string) =>
      apiClient.delete(`/departments/${id}`),
  },

  // Payroll methods
  payroll: {
    getAll: (params?: any) =>
      apiClient.get('/payroll', { params }),
    
    getById: (id: string) =>
      apiClient.get(`/payroll/${id}`),
    
    create: (data: any) =>
      apiClient.post('/payroll', data),
    
    update: (id: string, data: any) =>
      apiClient.put(`/payroll/${id}`, data),
    
    delete: (id: string) =>
      apiClient.delete(`/payroll/${id}`),
  },

  // Dashboard methods
  dashboard: {
    getStats: () =>
      apiClient.get('/dashboard/stats'),
    
    getMetrics: (period?: string) =>
      apiClient.get('/dashboard/metrics', { params: { period } }),
    
    getRecentActivity: () =>
      apiClient.get('/dashboard/activity'),
  },
};

export { apiClient };
export default api;
