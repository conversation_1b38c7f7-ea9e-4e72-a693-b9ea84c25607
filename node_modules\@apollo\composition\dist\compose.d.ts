import { Schema, Subgraphs, PrintOptions, ServiceDefinition, SubtypingRule } from "@apollo/federation-internals";
import { GraphQLError } from "graphql";
import { CompositionHint } from "./hints";
export type CompositionResult = CompositionFailure | CompositionSuccess;
export interface CompositionFailure {
    errors: GraphQLError[];
    schema?: undefined;
    supergraphSdl?: undefined;
    hints?: undefined;
}
export interface CompositionSuccess {
    schema: Schema;
    supergraphSdl: string;
    hints: CompositionHint[];
    errors?: undefined;
}
export interface CompositionOptions {
    sdlPrintOptions?: PrintOptions;
    allowedFieldTypeMergingSubtypingRules?: SubtypingRule[];
    runSatisfiability?: boolean;
    maxValidationSubgraphPaths?: number;
}
export declare function compose(subgraphs: Subgraphs, options?: CompositionOptions): CompositionResult;
export declare function composeServices(services: ServiceDefinition[], options?: CompositionOptions): CompositionResult;
type SatisfiabilityArgs = {
    supergraphSchema: Schema;
    supergraphSdl?: never;
} | {
    supergraphSdl: string;
    supergraphSchema?: never;
};
export declare function validateSatisfiability({ supergraphSchema, supergraphSdl }: SatisfiabilityArgs, options?: CompositionOptions): {
    errors?: GraphQLError[];
    hints?: CompositionHint[];
};
export {};
//# sourceMappingURL=compose.d.ts.map