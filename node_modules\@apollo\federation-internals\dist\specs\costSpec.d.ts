import { FeatureDefinition, FeatureDefinitions, FeatureVersion } from './coreSpec';
import { DirectiveDefinition, Schema } from '../definitions';
export declare const costIdentity = "https://specs.apollo.dev/cost";
export declare class CostSpecDefinition extends FeatureDefinition {
    readonly minimumFederationVersion: FeatureVersion;
    constructor(version: FeatureVersion, minimumFederationVersion: FeatureVersion);
    costDirective(schema: Schema): DirectiveDefinition<CostDirectiveArguments> | undefined;
    listSizeDirective(schema: Schema): DirectiveDefinition<ListSizeDirectiveArguments> | undefined;
}
export declare const COST_VERSIONS: FeatureDefinitions<CostSpecDefinition>;
export interface CostDirectiveArguments {
    weight: number;
}
export interface ListSizeDirectiveArguments {
    assumedSize?: number;
    slicingArguments?: string[];
    sizedFields?: string[];
    requireOneSlicingArgument?: boolean;
}
//# sourceMappingURL=costSpec.d.ts.map