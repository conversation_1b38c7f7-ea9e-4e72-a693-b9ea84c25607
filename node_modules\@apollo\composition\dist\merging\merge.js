"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.mergeSubgraphs = exports.isMergeFailure = exports.isMergeSuccessful = exports.sourcesFromArray = void 0;
const federation_internals_1 = require("@apollo/federation-internals");
const graphql_1 = require("graphql");
const hints_1 = require("../hints");
const composeDirectiveManager_1 = require("../composeDirectiveManager");
const reporter_1 = require("./reporter");
const util_1 = require("util");
const coreDirectiveCollector_1 = require("./coreDirectiveCollector");
function mapSources(sources, mapper) {
    const result = new Map;
    sources.forEach((source, idx) => {
        result.set(idx, mapper(source, idx));
    });
    return result;
}
function filterSources(sources) {
    const result = new Map;
    sources.forEach((source, idx) => {
        if (typeof source !== 'undefined') {
            result.set(idx, source);
        }
    });
    return result;
}
function someSources(sources, predicate) {
    for (const [idx, source] of sources.entries()) {
        if (predicate(source, idx)) {
            return true;
        }
    }
    return false;
}
function sourcesFromArray(array) {
    const sources = new Map;
    array.forEach((source, idx) => {
        sources.set(idx, source);
    });
    return sources;
}
exports.sourcesFromArray = sourcesFromArray;
class FieldMergeContext {
    constructor(sources) {
        this._props = new Map;
        sources.forEach((_, i) => {
            this._props.set(i, {
                usedOverridden: false,
                unusedOverridden: false,
                overrideWithUnknownTarget: false,
                overrideLabel: undefined,
            });
        });
    }
    isUsedOverridden(idx) {
        var _a;
        return !!((_a = this._props.get(idx)) === null || _a === void 0 ? void 0 : _a.usedOverridden);
    }
    isUnusedOverridden(idx) {
        var _a;
        return !!((_a = this._props.get(idx)) === null || _a === void 0 ? void 0 : _a.unusedOverridden);
    }
    hasOverrideWithUnknownTarget(idx) {
        var _a;
        return !!((_a = this._props.get(idx)) === null || _a === void 0 ? void 0 : _a.overrideWithUnknownTarget);
    }
    overrideLabel(idx) {
        var _a;
        return (_a = this._props.get(idx)) === null || _a === void 0 ? void 0 : _a.overrideLabel;
    }
    setUsedOverridden(idx) {
        this._props.get(idx).usedOverridden = true;
    }
    setUnusedOverridden(idx) {
        this._props.get(idx).unusedOverridden = true;
    }
    setOverrideWithUnknownTarget(idx) {
        this._props.get(idx).overrideWithUnknownTarget = true;
    }
    setOverrideLabel(idx, label) {
        this._props.get(idx).overrideLabel = label;
    }
    some(predicate) {
        for (const [i, props] of this._props.entries()) {
            if (predicate(props, i)) {
                return true;
            }
        }
        return false;
    }
}
function isMergeSuccessful(mergeResult) {
    return !isMergeFailure(mergeResult);
}
exports.isMergeSuccessful = isMergeSuccessful;
function isMergeFailure(mergeResult) {
    return !!mergeResult.errors;
}
exports.isMergeFailure = isMergeFailure;
function mergeSubgraphs(subgraphs, options = {}) {
    (0, federation_internals_1.assert)(subgraphs.values().every((s) => s.isFed2Subgraph()), 'Merging should only be applied to federation 2 subgraphs');
    return new Merger(subgraphs, options).merge();
}
exports.mergeSubgraphs = mergeSubgraphs;
function copyTypeReference(source, dest) {
    switch (source.kind) {
        case 'ListType':
            return new federation_internals_1.ListType(copyTypeReference(source.ofType, dest));
        case 'NonNullType':
            return new federation_internals_1.NonNullType(copyTypeReference(source.ofType, dest));
        default:
            const type = dest.type(source.name);
            (0, federation_internals_1.assert)(type, () => `Cannot find type ${source} in destination schema (with types: ${dest.types().join(', ')})`);
            return type;
    }
}
const NON_MERGED_CORE_FEATURES = [federation_internals_1.federationIdentity, federation_internals_1.linkIdentity, federation_internals_1.coreIdentity, federation_internals_1.connectIdentity];
function isMergedType(type) {
    var _a;
    if (type.isIntrospectionType() || federation_internals_1.FEDERATION_OPERATION_TYPES.map((s) => s.name).includes(type.name)) {
        return false;
    }
    const coreFeatures = type.schema().coreFeatures;
    const typeFeature = (_a = coreFeatures === null || coreFeatures === void 0 ? void 0 : coreFeatures.sourceFeature(type)) === null || _a === void 0 ? void 0 : _a.feature.url.identity;
    return !(typeFeature && NON_MERGED_CORE_FEATURES.includes(typeFeature));
}
function isMergedField(field) {
    return field.kind !== 'FieldDefinition' || !(0, federation_internals_1.isFederationField)(field);
}
function isGraphQLBuiltInDirective(def) {
    return !!def.schema().builtInDirective(def.name);
}
function printTypes(types) {
    return (0, federation_internals_1.printHumanReadableList)(types.map((t) => `"${t.coordinate}"`), {
        prefix: 'type',
        prefixPlural: 'types',
    });
}
function filteredRoot(def, rootKind) {
    var _a;
    const type = (_a = def.root(rootKind)) === null || _a === void 0 ? void 0 : _a.type;
    return type && hasMergedFields(type) ? type : undefined;
}
function hasMergedFields(type) {
    for (const field of type.fields()) {
        if (isMergedField(field)) {
            return true;
        }
    }
    return false;
}
function indexOfMax(arr) {
    if (arr.length === 0) {
        return -1;
    }
    let indexOfMax = 0;
    for (let i = 1; i < arr.length; i++) {
        if (arr[i] > arr[indexOfMax]) {
            indexOfMax = i;
        }
    }
    return indexOfMax;
}
function descriptionString(toIndent, indentation) {
    return indentation + '"""\n' + indentation + toIndent.replace('\n', '\n' + indentation) + '\n' + indentation + '"""';
}
function locationString(locations) {
    if (locations.length === 0) {
        return "";
    }
    return (locations.length === 1 ? 'location ' : 'locations ') + '"' + locations.join(', ') + '"';
}
class Merger {
    constructor(subgraphs, options) {
        this.subgraphs = subgraphs;
        this.options = options;
        this.errors = [];
        this.hints = [];
        this.merged = new federation_internals_1.Schema();
        this.mergedFederationDirectiveNames = new Set();
        this.mergedFederationDirectiveInSupergraphByDirectiveName = new Map();
        this.enumUsages = new Map();
        this.joinDirectiveFeatureDefinitionsByIdentity = new Map();
        this.schemaToImportNameToFeatureUrl = new Map();
        this.latestFedVersionUsed = this.getLatestFederationVersionUsed();
        this.joinSpec = federation_internals_1.JOIN_VERSIONS.getMinimumRequiredVersion(this.latestFedVersionUsed);
        this.linkSpec = federation_internals_1.LINK_VERSIONS.getMinimumRequiredVersion(this.latestFedVersionUsed);
        this.fieldsWithFromContext = this.getFieldsWithFromContextDirective();
        this.fieldsWithOverride = this.getFieldsWithOverrideDirective();
        this.names = subgraphs.names();
        this.composeDirectiveManager = new composeDirectiveManager_1.ComposeDirectiveManager(this.subgraphs, (error) => { this.errors.push(error); }, (hint) => { this.hints.push(hint); });
        this.mismatchReporter = new reporter_1.MismatchReporter(this.names, (error) => { this.errors.push(error); }, (hint) => { this.hints.push(hint); });
        this.subgraphsSchema = subgraphs.values().map(({ schema }) => {
            if (!this.schemaToImportNameToFeatureUrl.has(schema)) {
                this.schemaToImportNameToFeatureUrl.set(schema, this.computeMapFromImportNameToIdentityUrl(schema));
            }
            return schema;
        });
        this.subgraphNamesToJoinSpecName = this.prepareSupergraph();
        this.appliedDirectivesToMerge = [];
        this.joinDirectiveFeatureDefinitionsByIdentity.set(federation_internals_1.CONNECT_VERSIONS.identity, federation_internals_1.CONNECT_VERSIONS);
    }
    getLatestFederationVersionUsed() {
        var _a;
        const versions = this.subgraphs.values()
            .map((s) => this.getLatestFederationVersionUsedInSubgraph(s))
            .filter(federation_internals_1.isDefined);
        return (_a = federation_internals_1.FeatureVersion.max(versions)) !== null && _a !== void 0 ? _a : federation_internals_1.FEDERATION_VERSIONS.latest().version;
    }
    getLatestFederationVersionUsedInSubgraph(subgraph) {
        var _a, _b, _c, _d, _e, _f;
        const linkedFederationVersion = (_b = (_a = subgraph.metadata()) === null || _a === void 0 ? void 0 : _a.federationFeature()) === null || _b === void 0 ? void 0 : _b.url.version;
        if (!linkedFederationVersion) {
            return undefined;
        }
        const versionsFromFeatures = [];
        for (const feature of (_d = (_c = subgraph.schema.coreFeatures) === null || _c === void 0 ? void 0 : _c.allFeatures()) !== null && _d !== void 0 ? _d : []) {
            const version = feature.minimumFederationVersion();
            if (version) {
                versionsFromFeatures.push(version);
            }
        }
        const impliedFederationVersion = federation_internals_1.FeatureVersion.max(versionsFromFeatures);
        if (!(impliedFederationVersion === null || impliedFederationVersion === void 0 ? void 0 : impliedFederationVersion.satisfies(linkedFederationVersion)) || linkedFederationVersion.gte(impliedFederationVersion)) {
            return linkedFederationVersion;
        }
        let featureCausingUpgrade;
        for (const feature of (_f = (_e = subgraph.schema.coreFeatures) === null || _e === void 0 ? void 0 : _e.allFeatures()) !== null && _f !== void 0 ? _f : []) {
            if (feature.minimumFederationVersion() == impliedFederationVersion) {
                featureCausingUpgrade = feature;
                break;
            }
        }
        if (featureCausingUpgrade) {
            this.hints.push(new hints_1.CompositionHint(hints_1.HINTS.IMPLICITLY_UPGRADED_FEDERATION_VERSION, `Subgraph ${subgraph.name} has been implicitly upgraded from federation ${linkedFederationVersion} to ${impliedFederationVersion}`, featureCausingUpgrade.directive.definition, featureCausingUpgrade.directive.sourceAST ?
                (0, federation_internals_1.addSubgraphToASTNode)(featureCausingUpgrade.directive.sourceAST, subgraph.name) :
                undefined));
        }
        return impliedFederationVersion;
    }
    prepareSupergraph() {
        this.linkSpec.addToSchema(this.merged);
        const errors = this.linkSpec.applyFeatureToSchema(this.merged, this.joinSpec, undefined, this.joinSpec.defaultCorePurpose);
        (0, federation_internals_1.assert)(errors.length === 0, "We shouldn't have errors adding the join spec to the (still empty) supergraph schema");
        const directivesMergeInfo = (0, coreDirectiveCollector_1.collectCoreDirectivesToCompose)(this.subgraphs);
        this.validateAndMaybeAddSpecs(directivesMergeInfo);
        return this.joinSpec.populateGraphEnum(this.merged, this.subgraphs);
    }
    validateAndMaybeAddSpecs(directivesMergeInfo) {
        var _a, _b;
        const supergraphInfoByIdentity = new Map;
        for (const { url, name, definitionsPerSubgraph, compositionSpec } of directivesMergeInfo) {
            if (!compositionSpec) {
                return;
            }
            let nameInSupergraph;
            for (const subgraph of this.subgraphs) {
                const directive = definitionsPerSubgraph.get(subgraph.name);
                if (!directive) {
                    continue;
                }
                if (!nameInSupergraph) {
                    nameInSupergraph = directive.name;
                }
                else if (nameInSupergraph !== directive.name) {
                    this.mismatchReporter.reportMismatchError(federation_internals_1.ERRORS.LINK_IMPORT_NAME_MISMATCH, `The "@${name}" directive (from ${url}) is imported with mismatched name between subgraphs: it is imported as `, directive, sourcesFromArray(this.subgraphs.values().map((s) => definitionsPerSubgraph.get(s.name))), (def) => `"@${def.name}"`);
                    return;
                }
            }
            if (nameInSupergraph) {
                const specInSupergraph = compositionSpec.supergraphSpecification(this.latestFedVersionUsed);
                let supergraphInfo = supergraphInfoByIdentity.get(specInSupergraph.url.identity);
                if (supergraphInfo) {
                    (0, federation_internals_1.assert)(specInSupergraph.url.equals(supergraphInfo.specInSupergraph.url), `Spec ${specInSupergraph.url} directives disagree on version for supergraph`);
                }
                else {
                    supergraphInfo = {
                        specInSupergraph,
                        directives: [],
                    };
                    supergraphInfoByIdentity.set(specInSupergraph.url.identity, supergraphInfo);
                }
                supergraphInfo.directives.push({
                    nameInFeature: name,
                    nameInSupergraph,
                    compositionSpec,
                });
            }
        }
        for (const { specInSupergraph, directives } of supergraphInfoByIdentity.values()) {
            const imports = [];
            for (const { nameInFeature, nameInSupergraph } of directives) {
                const defaultNameInSupergraph = federation_internals_1.CoreFeature.directiveNameInSchemaForCoreArguments(specInSupergraph.url, specInSupergraph.url.name, [], nameInFeature);
                if (nameInSupergraph !== defaultNameInSupergraph) {
                    imports.push(nameInFeature === nameInSupergraph
                        ? { name: `@${nameInFeature}` }
                        : { name: `@${nameInFeature}`, as: `@${nameInSupergraph}` });
                }
            }
            const errors = this.linkSpec.applyFeatureToSchema(this.merged, specInSupergraph, undefined, specInSupergraph.defaultCorePurpose, imports);
            (0, federation_internals_1.assert)(errors.length === 0, "We shouldn't have errors adding the join spec to the (still empty) supergraph schema");
            const feature = (_a = this.merged.coreFeatures) === null || _a === void 0 ? void 0 : _a.getByIdentity(specInSupergraph.url.identity);
            (0, federation_internals_1.assert)(feature, 'Should have found the feature we just added');
            for (const { nameInFeature, nameInSupergraph, compositionSpec } of directives) {
                const argumentsMerger = (_b = compositionSpec.argumentsMerger) === null || _b === void 0 ? void 0 : _b.call(null, this.merged, feature);
                if (argumentsMerger instanceof graphql_1.GraphQLError) {
                    throw argumentsMerger;
                }
                this.mergedFederationDirectiveNames.add(nameInSupergraph);
                this.mergedFederationDirectiveInSupergraphByDirectiveName.set(nameInSupergraph, {
                    definition: this.merged.directive(nameInSupergraph),
                    argumentsMerger,
                    staticArgumentTransform: compositionSpec.staticArgumentTransform,
                });
                if (specInSupergraph.identity === federation_internals_1.inaccessibleIdentity
                    && nameInFeature === specInSupergraph.url.name) {
                    this.inaccessibleDirectiveInSupergraph = this.merged.directive(nameInSupergraph);
                }
            }
        }
    }
    joinSpecName(subgraphIndex) {
        return this.subgraphNamesToJoinSpecName.get(this.names[subgraphIndex]);
    }
    metadata(idx) {
        return this.subgraphs.values()[idx].metadata();
    }
    isMergedDirective(subgraphName, definition) {
        if (this.composeDirectiveManager.shouldComposeDirective({ subgraphName, directiveName: definition.name })) {
            return true;
        }
        if (definition instanceof federation_internals_1.Directive) {
            return this.mergedFederationDirectiveNames.has(definition.name) || isGraphQLBuiltInDirective(definition.definition);
        }
        else if (isGraphQLBuiltInDirective(definition)) {
            return false;
        }
        return definition.hasExecutableLocations();
    }
    merge() {
        this.composeDirectiveManager.validate();
        this.addCoreFeatures();
        this.addTypesShallow();
        this.addDirectivesShallow();
        const objectTypes = [];
        const interfaceTypes = [];
        const unionTypes = [];
        const enumTypes = [];
        const nonUnionEnumTypes = [];
        this.merged.types().forEach(type => {
            if (this.linkSpec.isSpecType(type) ||
                this.joinSpec.isSpecType(type)) {
                return;
            }
            switch (type.kind) {
                case 'UnionType':
                    unionTypes.push(type);
                    return;
                case 'EnumType':
                    enumTypes.push(type);
                    return;
                case 'ObjectType':
                    objectTypes.push(type);
                    break;
                case 'InterfaceType':
                    interfaceTypes.push(type);
                    break;
            }
            nonUnionEnumTypes.push(type);
        });
        for (const objectType of objectTypes) {
            this.mergeImplements(this.subgraphsTypes(objectType), objectType);
        }
        for (const interfaceType of interfaceTypes) {
            this.mergeImplements(this.subgraphsTypes(interfaceType), interfaceType);
        }
        for (const unionType of unionTypes) {
            this.mergeType(this.subgraphsTypes(unionType), unionType);
        }
        this.mergeSchemaDefinition(sourcesFromArray(this.subgraphsSchema.map(s => s.schemaDefinition)), this.merged.schemaDefinition);
        for (const type of nonUnionEnumTypes) {
            this.mergeType(this.subgraphsTypes(type), type);
        }
        for (const definition of this.merged.directives()) {
            if (this.linkSpec.isSpecDirective(definition) || this.joinSpec.isSpecDirective(definition)) {
                continue;
            }
            this.mergeDirectiveDefinition(sourcesFromArray(this.subgraphsSchema.map(s => s.directive(definition.name))), definition);
        }
        for (const enumType of enumTypes) {
            this.mergeType(this.subgraphsTypes(enumType), enumType);
        }
        if (!this.merged.schemaDefinition.rootType('query')) {
            this.errors.push(federation_internals_1.ERRORS.NO_QUERIES.err("No queries found in any subgraph: a supergraph must have a query root type."));
        }
        this.mergeAllAppliedDirectives();
        this.addMissingInterfaceObjectFieldsToImplementations();
        if (this.errors.length === 0) {
            this.postMergeValidations();
            if (this.errors.length === 0) {
                try {
                    this.merged.validate();
                    this.merged.toAPISchema();
                }
                catch (e) {
                    const causes = (0, federation_internals_1.errorCauses)(e);
                    if (causes) {
                        this.errors.push(...this.updateInaccessibleErrorsWithLinkToSubgraphs(causes));
                    }
                    else {
                        throw e;
                    }
                }
            }
        }
        if (this.errors.length > 0) {
            return { errors: this.errors };
        }
        else {
            return {
                supergraph: this.merged,
                hints: this.hints
            };
        }
    }
    addTypesShallow() {
        const mismatchedTypes = new Set();
        const typesWithInterfaceObject = new Set();
        for (const subgraph of this.subgraphs) {
            const metadata = subgraph.metadata();
            for (const type of subgraph.schema.allTypes()) {
                if (!isMergedType(type)) {
                    continue;
                }
                let expectedKind = type.kind;
                if (metadata.isInterfaceObjectType(type)) {
                    expectedKind = 'InterfaceType';
                    typesWithInterfaceObject.add(type.name);
                }
                const previous = this.merged.type(type.name);
                if (!previous) {
                    this.merged.addType((0, federation_internals_1.newNamedType)(expectedKind, type.name));
                }
                else if (previous.kind !== expectedKind) {
                    mismatchedTypes.add(type.name);
                }
            }
        }
        mismatchedTypes.forEach(t => this.reportMismatchedTypeDefinitions(t));
        for (const itfObjectType of typesWithInterfaceObject) {
            if (mismatchedTypes.has(itfObjectType)) {
                continue;
            }
            if (!this.subgraphsSchema.some((s) => { var _a; return ((_a = s.type(itfObjectType)) === null || _a === void 0 ? void 0 : _a.kind) === 'InterfaceType'; })) {
                const subgraphsWithType = this.subgraphs.values().filter((s) => s.schema.type(itfObjectType) !== undefined);
                this.errors.push(federation_internals_1.ERRORS.INTERFACE_OBJECT_USAGE_ERROR.err(`Type "${itfObjectType}" is declared with @interfaceObject in all the subgraphs in which is is defined (it is defined in ${(0, federation_internals_1.printSubgraphNames)(subgraphsWithType.map((s) => s.name))} but should be defined as an interface in at least one subgraph)`, { nodes: (0, federation_internals_1.sourceASTs)(...subgraphsWithType.map((s) => s.schema.type(itfObjectType))) }));
            }
        }
    }
    addCoreFeatures() {
        const features = this.composeDirectiveManager.allComposedCoreFeatures();
        for (const [feature, directives] of features) {
            const imports = directives.map(([asName, origName]) => {
                if (asName === origName) {
                    return `@${asName}`;
                }
                else {
                    return {
                        name: `@${origName}`,
                        as: `@${asName}`,
                    };
                }
            });
            this.merged.schemaDefinition.applyDirective('link', {
                url: feature.url.toString(),
                import: imports,
            });
        }
    }
    addDirectivesShallow() {
        this.subgraphsSchema.forEach((subgraph, idx) => {
            for (const directive of subgraph.allDirectives()) {
                if (!this.isMergedDirective(this.names[idx], directive)) {
                    continue;
                }
                if (!this.merged.directive(directive.name)) {
                    this.merged.addDirectiveDefinition(new federation_internals_1.DirectiveDefinition(directive.name));
                }
            }
        });
    }
    reportMismatchedTypeDefinitions(mismatchedType) {
        const supergraphType = this.merged.type(mismatchedType);
        const typeKindToString = (t) => {
            const metadata = (0, federation_internals_1.federationMetadata)(t.schema());
            if (metadata === null || metadata === void 0 ? void 0 : metadata.isInterfaceObjectType(t)) {
                return 'Interface Object Type (Object Type with @interfaceObject)';
            }
            else {
                return t.kind.replace("Type", " Type");
            }
        };
        this.mismatchReporter.reportMismatchError(federation_internals_1.ERRORS.TYPE_KIND_MISMATCH, `Type "${mismatchedType}" has mismatched kind: it is defined as `, supergraphType, sourcesFromArray(this.subgraphsSchema.map(s => s.type(mismatchedType))), typeKindToString);
    }
    subgraphsTypes(supergraphType) {
        return sourcesFromArray(this.subgraphs.values().map(subgraph => {
            const type = subgraph.schema.type(supergraphType.name);
            if (!type) {
                return;
            }
            const kind = subgraph.metadata().isInterfaceObjectType(type) ? 'InterfaceType' : type.kind;
            if (kind !== supergraphType.kind) {
                return;
            }
            return type;
        }));
    }
    mergeImplements(sources, dest) {
        const implemented = new Set();
        const joinImplementsDirective = this.joinSpec.implementsDirective(this.merged);
        for (const [idx, source] of sources.entries()) {
            if (source) {
                const name = this.joinSpecName(idx);
                for (const itf of source.interfaces()) {
                    implemented.add(itf.name);
                    dest.applyDirective(joinImplementsDirective, { graph: name, interface: itf.name });
                }
            }
        }
        implemented.forEach(itf => dest.addImplementedInterface(itf));
    }
    mergeDescription(sources, dest) {
        const descriptions = [];
        const counts = [];
        for (const source of sources.values()) {
            if (!source || source.description === undefined) {
                continue;
            }
            const idx = descriptions.indexOf(source.description);
            if (idx < 0) {
                descriptions.push(source.description);
                counts.push(source.description === '' ? Number.MIN_SAFE_INTEGER : 1);
            }
            else {
                counts[idx]++;
            }
        }
        if (descriptions.length > 0) {
            const nonEmptyDescriptions = descriptions.filter(desc => desc !== '');
            if (descriptions.length === 1) {
                dest.description = descriptions[0];
            }
            else if (nonEmptyDescriptions.length === 1) {
                dest.description = nonEmptyDescriptions[0];
            }
            else {
                const idx = indexOfMax(counts);
                dest.description = descriptions[idx];
                const name = dest instanceof federation_internals_1.NamedSchemaElement ? `Element "${dest.coordinate}"` : 'The schema definition';
                this.mismatchReporter.reportMismatchHint({
                    code: hints_1.HINTS.INCONSISTENT_DESCRIPTION,
                    message: `${name} has inconsistent descriptions across subgraphs. `,
                    supergraphElement: dest,
                    subgraphElements: sources,
                    elementToString: elt => elt.description,
                    supergraphElementPrinter: (desc, subgraphs) => `The supergraph will use description (from ${subgraphs}):\n${descriptionString(desc, '  ')}`,
                    otherElementsPrinter: (desc, subgraphs) => `\nIn ${subgraphs}, the description is:\n${descriptionString(desc, '  ')}`,
                    ignorePredicate: elt => (elt === null || elt === void 0 ? void 0 : elt.description) === undefined,
                    noEndOfMessageDot: true,
                });
            }
        }
    }
    mergeType(sources, dest) {
        this.checkForExtensionWithNoBase(sources, dest);
        this.mergeDescription(sources, dest);
        this.addJoinType(sources, dest);
        this.recordAppliedDirectivesToMerge(sources, dest);
        this.addJoinDirectiveDirectives(sources, dest);
        switch (dest.kind) {
            case 'ScalarType':
                break;
            case 'ObjectType':
                this.mergeObject(sources, dest);
                break;
            case 'InterfaceType':
                this.mergeInterface(sources, dest);
                break;
            case 'UnionType':
                this.mergeUnion(sources, dest);
                break;
            case 'EnumType':
                this.mergeEnum(sources, dest);
                break;
            case 'InputObjectType':
                this.mergeInput(sources, dest);
                break;
        }
    }
    checkForExtensionWithNoBase(sources, dest) {
        if ((0, federation_internals_1.isObjectType)(dest) && dest.isRootType()) {
            return;
        }
        const defSubgraphs = [];
        const extensionSubgraphs = [];
        const extensionASTs = [];
        for (const [i, source] of sources.entries()) {
            if (!source) {
                continue;
            }
            if (source.hasNonExtensionElements()) {
                defSubgraphs.push(this.names[i]);
            }
            if (source.hasExtensionElements()) {
                extensionSubgraphs.push(this.names[i]);
                extensionASTs.push((0, federation_internals_1.firstOf)(source.extensions().values()).sourceAST);
            }
        }
        if (extensionSubgraphs.length > 0 && defSubgraphs.length === 0) {
            for (const [i, subgraph] of extensionSubgraphs.entries()) {
                this.errors.push(federation_internals_1.ERRORS.EXTENSION_WITH_NO_BASE.err(`[${subgraph}] Type "${dest}" is an extension type, but there is no type definition for "${dest}" in any subgraph.`, { nodes: extensionASTs[i] }));
            }
        }
    }
    addJoinType(sources, dest) {
        const joinTypeDirective = this.joinSpec.typeDirective(this.merged);
        for (const [idx, source] of sources.entries()) {
            if (!source) {
                continue;
            }
            const sourceMetadata = this.subgraphs.values()[idx].metadata();
            const isInterfaceObject = sourceMetadata.isInterfaceObjectType(source) ? true : undefined;
            const keys = source.appliedDirectivesOf(sourceMetadata.keyDirective());
            const name = this.joinSpecName(idx);
            if (!keys.length) {
                dest.applyDirective(joinTypeDirective, { graph: name, isInterfaceObject });
            }
            else {
                for (const key of keys) {
                    const extension = key.ofExtension() || source.hasAppliedDirective(sourceMetadata.extendsDirective()) ? true : undefined;
                    const { resolvable } = key.arguments();
                    dest.applyDirective(joinTypeDirective, { graph: name, key: key.arguments().fields, extension, resolvable, isInterfaceObject });
                }
            }
        }
    }
    mergeObject(sources, dest) {
        const isEntity = this.hintOnInconsistentEntity(sources, dest);
        const isValueType = !isEntity && !dest.isRootType();
        const isSubscription = dest.isSubscriptionRootType();
        const added = this.addFieldsShallow(sources, dest);
        if (!added.size) {
            dest.remove();
        }
        else {
            added.forEach((subgraphFields, destField) => {
                if (isValueType) {
                    this.hintOnInconsistentValueTypeField(sources, dest, destField);
                }
                const mergeContext = this.validateOverride(subgraphFields, destField);
                if (isSubscription) {
                    this.validateSubscriptionField(subgraphFields);
                }
                this.mergeField({
                    sources: subgraphFields,
                    dest: destField,
                    mergeContext,
                });
                this.validateFieldSharing(subgraphFields, destField, mergeContext);
            });
        }
    }
    hintOnInconsistentEntity(sources, dest) {
        const sourceAsEntity = [];
        const sourceAsNonEntity = [];
        for (const [idx, source] of sources.entries()) {
            if (!source) {
                continue;
            }
            const sourceMetadata = this.subgraphs.values()[idx].metadata();
            const keyDirective = sourceMetadata.keyDirective();
            if (source.hasAppliedDirective(keyDirective)) {
                sourceAsEntity.push(source);
            }
            else {
                sourceAsNonEntity.push(source);
            }
        }
        if (sourceAsEntity.length > 0 && sourceAsNonEntity.length > 0) {
            this.mismatchReporter.reportMismatchHint({
                code: hints_1.HINTS.INCONSISTENT_ENTITY,
                message: `Type "${dest}" is declared as an entity (has a @key applied) in some but not all defining subgraphs: `,
                supergraphElement: dest,
                subgraphElements: sources,
                elementToString: type => sourceAsEntity.find(entity => entity === type) ? 'yes' : 'no',
                supergraphElementPrinter: (_, subgraphs) => `it has no @key in ${subgraphs}`,
                otherElementsPrinter: (_, subgraphs) => ` but has some @key in ${subgraphs}`,
            });
        }
        return sourceAsEntity.length > 0;
    }
    hintOnInconsistentValueTypeField(sources, dest, field) {
        let hintId;
        let typeDescription;
        switch (dest.kind) {
            case 'ObjectType':
                hintId = hints_1.HINTS.INCONSISTENT_OBJECT_VALUE_TYPE_FIELD;
                typeDescription = 'non-entity object';
                break;
            case 'InterfaceType':
                hintId = hints_1.HINTS.INCONSISTENT_INTERFACE_VALUE_TYPE_FIELD;
                typeDescription = 'interface';
                break;
        }
        for (const [index, source] of sources.entries()) {
            if (source && !source.field(field.name) && !this.areAllFieldsExternal(index, source)) {
                this.mismatchReporter.reportMismatchHint({
                    code: hintId,
                    message: `Field "${field.coordinate}" of ${typeDescription} type "${dest}" is defined in some but not all subgraphs that define "${dest}": `,
                    supergraphElement: dest,
                    subgraphElements: sources,
                    elementToString: type => type.field(field.name) ? 'yes' : 'no',
                    supergraphElementPrinter: (_, subgraphs) => `"${field.coordinate}" is defined in ${subgraphs}`,
                    otherElementsPrinter: (_, subgraphs) => ` but not in ${subgraphs}`,
                });
                break;
            }
        }
    }
    addMissingInterfaceObjectFieldsToImplementations() {
        for (const type of this.merged.objectTypes()) {
            for (const implementedItf of type.interfaces()) {
                for (const itfField of implementedItf.fields()) {
                    if (type.field(itfField.name)) {
                        continue;
                    }
                    if (this.isFieldProvidedByAnInterfaceObject(itfField.name, implementedItf.name)) {
                        const implemField = type.addField(itfField.name, itfField.type);
                        implemField.description = itfField.description;
                        this.copyNonJoinAppliedDirectives(itfField, implemField);
                        for (const itfArg of itfField.arguments()) {
                            const implemArg = implemField.addArgument(itfArg.name, itfArg.type, itfArg.defaultValue);
                            implemArg.description = itfArg.description;
                            this.copyNonJoinAppliedDirectives(itfArg, implemArg);
                        }
                        implemField.applyDirective(this.joinSpec.fieldDirective(this.merged), { graph: undefined });
                        const sources = new Map;
                        for (let i = 0; i < this.names.length; ++i) {
                            sources.set(i, undefined);
                        }
                        this.validateFieldSharing(sources, implemField, new FieldMergeContext(sources));
                    }
                }
            }
        }
    }
    copyNonJoinAppliedDirectives(source, dest) {
        source.appliedDirectives.forEach((d) => {
            if (!this.joinSpec.isSpecDirective(d.definition)) {
                dest.applyDirective(d.name, { ...d.arguments() });
            }
        });
    }
    isFieldProvidedByAnInterfaceObject(fieldName, interfaceName) {
        return this.subgraphs.values().some((s) => {
            const meta = s.metadata();
            const type = s.schema.type(interfaceName);
            const field = type && meta.isInterfaceObjectType(type) ? type.field(fieldName) : undefined;
            return field && !meta.isFieldExternal(field);
        });
    }
    addFieldsShallow(sources, dest) {
        const added = new Map();
        const fieldsToAdd = new Map();
        function fieldSet(sourceIndex) {
            let set = fieldsToAdd.get(sourceIndex);
            if (!set)
                fieldsToAdd.set(sourceIndex, set = new Set);
            return set;
        }
        const extraSources = new Map;
        sources.forEach((source, sourceIndex) => {
            const schema = this.subgraphsSchema[sourceIndex];
            const fields = fieldSet(sourceIndex);
            if ((0, federation_internals_1.isObjectType)(dest) || (0, federation_internals_1.isInterfaceType)(dest)) {
                for (const itf of dest.interfaces()) {
                    const itfType = schema.type(itf.name);
                    const subgraph = this.subgraphs.get(this.names[sourceIndex]);
                    if (itfType &&
                        (0, federation_internals_1.isObjectType)(itfType) &&
                        (subgraph === null || subgraph === void 0 ? void 0 : subgraph.metadata().isInterfaceObjectType(itfType))) {
                        extraSources.set(sourceIndex, undefined);
                    }
                }
            }
            if (source) {
                for (const field of source.fields()) {
                    fields.add(field);
                }
            }
            if (schema.type(dest.name)) {
                extraSources.set(sourceIndex, undefined);
            }
        });
        fieldsToAdd.forEach((fieldSet, sourceIndex) => {
            fieldSet.forEach(field => {
                if (field && isMergedField(field)) {
                    const destField = dest.field(field.name) || dest.addField(field.name);
                    let sources = added.get(destField);
                    if (!sources) {
                        sources = new Map(extraSources);
                        added.set(destField, sources);
                    }
                    sources.set(sourceIndex, field);
                }
            });
        });
        return added;
    }
    isExternal(sourceIdx, field) {
        return this.metadata(sourceIdx).isFieldExternal(field);
    }
    isFullyExternal(sourceIdx, field) {
        return this.metadata(sourceIdx).isFieldFullyExternal(field);
    }
    areAllFieldsExternal(sourceIdx, type) {
        return type.fields().every(f => this.isExternal(sourceIdx, f));
    }
    validateAndFilterExternal(sources) {
        const filtered = new Map;
        for (const [i, source] of sources.entries()) {
            if (!source || !this.isExternal(i, source)) {
                filtered.set(i, source);
            }
            else {
                filtered.set(i, undefined);
                for (const directive of source.appliedDirectives) {
                    if (this.isMergedDirective(source.name, directive)) {
                        this.errors.push(federation_internals_1.ERRORS.MERGED_DIRECTIVE_APPLICATION_ON_EXTERNAL.err(`[${this.names[i]}] Cannot apply merged directive ${directive} to external field "${source.coordinate}"`, { nodes: directive.sourceAST }));
                    }
                }
            }
        }
        return filtered;
    }
    hasExternal(sources) {
        for (const [i, source] of sources.entries()) {
            if (source && this.isExternal(i, source)) {
                return true;
            }
        }
        return false;
    }
    isShareable(sourceIdx, field) {
        return this.metadata(sourceIdx).isFieldShareable(field);
    }
    getOverrideDirective(sourceIdx, field) {
        const metadata = this.metadata(sourceIdx);
        const overrideDirective = metadata.isFed2Schema() ? metadata.overrideDirective() : undefined;
        const allFieldOverrides = overrideDirective ? field.appliedDirectivesOf(overrideDirective) : [];
        return allFieldOverrides[0];
    }
    overrideConflictsWithOtherDirective({ idx, field, subgraphName, fromIdx, fromField, }) {
        const fromMetadata = this.metadata(fromIdx);
        for (const directive of [fromMetadata.requiresDirective(), fromMetadata.providesDirective()]) {
            if (fromField === null || fromField === void 0 ? void 0 : fromField.hasAppliedDirective(directive)) {
                return {
                    result: true,
                    conflictingDirective: directive,
                    subgraph: this.names[fromIdx],
                };
            }
        }
        if (field && this.isExternal(idx, field)) {
            return {
                result: true,
                conflictingDirective: fromMetadata.externalDirective(),
                subgraph: subgraphName,
            };
        }
        return { result: false };
    }
    validateOverride(sources, dest) {
        const result = new FieldMergeContext(sources);
        if (!this.fieldsWithOverride.has(dest.coordinate)) {
            return result;
        }
        const mapped = mapSources(sources, (source, idx) => {
            if (!source) {
                const interfaceObjectAbstractingFields = this.fieldsInSourceIfAbstractedByInterfaceObject(dest, idx);
                if (interfaceObjectAbstractingFields.length > 0) {
                    return {
                        idx,
                        name: this.names[idx],
                        interfaceObjectAbstractingFields,
                    };
                }
                return undefined;
            }
            return {
                idx,
                name: this.names[idx],
                isInterfaceField: (0, federation_internals_1.isInterfaceType)(source.parent),
                isInterfaceObject: this.metadata(idx).isInterfaceObjectType(source.parent),
                overrideDirective: this.getOverrideDirective(idx, source),
            };
        });
        const { subgraphsWithOverride, subgraphMap } = Array.from(mapped.values()).reduce((acc, elem) => {
            if (elem !== undefined) {
                acc.subgraphMap[elem.name] = elem;
                if (elem.overrideDirective !== undefined) {
                    acc.subgraphsWithOverride.push(elem.name);
                }
            }
            return acc;
        }, { subgraphsWithOverride: [], subgraphMap: {} });
        subgraphsWithOverride.forEach((subgraphName) => {
            const { overrideDirective, idx, isInterfaceObject, isInterfaceField } = subgraphMap[subgraphName];
            if (!overrideDirective)
                return;
            const overridingSubgraphASTNode = overrideDirective.sourceAST ? (0, federation_internals_1.addSubgraphToASTNode)(overrideDirective.sourceAST, subgraphName) : undefined;
            if (isInterfaceField) {
                this.errors.push(federation_internals_1.ERRORS.OVERRIDE_ON_INTERFACE.err(`@override cannot be used on field "${dest.coordinate}" on subgraph "${subgraphName}": @override is not supported on interface type fields.`, { nodes: overridingSubgraphASTNode }));
                return;
            }
            if (isInterfaceObject) {
                this.errors.push(federation_internals_1.ERRORS.OVERRIDE_COLLISION_WITH_ANOTHER_DIRECTIVE.err(`@override is not yet supported on fields of @interfaceObject types: cannot be used on field "${dest.coordinate}" on subgraph "${subgraphName}".`, { nodes: overridingSubgraphASTNode }));
                return;
            }
            const sourceSubgraphName = overrideDirective.arguments().from;
            if (!this.names.includes(sourceSubgraphName)) {
                result.setOverrideWithUnknownTarget(idx);
                const suggestions = (0, federation_internals_1.suggestionList)(sourceSubgraphName, this.names);
                const extraMsg = (0, federation_internals_1.didYouMean)(suggestions);
                this.hints.push(new hints_1.CompositionHint(hints_1.HINTS.FROM_SUBGRAPH_DOES_NOT_EXIST, `Source subgraph "${sourceSubgraphName}" for field "${dest.coordinate}" on subgraph "${subgraphName}" does not exist.${extraMsg}`, dest, overridingSubgraphASTNode));
            }
            else if (sourceSubgraphName === subgraphName) {
                this.errors.push(federation_internals_1.ERRORS.OVERRIDE_FROM_SELF_ERROR.err(`Source and destination subgraphs "${sourceSubgraphName}" are the same for overridden field "${dest.coordinate}"`, { nodes: overrideDirective.sourceAST }));
            }
            else if (subgraphsWithOverride.includes(sourceSubgraphName)) {
                this.errors.push(federation_internals_1.ERRORS.OVERRIDE_SOURCE_HAS_OVERRIDE.err(`Field "${dest.coordinate}" on subgraph "${subgraphName}" is also marked with directive @override in subgraph "${sourceSubgraphName}". Only one @override directive is allowed per field.`, { nodes: (0, federation_internals_1.sourceASTs)(overrideDirective, subgraphMap[sourceSubgraphName].overrideDirective) }));
            }
            else if (subgraphMap[sourceSubgraphName] === undefined) {
                this.hints.push(new hints_1.CompositionHint(hints_1.HINTS.OVERRIDE_DIRECTIVE_CAN_BE_REMOVED, `Field "${dest.coordinate}" on subgraph "${subgraphName}" no longer exists in the from subgraph. The @override directive can be removed.`, dest, overridingSubgraphASTNode));
            }
            else {
                const { interfaceObjectAbstractingFields } = subgraphMap[sourceSubgraphName];
                if (interfaceObjectAbstractingFields) {
                    const abstractingTypes = printTypes(interfaceObjectAbstractingFields.map((f) => f.parent));
                    this.errors.push(federation_internals_1.ERRORS.OVERRIDE_COLLISION_WITH_ANOTHER_DIRECTIVE.err(`Invalid @override on field "${dest.coordinate}" of subgraph "${subgraphName}": source subgraph "${sourceSubgraphName}" does not have field "${dest.coordinate}" but abstract it in ${abstractingTypes} and overriding abstracted fields is not supported.`, { nodes: (0, federation_internals_1.sourceASTs)(overrideDirective, subgraphMap[sourceSubgraphName].overrideDirective) }));
                    return;
                }
                const fromIdx = this.names.indexOf(sourceSubgraphName);
                const fromField = sources.get(fromIdx);
                const { result: hasIncompatible, conflictingDirective, subgraph } = this.overrideConflictsWithOtherDirective({
                    idx,
                    field: sources.get(idx),
                    subgraphName,
                    fromIdx: this.names.indexOf(sourceSubgraphName),
                    fromField: sources.get(fromIdx),
                });
                if (hasIncompatible) {
                    (0, federation_internals_1.assert)(conflictingDirective !== undefined, 'conflictingDirective should not be undefined');
                    this.errors.push(federation_internals_1.ERRORS.OVERRIDE_COLLISION_WITH_ANOTHER_DIRECTIVE.err(`@override cannot be used on field "${fromField === null || fromField === void 0 ? void 0 : fromField.coordinate}" on subgraph "${subgraphName}" since "${fromField === null || fromField === void 0 ? void 0 : fromField.coordinate}" on "${subgraph}" is marked with directive "@${conflictingDirective.name}"`, { nodes: (0, federation_internals_1.sourceASTs)(overrideDirective, conflictingDirective) }));
                }
                else {
                    (0, federation_internals_1.assert)(fromField, 'fromField should not be undefined');
                    const overriddenSubgraphASTNode = fromField.sourceAST ? (0, federation_internals_1.addSubgraphToASTNode)(fromField.sourceAST, sourceSubgraphName) : undefined;
                    const overrideLabel = overrideDirective.arguments().label;
                    const overriddenFieldIsReferenced = !!this.metadata(fromIdx).isFieldUsed(fromField);
                    if (this.isExternal(fromIdx, fromField)) {
                        this.hints.push(new hints_1.CompositionHint(hints_1.HINTS.OVERRIDE_DIRECTIVE_CAN_BE_REMOVED, `Field "${dest.coordinate}" on subgraph "${subgraphName}" is not resolved anymore by the from subgraph (it is marked "@external" in "${sourceSubgraphName}"). The @override directive can be removed.`, dest, overridingSubgraphASTNode));
                    }
                    else if (overriddenFieldIsReferenced) {
                        result.setUsedOverridden(fromIdx);
                        if (!overrideLabel) {
                            this.hints.push(new hints_1.CompositionHint(hints_1.HINTS.OVERRIDDEN_FIELD_CAN_BE_REMOVED, `Field "${dest.coordinate}" on subgraph "${sourceSubgraphName}" is overridden. It is still used in some federation directive(s) (@key, @requires, and/or @provides) and/or to satisfy interface constraint(s), but consider marking it @external explicitly or removing it along with its references.`, dest, overriddenSubgraphASTNode));
                        }
                    }
                    else {
                        result.setUnusedOverridden(fromIdx);
                        if (!overrideLabel) {
                            this.hints.push(new hints_1.CompositionHint(hints_1.HINTS.OVERRIDDEN_FIELD_CAN_BE_REMOVED, `Field "${dest.coordinate}" on subgraph "${sourceSubgraphName}" is overridden. Consider removing it.`, dest, overriddenSubgraphASTNode));
                        }
                    }
                    if (overrideLabel) {
                        const labelRegex = /^[a-zA-Z][a-zA-Z0-9_\-:./]*$/;
                        const percentRegex = /^percent\((\d{1,2}(\.\d{1,8})?|100)\)$/;
                        if (labelRegex.test(overrideLabel)) {
                            result.setOverrideLabel(idx, overrideLabel);
                            result.setOverrideLabel(fromIdx, overrideLabel);
                        }
                        else if (percentRegex.test(overrideLabel)) {
                            const parts = percentRegex.exec(overrideLabel);
                            if (parts) {
                                const percent = parseFloat(parts[1]);
                                if (percent >= 0 && percent <= 100) {
                                    result.setOverrideLabel(idx, overrideLabel);
                                    result.setOverrideLabel(fromIdx, overrideLabel);
                                }
                            }
                        }
                        if (!result.overrideLabel(idx)) {
                            this.errors.push(federation_internals_1.ERRORS.OVERRIDE_LABEL_INVALID.err(`Invalid @override label "${overrideLabel}" on field "${dest.coordinate}" on subgraph "${subgraphName}": labels must start with a letter and after that may contain alphanumerics, underscores, minuses, colons, periods, or slashes. Alternatively, labels may be of the form "percent(x)" where x is a float between 0-100 inclusive.`, { nodes: overridingSubgraphASTNode }));
                        }
                        const message = overriddenFieldIsReferenced
                            ? `Field "${dest.coordinate}" on subgraph "${sourceSubgraphName}" is currently being migrated via progressive @override. It is still used in some federation directive(s) (@key, @requires, and/or @provides) and/or to satisfy interface constraint(s). Once the migration is complete, consider marking it @external explicitly or removing it along with its references.`
                            : `Field "${dest.coordinate}" is currently being migrated with progressive @override. Once the migration is complete, remove the field from subgraph "${sourceSubgraphName}".`;
                        this.hints.push(new hints_1.CompositionHint(hints_1.HINTS.OVERRIDE_MIGRATION_IN_PROGRESS, message, dest, overriddenSubgraphASTNode));
                    }
                }
            }
        });
        return result;
    }
    fieldsInSourceIfAbstractedByInterfaceObject(destField, sourceIdx) {
        const parentInSupergraph = destField.parent;
        const schema = this.subgraphsSchema[sourceIdx];
        if (!(0, federation_internals_1.isObjectType)(parentInSupergraph) || schema.type(parentInSupergraph.name)) {
            return [];
        }
        return parentInSupergraph.interfaces().map((itfType) => {
            if (!itfType.field(destField.name)) {
                return undefined;
            }
            const typeInSchema = schema.type(itfType.name);
            if (!typeInSchema || !(0, federation_internals_1.isObjectType)(typeInSchema)) {
                return undefined;
            }
            return typeInSchema.field(destField.name);
        }).filter(federation_internals_1.isDefined);
    }
    mergeField({ sources, dest, mergeContext = new FieldMergeContext(sources), }) {
        let everySourceIsExternal = true;
        for (const [i, s] of sources.entries()) {
            if (s === undefined) {
                everySourceIsExternal = everySourceIsExternal &&
                    this.fieldsInSourceIfAbstractedByInterfaceObject(dest, i)
                        .every((f) => this.isExternal(i, f));
            }
            else {
                everySourceIsExternal = everySourceIsExternal && this.isExternal(i, s);
            }
            if (!everySourceIsExternal) {
                break;
            }
        }
        if (everySourceIsExternal) {
            const nodes = [];
            const definingSubgraphs = [];
            sources.forEach((source, i) => {
                if (source) {
                    definingSubgraphs.push(this.names[i]);
                    if (source.sourceAST) {
                        nodes.push(source.sourceAST);
                    }
                    return;
                }
                const itfObjectFields = this.fieldsInSourceIfAbstractedByInterfaceObject(dest, i);
                if (itfObjectFields.length === 0) {
                    return;
                }
                definingSubgraphs.push(`${this.names[i]} (through @interaceObject ${printTypes(itfObjectFields.map((f) => f.parent))})`);
            });
            this.errors.push(federation_internals_1.ERRORS.EXTERNAL_MISSING_ON_BASE.err(`Field "${dest.coordinate}" is marked @external on all the subgraphs in which it is listed (${(0, federation_internals_1.printSubgraphNames)(definingSubgraphs)}).`, { nodes }));
            return;
        }
        const withoutExternal = this.validateAndFilterExternal(sources);
        this.mergeDescription(withoutExternal, dest);
        this.recordAppliedDirectivesToMerge(withoutExternal, dest);
        this.addArgumentsShallow(withoutExternal, dest);
        for (const destArg of dest.arguments()) {
            const subgraphArgs = mapSources(withoutExternal, f => f === null || f === void 0 ? void 0 : f.argument(destArg.name));
            this.mergeArgument(subgraphArgs, destArg);
        }
        const allTypesEqual = this.mergeTypeReference(someSources(withoutExternal, federation_internals_1.isDefined) ? withoutExternal : sources, dest);
        if (this.hasExternal(sources)) {
            this.validateExternalFields(sources, dest, allTypesEqual);
        }
        this.addJoinField({ sources, dest, allTypesEqual, mergeContext });
        this.addJoinDirectiveDirectives(sources, dest);
    }
    validateFieldSharing(sources, dest, mergeContext) {
        const shareableSources = [];
        const nonShareableSources = [];
        const allResolving = [];
        const categorizeField = (idx, subgraph, field) => {
            if (!this.isFullyExternal(idx, field)) {
                allResolving.push({ subgraph, field });
                if (this.isShareable(idx, field)) {
                    shareableSources.push({ subgraph, idx });
                }
                else {
                    nonShareableSources.push({ subgraph, idx });
                }
            }
        };
        for (const [i, source] of sources.entries()) {
            const subgraph = '"' + this.names[i] + '"';
            if (!source) {
                const itfObjectFields = this.fieldsInSourceIfAbstractedByInterfaceObject(dest, i);
                itfObjectFields.forEach((field) => categorizeField(i, subgraph + ` (through @interfaceObject field "${field.coordinate}")`, field));
                continue;
            }
            if (mergeContext.isUsedOverridden(i) || mergeContext.isUnusedOverridden(i)) {
                continue;
            }
            categorizeField(i, subgraph, source);
        }
        if (nonShareableSources.length > 0 && (shareableSources.length > 0 || nonShareableSources.length > 1)) {
            const printSubgraphs = (l) => (0, federation_internals_1.printHumanReadableList)(l.map(({ subgraph }) => subgraph), { prefix: 'subgraph', prefixPlural: 'subgraphs', cutoff_output_length: 500 });
            const resolvingSubgraphs = printSubgraphs(allResolving);
            const nonShareables = shareableSources.length > 0 ? printSubgraphs(nonShareableSources) : 'all of them';
            const subgraphWithTargetlessOverride = nonShareableSources.find(({ idx }) => mergeContext.hasOverrideWithUnknownTarget(idx));
            let extraHint = '';
            if (subgraphWithTargetlessOverride !== undefined) {
                extraHint = ` (please note that "${dest.coordinate}" has an @override directive in ${subgraphWithTargetlessOverride.subgraph} that targets an unknown subgraph so this could be due to misspelling the @override(from:) argument)`;
            }
            this.errors.push(federation_internals_1.ERRORS.INVALID_FIELD_SHARING.err(`Non-shareable field "${dest.coordinate}" is resolved from multiple subgraphs: it is resolved from ${resolvingSubgraphs} and defined as non-shareable in ${nonShareables}${extraHint}`, { nodes: (0, federation_internals_1.sourceASTs)(...allResolving.map(({ field }) => field)) }));
        }
    }
    validateExternalFields(sources, dest, allTypesEqual) {
        let hasInvalidTypes = false;
        const invalidArgsPresence = new Set();
        const invalidArgsTypes = new Set();
        const invalidArgsDefaults = new Set();
        for (const [i, source] of sources.entries()) {
            if (!source || !this.isExternal(i, source)) {
                continue;
            }
            if (!((0, federation_internals_1.sameType)(dest.type, source.type) || (!allTypesEqual && this.isStrictSubtype(dest.type, source.type)))) {
                hasInvalidTypes = true;
            }
            for (const destArg of dest.arguments()) {
                const name = destArg.name;
                const arg = source.argument(name);
                if (!arg) {
                    invalidArgsPresence.add(name);
                    continue;
                }
                if (!(0, federation_internals_1.sameType)(destArg.type, arg.type) && !this.isStrictSubtype(arg.type, destArg.type)) {
                    invalidArgsTypes.add(name);
                }
                if (!(0, federation_internals_1.valueEquals)(destArg.defaultValue, arg.defaultValue)) {
                    invalidArgsDefaults.add(name);
                }
            }
        }
        if (hasInvalidTypes) {
            this.mismatchReporter.reportMismatchError(federation_internals_1.ERRORS.EXTERNAL_TYPE_MISMATCH, `Type of field "${dest.coordinate}" is incompatible across subgraphs (where marked @external): it has `, dest, sources, field => `type "${field.type}"`);
        }
        for (const arg of invalidArgsPresence) {
            const destArg = dest.argument(arg);
            this.mismatchReporter.reportMismatchErrorWithSpecifics({
                code: federation_internals_1.ERRORS.EXTERNAL_ARGUMENT_MISSING,
                message: `Field "${dest.coordinate}" is missing argument "${destArg.coordinate}" in some subgraphs where it is marked @external: `,
                mismatchedElement: destArg,
                subgraphElements: this.argumentSources(sources, destArg),
                mismatchAccessor: arg => arg ? `argument "${arg.coordinate}"` : undefined,
                supergraphElementPrinter: (elt, subgraphs) => `${elt} is declared in ${subgraphs}`,
                otherElementsPrinter: (_, subgraphs) => ` but not in ${subgraphs} (where "${dest.coordinate}" is @external).`,
                includeMissingSources: true,
            });
        }
        for (const arg of invalidArgsTypes) {
            const destArg = dest.argument(arg);
            this.mismatchReporter.reportMismatchError(federation_internals_1.ERRORS.EXTERNAL_ARGUMENT_TYPE_MISMATCH, `Type of argument "${destArg.coordinate}" is incompatible across subgraphs (where "${dest.coordinate}" is marked @external): it has `, destArg, this.argumentSources(sources, destArg), arg => `type "${arg.type}"`);
        }
        for (const arg of invalidArgsDefaults) {
            const destArg = dest.argument(arg);
            this.mismatchReporter.reportMismatchError(federation_internals_1.ERRORS.EXTERNAL_ARGUMENT_DEFAULT_MISMATCH, `Argument "${destArg.coordinate}" has incompatible defaults across subgraphs (where "${dest.coordinate}" is marked @external): it has `, destArg, this.argumentSources(sources, destArg), arg => arg.defaultValue !== undefined ? `default value ${(0, federation_internals_1.valueToString)(arg.defaultValue, arg.type)}` : 'no default value');
        }
    }
    argumentSources(sources, destArg) {
        const argSources = new Map;
        for (const [i, source] of sources.entries()) {
            argSources.set(i, source === null || source === void 0 ? void 0 : source.argument(destArg.name));
        }
        return argSources;
    }
    needsJoinField({ sources, parentName, allTypesEqual, mergeContext, }) {
        if (!allTypesEqual) {
            return true;
        }
        if (mergeContext.some(({ usedOverridden, overrideLabel }) => usedOverridden || !!overrideLabel)) {
            return true;
        }
        for (const source of sources.values()) {
            const coordinate = source === null || source === void 0 ? void 0 : source.coordinate;
            if (coordinate && this.fieldsWithFromContext.has(coordinate)) {
                return true;
            }
        }
        for (const [idx, source] of sources.entries()) {
            const overridden = mergeContext.isUnusedOverridden(idx);
            if (source && !overridden) {
                const sourceMeta = this.subgraphs.values()[idx].metadata();
                if (this.isExternal(idx, source)
                    || source.hasAppliedDirective(sourceMeta.providesDirective())
                    || source.hasAppliedDirective(sourceMeta.requiresDirective())) {
                    return true;
                }
            }
            else {
                if (this.subgraphsSchema[idx].type(parentName)) {
                    return true;
                }
            }
        }
        return false;
    }
    addJoinField({ sources, dest, allTypesEqual, mergeContext, }) {
        var _a, _b, _c;
        if (!this.needsJoinField({
            sources,
            parentName: dest.parent.name,
            allTypesEqual,
            mergeContext,
        })) {
            return;
        }
        const joinFieldDirective = this.joinSpec.fieldDirective(this.merged);
        for (const [idx, source] of sources.entries()) {
            const usedOverridden = mergeContext.isUsedOverridden(idx);
            const unusedOverridden = mergeContext.isUnusedOverridden(idx);
            const overrideLabel = mergeContext.overrideLabel(idx);
            if (!source || (unusedOverridden && !overrideLabel)) {
                continue;
            }
            const fromContextDirective = this.subgraphs.values()[idx].metadata().fromContextDirective();
            const contextArguments = (source.kind === 'FieldDefinition' ? source.arguments() : [])
                .map((arg) => {
                if (!(0, federation_internals_1.isFederationDirectiveDefinedInSchema)(fromContextDirective)) {
                    return undefined;
                }
                const appliedDirectives = arg.appliedDirectivesOf(fromContextDirective);
                if (appliedDirectives.length === 0) {
                    return undefined;
                }
                (0, federation_internals_1.assert)(appliedDirectives.length === 1, 'There should be at most one @fromContext directive applied to an argument');
                const directive = appliedDirectives[0];
                const { context, selection } = (0, federation_internals_1.parseContext)(directive.arguments().field);
                (0, federation_internals_1.assert)(context, 'Context should be defined');
                (0, federation_internals_1.assert)(selection, 'Selection should be defined');
                return {
                    context: `${this.subgraphs.values()[idx].name}__${context}`,
                    name: arg.name,
                    type: arg.type.toString(),
                    selection,
                };
            })
                .filter(federation_internals_1.isDefined);
            const external = this.isExternal(idx, source);
            const sourceMeta = this.subgraphs.values()[idx].metadata();
            const name = this.joinSpecName(idx);
            dest.applyDirective(joinFieldDirective, {
                graph: name,
                requires: this.getFieldSet(source, sourceMeta.requiresDirective()),
                provides: this.getFieldSet(source, sourceMeta.providesDirective()),
                override: (_b = (_a = source.appliedDirectivesOf(sourceMeta.overrideDirective()).pop()) === null || _a === void 0 ? void 0 : _a.arguments()) === null || _b === void 0 ? void 0 : _b.from,
                type: allTypesEqual ? undefined : (_c = source.type) === null || _c === void 0 ? void 0 : _c.toString(),
                external: external ? true : undefined,
                usedOverridden: usedOverridden ? true : undefined,
                overrideLabel: mergeContext.overrideLabel(idx),
                contextArguments: contextArguments.length > 0 ? contextArguments : undefined,
            });
        }
    }
    getFieldSet(element, directive) {
        const applications = element.appliedDirectivesOf(directive);
        (0, federation_internals_1.assert)(applications.length <= 1, () => `Found more than one application of ${directive} on ${element}`);
        return applications.length === 0 ? undefined : applications[0].arguments().fields;
    }
    mergeTypeReference(sources, dest, isInputPosition = false) {
        var _a;
        let destType;
        let hasSubtypes = false;
        let hasIncompatible = false;
        for (const source of sources.values()) {
            if (!source) {
                continue;
            }
            const sourceType = source.type;
            if (!destType || (0, federation_internals_1.sameType)(destType, sourceType)) {
                destType = sourceType;
            }
            else if (this.isStrictSubtype(destType, sourceType)) {
                hasSubtypes = true;
                if (isInputPosition) {
                    destType = sourceType;
                }
            }
            else if (this.isStrictSubtype(sourceType, destType)) {
                hasSubtypes = true;
                if (!isInputPosition) {
                    destType = sourceType;
                }
            }
            else {
                hasIncompatible = true;
            }
        }
        (0, federation_internals_1.assert)(destType, () => `We should have found at least one subgraph with a type for ${dest.coordinate}`);
        dest.type = copyTypeReference(destType, this.merged);
        const isArgument = dest instanceof federation_internals_1.ArgumentDefinition;
        const elementKind = isArgument ? 'argument' : 'field';
        const base = (0, federation_internals_1.baseType)(dest.type);
        if ((0, federation_internals_1.isEnumType)(base)) {
            const existing = this.enumUsages.get(base.name);
            const thisPosition = isInputPosition ? 'Input' : 'Output';
            const position = existing && existing.position !== thisPosition ? 'Both' : thisPosition;
            const examples = (_a = existing === null || existing === void 0 ? void 0 : existing.examples) !== null && _a !== void 0 ? _a : {};
            if (!examples[thisPosition]) {
                let idx = -1;
                for (const [i, source] of sources.entries()) {
                    if (source) {
                        idx = i;
                        break;
                    }
                }
                if (idx >= 0) {
                    const example = sources.get(idx);
                    examples[thisPosition] = {
                        coordinate: example.coordinate,
                        sourceAST: example.sourceAST ? (0, federation_internals_1.addSubgraphToASTNode)(example.sourceAST, this.names[idx]) : undefined,
                    };
                }
            }
            this.enumUsages.set(base.name, { position, examples });
        }
        if (hasIncompatible) {
            this.mismatchReporter.reportMismatchError(isArgument ? federation_internals_1.ERRORS.ARGUMENT_TYPE_MISMATCH : federation_internals_1.ERRORS.FIELD_TYPE_MISMATCH, `Type of ${elementKind} "${dest.coordinate}" is incompatible across subgraphs: it has `, dest, sources, field => `type "${field.type}"`);
            return false;
        }
        else if (hasSubtypes) {
            this.mismatchReporter.reportMismatchHint({
                code: isArgument ? hints_1.HINTS.INCONSISTENT_BUT_COMPATIBLE_ARGUMENT_TYPE : hints_1.HINTS.INCONSISTENT_BUT_COMPATIBLE_FIELD_TYPE,
                message: `Type of ${elementKind} "${dest.coordinate}" is inconsistent but compatible across subgraphs: `,
                supergraphElement: dest,
                subgraphElements: sources,
                elementToString: field => field.type.toString(),
                supergraphElementPrinter: (elt, subgraphs) => `will use type "${elt}" (from ${subgraphs}) in supergraph but "${dest.coordinate}" has `,
                otherElementsPrinter: (elt, subgraphs) => `${isInputPosition ? 'supertype' : 'subtype'} "${elt}" in ${subgraphs}`
            });
            return false;
        }
        return true;
    }
    isStrictSubtype(type, maybeSubType) {
        return (0, federation_internals_1.isStrictSubtype)(type, maybeSubType, this.options.allowedFieldTypeMergingSubtypingRules, (union, maybeMember) => this.merged.type(union.name).hasTypeMember(maybeMember.name), (maybeImplementer, itf) => this.merged.type(maybeImplementer.name).implementsInterface(itf));
    }
    addArgumentsShallow(sources, dest) {
        const argNames = new Set();
        for (const source of sources.values()) {
            if (!source) {
                continue;
            }
            source.arguments().forEach((arg) => argNames.add(arg.name));
        }
        for (const argName of argNames) {
            const arg = dest.addArgument(argName);
            const isContextualArg = (index, arg) => {
                const fromContextDirective = this.metadata(index).fromContextDirective();
                return fromContextDirective && (0, federation_internals_1.isFederationDirectiveDefinedInSchema)(fromContextDirective) && arg.appliedDirectivesOf(fromContextDirective).length >= 1;
            };
            const isContextualMap = new Map();
            let sawContextualArgs = false;
            sources.forEach((s, idx) => {
                const arg = s === null || s === void 0 ? void 0 : s.argument(argName);
                const isContextual = arg && isContextualArg(idx, arg) || false;
                isContextualMap.set(idx, isContextual);
                if (isContextual) {
                    sawContextualArgs = true;
                }
            });
            if (sawContextualArgs) {
                isContextualMap.forEach((isContextual, idx) => {
                    var _a, _b;
                    const argument = (_a = sources.get(idx)) === null || _a === void 0 ? void 0 : _a.argument(argName);
                    const argType = argument === null || argument === void 0 ? void 0 : argument.type;
                    if (!isContextual && argument && argType && (0, federation_internals_1.isNonNullType)(argType) && argument.defaultValue === undefined) {
                        this.errors.push(federation_internals_1.ERRORS.CONTEXTUAL_ARGUMENT_NOT_CONTEXTUAL_IN_ALL_SUBGRAPHS.err(`Argument "${arg.coordinate}" is contextual in at least one subgraph but in "${argument.coordinate}" it does not have @fromContext, is not nullable and has no default value.`, { nodes: (0, federation_internals_1.sourceASTs)((_b = sources.get(idx)) === null || _b === void 0 ? void 0 : _b.argument(argName)) }));
                    }
                    if (!isContextual && argument && argType && ((0, federation_internals_1.isNullableType)(argType) || argument.defaultValue !== undefined)) {
                        this.mismatchReporter.pushHint(new hints_1.CompositionHint(hints_1.HINTS.CONTEXTUAL_ARGUMENT_NOT_CONTEXTUAL_IN_ALL_SUBGRAPHS, `Contextual argument "${argument.coordinate}" will not be included in the supergraph since it is contextual in at least one subgraph`, undefined));
                    }
                });
                arg.remove();
                continue;
            }
            if (someSources(sources, (s) => s && !s.argument(argName))) {
                const nonOptionalSources = filterSources(mapSources(sources, (s, i) => { var _a; return s && ((_a = s.argument(argName)) === null || _a === void 0 ? void 0 : _a.isRequired()) ? this.names[i] : undefined; }));
                if (nonOptionalSources.size > 0) {
                    const nonOptionalSubgraphs = (0, federation_internals_1.printSubgraphNames)(Array.from(nonOptionalSources.values()).filter(federation_internals_1.isDefined));
                    const missingSources = (0, federation_internals_1.printSubgraphNames)(Array.from(mapSources(sources, (s, i) => s && !s.argument(argName) ? this.names[i] : undefined).values()).filter(federation_internals_1.isDefined));
                    this.errors.push(federation_internals_1.ERRORS.REQUIRED_ARGUMENT_MISSING_IN_SOME_SUBGRAPH.err(`Argument "${arg.coordinate}" is required in some subgraphs but does not appear in all subgraphs: it is required in ${nonOptionalSubgraphs} but does not appear in ${missingSources}`, { nodes: (0, federation_internals_1.sourceASTs)(...mapSources(sources, (s) => s === null || s === void 0 ? void 0 : s.argument(argName)).values()) }));
                }
                else {
                    this.mismatchReporter.reportMismatchHint({
                        code: hints_1.HINTS.INCONSISTENT_ARGUMENT_PRESENCE,
                        message: `Optional argument "${arg.coordinate}" will not be included in the supergraph as it does not appear in all subgraphs: `,
                        supergraphElement: arg,
                        subgraphElements: mapSources(sources, (s) => s ? s.argument(argName) : undefined),
                        elementToString: _ => 'yes',
                        supergraphElementPrinter: (_, subgraphs) => `it is defined in ${subgraphs}`,
                        otherElementsPrinter: (_, subgraphs) => ` but not in ${subgraphs}`,
                        includeMissingSources: true
                    });
                }
                arg.remove();
            }
        }
    }
    mergeArgument(sources, dest) {
        this.mergeDescription(sources, dest);
        this.recordAppliedDirectivesToMerge(sources, dest);
        this.mergeTypeReference(sources, dest, true);
        this.mergeDefaultValue(sources, dest, 'Argument');
    }
    mergeDefaultValue(sources, dest, kind) {
        let destDefault;
        let hasSeenSource = false;
        let isInconsistent = false;
        let isIncompatible = false;
        for (const source of sources.values()) {
            if (!source) {
                continue;
            }
            const sourceDefault = source.defaultValue;
            if (destDefault === undefined) {
                destDefault = sourceDefault;
                if (hasSeenSource && sourceDefault !== undefined) {
                    isInconsistent = true;
                }
            }
            else if (!(0, federation_internals_1.valueEquals)(destDefault, sourceDefault)) {
                isInconsistent = true;
                if (sourceDefault !== undefined) {
                    isIncompatible = true;
                }
            }
            hasSeenSource = true;
        }
        if (!isInconsistent || isIncompatible) {
            dest.defaultValue = destDefault;
        }
        if (isIncompatible) {
            this.mismatchReporter.reportMismatchError(kind === 'Argument' ? federation_internals_1.ERRORS.ARGUMENT_DEFAULT_MISMATCH : federation_internals_1.ERRORS.INPUT_FIELD_DEFAULT_MISMATCH, `${kind} "${dest.coordinate}" has incompatible default values across subgraphs: it has `, dest, sources, arg => arg.defaultValue !== undefined ? `default value ${(0, federation_internals_1.valueToString)(arg.defaultValue, arg.type)}` : 'no default value');
        }
        else if (isInconsistent) {
            this.mismatchReporter.reportMismatchHint({
                code: hints_1.HINTS.INCONSISTENT_DEFAULT_VALUE_PRESENCE,
                message: `${kind} "${dest.coordinate}" has a default value in only some subgraphs: `,
                supergraphElement: dest,
                subgraphElements: sources,
                elementToString: arg => arg.defaultValue !== undefined ? (0, federation_internals_1.valueToString)(arg.defaultValue, arg.type) : undefined,
                supergraphElementPrinter: (_, subgraphs) => `will not use a default in the supergraph (there is no default in ${subgraphs}) but `,
                otherElementsPrinter: (elt, subgraphs) => `"${dest.coordinate}" has default value ${elt} in ${subgraphs}`
            });
        }
    }
    mergeInterface(sources, dest) {
        const hasKey = this.validateInterfaceKeys(sources, dest);
        this.validateInterfaceObjects(sources, dest);
        const added = this.addFieldsShallow(sources, dest);
        added.forEach((subgraphFields, destField) => {
            if (!hasKey) {
                this.hintOnInconsistentValueTypeField(sources, dest, destField);
            }
            const mergeContext = this.validateOverride(subgraphFields, destField);
            this.mergeField({
                sources: subgraphFields,
                dest: destField,
                mergeContext,
            });
        });
    }
    validateInterfaceKeys(sources, dest) {
        const supergraphImplementations = dest.possibleRuntimeTypes();
        let hasKey = false;
        for (const [idx, source] of sources.entries()) {
            if (!source || !(0, federation_internals_1.isInterfaceType)(source)) {
                continue;
            }
            const sourceMetadata = this.subgraphs.values()[idx].metadata();
            const keys = source.appliedDirectivesOf(sourceMetadata.keyDirective());
            hasKey || (hasKey = keys.length > 0);
            const resolvableKey = keys.find((k) => k.arguments().resolvable !== false);
            if (!resolvableKey) {
                continue;
            }
            const implementationsInSubgraph = source.possibleRuntimeTypes();
            if (implementationsInSubgraph.length < supergraphImplementations.length) {
                const missingImplementations = supergraphImplementations.filter((superImpl) => !implementationsInSubgraph.some((subgImpl) => superImpl.name === subgImpl.name));
                this.errors.push((0, federation_internals_1.addSubgraphToError)(federation_internals_1.ERRORS.INTERFACE_KEY_MISSING_IMPLEMENTATION_TYPE.err(`Interface type "${source.coordinate}" has a resolvable key (${resolvableKey}) in subgraph "${this.names[idx]}" but that subgraph is missing some of the supergraph implementation types of "${dest.coordinate}". `
                    + `Subgraph "${this.names[idx]}" should define ${printTypes(missingImplementations)} (and have ${missingImplementations.length > 1 ? 'them' : 'it'} implement "${source.coordinate}").`, { nodes: resolvableKey.sourceAST }), this.names[idx]));
            }
        }
        return hasKey;
    }
    validateInterfaceObjects(sources, dest) {
        const supergraphImplementations = dest.possibleRuntimeTypes();
        for (const [idx, source] of sources.entries()) {
            if (!source || !this.metadata(idx).isInterfaceObjectType(source)) {
                continue;
            }
            const subgraphName = this.names[idx];
            const schema = source.schema();
            const definedImplementations = supergraphImplementations.map((i) => schema.type(i.name)).filter(federation_internals_1.isDefined);
            if (definedImplementations.length > 0) {
                this.errors.push((0, federation_internals_1.addSubgraphToError)(federation_internals_1.ERRORS.INTERFACE_OBJECT_USAGE_ERROR.err(`Interface type "${dest.coordinate}" is defined as an @interfaceObject in subgraph "${subgraphName}" so that subgraph should not define any of the implementation types of "${dest.coordinate}", but it defines ${printTypes(definedImplementations)}`, { nodes: (0, federation_internals_1.sourceASTs)(source, ...definedImplementations) }), subgraphName));
            }
        }
    }
    mergeUnion(sources, dest) {
        for (const source of sources.values()) {
            if (!source) {
                continue;
            }
            for (const type of source.types()) {
                if (!dest.hasTypeMember(type.name)) {
                    dest.addType(type.name);
                }
            }
        }
        for (const type of dest.types()) {
            this.addJoinUnionMember(sources, dest, type);
            this.hintOnInconsistentUnionMember(sources, dest, type.name);
        }
    }
    addJoinUnionMember(sources, dest, member) {
        const joinUnionMemberDirective = this.joinSpec.unionMemberDirective(this.merged);
        if (!joinUnionMemberDirective) {
            return;
        }
        for (const [idx, source] of sources.entries()) {
            if (!(source === null || source === void 0 ? void 0 : source.hasTypeMember(member.name))) {
                continue;
            }
            const name = this.joinSpecName(idx);
            dest.applyDirective(joinUnionMemberDirective, {
                graph: name,
                member: member.name,
            });
        }
    }
    hintOnInconsistentUnionMember(sources, dest, memberName) {
        for (const source of sources.values()) {
            if (source && !source.hasTypeMember(memberName)) {
                this.mismatchReporter.reportMismatchHint({
                    code: hints_1.HINTS.INCONSISTENT_UNION_MEMBER,
                    message: `Union type "${dest}" includes member type "${memberName}" in some but not all defining subgraphs: `,
                    supergraphElement: dest,
                    subgraphElements: sources,
                    elementToString: type => type.hasTypeMember(memberName) ? 'yes' : 'no',
                    supergraphElementPrinter: (_, subgraphs) => `"${memberName}" is defined in ${subgraphs}`,
                    otherElementsPrinter: (_, subgraphs) => ` but not in ${subgraphs}`,
                });
                return;
            }
        }
    }
    mergeEnum(sources, dest) {
        let usage = this.enumUsages.get(dest.name);
        if (!usage) {
            usage = { position: 'Output', examples: {} };
            this.hints.push(new hints_1.CompositionHint(hints_1.HINTS.UNUSED_ENUM_TYPE, `Enum type "${dest}" is defined but unused. It will be included in the supergraph with all the values appearing in any subgraph ("as if" it was only used as an output type).`, dest));
        }
        for (const source of sources.values()) {
            if (!source) {
                continue;
            }
            for (const value of source.values) {
                if (!dest.value(value.name)) {
                    dest.addValue(value.name);
                }
            }
        }
        for (const value of dest.values) {
            this.mergeEnumValue(sources, dest, value, usage);
        }
        if (dest.values.length === 0) {
            this.errors.push(federation_internals_1.ERRORS.EMPTY_MERGED_ENUM_TYPE.err(`None of the values of enum type "${dest}" are defined consistently in all the subgraphs defining that type. As only values common to all subgraphs are merged, this would result in an empty type.`, { nodes: (0, federation_internals_1.sourceASTs)(...sources.values()) }));
        }
    }
    mergeEnumValue(sources, dest, value, { position, examples }) {
        const valueSources = mapSources(sources, s => s === null || s === void 0 ? void 0 : s.value(value.name));
        this.mergeDescription(valueSources, value);
        this.recordAppliedDirectivesToMerge(valueSources, value);
        this.addJoinEnumValue(valueSources, value);
        const isInaccessible = this.inaccessibleDirectiveInSupergraph
            && value.hasAppliedDirective(this.inaccessibleDirectiveInSupergraph);
        if (!isInaccessible &&
            position !== 'Output' &&
            someSources(sources, (source) => source && !source.value(value.name))) {
            if (position === 'Both') {
                const inputExample = examples.Input;
                const outputExample = examples.Output;
                this.mismatchReporter.reportMismatchErrorWithSpecifics({
                    code: federation_internals_1.ERRORS.ENUM_VALUE_MISMATCH,
                    message: `Enum type "${dest}" is used as both input type (for example, as type of "${inputExample.coordinate}") and output type (for example, as type of "${outputExample.coordinate}"), but value "${value}" is not defined in all the subgraphs defining "${dest}": `,
                    mismatchedElement: dest,
                    subgraphElements: sources,
                    mismatchAccessor: (type) => (type === null || type === void 0 ? void 0 : type.value(value.name)) ? 'yes' : 'no',
                    supergraphElementPrinter: (_, subgraphs) => `"${value}" is defined in ${subgraphs}`,
                    otherElementsPrinter: (_, subgraphs) => ` but not in ${subgraphs}`,
                    extraNodes: (0, federation_internals_1.sourceASTs)(inputExample, outputExample),
                });
            }
            else {
                this.mismatchReporter.reportMismatchHint({
                    code: hints_1.HINTS.INCONSISTENT_ENUM_VALUE_FOR_INPUT_ENUM,
                    message: `Value "${value}" of enum type "${dest}" will not be part of the supergraph as it is not defined in all the subgraphs defining "${dest}": `,
                    supergraphElement: dest,
                    subgraphElements: sources,
                    targetedElement: value,
                    elementToString: (type) => type.value(value.name) ? 'yes' : 'no',
                    supergraphElementPrinter: (_, subgraphs) => `"${value}" is defined in ${subgraphs}`,
                    otherElementsPrinter: (_, subgraphs) => ` but not in ${subgraphs}`,
                });
                value.remove();
            }
        }
        else if (position === 'Output') {
            this.hintOnInconsistentOutputEnumValue(sources, dest, value);
        }
    }
    addJoinEnumValue(sources, dest) {
        const joinEnumValueDirective = this.joinSpec.enumValueDirective(this.merged);
        if (!joinEnumValueDirective) {
            return;
        }
        for (const [idx, source] of sources.entries()) {
            if (!source) {
                continue;
            }
            const name = this.joinSpecName(idx);
            dest.applyDirective(joinEnumValueDirective, {
                graph: name,
            });
        }
    }
    hintOnInconsistentOutputEnumValue(sources, dest, value) {
        const valueName = value.name;
        for (const source of sources.values()) {
            if (source && !source.value(valueName)) {
                this.mismatchReporter.reportMismatchHint({
                    code: hints_1.HINTS.INCONSISTENT_ENUM_VALUE_FOR_OUTPUT_ENUM,
                    message: `Value "${valueName}" of enum type "${dest}" has been added to the supergraph but is only defined in a subset of the subgraphs defining "${dest}": `,
                    supergraphElement: dest,
                    subgraphElements: sources,
                    targetedElement: value,
                    elementToString: type => type.value(valueName) ? 'yes' : 'no',
                    supergraphElementPrinter: (_, subgraphs) => `"${valueName}" is defined in ${subgraphs}`,
                    otherElementsPrinter: (_, subgraphs) => ` but not in ${subgraphs}`,
                });
                return;
            }
        }
    }
    mergeInput(inputSources, dest) {
        const added = this.addFieldsShallow(inputSources, dest);
        added.forEach((subgraphFields, destField) => {
            this.mergeInputField(subgraphFields, destField);
            const isInaccessible = this.inaccessibleDirectiveInSupergraph
                && destField.hasAppliedDirective(this.inaccessibleDirectiveInSupergraph);
            if (!isInaccessible && someSources(subgraphFields, field => !field)) {
                const nonOptionalSources = filterSources(mapSources(subgraphFields, (field, i) => (field === null || field === void 0 ? void 0 : field.isRequired()) ? this.names[i] : undefined));
                if (nonOptionalSources.size > 0) {
                    const nonOptionalSubgraphs = (0, federation_internals_1.printSubgraphNames)(Array.from(nonOptionalSources.values()).filter(federation_internals_1.isDefined));
                    const missingSources = (0, federation_internals_1.printSubgraphNames)(Array.from(mapSources(subgraphFields, (field, i) => !field ? this.names[i] : undefined).values()).filter(federation_internals_1.isDefined));
                    this.errors.push(federation_internals_1.ERRORS.REQUIRED_INPUT_FIELD_MISSING_IN_SOME_SUBGRAPH.err(`Input object field "${destField.coordinate}" is required in some subgraphs but does not appear in all subgraphs: it is required in ${nonOptionalSubgraphs} but does not appear in ${missingSources}`, { nodes: (0, federation_internals_1.sourceASTs)(...subgraphFields.values()) }));
                }
                else {
                    this.mismatchReporter.reportMismatchHint({
                        code: hints_1.HINTS.INCONSISTENT_INPUT_OBJECT_FIELD,
                        message: `Input object field "${destField.name}" will not be added to "${dest}" in the supergraph as it does not appear in all subgraphs: `,
                        supergraphElement: destField,
                        subgraphElements: subgraphFields,
                        elementToString: _ => 'yes',
                        supergraphElementPrinter: (_, subgraphs) => `it is defined in ${subgraphs}`,
                        otherElementsPrinter: (_, subgraphs) => ` but not in ${subgraphs}`,
                        includeMissingSources: true
                    });
                }
                destField.remove();
            }
        });
        if (!dest.hasFields()) {
            this.errors.push(federation_internals_1.ERRORS.EMPTY_MERGED_INPUT_TYPE.err(`None of the fields of input object type "${dest}" are consistently defined in all the subgraphs defining that type. As only fields common to all subgraphs are merged, this would result in an empty type.`, { nodes: (0, federation_internals_1.sourceASTs)(...inputSources.values()) }));
        }
    }
    mergeInputField(sources, dest) {
        this.mergeDescription(sources, dest);
        this.recordAppliedDirectivesToMerge(sources, dest);
        const allTypesEqual = this.mergeTypeReference(sources, dest, true);
        const mergeContext = new FieldMergeContext(sources);
        this.addJoinField({ sources, dest, allTypesEqual, mergeContext });
        this.mergeDefaultValue(sources, dest, 'Input field');
    }
    mergeDirectiveDefinition(sources, dest) {
        if (this.composeDirectiveManager.directiveExistsInSupergraph(dest.name)) {
            this.mergeCustomCoreDirective(dest);
        }
        else if (someSources(sources, (s, idx) => s && this.isMergedDirective(this.names[idx], s))) {
            this.mergeExecutableDirectiveDefinition(sources, dest);
        }
    }
    mergeCustomCoreDirective(dest) {
        const def = this.composeDirectiveManager.getLatestDirectiveDefinition(dest.name);
        if (def) {
            dest.repeatable = def.repeatable;
            dest.description = def.description;
            dest.addLocations(...def.locations);
            this.addArgumentsShallow(sourcesFromArray([def]), dest);
            for (const arg of def.arguments()) {
                const destArg = dest.argument(arg.name);
                (0, federation_internals_1.assert)(destArg, 'argument must exist on destination directive');
                this.mergeArgument(sourcesFromArray([arg]), destArg);
            }
        }
    }
    mergeExecutableDirectiveDefinition(sources, dest) {
        let repeatable = undefined;
        let inconsistentRepeatable = false;
        let locations = undefined;
        let inconsistentLocations = false;
        for (const source of sources.values()) {
            if (!source) {
                const usages = dest.remove();
                (0, federation_internals_1.assert)(usages.length === 0, () => `Found usages of executable directive ${dest}: ${usages}`);
                this.mismatchReporter.reportMismatchHint({
                    code: hints_1.HINTS.INCONSISTENT_EXECUTABLE_DIRECTIVE_PRESENCE,
                    message: `Executable directive "${dest}" will not be part of the supergraph as it does not appear in all subgraphs: `,
                    supergraphElement: dest,
                    subgraphElements: sources,
                    elementToString: _ => 'yes',
                    supergraphElementPrinter: (_, subgraphs) => `it is defined in ${subgraphs}`,
                    otherElementsPrinter: (_, subgraphs) => ` but not in ${subgraphs}`,
                    includeMissingSources: true,
                });
                return;
            }
            if (repeatable === undefined) {
                repeatable = source.repeatable;
            }
            else if (repeatable !== source.repeatable) {
                inconsistentRepeatable = true;
                repeatable = false;
            }
            const sourceLocations = this.extractExecutableLocations(source);
            if (!locations) {
                locations = sourceLocations;
            }
            else {
                if (!(0, federation_internals_1.arrayEquals)(locations, sourceLocations)) {
                    inconsistentLocations = true;
                }
                locations = locations.filter(loc => sourceLocations.includes(loc));
                if (locations.length === 0) {
                    const usages = dest.remove();
                    (0, federation_internals_1.assert)(usages.length === 0, () => `Found usages of executable directive ${dest}: ${usages}`);
                    this.mismatchReporter.reportMismatchHint({
                        code: hints_1.HINTS.NO_EXECUTABLE_DIRECTIVE_LOCATIONS_INTERSECTION,
                        message: `Executable directive "${dest}" has no location that is common to all subgraphs: `,
                        supergraphElement: dest,
                        subgraphElements: sources,
                        elementToString: directive => locationString(this.extractExecutableLocations(directive)),
                        supergraphElementPrinter: () => `it will not appear in the supergraph as there no intersection between `,
                        otherElementsPrinter: (locs, subgraphs) => `${locs} in ${subgraphs}`,
                    });
                    return;
                }
            }
        }
        dest.repeatable = repeatable;
        dest.addLocations(...locations);
        this.mergeDescription(sources, dest);
        if (inconsistentRepeatable) {
            this.mismatchReporter.reportMismatchHint({
                code: hints_1.HINTS.INCONSISTENT_EXECUTABLE_DIRECTIVE_REPEATABLE,
                message: `Executable directive "${dest}" will not be marked repeatable in the supergraph as it is inconsistently marked repeatable in subgraphs: `,
                supergraphElement: dest,
                subgraphElements: sources,
                elementToString: directive => directive.repeatable ? 'yes' : 'no',
                supergraphElementPrinter: (_, subgraphs) => `it is not repeatable in ${subgraphs}`,
                otherElementsPrinter: (_, subgraphs) => ` but is repeatable in ${subgraphs}`,
            });
        }
        if (inconsistentLocations) {
            this.mismatchReporter.reportMismatchHint({
                code: hints_1.HINTS.INCONSISTENT_EXECUTABLE_DIRECTIVE_LOCATIONS,
                message: `Executable directive "${dest}" has inconsistent locations across subgraphs `,
                supergraphElement: dest,
                subgraphElements: sources,
                elementToString: directive => locationString(this.extractExecutableLocations(directive)),
                supergraphElementPrinter: (locs, subgraphs) => `and will use ${locs} (intersection of all subgraphs) in the supergraph, but has: ${subgraphs ? `${locs} in ${subgraphs} and ` : ''}`,
                otherElementsPrinter: (locs, subgraphs) => `${locs} in ${subgraphs}`,
            });
        }
        this.addArgumentsShallow(sources, dest);
        for (const destArg of dest.arguments()) {
            const subgraphArgs = mapSources(sources, f => f === null || f === void 0 ? void 0 : f.argument(destArg.name));
            this.mergeArgument(subgraphArgs, destArg);
        }
    }
    extractExecutableLocations(source) {
        return this.filterExecutableDirectiveLocations(source).concat().sort();
    }
    filterExecutableDirectiveLocations(source) {
        return source.locations.filter(loc => (0, federation_internals_1.isExecutableDirectiveLocation)(loc));
    }
    recordAppliedDirectivesToMerge(sources, dest) {
        var _a;
        const inaccessibleName = (_a = this.inaccessibleDirectiveInSupergraph) === null || _a === void 0 ? void 0 : _a.name;
        const names = this.gatherAppliedDirectiveNames(sources);
        if (inaccessibleName && names.has(inaccessibleName)) {
            this.mergeAppliedDirective(inaccessibleName, sources, dest);
            names.delete(inaccessibleName);
        }
        this.appliedDirectivesToMerge.push({
            names,
            sources,
            dest,
        });
    }
    mergeAllAppliedDirectives() {
        for (const { names, sources, dest } of this.appliedDirectivesToMerge) {
            if (!dest.isAttached()) {
                continue;
            }
            for (const name of names) {
                this.mergeAppliedDirective(name, sources, dest);
            }
        }
        this.appliedDirectivesToMerge = [];
    }
    gatherAppliedDirectiveNames(sources) {
        const names = new Set();
        sources.forEach((source, idx) => {
            if (source) {
                for (const directive of source.appliedDirectives) {
                    if (this.isMergedDirective(this.names[idx], directive)) {
                        names.add(directive.name);
                    }
                }
            }
        });
        return names;
    }
    mergeAppliedDirective(name, sources, dest) {
        var _a, _b, _c;
        let perSource = [];
        sources.forEach((source, index) => {
            if (source) {
                const directives = source.appliedDirectivesOf(name);
                if (directives.length > 0) {
                    perSource.push({ directives, subgraphIndex: index });
                }
            }
        });
        if (perSource.length === 0) {
            return;
        }
        const directiveInSupergraph = this.mergedFederationDirectiveInSupergraphByDirectiveName.get(name);
        if ((_a = dest.schema().directive(name)) === null || _a === void 0 ? void 0 : _a.repeatable) {
            while (perSource.length > 0) {
                const directive = perSource[0].directives[0];
                const subgraphIndex = perSource[0].subgraphIndex;
                const transformedArgs = directiveInSupergraph && directiveInSupergraph.staticArgumentTransform && directiveInSupergraph.staticArgumentTransform(this.subgraphs.values()[subgraphIndex], directive.arguments(false));
                dest.applyDirective(directive.name, transformedArgs !== null && transformedArgs !== void 0 ? transformedArgs : directive.arguments(false));
                perSource = perSource
                    .map(ds => ({ directives: ds.directives.filter(d => !this.sameDirectiveApplication(directive, d)), subgraphIndex: ds.subgraphIndex }))
                    .filter(ds => ds.directives.length);
            }
        }
        else {
            const differentApplications = [];
            const counts = [];
            for (const { directives: source } of perSource) {
                (0, federation_internals_1.assert)(source.length === 1, () => `Non-repeatable directive shouldn't have multiple application ${source} in a subgraph`);
                const application = source[0];
                const idx = differentApplications.findIndex((existing) => this.sameDirectiveApplication(existing, application));
                if (idx < 0) {
                    differentApplications.push(application);
                    counts.push(1);
                }
                else {
                    counts[idx]++;
                }
            }
            (0, federation_internals_1.assert)(differentApplications.length > 0, 'We exited early when there was no applications, so we should have found one');
            if (differentApplications.length === 1) {
                dest.applyDirective(name, differentApplications[0].arguments(false));
            }
            else {
                const info = this.mergedFederationDirectiveInSupergraphByDirectiveName.get(name);
                if (info && info.argumentsMerger) {
                    const mergedArguments = Object.create(null);
                    const applicationsArguments = differentApplications.map((a) => a.arguments(true));
                    for (const argDef of info.definition.arguments()) {
                        const values = applicationsArguments.map((args) => args[argDef.name]);
                        mergedArguments[argDef.name] = info.argumentsMerger.merge(argDef.name, values);
                    }
                    dest.applyDirective(name, mergedArguments);
                    this.mismatchReporter.pushHint(new hints_1.CompositionHint(hints_1.HINTS.MERGED_NON_REPEATABLE_DIRECTIVE_ARGUMENTS, `Directive @${name} is applied to "${(_b = dest['coordinate']) !== null && _b !== void 0 ? _b : dest}" in multiple subgraphs with different arguments. Merging strategies used by arguments: ${info.argumentsMerger}`, undefined));
                }
                else {
                    const idx = indexOfMax(counts);
                    dest.applyDirective(name, differentApplications[idx].arguments(false));
                    this.mismatchReporter.reportMismatchHint({
                        code: hints_1.HINTS.INCONSISTENT_NON_REPEATABLE_DIRECTIVE_ARGUMENTS,
                        message: `Non-repeatable directive @${name} is applied to "${(_c = dest['coordinate']) !== null && _c !== void 0 ? _c : dest}" in multiple subgraphs but with incompatible arguments. `,
                        supergraphElement: dest,
                        subgraphElements: sources,
                        elementToString: (elt) => {
                            var _a;
                            const args = (_a = elt.appliedDirectivesOf(name).pop()) === null || _a === void 0 ? void 0 : _a.arguments();
                            return args === undefined
                                ? undefined
                                : Object.values(args).length === 0 ? 'no arguments' : (`arguments ${(0, federation_internals_1.valueToString)(args)}`);
                        },
                        supergraphElementPrinter: (application, subgraphs) => `The supergraph will use ${application} (from ${subgraphs}), but found `,
                        otherElementsPrinter: (application, subgraphs) => `${application} in ${subgraphs}`,
                    });
                }
            }
        }
    }
    sameDirectiveApplication(application1, application2) {
        return application1.name === application2.name
            && (0, federation_internals_1.valueEquals)(application1.arguments(true), application2.arguments(true));
    }
    mergeSchemaDefinition(sources, dest) {
        this.mergeDescription(sources, dest);
        this.recordAppliedDirectivesToMerge(sources, dest);
        for (const rootKind of federation_internals_1.allSchemaRootKinds) {
            let rootType;
            let isIncompatible = false;
            for (const source of sources.values()) {
                if (!source)
                    continue;
                const sourceType = filteredRoot(source, rootKind);
                if (!sourceType)
                    continue;
                if (rootType) {
                    isIncompatible = isIncompatible || rootType !== sourceType.name;
                }
                else {
                    rootType = sourceType.name;
                }
            }
            if (!rootType) {
                continue;
            }
            dest.setRoot(rootKind, rootType);
            (0, federation_internals_1.assert)(!isIncompatible, () => `Should not have incompatible root type for ${rootKind}`);
        }
        this.addJoinDirectiveDirectives(sources, dest);
    }
    shouldUseJoinDirectiveForURL(url) {
        return Boolean(url && this.joinDirectiveFeatureDefinitionsByIdentity.has(url.identity));
    }
    computeMapFromImportNameToIdentityUrl(schema) {
        var _a;
        const map = new Map();
        for (const linkDirective of schema.schemaDefinition.appliedDirectivesOf('link')) {
            const { url, as, import: imports } = linkDirective.arguments();
            const parsedUrl = federation_internals_1.FeatureUrl.maybeParse(url);
            if (parsedUrl) {
                map.set(`@${as !== null && as !== void 0 ? as : parsedUrl.name}`, parsedUrl);
                if (imports) {
                    for (const i of imports) {
                        if (typeof i === 'string') {
                            map.set(i, parsedUrl);
                        }
                        else {
                            map.set((_a = i.as) !== null && _a !== void 0 ? _a : i.name, parsedUrl);
                        }
                    }
                }
            }
        }
        return map;
    }
    addJoinDirectiveDirectives(sources, dest) {
        var _a;
        var _b;
        const joinsByDirectiveName = Object.create(null);
        const linksToPersist = new Set();
        for (const [idx, source] of sources.entries()) {
            if (!source)
                continue;
            const graph = this.joinSpecName(idx);
            const linkImportIdentityURLMap = this.schemaToImportNameToFeatureUrl.get(source.schema());
            if (!linkImportIdentityURLMap)
                continue;
            for (const directive of source.appliedDirectives) {
                let shouldIncludeAsJoinDirective = false;
                if (directive.name === 'link') {
                    const { url } = directive.arguments();
                    const parsedUrl = federation_internals_1.FeatureUrl.maybeParse(url);
                    if (typeof url === 'string' && parsedUrl) {
                        shouldIncludeAsJoinDirective =
                            this.shouldUseJoinDirectiveForURL(parsedUrl);
                        if (shouldIncludeAsJoinDirective) {
                            const featureDefinition = (0, federation_internals_1.coreFeatureDefinitionIfKnown)(parsedUrl);
                            if (featureDefinition) {
                                linksToPersist.add(featureDefinition);
                            }
                        }
                    }
                }
                else {
                    const nameWithAtSymbol = directive.name.startsWith('@') ? directive.name : '@' + directive.name;
                    shouldIncludeAsJoinDirective = this.shouldUseJoinDirectiveForURL(linkImportIdentityURLMap.get(nameWithAtSymbol));
                }
                if (shouldIncludeAsJoinDirective) {
                    const existingJoins = ((_a = joinsByDirectiveName[_b = directive.name]) !== null && _a !== void 0 ? _a : (joinsByDirectiveName[_b] = []));
                    let found = false;
                    for (const existingJoin of existingJoins) {
                        if ((0, federation_internals_1.valueEquals)(existingJoin.args, directive.arguments())) {
                            existingJoin.graphs.push(graph);
                            found = true;
                            break;
                        }
                    }
                    if (!found) {
                        existingJoins.push({
                            graphs: [graph],
                            args: directive.arguments(),
                        });
                    }
                }
            }
        }
        const linkDirective = this.linkSpec.coreDirective(this.merged);
        const latestOrHighestLinkByIdentity = [...linksToPersist].reduce((map, link) => {
            var _a, _b;
            let latest = (_a = this.joinDirectiveFeatureDefinitionsByIdentity.get(link.identity)) === null || _a === void 0 ? void 0 : _a.latest();
            const existing = (_b = map.get(link.identity)) !== null && _b !== void 0 ? _b : link;
            if (!latest || (existing === null || existing === void 0 ? void 0 : existing.version.gt(latest.version))) {
                latest = existing;
            }
            map.set(link.identity, latest);
            return map;
        }, new Map());
        for (const [_, link] of latestOrHighestLinkByIdentity) {
            dest.applyDirective(linkDirective, {
                url: link.toString(),
                for: link.defaultCorePurpose,
                feature: undefined
            });
        }
        const joinDirective = this.joinSpec.directiveDirective(this.merged);
        Object.keys(joinsByDirectiveName).forEach(directiveName => {
            joinsByDirectiveName[directiveName].forEach(join => {
                dest.applyDirective(joinDirective, {
                    graphs: join.graphs,
                    name: directiveName,
                    args: join.args,
                });
            });
        });
    }
    filterSubgraphs(predicate) {
        return this.subgraphsSchema.map((s, i) => predicate(s) ? this.names[i] : undefined).filter(n => n !== undefined);
    }
    subgraphByName(name) {
        return this.subgraphsSchema[this.names.indexOf(name)];
    }
    postMergeValidations() {
        var _a, _b, _c, _d;
        for (const type of this.merged.types()) {
            if (!(0, federation_internals_1.isObjectType)(type) && !(0, federation_internals_1.isInterfaceType)(type)) {
                continue;
            }
            for (const itf of type.interfaces()) {
                for (const itfField of itf.fields()) {
                    const field = type.field(itfField.name);
                    if (!field) {
                        const subgraphsWithTheField = this.filterSubgraphs(s => { var _a; return ((_a = s.typeOfKind(itf.name, 'InterfaceType')) === null || _a === void 0 ? void 0 : _a.field(itfField.name)) !== undefined; });
                        const subgraphsWithTypeImplementingItf = this.filterSubgraphs(s => {
                            const typeInSubgraph = s.type(type.name);
                            return typeInSubgraph !== undefined && typeInSubgraph.implementsInterface(itf.name);
                        });
                        this.errors.push(federation_internals_1.ERRORS.INTERFACE_FIELD_NO_IMPLEM.err(`Interface field "${itfField.coordinate}" is declared in ${(0, federation_internals_1.printSubgraphNames)(subgraphsWithTheField)} but type "${type}", `
                            + `which implements "${itf}" only in ${(0, federation_internals_1.printSubgraphNames)(subgraphsWithTypeImplementingItf)} does not have field "${itfField.name}".`, {
                            nodes: (0, federation_internals_1.sourceASTs)(...subgraphsWithTheField.map(s => { var _a; return (_a = this.subgraphByName(s).typeOfKind(itf.name, 'InterfaceType')) === null || _a === void 0 ? void 0 : _a.field(itfField.name); }), ...subgraphsWithTypeImplementingItf.map(s => this.subgraphByName(s).type(type.name)))
                        }));
                        continue;
                    }
                }
            }
        }
        for (const subgraph of this.subgraphs) {
            for (const requiresApplication of subgraph.metadata().requiresDirective().applications()) {
                const originalField = requiresApplication.parent;
                (0, federation_internals_1.assert)(originalField.kind === 'FieldDefinition', () => `Expected ${(0, util_1.inspect)(originalField)} to be a field`);
                const mergedType = this.merged.type(originalField.parent.name);
                (0, federation_internals_1.assert)(mergedType && (0, federation_internals_1.isCompositeType)(mergedType), () => `Merged type ${originalField.parent.name} should exist should have field ${originalField.name}`);
                (0, federation_internals_1.assert)((0, federation_internals_1.isCompositeType)(mergedType), `${mergedType} should be a composite type but got ${mergedType.kind}`);
                try {
                    (0, federation_internals_1.parseFieldSetArgument)({
                        parentType: mergedType,
                        directive: requiresApplication,
                        decorateValidationErrors: false,
                    });
                }
                catch (e) {
                    if (!(e instanceof graphql_1.GraphQLError)) {
                        throw e;
                    }
                    const requireAST = requiresApplication.sourceAST ? [(0, federation_internals_1.addSubgraphToASTNode)(requiresApplication.sourceAST, subgraph.name)] : [];
                    const that = this;
                    const registerError = (arg, field, isIncompatible, makeMsg) => {
                        const incompatibleSubgraphs = that.subgraphs.values().map((otherSubgraph) => {
                            if (otherSubgraph.name === subgraph.name) {
                                return undefined;
                            }
                            const fieldInOther = otherSubgraph.schema.elementByCoordinate(field);
                            const fieldIsIncompatible = fieldInOther
                                && fieldInOther instanceof federation_internals_1.FieldDefinition
                                && isIncompatible(fieldInOther);
                            return fieldIsIncompatible
                                ? {
                                    name: otherSubgraph.name,
                                    node: fieldInOther.sourceAST ? (0, federation_internals_1.addSubgraphToASTNode)(fieldInOther.sourceAST, otherSubgraph.name) : undefined,
                                }
                                : undefined;
                        }).filter(federation_internals_1.isDefined);
                        (0, federation_internals_1.assert)(incompatibleSubgraphs.length > 0, () => `Got error on ${arg} of ${field} but no "incompatible" subgraphs (error: ${e})`);
                        const nodes = requireAST.concat(incompatibleSubgraphs.map((s) => s.node).filter(federation_internals_1.isDefined));
                        const error = federation_internals_1.ERRORS.REQUIRES_INVALID_FIELDS.err(`On field "${originalField.coordinate}", for ${requiresApplication}: ${makeMsg((0, federation_internals_1.printSubgraphNames)(incompatibleSubgraphs.map((s) => s.name)))}`, { nodes });
                        that.errors.push((0, federation_internals_1.addSubgraphToError)(error, subgraph.name));
                    };
                    const unknownArgument = e.message.match(/Unknown argument \"(?<arg>[^"]*)\" found in value: \"(?<field>[^"]*)\" has no argument.*/);
                    if (unknownArgument) {
                        const arg = (_a = unknownArgument.groups) === null || _a === void 0 ? void 0 : _a.arg;
                        const field = (_b = unknownArgument.groups) === null || _b === void 0 ? void 0 : _b.field;
                        registerError(arg, field, (f) => !f.argument(arg), (incompatibleSubgraphs) => `cannot provide a value for argument "${arg}" of field "${field}" as argument "${arg}" is not defined in ${incompatibleSubgraphs}`);
                        continue;
                    }
                    const missingMandatory = e.message.match(/Missing mandatory value for argument \"(?<arg>[^"]*)\" of field \"(?<field>[^"]*)\".*/);
                    if (missingMandatory) {
                        const arg = (_c = missingMandatory.groups) === null || _c === void 0 ? void 0 : _c.arg;
                        const field = (_d = missingMandatory.groups) === null || _d === void 0 ? void 0 : _d.field;
                        registerError(arg, field, (f) => { var _a; return !!((_a = f.argument(arg)) === null || _a === void 0 ? void 0 : _a.isRequired()); }, (incompatibleSubgraphs) => `no value provided for argument "${arg}" of field "${field}" but a value is mandatory as "${arg}" is required in ${incompatibleSubgraphs}`);
                        continue;
                    }
                    (0, federation_internals_1.assert)(false, () => `Unexpected error throw by ${requiresApplication} when evaluated on supergraph: ${e.message}`);
                }
            }
        }
    }
    updateInaccessibleErrorsWithLinkToSubgraphs(errors) {
        function isRelevantSubgraphReferencer(subgraphReferencer, err, supergraphElements, hasInaccessibleElements) {
            switch ((0, federation_internals_1.errorCode)(err)) {
                case federation_internals_1.ERRORS.REFERENCED_INACCESSIBLE.code: {
                    if (!((subgraphReferencer instanceof federation_internals_1.FieldDefinition) ||
                        (subgraphReferencer instanceof federation_internals_1.ArgumentDefinition) ||
                        (subgraphReferencer instanceof federation_internals_1.InputFieldDefinition))) {
                        return false;
                    }
                    const subgraphType = subgraphReferencer.type;
                    const supergraphType = supergraphElements[0];
                    return !!subgraphType &&
                        (0, federation_internals_1.baseType)(subgraphType).name === supergraphType;
                }
                case federation_internals_1.ERRORS.DEFAULT_VALUE_USES_INACCESSIBLE.code: {
                    return true;
                }
                case federation_internals_1.ERRORS.REQUIRED_INACCESSIBLE.code: {
                    if (!((subgraphReferencer instanceof federation_internals_1.ArgumentDefinition) ||
                        (subgraphReferencer instanceof federation_internals_1.InputFieldDefinition))) {
                        return false;
                    }
                    const subgraphType = subgraphReferencer.type;
                    return (subgraphType && (0, federation_internals_1.isNonNullType)(subgraphType)) ||
                        subgraphReferencer.defaultValue === undefined;
                }
                case federation_internals_1.ERRORS.IMPLEMENTED_BY_INACCESSIBLE.code: {
                    return true;
                }
                case federation_internals_1.ERRORS.DISALLOWED_INACCESSIBLE.code: {
                    return hasInaccessibleElements;
                }
                case federation_internals_1.ERRORS.ONLY_INACCESSIBLE_CHILDREN.code: {
                    return hasInaccessibleElements;
                }
                default: {
                    return false;
                }
            }
        }
        return errors.map((err) => {
            const elements = err.extensions['inaccessible_elements'];
            if (!Array.isArray(elements))
                return err;
            const errorNodes = [];
            const subgraphHasInaccessibleElements = [];
            for (const coordinate of elements) {
                if (typeof coordinate !== 'string')
                    continue;
                errorNodes.push(...(0, federation_internals_1.sourceASTs)(...this.subgraphsSchema.flatMap((subgraphSchema, subgraphIndex) => {
                    const subgraphElement = subgraphSchema.elementByCoordinate(coordinate);
                    if (subgraphElement) {
                        const inaccessibleDirective = (0, federation_internals_1.federationMetadata)(subgraphSchema).inaccessibleDirective();
                        if (subgraphElement.hasAppliedDirective(inaccessibleDirective)) {
                            subgraphHasInaccessibleElements[subgraphIndex] = true;
                            return [subgraphElement];
                        }
                    }
                    return [];
                })));
            }
            const referencers = err.extensions['inaccessible_referencers'];
            if (Array.isArray(referencers)) {
                for (const coordinate of referencers) {
                    if (typeof coordinate !== 'string')
                        continue;
                    errorNodes.push(...(0, federation_internals_1.sourceASTs)(...this.subgraphsSchema.flatMap((subgraphSchema, subgraphIndex) => {
                        const subgraphReferencer = subgraphSchema.elementByCoordinate(coordinate);
                        if (subgraphReferencer &&
                            isRelevantSubgraphReferencer(subgraphReferencer, err, elements, subgraphHasInaccessibleElements[subgraphIndex])) {
                            return [subgraphReferencer];
                        }
                        return [];
                    })));
                }
            }
            return errorNodes.length > 0
                ? (0, federation_internals_1.withModifiedErrorNodes)(err, errorNodes)
                : err;
        });
    }
    validateSubscriptionField(sources) {
        const fieldsWithShareable = [];
        for (const [idx, source] of sources.entries()) {
            if (source && source.hasAppliedDirective(this.metadata(idx).shareableDirective())) {
                fieldsWithShareable.push(source);
            }
        }
        if (fieldsWithShareable.length > 0) {
            const nodes = (0, federation_internals_1.sourceASTs)(...fieldsWithShareable);
            this.errors.push(federation_internals_1.ERRORS.INVALID_FIELD_SHARING.err(`Fields on root level subscription object cannot be marked as shareable`, { nodes }));
        }
    }
    getFieldsWithFromContextDirective() {
        return this.getFieldsWithAppliedDirective((subgraph) => subgraph.metadata().fromContextDirective(), (application) => {
            const field = application.parent.parent;
            (0, federation_internals_1.assert)((0, federation_internals_1.isFieldDefinition)(field), () => `Expected ${application.parent} to be a field`);
            return field;
        });
    }
    getFieldsWithOverrideDirective() {
        return this.getFieldsWithAppliedDirective((subgraph) => subgraph.metadata().overrideDirective(), (application) => {
            const field = application.parent;
            (0, federation_internals_1.assert)((0, federation_internals_1.isFieldDefinition)(field), () => `Expected ${application.parent} to be a field`);
            return field;
        });
    }
    getFieldsWithAppliedDirective(getDirective, getField) {
        const fields = new Set();
        for (const subgraph of this.subgraphs) {
            const directive = getDirective(subgraph);
            if ((0, federation_internals_1.isFederationDirectiveDefinedInSchema)(directive)) {
                for (const application of directive.applications()) {
                    const field = getField(application);
                    const coordinate = field.coordinate;
                    if (!fields.has(coordinate)) {
                        fields.add(coordinate);
                    }
                }
            }
        }
        return fields;
    }
}
//# sourceMappingURL=merge.js.map