import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ThrottlerModule } from '@nestjs/throttler';
import { HttpModule } from '@nestjs/axios';

// Core modules
import { DatabaseModule } from '@app/database';
import { SecurityModule } from '@app/security';
import { CommonModule } from '@app/common';

// Gateway modules
import { GatewayController } from './controllers/gateway.controller';
import { ApiGatewayService } from './services/api-gateway.service';
import { ServiceDiscoveryService } from './services/service-discovery.service';
import { LoadBalancerService } from './services/load-balancer.service';
import { CircuitBreakerService } from './services/circuit-breaker.service';

// Controllers and services
import { AppController } from './app.controller';
import { AppService } from './app.service';

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
      cache: true,
      expandVariables: true,
    }),

    // Database
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        type: 'postgres',
        host: configService.get<string>('DATABASE_HOST', 'localhost'),
        port: configService.get<number>('DATABASE_PORT', 5432),
        username: configService.get<string>('DATABASE_USERNAME', 'postgres'),
        password: configService.get<string>('DATABASE_PASSWORD', 'postgres123'),
        database: configService.get<string>('DATABASE_NAME', 'peoplenest_dev'),
        ssl: configService.get<boolean>('DATABASE_SSL', false),
        logging: configService.get<boolean>('DATABASE_LOGGING', false),
        synchronize: configService.get<boolean>('DATABASE_SYNCHRONIZE', false),
        migrationsRun: configService.get<boolean>('DATABASE_MIGRATIONS_RUN', true),
        entities: [__dirname + '/../**/*.entity{.ts,.js}'],
        autoLoadEntities: true,
        retryAttempts: 3,
        retryDelay: 3000,
      }),
      inject: [ConfigService],
    }),

    // Event system
    EventEmitterModule.forRoot(),

    // Rate limiting
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        ttl: configService.get<number>('THROTTLE_TTL', 60),
        limit: configService.get<number>('THROTTLE_LIMIT', 1000),
      }),
      inject: [ConfigService],
    }),

    // HTTP client for service communication
    HttpModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        timeout: configService.get<number>('HTTP_TIMEOUT', 10000),
        maxRedirects: configService.get<number>('HTTP_MAX_REDIRECTS', 5),
      }),
      inject: [ConfigService],
    }),

    // Core modules
    DatabaseModule,
    SecurityModule,
    CommonModule,
  ],
  controllers: [
    AppController,
    GatewayController,
  ],
  providers: [
    AppService,
    ApiGatewayService,
    ServiceDiscoveryService,
    LoadBalancerService,
    CircuitBreakerService,
  ],
})
export class AppModule {
  constructor(private configService: ConfigService) {
    const environment = this.configService.get<string>('NODE_ENV', 'development');
    console.log(`🚀 PeopleNest API Gateway initialized in ${environment} mode`);
  }
}
