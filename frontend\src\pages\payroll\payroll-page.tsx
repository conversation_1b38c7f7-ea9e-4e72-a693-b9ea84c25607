import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { PageContainer, PageHeader, PageSection, GridContainer } from '@/components/layout/page-container';
import {
  DollarSign,
  Calendar,
  Download,
  Plus,
  TrendingUp,
  Users,
  Clock,
  AlertCircle,
  CheckCircle,
  FileText,
  Calculator
} from 'lucide-react';

const PayrollPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');

  return (
    <PageContainer>
      <PageHeader
        title="Payroll Management"
        description="Manage employee payroll, compensation, and benefits"
      >
        <Button variant="outline" size="sm">
          <Download className="h-4 w-4 mr-2" />
          Export Reports
        </Button>
        <Button className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700">
            <Plus className="h-4 w-4 mr-2" />
            Run Payroll
          </Button>
      </PageHeader>

      <PageSection title="Key Metrics">
        <GridContainer cols={4} gap="lg">
        <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-green-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Total Payroll</CardTitle>
            <div className="p-2 bg-green-100 rounded-lg">
              <DollarSign className="h-5 w-5 text-green-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-gray-900">$245,000</div>
            <div className="flex items-center mt-2">
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                +5.2% from last month
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Current month total
            </p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-blue-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Employees Paid</CardTitle>
            <div className="p-2 bg-blue-100 rounded-lg">
              <Users className="h-5 w-5 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-gray-900">42</div>
            <div className="flex items-center mt-2">
              <Badge variant="secondary" className="bg-orange-100 text-orange-800">
                3 pending
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Out of 45 total employees
            </p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-purple-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Next Payroll</CardTitle>
            <div className="p-2 bg-purple-100 rounded-lg">
              <Calendar className="h-5 w-5 text-purple-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-gray-900">Dec 15</div>
            <div className="flex items-center mt-2">
              <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                <Clock className="h-3 w-3 mr-1" />
                In 5 days
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Bi-weekly schedule
            </p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300 border-l-4 border-l-orange-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Average Salary</CardTitle>
            <div className="p-2 bg-orange-100 rounded-lg">
              <Calculator className="h-5 w-5 text-orange-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-gray-900">$5,444</div>
            <div className="flex items-center mt-2">
              <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                <TrendingUp className="h-3 w-3 mr-1" />
                +2.1% YoY
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Per employee monthly
            </p>
          </CardContent>
        </Card>
        </GridContainer>
      </PageSection>

      <PageSection title="Payroll Management">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="runs">Payroll Runs</TabsTrigger>
          <TabsTrigger value="employees">Employee Pay</TabsTrigger>
          <TabsTrigger value="reports">Reports</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Recent Payroll Runs */}
          <Card className="shadow-lg">
            <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100">
              <div className="flex items-center justify-between">
                <CardTitle className="text-xl font-semibold text-gray-800">Recent Payroll Runs</CardTitle>
                <Button variant="outline" size="sm">
                  <FileText className="h-4 w-4 mr-2" />
                  View All
                </Button>
              </div>
            </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-4">
                {[
                  { date: '2023-12-01', amount: '$245,000', employees: 42, status: 'completed' },
                  { date: '2023-11-15', amount: '$238,500', employees: 41, status: 'completed' },
                  { date: '2023-11-01', amount: '$242,000', employees: 40, status: 'completed' },
                ].map((payroll, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-4 bg-white border border-gray-200 rounded-lg hover:shadow-md transition-shadow duration-200"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="p-2 bg-green-100 rounded-lg">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      </div>
                      <div>
                        <p className="text-sm font-semibold text-gray-900">
                          Payroll - {new Date(payroll.date).toLocaleDateString()}
                        </p>
                        <p className="text-sm text-gray-600">
                          {payroll.employees} employees • {payroll.amount}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Badge className="bg-green-100 text-green-800 hover:bg-green-200">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        {payroll.status}
                      </Badge>
                      <Button variant="outline" size="sm">
                        <Download className="h-4 w-4 mr-2" />
                        Download
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="runs" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Payroll Run History</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Detailed payroll run management coming soon...</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="employees" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Employee Compensation</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Employee pay management coming soon...</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reports" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Payroll Reports</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Payroll reporting and analytics coming soon...</p>
            </CardContent>
          </Card>
        </TabsContent>
        </Tabs>
      </PageSection>
    </PageContainer>
  );
};

export default PayrollPage;
