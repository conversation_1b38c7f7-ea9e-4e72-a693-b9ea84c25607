import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { PageContainer, PageHeader, PageSection, GridContainer } from '@/components/layout/page-container';
import {
  DollarSign,
  Calendar,
  Download,
  Plus,
  TrendingUp,
  Users,
  Clock,
  AlertCircle,
  CheckCircle,
  FileText,
  Calculator
} from 'lucide-react';

const PayrollPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');

  return (
    <PageContainer>
      <PageHeader
        title="Payroll Management"
        description="Manage employee payroll, compensation, and benefits"
      >
        <Button variant="outline" size="sm">
          <Download className="h-4 w-4 mr-2" />
          Export Reports
        </Button>
        <Button className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700">
            <Plus className="h-4 w-4 mr-2" />
            Run Payroll
          </Button>
      </PageHeader>

      <PageSection title="Key Metrics">
        <GridContainer cols={4} gap="lg">
        <Card className="group hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br from-green-50 to-green-100/50 hover:from-green-100 hover:to-green-200/50 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-green-500/5 to-transparent" />
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 relative">
            <CardTitle className="text-sm font-semibold text-green-900/80">Total Payroll</CardTitle>
            <div className="p-3 bg-green-500/10 rounded-xl group-hover:bg-green-500/20 transition-colors">
              <DollarSign className="h-6 w-6 text-green-600" />
            </div>
          </CardHeader>
          <CardContent className="relative">
            <div className="text-4xl font-bold text-green-900 mb-3">$245,000</div>
            <div className="flex items-center mb-2">
              <Badge variant="secondary" className="bg-green-200 text-green-800 border-green-300 font-medium">
                +5.2% from last month
              </Badge>
            </div>
            <p className="text-sm text-green-700/70 font-medium">
              Current month total
            </p>
          </CardContent>
        </Card>

        <Card className="group hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br from-blue-50 to-blue-100/50 hover:from-blue-100 hover:to-blue-200/50 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent" />
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 relative">
            <CardTitle className="text-sm font-semibold text-blue-900/80">Employees Paid</CardTitle>
            <div className="p-3 bg-blue-500/10 rounded-xl group-hover:bg-blue-500/20 transition-colors">
              <Users className="h-6 w-6 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent className="relative">
            <div className="text-4xl font-bold text-blue-900 mb-3">42</div>
            <div className="flex items-center mb-2">
              <Badge variant="secondary" className="bg-orange-100 text-orange-800 border-orange-200 font-medium">
                3 pending
              </Badge>
            </div>
            <p className="text-sm text-blue-700/70 font-medium">
              Out of 45 total employees
            </p>
          </CardContent>
        </Card>

        <Card className="group hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br from-purple-50 to-purple-100/50 hover:from-purple-100 hover:to-purple-200/50 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-transparent" />
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 relative">
            <CardTitle className="text-sm font-semibold text-purple-900/80">Next Payroll</CardTitle>
            <div className="p-3 bg-purple-500/10 rounded-xl group-hover:bg-purple-500/20 transition-colors">
              <Calendar className="h-6 w-6 text-purple-600" />
            </div>
          </CardHeader>
          <CardContent className="relative">
            <div className="text-4xl font-bold text-purple-900 mb-3">Dec 15</div>
            <div className="flex items-center mb-2">
              <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 border-yellow-200 font-medium">
                <Clock className="h-3 w-3 mr-1" />
                In 5 days
              </Badge>
            </div>
            <p className="text-sm text-purple-700/70 font-medium">
              Bi-weekly schedule
            </p>
          </CardContent>
        </Card>

        <Card className="group hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br from-orange-50 to-orange-100/50 hover:from-orange-100 hover:to-orange-200/50 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-orange-500/5 to-transparent" />
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 relative">
            <CardTitle className="text-sm font-semibold text-orange-900/80">Average Salary</CardTitle>
            <div className="p-3 bg-orange-500/10 rounded-xl group-hover:bg-orange-500/20 transition-colors">
              <Calculator className="h-6 w-6 text-orange-600" />
            </div>
          </CardHeader>
          <CardContent className="relative">
            <div className="text-4xl font-bold text-orange-900 mb-3">$5,444</div>
            <div className="flex items-center mb-2">
              <Badge variant="secondary" className="bg-blue-100 text-blue-800 border-blue-200 font-medium">
                <TrendingUp className="h-3 w-3 mr-1" />
                +2.1% YoY
              </Badge>
            </div>
            <p className="text-sm text-orange-700/70 font-medium">
              Per employee monthly
            </p>
          </CardContent>
        </Card>
        </GridContainer>
      </PageSection>

      <PageSection title="Payroll Management">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4 bg-gray-100/50 p-1 rounded-xl">
          <TabsTrigger value="overview" className="rounded-lg data-[state=active]:bg-white data-[state=active]:shadow-sm">Overview</TabsTrigger>
          <TabsTrigger value="runs" className="rounded-lg data-[state=active]:bg-white data-[state=active]:shadow-sm">Payroll Runs</TabsTrigger>
          <TabsTrigger value="employees" className="rounded-lg data-[state=active]:bg-white data-[state=active]:shadow-sm">Employee Pay</TabsTrigger>
          <TabsTrigger value="reports" className="rounded-lg data-[state=active]:bg-white data-[state=active]:shadow-sm">Reports</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6 mt-8">
          {/* Recent Payroll Runs */}
          <Card className="shadow-lg border-0 bg-white/95 backdrop-blur-sm">
            <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100">
              <div className="flex items-center justify-between">
                <CardTitle className="text-xl font-semibold text-gray-800">Recent Payroll Runs</CardTitle>
                <Button variant="outline" size="sm">
                  <FileText className="h-4 w-4 mr-2" />
                  View All
                </Button>
              </div>
            </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-4">
                {[
                  { date: '2023-12-01', amount: '$245,000', employees: 42, status: 'completed' },
                  { date: '2023-11-15', amount: '$238,500', employees: 41, status: 'completed' },
                  { date: '2023-11-01', amount: '$242,000', employees: 40, status: 'completed' },
                ].map((payroll, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-4 bg-white border border-gray-200 rounded-lg hover:shadow-md transition-shadow duration-200"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="p-2 bg-green-100 rounded-lg">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      </div>
                      <div>
                        <p className="text-sm font-semibold text-gray-900">
                          Payroll - {new Date(payroll.date).toLocaleDateString()}
                        </p>
                        <p className="text-sm text-gray-600">
                          {payroll.employees} employees • {payroll.amount}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Badge className="bg-green-100 text-green-800 hover:bg-green-200">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        {payroll.status}
                      </Badge>
                      <Button variant="outline" size="sm">
                        <Download className="h-4 w-4 mr-2" />
                        Download
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="runs" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Payroll Run History</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Detailed payroll run management coming soon...</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="employees" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Employee Compensation</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Employee pay management coming soon...</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reports" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Payroll Reports</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">Payroll reporting and analytics coming soon...</p>
            </CardContent>
          </Card>
        </TabsContent>
        </Tabs>
      </PageSection>
    </PageContainer>
  );
};

export default PayrollPage;
