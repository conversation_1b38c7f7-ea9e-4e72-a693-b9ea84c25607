{"version": 3, "file": "validate.d.ts", "sourceRoot": "", "sources": ["../src/validate.ts"], "names": [], "mappings": "AAAA,OAAO,EAGL,aAAa,EAIb,eAAe,EAef,SAAS,EAIT,MAAM,EACN,cAAc,EAcf,MAAM,8BAA8B,CAAC;AACtC,OAAO,EACL,IAAI,EAEJ,UAAU,EAGV,QAAQ,EAER,UAAU,EAKV,mCAAmC,EACnC,UAAU,EAEV,iBAAiB,EAGlB,MAAM,sBAAsB,CAAC;AAC9B,OAAO,EAAE,eAAe,EAAS,MAAM,SAAS,CAAC;AACjD,OAAO,EAAW,YAAY,EAAS,MAAM,SAAS,CAAC;AACvD,OAAO,EAAE,kBAAkB,EAAE,MAAM,WAAW,CAAC;AAI/C,qBAAa,eAAgB,SAAQ,KAAK;IAGtC,QAAQ,CAAC,2BAA2B,EAAE,QAAQ,CAAC,UAAU,CAAC;IAC1D,QAAQ,CAAC,cAAc,EAAE,QAAQ,CAAC,UAAU,CAAC,EAAE;IAC/C,QAAQ,CAAC,OAAO,EAAE,SAAS;gBAH3B,OAAO,EAAE,MAAM,EACN,2BAA2B,EAAE,QAAQ,CAAC,UAAU,CAAC,EACjD,cAAc,EAAE,QAAQ,CAAC,UAAU,CAAC,EAAE,EACtC,OAAO,EAAE,SAAS;CAK9B;AAsOD,wBAAgB,wBAAwB,CACtC,gBAAgB,EAAE,MAAM,EACxB,4BAA4B,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,EACjD,aAAa,EAAE,UAAU,EACzB,mBAAmB,EAAE,UAAU,EAC/B,kBAAkB,GAAE,kBAAuB,GAC1C;IACD,MAAM,CAAC,EAAG,YAAY,EAAE,CAAC;IACzB,KAAK,CAAC,EAAG,eAAe,EAAE,CAAC;CAC5B,CASA;AAkBD,wBAAgB,sBAAsB,CAAC,KAAK,EAAE,GAAG,GAAG,eAAe,GAAG,SAAS,CAK9E;AAED,qBAAa,iBAAiB;IAM1B,QAAQ,CAAC,gBAAgB,EAAE,MAAM;IACjC,QAAQ,CAAC,4BAA4B,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC;IAN5D,OAAO,CAAC,QAAQ,CAAC,iBAAiB,CAAsB;IACxD,OAAO,CAAC,QAAQ,CAAC,kBAAkB,CAAmD;IACtF,OAAO,CAAC,QAAQ,CAAC,eAAe,CAA0B;gBAG/C,gBAAgB,EAAE,MAAM,EACxB,4BAA4B,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC;IAuC5D,WAAW,CAAC,KAAK,EAAE,eAAe,CAAC,aAAa,CAAC,GAAG,OAAO;IAqB3D,gBAAgB,CAAC,QAAQ,EAAE,MAAM,GAAG,MAAM,EAAE;CAG7C;AAED,KAAK,gBAAgB,GAAG;IACtB,IAAI,EAAE,mCAAmC,CAAC,UAAU,CAAC,CAAC;IAEtD,QAAQ,EAAE,GAAG,CAAC,MAAM,EAAE;QAAE,YAAY,EAAE,MAAM,CAAC;QAAC,QAAQ,EAAE,MAAM,CAAA;KAAE,CAAC,CAAC;CACnE,CAAA;AAED,qBAAa,eAAe;aAGR,cAAc,EAAE,QAAQ,CAAC,UAAU,CAAC;aAEpC,iBAAiB,EAAE,gBAAgB,EAAE;IAK9C,0BAA0B,EAAE,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC;gBAPvC,cAAc,EAAE,QAAQ,CAAC,UAAU,CAAC,EAEpC,iBAAiB,EAAE,gBAAgB,EAAE,EAK9C,0BAA0B,GAAE,GAAG,CAAC,MAAM,EAAE,OAAO,CAAa;IAIrE,MAAM,CAAC,OAAO,CAAC,EACb,aAAa,EACb,IAAI,EACJ,mBAAmB,EACnB,iBAAiB,EACjB,kBAAkB,GACnB,EAAE;QACD,aAAa,EAAE,UAAU,CAAC;QAC1B,IAAI,EAAE,cAAc,CAAC;QACrB,mBAAmB,EAAE,UAAU,CAAC;QAChC,iBAAiB,EAAE,iBAAiB,CAAC;QACrC,kBAAkB,EAAE,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;KAC1C;IA2BD,kBAAkB,CAAC,OAAO,EAAE,iBAAiB,EAAE,cAAc,EAAE,IAAI,EAAE,gBAAgB,EAAE,MAAM,EAAE,GAAG;QAChG,KAAK,CAAC,EAAE,eAAe,CAAC;QACxB,KAAK,CAAC,EAAE,YAAY,CAAC;QACrB,IAAI,CAAC,EAAE,eAAe,CAAC;KACxB;IA6ID,oBAAoB,IAAI,MAAM,EAAE;IAWhC,0BAA0B,CAAC,4BAA4B,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC;IAoB1F,gBAAgB,IAAI;QAAE,IAAI,EAAE,MAAM,CAAC;QAAC,MAAM,EAAE,MAAM,CAAA;KAAE,EAAE;IAQtD,QAAQ,IAAI,MAAM;CAGnB"}