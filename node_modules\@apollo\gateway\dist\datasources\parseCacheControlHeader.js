"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.parseCacheControlHeader = void 0;
function parseCacheControlHeader(header) {
    const cc = {};
    if (!header)
        return cc;
    const parts = header.trim().split(/\s*,\s*/);
    for (const part of parts) {
        const [k, v] = part.split(/\s*=\s*/, 2);
        cc[k] = v === undefined ? true : v.replace(/^"|"$/g, '');
    }
    return cc;
}
exports.parseCacheControlHeader = parseCacheControlHeader;
//# sourceMappingURL=parseCacheControlHeader.js.map