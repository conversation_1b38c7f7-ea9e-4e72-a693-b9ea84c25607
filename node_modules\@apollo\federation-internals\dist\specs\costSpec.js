"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.COST_VERSIONS = exports.CostSpecDefinition = exports.costIdentity = void 0;
const graphql_1 = require("graphql");
const directiveAndTypeSpecification_1 = require("../directiveAndTypeSpecification");
const coreSpec_1 = require("./coreSpec");
const definitions_1 = require("../definitions");
const knownCoreFeatures_1 = require("../knownCoreFeatures");
const argumentCompositionStrategies_1 = require("../argumentCompositionStrategies");
exports.costIdentity = 'https://specs.apollo.dev/cost';
class CostSpecDefinition extends coreSpec_1.FeatureDefinition {
    constructor(version, minimumFederationVersion) {
        super(new coreSpec_1.FeatureUrl(exports.costIdentity, 'cost', version), minimumFederationVersion);
        this.minimumFederationVersion = minimumFederationVersion;
        this.registerDirective((0, directiveAndTypeSpecification_1.createDirectiveSpecification)({
            name: 'cost',
            locations: [
                graphql_1.DirectiveLocation.ARGUMENT_DEFINITION,
                graphql_1.DirectiveLocation.ENUM,
                graphql_1.DirectiveLocation.FIELD_DEFINITION,
                graphql_1.DirectiveLocation.INPUT_FIELD_DEFINITION,
                graphql_1.DirectiveLocation.OBJECT,
                graphql_1.DirectiveLocation.SCALAR
            ],
            args: [{ name: 'weight', type: (schema) => new definitions_1.NonNullType(schema.intType()), compositionStrategy: argumentCompositionStrategies_1.ARGUMENT_COMPOSITION_STRATEGIES.MAX }],
            composes: true,
            repeatable: false,
            supergraphSpecification: (fedVersion) => exports.COST_VERSIONS.getMinimumRequiredVersion(fedVersion),
        }));
        this.registerDirective((0, directiveAndTypeSpecification_1.createDirectiveSpecification)({
            name: 'listSize',
            locations: [graphql_1.DirectiveLocation.FIELD_DEFINITION],
            args: [
                { name: 'assumedSize', type: (schema) => schema.intType(), compositionStrategy: argumentCompositionStrategies_1.ARGUMENT_COMPOSITION_STRATEGIES.NULLABLE_MAX },
                { name: 'slicingArguments', type: (schema) => new definitions_1.ListType(new definitions_1.NonNullType(schema.stringType())), compositionStrategy: argumentCompositionStrategies_1.ARGUMENT_COMPOSITION_STRATEGIES.NULLABLE_UNION },
                { name: 'sizedFields', type: (schema) => new definitions_1.ListType(new definitions_1.NonNullType(schema.stringType())), compositionStrategy: argumentCompositionStrategies_1.ARGUMENT_COMPOSITION_STRATEGIES.NULLABLE_UNION },
                { name: 'requireOneSlicingArgument', type: (schema) => schema.booleanType(), defaultValue: true, compositionStrategy: argumentCompositionStrategies_1.ARGUMENT_COMPOSITION_STRATEGIES.NULLABLE_AND },
            ],
            composes: true,
            repeatable: false,
            supergraphSpecification: (fedVersion) => exports.COST_VERSIONS.getMinimumRequiredVersion(fedVersion)
        }));
    }
    costDirective(schema) {
        return this.directive(schema, 'cost');
    }
    listSizeDirective(schema) {
        return this.directive(schema, 'listSize');
    }
}
exports.CostSpecDefinition = CostSpecDefinition;
exports.COST_VERSIONS = new coreSpec_1.FeatureDefinitions(exports.costIdentity)
    .add(new CostSpecDefinition(new coreSpec_1.FeatureVersion(0, 1), new coreSpec_1.FeatureVersion(2, 9)));
(0, knownCoreFeatures_1.registerKnownFeature)(exports.COST_VERSIONS);
//# sourceMappingURL=costSpec.js.map