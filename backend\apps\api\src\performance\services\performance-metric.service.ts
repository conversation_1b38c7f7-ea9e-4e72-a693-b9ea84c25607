import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere, Between } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';

import { 
  PerformanceMetric, 
  MetricType, 
  MetricCategory, 
  MeasurementUnit, 
  AggregationMethod 
} from '@app/database/entities/performance-metric.entity';
import { Employee } from '@app/database/entities/employee.entity';
import { Department } from '@app/database/entities/department.entity';
import { TenantService } from '../../tenant/tenant.service';

export interface CreatePerformanceMetricDto {
  employeeId: string;
  departmentId?: string;
  metricName: string;
  metricType: MetricType;
  category: MetricCategory;
  measurementUnit: MeasurementUnit;
  value: number;
  targetValue?: number;
  benchmarkValue?: number;
  measurementPeriod: string;
  measurementDate: Date;
  aggregationMethod?: AggregationMethod;
  weight?: number;
  description?: string;
  dataSource?: string;
  metadata?: any;
}

export interface UpdatePerformanceMetricDto {
  value?: number;
  targetValue?: number;
  benchmarkValue?: number;
  measurementPeriod?: string;
  measurementDate?: Date;
  weight?: number;
  description?: string;
  dataSource?: string;
  aiInsights?: any;
  metadata?: any;
}

export interface PerformanceMetricFilters {
  employeeId?: string;
  departmentId?: string;
  metricType?: MetricType;
  category?: MetricCategory;
  measurementUnit?: MeasurementUnit;
  measurementPeriod?: string;
  measurementDateFrom?: Date;
  measurementDateTo?: Date;
}

export interface MetricAnalytics {
  average: number;
  median: number;
  min: number;
  max: number;
  standardDeviation: number;
  trend: 'increasing' | 'decreasing' | 'stable';
  trendPercentage: number;
  achievementRate: number;
  benchmarkComparison: number;
}

@Injectable()
export class PerformanceMetricService {
  constructor(
    @InjectRepository(PerformanceMetric)
    private performanceMetricRepository: Repository<PerformanceMetric>,
    @InjectRepository(Employee)
    private employeeRepository: Repository<Employee>,
    @InjectRepository(Department)
    private departmentRepository: Repository<Department>,
    private tenantService: TenantService,
    private eventEmitter: EventEmitter2,
  ) {}

  async create(createDto: CreatePerformanceMetricDto, userId: string): Promise<PerformanceMetric> {
    const tenantId = this.tenantService.getCurrentTenantId();

    // Validate employee exists
    const employee = await this.employeeRepository.findOne({
      where: { id: createDto.employeeId, tenantId },
    });

    if (!employee) {
      throw new NotFoundException('Employee not found');
    }

    // Validate department if provided
    if (createDto.departmentId) {
      const department = await this.departmentRepository.findOne({
        where: { id: createDto.departmentId, tenantId },
      });
      if (!department) {
        throw new NotFoundException('Department not found');
      }
    }

    // Calculate derived values
    const targetAchievement = createDto.targetValue 
      ? (createDto.value / createDto.targetValue) * 100 
      : null;

    const benchmarkComparison = createDto.benchmarkValue 
      ? ((createDto.value - createDto.benchmarkValue) / createDto.benchmarkValue) * 100 
      : null;

    const performanceMetric = this.performanceMetricRepository.create({
      ...createDto,
      tenantId,
      createdBy: userId,
      targetAchievement,
      benchmarkComparison,
    });

    const savedMetric = await this.performanceMetricRepository.save(performanceMetric);

    // Emit event
    this.eventEmitter.emit('performance-metric.created', {
      performanceMetric: savedMetric,
      tenantId,
      userId,
    });

    return savedMetric;
  }

  async findAll(
    filters: PerformanceMetricFilters = {},
    page: number = 1,
    limit: number = 10,
  ): Promise<{ data: PerformanceMetric[]; total: number; page: number; limit: number }> {
    const tenantId = this.tenantService.getCurrentTenantId();

    const where: FindOptionsWhere<PerformanceMetric> = {
      tenantId,
      ...filters,
    };

    // Handle date range filtering
    if (filters.measurementDateFrom || filters.measurementDateTo) {
      where.measurementDate = Between(
        filters.measurementDateFrom || new Date('1900-01-01'),
        filters.measurementDateTo || new Date(),
      );
    }

    const [data, total] = await this.performanceMetricRepository.findAndCount({
      where,
      relations: ['employee', 'department'],
      order: { measurementDate: 'DESC' },
      skip: (page - 1) * limit,
      take: limit,
    });

    return { data, total, page, limit };
  }

  async findOne(id: string): Promise<PerformanceMetric> {
    const tenantId = this.tenantService.getCurrentTenantId();

    const performanceMetric = await this.performanceMetricRepository.findOne({
      where: { id, tenantId },
      relations: ['employee', 'department'],
    });

    if (!performanceMetric) {
      throw new NotFoundException('Performance metric not found');
    }

    return performanceMetric;
  }

  async update(
    id: string,
    updateDto: UpdatePerformanceMetricDto,
    userId: string,
  ): Promise<PerformanceMetric> {
    const performanceMetric = await this.findOne(id);

    // Recalculate derived values if relevant fields are updated
    if (updateDto.value !== undefined || updateDto.targetValue !== undefined) {
      const newValue = updateDto.value ?? performanceMetric.value;
      const newTargetValue = updateDto.targetValue ?? performanceMetric.targetValue;
      
      if (newTargetValue) {
        updateDto['targetAchievement'] = (newValue / newTargetValue) * 100;
      }
    }

    if (updateDto.value !== undefined || updateDto.benchmarkValue !== undefined) {
      const newValue = updateDto.value ?? performanceMetric.value;
      const newBenchmarkValue = updateDto.benchmarkValue ?? performanceMetric.benchmarkValue;
      
      if (newBenchmarkValue) {
        updateDto['benchmarkComparison'] = ((newValue - newBenchmarkValue) / newBenchmarkValue) * 100;
      }
    }

    Object.assign(performanceMetric, updateDto);
    performanceMetric.updatedBy = userId;

    const savedMetric = await this.performanceMetricRepository.save(performanceMetric);

    // Emit event
    this.eventEmitter.emit('performance-metric.updated', {
      performanceMetric: savedMetric,
      tenantId: this.tenantService.getCurrentTenantId(),
      userId,
      changes: updateDto,
    });

    return savedMetric;
  }

  async delete(id: string, userId: string): Promise<void> {
    const performanceMetric = await this.findOne(id);

    await this.performanceMetricRepository.remove(performanceMetric);

    // Emit event
    this.eventEmitter.emit('performance-metric.deleted', {
      performanceMetricId: id,
      tenantId: this.tenantService.getCurrentTenantId(),
      userId,
    });
  }

  async getEmployeeMetrics(
    employeeId: string,
    page: number = 1,
    limit: number = 10,
  ): Promise<{ data: PerformanceMetric[]; total: number; page: number; limit: number }> {
    return this.findAll({ employeeId }, page, limit);
  }

  async getDepartmentMetrics(
    departmentId: string,
    page: number = 1,
    limit: number = 10,
  ): Promise<{ data: PerformanceMetric[]; total: number; page: number; limit: number }> {
    return this.findAll({ departmentId }, page, limit);
  }

  async getMetricAnalytics(
    metricName: string,
    employeeId?: string,
    departmentId?: string,
    dateFrom?: Date,
    dateTo?: Date,
  ): Promise<MetricAnalytics> {
    const tenantId = this.tenantService.getCurrentTenantId();

    const queryBuilder = this.performanceMetricRepository
      .createQueryBuilder('metric')
      .where('metric.tenantId = :tenantId', { tenantId })
      .andWhere('metric.metricName = :metricName', { metricName });

    if (employeeId) {
      queryBuilder.andWhere('metric.employeeId = :employeeId', { employeeId });
    }

    if (departmentId) {
      queryBuilder.andWhere('metric.departmentId = :departmentId', { departmentId });
    }

    if (dateFrom) {
      queryBuilder.andWhere('metric.measurementDate >= :dateFrom', { dateFrom });
    }

    if (dateTo) {
      queryBuilder.andWhere('metric.measurementDate <= :dateTo', { dateTo });
    }

    const metrics = await queryBuilder
      .orderBy('metric.measurementDate', 'ASC')
      .getMany();

    if (metrics.length === 0) {
      throw new NotFoundException('No metrics found for analysis');
    }

    return this.calculateAnalytics(metrics);
  }

  async getMetricTrends(
    metricName: string,
    employeeId?: string,
    departmentId?: string,
    periods: number = 12,
  ): Promise<Array<{ period: string; value: number; targetValue?: number; benchmarkValue?: number }>> {
    const tenantId = this.tenantService.getCurrentTenantId();

    const queryBuilder = this.performanceMetricRepository
      .createQueryBuilder('metric')
      .where('metric.tenantId = :tenantId', { tenantId })
      .andWhere('metric.metricName = :metricName', { metricName });

    if (employeeId) {
      queryBuilder.andWhere('metric.employeeId = :employeeId', { employeeId });
    }

    if (departmentId) {
      queryBuilder.andWhere('metric.departmentId = :departmentId', { departmentId });
    }

    const metrics = await queryBuilder
      .orderBy('metric.measurementDate', 'DESC')
      .limit(periods)
      .getMany();

    return metrics.map(metric => ({
      period: metric.measurementPeriod,
      value: metric.value,
      targetValue: metric.targetValue,
      benchmarkValue: metric.benchmarkValue,
    })).reverse();
  }

  async bulkCreateMetrics(
    metrics: CreatePerformanceMetricDto[],
    userId: string,
  ): Promise<PerformanceMetric[]> {
    const savedMetrics: PerformanceMetric[] = [];

    for (const metricDto of metrics) {
      try {
        const metric = await this.create(metricDto, userId);
        savedMetrics.push(metric);
      } catch (error) {
        // Log error but continue with other metrics
        console.error(`Failed to create metric for employee ${metricDto.employeeId}:`, error);
      }
    }

    return savedMetrics;
  }

  private calculateAnalytics(metrics: PerformanceMetric[]): MetricAnalytics {
    const values = metrics.map(m => m.value);
    const targetValues = metrics.filter(m => m.targetValue).map(m => m.targetValue!);
    const benchmarkValues = metrics.filter(m => m.benchmarkValue).map(m => m.benchmarkValue!);

    // Basic statistics
    const average = values.reduce((sum, val) => sum + val, 0) / values.length;
    const sortedValues = [...values].sort((a, b) => a - b);
    const median = sortedValues.length % 2 === 0
      ? (sortedValues[sortedValues.length / 2 - 1] + sortedValues[sortedValues.length / 2]) / 2
      : sortedValues[Math.floor(sortedValues.length / 2)];
    const min = Math.min(...values);
    const max = Math.max(...values);

    // Standard deviation
    const variance = values.reduce((sum, val) => sum + Math.pow(val - average, 2), 0) / values.length;
    const standardDeviation = Math.sqrt(variance);

    // Trend analysis
    let trend: 'increasing' | 'decreasing' | 'stable' = 'stable';
    let trendPercentage = 0;

    if (values.length >= 2) {
      const firstHalf = values.slice(0, Math.floor(values.length / 2));
      const secondHalf = values.slice(Math.floor(values.length / 2));
      
      const firstHalfAvg = firstHalf.reduce((sum, val) => sum + val, 0) / firstHalf.length;
      const secondHalfAvg = secondHalf.reduce((sum, val) => sum + val, 0) / secondHalf.length;
      
      trendPercentage = ((secondHalfAvg - firstHalfAvg) / firstHalfAvg) * 100;
      
      if (Math.abs(trendPercentage) > 5) {
        trend = trendPercentage > 0 ? 'increasing' : 'decreasing';
      }
    }

    // Achievement rate
    const achievementRate = targetValues.length > 0
      ? (metrics.filter(m => m.targetAchievement && m.targetAchievement >= 100).length / targetValues.length) * 100
      : 0;

    // Benchmark comparison
    const benchmarkComparison = benchmarkValues.length > 0
      ? metrics.reduce((sum, m) => sum + (m.benchmarkComparison || 0), 0) / benchmarkValues.length
      : 0;

    return {
      average,
      median,
      min,
      max,
      standardDeviation,
      trend,
      trendPercentage,
      achievementRate,
      benchmarkComparison,
    };
  }
}
