import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// Entities
import {
  BenefitPlan,
  EmployeeBenefit,
  Deduction,
  EmployeeDeduction,
  Employee,
  User,
  Tenant,
  AuditLog,
} from '@app/database';

// Controllers
import { BenefitPlanController } from './controllers/benefit-plan.controller';
import { EmployeeBenefitController } from './controllers/employee-benefit.controller';
import { DeductionController } from './controllers/deduction.controller';

// Services
import { BenefitPlanService } from './services/benefit-plan.service';
import { EmployeeBenefitService } from './services/employee-benefit.service';
import { DeductionService } from './services/deduction.service';
import { BenefitCalculationService } from './services/benefit-calculation.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      BenefitPlan,
      EmployeeBenefit,
      Deduction,
      EmployeeDeduction,
      Employee,
      User,
      Tenant,
      AuditLog,
    ]),
  ],
  controllers: [
    BenefitPlanController,
    EmployeeBenefitController,
    DeductionController,
  ],
  providers: [
    BenefitPlanService,
    EmployeeBenefitService,
    DeductionService,
    BenefitCalculationService,
  ],
  exports: [
    BenefitPlanService,
    EmployeeBenefitService,
    DeductionService,
    BenefitCalculationService,
  ],
})
export class BenefitsModule {}
