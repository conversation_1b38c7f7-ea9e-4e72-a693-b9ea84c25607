import { Edge, QueryGraph, Vertex } from "./querygraph";
export type MermaidOptions = {
    includeRootTypeLinks?: boolean;
};
export declare class MermaidGraph {
    private readonly graph;
    private readonly options;
    private readonly before;
    private readonly after;
    private readonly subgraphs;
    private isBuilt;
    constructor(graph: QueryGraph, options?: MermaidOptions);
    private subgraphName;
    private vertexName;
    addVertex(vertex: Vertex): void;
    addEdge(edge: Edge): boolean;
    build(): void;
    toString(): string;
}
//# sourceMappingURL=mermaid.d.ts.map