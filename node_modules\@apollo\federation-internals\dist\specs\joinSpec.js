"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.JOIN_VERSIONS = exports.JoinSpecDefinition = exports.joinIdentity = void 0;
const graphql_1 = require("graphql");
const coreSpec_1 = require("./coreSpec");
const definitions_1 = require("../definitions");
const knownCoreFeatures_1 = require("../knownCoreFeatures");
const utils_1 = require("../utils");
exports.joinIdentity = 'https://specs.apollo.dev/join';
function sanitizeGraphQLName(name) {
    const alphaNumericUnderscoreOnly = name.replace(/[\W]/g, '_');
    const noNumericFirstChar = alphaNumericUnderscoreOnly.match(/^\d/)
        ? '_' + alphaNumericUnderscoreOnly
        : alphaNumericUnderscoreOnly;
    const noUnderscoreNumericEnding = noNumericFirstChar.match(/_\d+$/)
        ? noNumericFirstChar + '_'
        : noNumericFirstChar;
    const toUpper = noUnderscoreNumericEnding.toLocaleUpperCase();
    return toUpper;
}
class JoinSpecDefinition extends coreSpec_1.FeatureDefinition {
    constructor(version, minimumFederationVersion) {
        super(new coreSpec_1.FeatureUrl(exports.joinIdentity, 'join', version), minimumFederationVersion);
    }
    isV01() {
        return this.version.equals(new coreSpec_1.FeatureVersion(0, 1));
    }
    addElementsToSchema(schema) {
        const joinGraph = this.addDirective(schema, 'graph').addLocations(graphql_1.DirectiveLocation.ENUM_VALUE);
        joinGraph.addArgument('name', new definitions_1.NonNullType(schema.stringType()));
        joinGraph.addArgument('url', new definitions_1.NonNullType(schema.stringType()));
        const graphEnum = this.addEnumType(schema, 'Graph');
        const joinFieldSet = this.addScalarType(schema, 'FieldSet');
        const joinType = this.addDirective(schema, 'type').addLocations(graphql_1.DirectiveLocation.OBJECT, graphql_1.DirectiveLocation.INTERFACE, graphql_1.DirectiveLocation.UNION, graphql_1.DirectiveLocation.ENUM, graphql_1.DirectiveLocation.INPUT_OBJECT, graphql_1.DirectiveLocation.SCALAR);
        if (!this.isV01()) {
            joinType.repeatable = true;
        }
        joinType.addArgument('graph', new definitions_1.NonNullType(graphEnum));
        joinType.addArgument('key', joinFieldSet);
        if (!this.isV01()) {
            joinType.addArgument('extension', new definitions_1.NonNullType(schema.booleanType()), false);
            joinType.addArgument('resolvable', new definitions_1.NonNullType(schema.booleanType()), true);
            if (this.version.gte(new coreSpec_1.FeatureVersion(0, 3))) {
                joinType.addArgument('isInterfaceObject', new definitions_1.NonNullType(schema.booleanType()), false);
            }
        }
        const joinField = this.addDirective(schema, 'field').addLocations(graphql_1.DirectiveLocation.FIELD_DEFINITION, graphql_1.DirectiveLocation.INPUT_FIELD_DEFINITION);
        joinField.repeatable = true;
        const graphArgType = this.version.gte(new coreSpec_1.FeatureVersion(0, 3))
            ? graphEnum
            : new definitions_1.NonNullType(graphEnum);
        joinField.addArgument('graph', graphArgType);
        joinField.addArgument('requires', joinFieldSet);
        joinField.addArgument('provides', joinFieldSet);
        if (!this.isV01()) {
            joinField.addArgument('type', schema.stringType());
            joinField.addArgument('external', schema.booleanType());
            joinField.addArgument('override', schema.stringType());
            joinField.addArgument('usedOverridden', schema.booleanType());
        }
        if (!this.isV01()) {
            const joinImplements = this.addDirective(schema, 'implements').addLocations(graphql_1.DirectiveLocation.OBJECT, graphql_1.DirectiveLocation.INTERFACE);
            joinImplements.repeatable = true;
            joinImplements.addArgument('graph', new definitions_1.NonNullType(graphEnum));
            joinImplements.addArgument('interface', new definitions_1.NonNullType(schema.stringType()));
        }
        if (this.version.gte(new coreSpec_1.FeatureVersion(0, 3))) {
            const joinUnionMember = this.addDirective(schema, 'unionMember').addLocations(graphql_1.DirectiveLocation.UNION);
            joinUnionMember.repeatable = true;
            joinUnionMember.addArgument('graph', new definitions_1.NonNullType(graphEnum));
            joinUnionMember.addArgument('member', new definitions_1.NonNullType(schema.stringType()));
            const joinEnumValue = this.addDirective(schema, 'enumValue').addLocations(graphql_1.DirectiveLocation.ENUM_VALUE);
            joinEnumValue.repeatable = true;
            joinEnumValue.addArgument('graph', new definitions_1.NonNullType(graphEnum));
        }
        if (this.version.gte(new coreSpec_1.FeatureVersion(0, 4))) {
            const joinDirective = this.addDirective(schema, 'directive').addLocations(graphql_1.DirectiveLocation.SCHEMA, graphql_1.DirectiveLocation.OBJECT, graphql_1.DirectiveLocation.INTERFACE, graphql_1.DirectiveLocation.FIELD_DEFINITION);
            joinDirective.repeatable = true;
            joinDirective.addArgument('graphs', new definitions_1.ListType(new definitions_1.NonNullType(graphEnum)));
            joinDirective.addArgument('name', new definitions_1.NonNullType(schema.stringType()));
            joinDirective.addArgument('args', this.addScalarType(schema, 'DirectiveArguments'));
            joinField.addArgument('overrideLabel', schema.stringType());
        }
        if (this.version.gte(new coreSpec_1.FeatureVersion(0, 5))) {
            const fieldValue = this.addScalarType(schema, 'FieldValue');
            const contextArgumentsType = schema.addType(new definitions_1.InputObjectType('join__ContextArgument'));
            contextArgumentsType.addField('name', new definitions_1.NonNullType(schema.stringType()));
            contextArgumentsType.addField('type', new definitions_1.NonNullType(schema.stringType()));
            contextArgumentsType.addField('context', new definitions_1.NonNullType(schema.stringType()));
            contextArgumentsType.addField('selection', new definitions_1.NonNullType(fieldValue));
            joinField.addArgument('contextArguments', new definitions_1.ListType(new definitions_1.NonNullType(contextArgumentsType)));
        }
        if (this.isV01()) {
            const joinOwner = this.addDirective(schema, 'owner').addLocations(graphql_1.DirectiveLocation.OBJECT);
            joinOwner.addArgument('graph', new definitions_1.NonNullType(graphEnum));
        }
        return [];
    }
    allElementNames() {
        const names = [
            'graph',
            'Graph',
            'FieldSet',
            '@type',
            '@field',
        ];
        if (this.isV01()) {
            names.push('@owner');
        }
        else {
            names.push('@implements');
        }
        return names;
    }
    populateGraphEnum(schema, subgraphs) {
        const sanitizedNameToSubgraphs = new utils_1.MultiMap();
        for (const subgraph of subgraphs) {
            const sanitized = sanitizeGraphQLName(subgraph.name);
            sanitizedNameToSubgraphs.add(sanitized, subgraph);
        }
        const subgraphToEnumName = new Map();
        for (const [sanitizedName, subgraphsForName] of sanitizedNameToSubgraphs) {
            if (subgraphsForName.length === 1) {
                subgraphToEnumName.set(subgraphsForName[0].name, sanitizedName);
            }
            else {
                for (const [index, subgraph] of subgraphsForName.entries()) {
                    subgraphToEnumName.set(subgraph.name, `${sanitizedName}_${index + 1}`);
                }
            }
        }
        const graphEnum = this.graphEnum(schema);
        const graphDirective = this.graphDirective(schema);
        for (const subgraph of subgraphs) {
            const enumValue = graphEnum.addValue(subgraphToEnumName.get(subgraph.name));
            enumValue.applyDirective(graphDirective, { name: subgraph.name, url: subgraph.url });
        }
        return subgraphToEnumName;
    }
    fieldSetScalar(schema) {
        return this.type(schema, 'FieldSet');
    }
    graphEnum(schema) {
        return this.type(schema, 'Graph');
    }
    graphDirective(schema) {
        return this.directive(schema, 'graph');
    }
    directiveDirective(schema) {
        return this.directive(schema, 'directive');
    }
    typeDirective(schema) {
        return this.directive(schema, 'type');
    }
    implementsDirective(schema) {
        return this.directive(schema, 'implements');
    }
    fieldDirective(schema) {
        return this.directive(schema, 'field');
    }
    unionMemberDirective(schema) {
        return this.directive(schema, 'unionMember');
    }
    enumValueDirective(schema) {
        return this.directive(schema, 'enumValue');
    }
    ownerDirective(schema) {
        return this.directive(schema, 'owner');
    }
    get defaultCorePurpose() {
        return 'EXECUTION';
    }
}
exports.JoinSpecDefinition = JoinSpecDefinition;
exports.JOIN_VERSIONS = new coreSpec_1.FeatureDefinitions(exports.joinIdentity)
    .add(new JoinSpecDefinition(new coreSpec_1.FeatureVersion(0, 1)))
    .add(new JoinSpecDefinition(new coreSpec_1.FeatureVersion(0, 2)))
    .add(new JoinSpecDefinition(new coreSpec_1.FeatureVersion(0, 3), new coreSpec_1.FeatureVersion(2, 0)))
    .add(new JoinSpecDefinition(new coreSpec_1.FeatureVersion(0, 4), new coreSpec_1.FeatureVersion(2, 7)))
    .add(new JoinSpecDefinition(new coreSpec_1.FeatureVersion(0, 5), new coreSpec_1.FeatureVersion(2, 8)));
(0, knownCoreFeatures_1.registerKnownFeature)(exports.JOIN_VERSIONS);
//# sourceMappingURL=joinSpec.js.map