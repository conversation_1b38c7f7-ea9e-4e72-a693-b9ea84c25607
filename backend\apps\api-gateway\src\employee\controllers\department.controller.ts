import { Controller, Get, UseGuards, Request } from '@nestjs/common';
import { ApiT<PERSON>s, ApiBearerAuth } from '@nestjs/swagger';

import { JwtAuthGuard, RolesGuard } from '@app/security/guards';
import { Roles } from '@app/security/decorators/roles.decorator';
import { UserRole } from '@app/common';
import { RequestWithUser } from '@app/security';

import { DepartmentService } from '../services/department.service';

@ApiTags('Departments')
@Controller('departments')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
export class DepartmentController {
  constructor(private readonly departmentService: DepartmentService) {}

  @Get()
  @Roles(UserRole.ADMIN, UserRole.HR_MANAGER, UserRole.MANAGER, UserRole.EMPLOYEE)
  async findAll(@Request() req: RequestWithUser) {
    return this.departmentService.findAll(req.user.tenantId);
  }
}
