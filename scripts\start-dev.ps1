# =============================================================================
# PeopleNest HRMS - Development Environment Startup Script (PowerShell)
# =============================================================================
# This script starts all services for development including infrastructure,
# backend, frontend, and AI services
# =============================================================================

param(
    [switch]$SkipInfrastructure,
    [switch]$BackendOnly,
    [switch]$FrontendOnly,
    [switch]$AIOnly,
    [switch]$Verbose
)

# Colors for output
$Green = "Green"
$Red = "Red"
$Yellow = "Yellow"
$Blue = "Blue"
$Cyan = "Cyan"

function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

function Write-Header {
    param([string]$Title)
    Write-Host ""
    Write-ColorOutput "=" * 80 $Blue
    Write-ColorOutput "  $Title" $Cyan
    Write-ColorOutput "=" * 80 $Blue
    Write-Host ""
}

function Test-ServiceHealth {
    param([string]$ServiceName, [string]$Url, [int]$TimeoutSeconds = 30)
    
    Write-ColorOutput "🔍 Checking $ServiceName health..." $Yellow
    
    $startTime = Get-Date
    do {
        try {
            $response = Invoke-WebRequest -Uri $Url -TimeoutSec 5 -UseBasicParsing -ErrorAction SilentlyContinue
            if ($response.StatusCode -eq 200) {
                Write-ColorOutput "✅ $ServiceName is healthy!" $Green
                return $true
            }
        }
        catch {
            # Service not ready yet
        }
        
        Start-Sleep -Seconds 2
        $elapsed = (Get-Date) - $startTime
    } while ($elapsed.TotalSeconds -lt $TimeoutSeconds)
    
    Write-ColorOutput "❌ $ServiceName health check failed after $TimeoutSeconds seconds" $Red
    return $false
}

function Start-Infrastructure {
    Write-Header "Starting Infrastructure Services"
    
    # Check if Docker is running
    try {
        docker version | Out-Null
        Write-ColorOutput "✅ Docker is running" $Green
    }
    catch {
        Write-ColorOutput "❌ Docker is not running. Please start Docker Desktop first." $Red
        exit 1
    }
    
    # Start infrastructure services
    Write-ColorOutput "🚀 Starting infrastructure services..." $Yellow
    docker-compose up -d
    
    if ($LASTEXITCODE -eq 0) {
        Write-ColorOutput "✅ Infrastructure services started successfully!" $Green
    } else {
        Write-ColorOutput "❌ Failed to start infrastructure services" $Red
        exit 1
    }
    
    # Wait for critical services to be healthy
    Write-ColorOutput "⏳ Waiting for services to be ready..." $Yellow
    Start-Sleep -Seconds 10
    
    # Check service health
    $services = @(
        @{Name="PostgreSQL"; Url="http://localhost:5432"; Skip=$true}, # PostgreSQL doesn't have HTTP endpoint
        @{Name="Redis"; Url="http://localhost:6379"; Skip=$true}, # Redis doesn't have HTTP endpoint  
        @{Name="Elasticsearch"; Url="http://localhost:9200"},
        @{Name="Kibana"; Url="http://localhost:5601"},
        @{Name="MinIO"; Url="http://localhost:9000"},
        @{Name="Grafana"; Url="http://localhost:3001"},
        @{Name="Prometheus"; Url="http://localhost:9090"}
    )
    
    foreach ($service in $services) {
        if (-not $service.Skip) {
            Test-ServiceHealth -ServiceName $service.Name -Url $service.Url -TimeoutSeconds 60
        }
    }
    
    Write-ColorOutput "🎉 Infrastructure services are ready!" $Green
}

function Start-Backend {
    Write-Header "Starting Backend Service"
    
    # Check if backend dependencies are installed
    if (-not (Test-Path "backend/node_modules")) {
        Write-ColorOutput "📦 Installing backend dependencies..." $Yellow
        Set-Location backend
        npm install
        Set-Location ..
    }
    
    Write-ColorOutput "🚀 Starting NestJS backend..." $Yellow
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd backend; npm run start:dev" -WindowStyle Normal
    
    # Wait for backend to start
    Start-Sleep -Seconds 5
    
    # Test backend health
    if (Test-ServiceHealth -ServiceName "Backend API" -Url "http://localhost:4000/health" -TimeoutSeconds 60) {
        Write-ColorOutput "✅ Backend service started successfully!" $Green
        Write-ColorOutput "🌐 Backend API: http://localhost:4000" $Cyan
        Write-ColorOutput "📚 GraphQL Playground: http://localhost:4000/graphql" $Cyan
    }
}

function Start-Frontend {
    Write-Header "Starting Frontend Service"
    
    # Check if frontend dependencies are installed
    if (-not (Test-Path "frontend/node_modules")) {
        Write-ColorOutput "📦 Installing frontend dependencies..." $Yellow
        Set-Location frontend
        npm install
        Set-Location ..
    }
    
    Write-ColorOutput "🚀 Starting React frontend..." $Yellow
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd frontend; npm run dev" -WindowStyle Normal
    
    # Wait for frontend to start
    Start-Sleep -Seconds 5
    
    # Test frontend health
    if (Test-ServiceHealth -ServiceName "Frontend" -Url "http://localhost:3000" -TimeoutSeconds 60) {
        Write-ColorOutput "✅ Frontend service started successfully!" $Green
        Write-ColorOutput "🌐 Frontend: http://localhost:3000" $Cyan
    }
}

function Start-AIServices {
    Write-Header "Starting AI Services"
    
    # Check if Python is available
    try {
        python --version | Out-Null
        Write-ColorOutput "✅ Python is available" $Green
    }
    catch {
        Write-ColorOutput "❌ Python is not available. Please install Python first." $Red
        return
    }
    
    # Check if AI service dependencies are installed
    if (-not (Test-Path "ai-services/venv")) {
        Write-ColorOutput "📦 Setting up AI services virtual environment..." $Yellow
        Set-Location ai-services
        python -m venv venv
        .\venv\Scripts\Activate.ps1
        pip install -r requirements.txt
        Set-Location ..
    }
    
    Write-ColorOutput "🚀 Starting AI services..." $Yellow
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd ai-services; .\venv\Scripts\Activate.ps1; python -m uvicorn main:app --reload --port 8000" -WindowStyle Normal
    
    # Wait for AI services to start
    Start-Sleep -Seconds 5
    
    # Test AI services health
    if (Test-ServiceHealth -ServiceName "AI Services" -Url "http://localhost:8000/health" -TimeoutSeconds 60) {
        Write-ColorOutput "✅ AI services started successfully!" $Green
        Write-ColorOutput "🌐 AI Services: http://localhost:8000" $Cyan
        Write-ColorOutput "📚 AI API Docs: http://localhost:8000/docs" $Cyan
    }
}

function Show-ServiceStatus {
    Write-Header "Service Status Summary"
    
    $services = @(
        @{Name="Frontend (React)"; Url="http://localhost:3000"; Port=3000},
        @{Name="Backend (NestJS)"; Url="http://localhost:4000"; Port=4000},
        @{Name="AI Services (FastAPI)"; Url="http://localhost:8000"; Port=8000},
        @{Name="PostgreSQL"; Url="localhost:5432"; Port=5432; IsDB=$true},
        @{Name="Redis"; Url="localhost:6379"; Port=6379; IsDB=$true},
        @{Name="Elasticsearch"; Url="http://localhost:9200"; Port=9200},
        @{Name="Kibana"; Url="http://localhost:5601"; Port=5601},
        @{Name="MinIO"; Url="http://localhost:9000"; Port=9000},
        @{Name="Grafana"; Url="http://localhost:3001"; Port=3001},
        @{Name="Prometheus"; Url="http://localhost:9090"; Port=9090}
    )
    
    foreach ($service in $services) {
        $status = "❌ Down"
        $color = $Red
        
        try {
            if ($service.IsDB) {
                # For database services, check if port is listening
                $connection = Test-NetConnection -ComputerName localhost -Port $service.Port -WarningAction SilentlyContinue
                if ($connection.TcpTestSucceeded) {
                    $status = "✅ Running"
                    $color = $Green
                }
            } else {
                # For HTTP services, check HTTP response
                $response = Invoke-WebRequest -Uri $service.Url -TimeoutSec 3 -UseBasicParsing -ErrorAction SilentlyContinue
                if ($response.StatusCode -eq 200) {
                    $status = "✅ Running"
                    $color = $Green
                }
            }
        }
        catch {
            # Service is down
        }
        
        Write-ColorOutput ("  {0,-25} {1,-15} {2}" -f $service.Name, $status, $service.Url) $color
    }
    
    Write-Host ""
    Write-ColorOutput "🔑 Default Admin Credentials:" $Yellow
    Write-ColorOutput "   Username: awadhesh" $Cyan
    Write-ColorOutput "   Password: awadhesh123" $Cyan
    Write-ColorOutput "   Email: <EMAIL>" $Cyan
}

# Main execution
Write-Header "PeopleNest HRMS - Development Environment Startup"

try {
    # Start services based on parameters
    if (-not $SkipInfrastructure -and -not $BackendOnly -and -not $FrontendOnly -and -not $AIOnly) {
        Start-Infrastructure
    }
    
    if (-not $FrontendOnly -and -not $AIOnly) {
        Start-Backend
    }
    
    if (-not $BackendOnly -and -not $AIOnly) {
        Start-Frontend
    }
    
    if (-not $BackendOnly -and -not $FrontendOnly) {
        Start-AIServices
    }
    
    # Show final status
    Start-Sleep -Seconds 3
    Show-ServiceStatus
    
    Write-Header "🎉 PeopleNest HRMS Development Environment Ready!"
    Write-ColorOutput "All services are running. You can now access the application." $Green
    Write-ColorOutput "Press Ctrl+C in any service window to stop that service." $Yellow
    
} catch {
    Write-ColorOutput "❌ An error occurred: $($_.Exception.Message)" $Red
    exit 1
}
