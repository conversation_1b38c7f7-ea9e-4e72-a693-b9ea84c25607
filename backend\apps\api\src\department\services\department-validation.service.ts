import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Department, Employee } from '@app/database';

@Injectable()
export class DepartmentValidationService {
  constructor(
    @InjectRepository(Department)
    private readonly departmentRepository: Repository<Department>,
    @InjectRepository(Employee)
    private readonly employeeRepository: Repository<Employee>,
  ) {}

  async validateDepartmentCode(code: string, tenantId: string, excludeId?: string): Promise<boolean> {
    const query = this.departmentRepository
      .createQueryBuilder('department')
      .where('department.code = :code', { code })
      .andWhere('department.tenantId = :tenantId', { tenantId });

    if (excludeId) {
      query.andWhere('department.id != :excludeId', { excludeId });
    }

    const existing = await query.getOne();
    return !existing;
  }

  async validateParentDepartment(parentId: string, childId: string, tenantId: string): Promise<boolean> {
    // Prevent circular reference
    if (parentId === childId) {
      return false;
    }

    // Check if parent exists
    const parent = await this.departmentRepository.findOne({
      where: { id: parentId, tenantId },
    });

    if (!parent) {
      return false;
    }

    // Check for circular dependency by traversing up the hierarchy
    return this.checkCircularDependency(parentId, childId, tenantId);
  }

  async canDeleteDepartment(departmentId: string, tenantId: string): Promise<{ canDelete: boolean; reason?: string }> {
    // Check for active employees
    const employeeCount = await this.employeeRepository.count({
      where: { departmentId, tenantId, isActive: true },
    });

    if (employeeCount > 0) {
      return {
        canDelete: false,
        reason: `Department has ${employeeCount} active employees`,
      };
    }

    // Check for child departments
    const childCount = await this.departmentRepository.count({
      where: { parentId: departmentId, tenantId, isActive: true },
    });

    if (childCount > 0) {
      return {
        canDelete: false,
        reason: `Department has ${childCount} child departments`,
      };
    }

    return { canDelete: true };
  }

  private async checkCircularDependency(parentId: string, childId: string, tenantId: string): Promise<boolean> {
    const visited = new Set<string>();
    let currentId = parentId;

    while (currentId && !visited.has(currentId)) {
      if (currentId === childId) {
        return false; // Circular dependency found
      }

      visited.add(currentId);

      const department = await this.departmentRepository.findOne({
        where: { id: currentId, tenantId },
        select: ['parentId'],
      });

      currentId = department?.parentId || null;
    }

    return true; // No circular dependency
  }
}
