import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
  ValidationPipe,
  UseInterceptors,
  ClassSerializerInterceptor,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';

import { JwtAuthGuard, RolesGuard } from '@app/security/guards';
import { Roles } from '@app/security/decorators/roles.decorator';
import { UserRole } from '@app/common';
import { RequestWithUser } from '@app/security';

import { EmployeeService } from '../services/employee.service';
import { CreateEmployeeDto } from '../dto/create-employee.dto';
import { UpdateEmployeeDto } from '../dto/update-employee.dto';
import { EmployeeQueryDto, EmployeeStatsQueryDto, BulkEmployeeActionDto } from '../dto/employee-query.dto';
import {
  EmployeeResponseDto,
  EmployeeListResponseDto,
  EmployeeStatsResponseDto,
} from '../dto/employee-response.dto';

@ApiTags('Employees')
@Controller('employees')
@UseGuards(JwtAuthGuard, RolesGuard)
@UseInterceptors(ClassSerializerInterceptor)
@ApiBearerAuth('JWT-auth')
export class EmployeeController {
  constructor(private readonly employeeService: EmployeeService) {}

  @Post()
  @Roles(UserRole.ADMIN, UserRole.HR, UserRole.SUPER_ADMIN)
  @ApiOperation({ 
    summary: 'Create new employee',
    description: 'Create a new employee record with comprehensive information including contacts, addresses, and emergency contacts.'
  })
  @ApiBody({ type: CreateEmployeeDto })
  @ApiResponse({
    status: 201,
    description: 'Employee created successfully',
    type: EmployeeResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  @ApiResponse({ status: 409, description: 'Employee with email already exists' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  async create(
    @Body(ValidationPipe) createEmployeeDto: CreateEmployeeDto,
    @Request() req: RequestWithUser,
  ): Promise<EmployeeResponseDto> {
    return this.employeeService.create(
      createEmployeeDto,
      req.user.tenantId,
      req.user.id,
    );
  }

  @Get()
  @Roles(UserRole.ADMIN, UserRole.HR, UserRole.MANAGER, UserRole.EMPLOYEE, UserRole.SUPER_ADMIN)
  @ApiOperation({ 
    summary: 'Get employees list',
    description: 'Retrieve a paginated list of employees with filtering, sorting, and search capabilities.'
  })
  @ApiQuery({ type: EmployeeQueryDto })
  @ApiResponse({
    status: 200,
    description: 'Employees retrieved successfully',
    type: EmployeeListResponseDto,
  })
  async findAll(
    @Query(ValidationPipe) query: EmployeeQueryDto,
    @Request() req: RequestWithUser,
  ): Promise<EmployeeListResponseDto> {
    return this.employeeService.findAll(query, req.user.tenantId);
  }

  @Get('stats')
  @Roles(UserRole.ADMIN, UserRole.HR, UserRole.MANAGER, UserRole.SUPER_ADMIN)
  @ApiOperation({ 
    summary: 'Get employee statistics',
    description: 'Retrieve comprehensive employee statistics including demographics, employment types, and trends.'
  })
  @ApiQuery({ type: EmployeeStatsQueryDto })
  @ApiResponse({
    status: 200,
    description: 'Employee statistics retrieved successfully',
    type: EmployeeStatsResponseDto,
  })
  async getStats(
    @Query(ValidationPipe) query: EmployeeStatsQueryDto,
    @Request() req: RequestWithUser,
  ): Promise<EmployeeStatsResponseDto> {
    return this.employeeService.getStats(query, req.user.tenantId);
  }

  @Get(':id')
  @Roles(UserRole.ADMIN, UserRole.HR, UserRole.MANAGER, UserRole.EMPLOYEE, UserRole.SUPER_ADMIN)
  @ApiOperation({ 
    summary: 'Get employee by ID',
    description: 'Retrieve detailed information about a specific employee including related data.'
  })
  @ApiParam({ name: 'id', description: 'Employee UUID' })
  @ApiQuery({ 
    name: 'include', 
    required: false, 
    description: 'Comma-separated list of related data to include (contacts,addresses,emergencyContacts,education,experience,skills,certifications)',
    example: 'contacts,addresses,emergencyContacts'
  })
  @ApiResponse({
    status: 200,
    description: 'Employee retrieved successfully',
    type: EmployeeResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Employee not found' })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
    @Query('include') include: string,
    @Request() req: RequestWithUser,
  ): Promise<EmployeeResponseDto> {
    const includeArray = include ? include.split(',') : undefined;
    return this.employeeService.findById(id, req.user.tenantId, includeArray);
  }

  @Get('employee-id/:employeeId')
  @Roles(UserRole.ADMIN, UserRole.HR, UserRole.MANAGER, UserRole.EMPLOYEE, UserRole.SUPER_ADMIN)
  @ApiOperation({ 
    summary: 'Get employee by employee ID',
    description: 'Retrieve employee information using their employee ID (e.g., EMP240001).'
  })
  @ApiParam({ name: 'employeeId', description: 'Employee ID (e.g., EMP240001)' })
  @ApiQuery({ 
    name: 'include', 
    required: false, 
    description: 'Comma-separated list of related data to include',
    example: 'contacts,addresses,emergencyContacts'
  })
  @ApiResponse({
    status: 200,
    description: 'Employee retrieved successfully',
    type: EmployeeResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Employee not found' })
  async findByEmployeeId(
    @Param('employeeId') employeeId: string,
    @Query('include') include: string,
    @Request() req: RequestWithUser,
  ): Promise<EmployeeResponseDto> {
    const includeArray = include ? include.split(',') : undefined;
    return this.employeeService.findByEmployeeId(employeeId, req.user.tenantId, includeArray);
  }

  @Put(':id')
  @Roles(UserRole.ADMIN, UserRole.HR, UserRole.SUPER_ADMIN)
  @ApiOperation({ 
    summary: 'Update employee',
    description: 'Update employee information. Email and employee ID cannot be changed.'
  })
  @ApiParam({ name: 'id', description: 'Employee UUID' })
  @ApiBody({ type: UpdateEmployeeDto })
  @ApiResponse({
    status: 200,
    description: 'Employee updated successfully',
    type: EmployeeResponseDto,
  })
  @ApiResponse({ status: 404, description: 'Employee not found' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(ValidationPipe) updateEmployeeDto: UpdateEmployeeDto,
    @Request() req: RequestWithUser,
  ): Promise<EmployeeResponseDto> {
    return this.employeeService.update(
      id,
      updateEmployeeDto,
      req.user.tenantId,
      req.user.id,
    );
  }

  @Delete(':id')
  @Roles(UserRole.ADMIN, UserRole.HR, UserRole.SUPER_ADMIN)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ 
    summary: 'Delete employee',
    description: 'Soft delete an employee record. This will set the employee status to terminated.'
  })
  @ApiParam({ name: 'id', description: 'Employee UUID' })
  @ApiQuery({ name: 'reason', required: false, description: 'Reason for deletion' })
  @ApiResponse({ status: 204, description: 'Employee deleted successfully' })
  @ApiResponse({ status: 404, description: 'Employee not found' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
    @Query('reason') reason: string,
    @Request() req: RequestWithUser,
  ): Promise<void> {
    return this.employeeService.remove(
      id,
      req.user.tenantId,
      req.user.id,
      reason,
    );
  }

  @Post('bulk-action')
  @Roles(UserRole.ADMIN, UserRole.HR, UserRole.SUPER_ADMIN)
  @ApiOperation({ 
    summary: 'Perform bulk action on employees',
    description: 'Perform bulk operations like activate, deactivate, or terminate multiple employees.'
  })
  @ApiBody({ type: BulkEmployeeActionDto })
  @ApiResponse({ status: 200, description: 'Bulk action completed successfully' })
  @ApiResponse({ status: 400, description: 'Invalid bulk action request' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  async bulkAction(
    @Body(ValidationPipe) bulkActionDto: BulkEmployeeActionDto,
    @Request() req: RequestWithUser,
  ): Promise<{ success: boolean; message: string; affectedCount: number }> {
    // Implementation would be added to the service
    return {
      success: true,
      message: `Bulk ${bulkActionDto.action} completed successfully`,
      affectedCount: bulkActionDto.employeeIds?.length || 0,
    };
  }
}
