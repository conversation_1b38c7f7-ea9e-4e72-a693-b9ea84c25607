# JWT Secret Generator Script for PowerShell
# Generates secure JWT secrets and updates environment files

param(
    [switch]$Force,
    [string]$EnvFile = ".env"
)

# Configuration
$ENV_FILES = @(".env", ".env.local", ".env.development", ".env.production", ".env.staging", ".env.test")
$JWT_SECRET_KEYS = @("JWT_SECRET", "JWT_REFRESH_SECRET")

function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Generate-SecureSecret {
    param([int]$Length = 64)
    
    # Generate cryptographically secure random bytes
    $bytes = New-Object byte[] $Length
    $rng = [System.Security.Cryptography.RNGCryptoServiceProvider]::Create()
    $rng.GetBytes($bytes)
    $rng.Dispose()
    
    # Convert to base64url (URL-safe base64)
    $base64 = [Convert]::ToBase64String($bytes)
    $base64url = $base64.Replace('+', '-').Replace('/', '_').TrimEnd('=')
    
    return $base64url
}

function Generate-JWTSecrets {
    $timestamp = (Get-Date).ToString("yyyy-MM-ddTHH-mm-ss")
    $secrets = @{
        JWT_SECRET = Generate-SecureSecret -Length 64
        JWT_REFRESH_SECRET = Generate-SecureSecret -Length 64
        generated_at = $timestamp
        entropy_bits = 512
    }
    
    Write-ColorOutput "🔐 Generated new JWT secrets:" "Green"
    Write-ColorOutput "   JWT_SECRET: $($secrets.JWT_SECRET.Substring(0, 20))..." "Yellow"
    Write-ColorOutput "   JWT_REFRESH_SECRET: $($secrets.JWT_REFRESH_SECRET.Substring(0, 20))..." "Yellow"
    Write-ColorOutput "   Entropy: $($secrets.entropy_bits) bits" "Cyan"
    Write-ColorOutput "   Generated at: $($secrets.generated_at)" "Cyan"
    
    return $secrets
}

function Update-EnvFile {
    param(
        [string]$FilePath,
        [hashtable]$Secrets
    )
    
    try {
        if (-not (Test-Path $FilePath)) {
            Write-ColorOutput "⚠️  File $FilePath does not exist, skipping..." "Yellow"
            return $false
        }

        $content = Get-Content $FilePath -Raw
        $updated = $false

        # Create backup
        $backupPath = "$FilePath.backup.$(Get-Date -Format 'yyyyMMddHHmmss')"
        Copy-Item $FilePath $backupPath
        
        # Update JWT_SECRET
        if ($content -match "^JWT_SECRET=.*$") {
            $content = $content -replace "^JWT_SECRET=.*$", "JWT_SECRET=$($Secrets.JWT_SECRET)"
            $updated = $true
        } else {
            $content += "`nJWT_SECRET=$($Secrets.JWT_SECRET)"
            $updated = $true
        }

        # Update JWT_REFRESH_SECRET
        if ($content -match "^JWT_REFRESH_SECRET=.*$") {
            $content = $content -replace "^JWT_REFRESH_SECRET=.*$", "JWT_REFRESH_SECRET=$($Secrets.JWT_REFRESH_SECRET)"
            $updated = $true
        } else {
            $content += "`nJWT_REFRESH_SECRET=$($Secrets.JWT_REFRESH_SECRET)"
            $updated = $true
        }

        if ($updated) {
            Set-Content -Path $FilePath -Value $content -NoNewline
            Write-ColorOutput "✅ Updated $FilePath" "Green"
            Write-ColorOutput "📁 Backup created: $backupPath" "Cyan"
            return $true
        }

        return $false
    }
    catch {
        Write-ColorOutput "❌ Error updating $FilePath : $($_.Exception.Message)" "Red"
        return $false
    }
}

function Save-SecretsReference {
    param([hashtable]$Secrets)
    
    $secretsDir = Join-Path $PWD ".secrets"
    $secretsFile = Join-Path $secretsDir "jwt-secrets-$($Secrets.generated_at).json"
    
    try {
        # Create .secrets directory if it doesn't exist
        if (-not (Test-Path $secretsDir)) {
            New-Item -ItemType Directory -Path $secretsDir -Force | Out-Null
        }

        # Save secrets with metadata
        $secretsData = @{
            JWT_SECRET = $Secrets.JWT_SECRET
            JWT_REFRESH_SECRET = $Secrets.JWT_REFRESH_SECRET
            generated_at = $Secrets.generated_at
            entropy_bits = $Secrets.entropy_bits
            note = "JWT secrets generated automatically. Keep this file secure!"
            usage = @{
                JWT_SECRET = "Used for signing access tokens"
                JWT_REFRESH_SECRET = "Used for signing refresh tokens"
            }
        }

        $secretsData | ConvertTo-Json -Depth 3 | Set-Content $secretsFile
        Write-ColorOutput "💾 Secrets reference saved: $secretsFile" "Green"
        
        # Add .secrets to .gitignore if not already present
        $gitignorePath = Join-Path $PWD ".gitignore"
        if (Test-Path $gitignorePath) {
            $gitignoreContent = Get-Content $gitignorePath -Raw
            if ($gitignoreContent -notmatch "\.secrets/") {
                Add-Content $gitignorePath "`n# JWT Secrets`n.secrets/"
                Write-ColorOutput "📝 Added .secrets/ to .gitignore" "Green"
            }
        }
    }
    catch {
        Write-ColorOutput "❌ Error saving secrets reference: $($_.Exception.Message)" "Red"
    }
}

# Main execution
function Main {
    Write-ColorOutput "🚀 PeopleNest HRMS - JWT Secret Generator" "Magenta"
    Write-ColorOutput "==========================================" "Magenta"
    Write-Host ""

    # Generate new secrets
    $secrets = Generate-JWTSecrets
    Write-Host ""

    # Update environment files
    $updatedFiles = 0
    foreach ($envFile in $ENV_FILES) {
        $filePath = Join-Path $PWD $envFile
        if (Update-EnvFile -FilePath $filePath -Secrets $secrets) {
            $updatedFiles++
        }
    }

    Write-Host ""
    
    if ($updatedFiles -gt 0) {
        Write-ColorOutput "✅ Successfully updated $updatedFiles environment file(s)" "Green"
        
        # Save secrets reference
        Save-SecretsReference -Secrets $secrets
        
        Write-Host ""
        Write-ColorOutput "🔒 Security Notes:" "Yellow"
        Write-ColorOutput "   • JWT secrets have been updated with cryptographically secure values" "White"
        Write-ColorOutput "   • Backup files have been created for rollback if needed" "White"
        Write-ColorOutput "   • Secrets reference saved in .secrets/ directory (git-ignored)" "White"
        Write-ColorOutput "   • Restart your application to use the new secrets" "White"
        Write-Host ""
        Write-ColorOutput "⚠️  Important:" "Red"
        Write-ColorOutput "   • Do not commit the new secrets to version control" "White"
        Write-ColorOutput "   • Update your production environment variables separately" "White"
        Write-ColorOutput "   • Existing JWT tokens will be invalidated" "White"
    } else {
        Write-ColorOutput "❌ No environment files were updated" "Red"
    }
}

# Run the script
Main
