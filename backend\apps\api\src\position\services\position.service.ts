import {
  Injectable,
  NotFoundException,
  ConflictException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';

import {
  Position,
  Department,
  Employee,
  User,
} from '@app/database';

import {
  CreatePositionDto,
  UpdatePositionDto,
  PositionQueryDto,
  PositionResponseDto,
  PaginatedPositionResponseDto,
} from '../dto';

@Injectable()
export class PositionService {
  private readonly logger = new Logger(PositionService.name);

  constructor(
    @InjectRepository(Position)
    private readonly positionRepository: Repository<Position>,
    @InjectRepository(Department)
    private readonly departmentRepository: Repository<Department>,
    @InjectRepository(Employee)
    private readonly employeeRepository: Repository<Employee>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async create(
    createPositionDto: CreatePositionDto,
    createdBy: string,
    tenantId: string,
  ): Promise<PositionResponseDto> {
    this.logger.log(`Creating position: ${createPositionDto.title}`);

    // Check if position code already exists
    const existingPosition = await this.positionRepository.findOne({
      where: { code: createPositionDto.code, tenantId },
    });

    if (existingPosition) {
      throw new ConflictException(`Position with code ${createPositionDto.code} already exists`);
    }

    // Validate department exists
    const department = await this.departmentRepository.findOne({
      where: { id: createPositionDto.departmentId, tenantId },
    });

    if (!department) {
      throw new NotFoundException(`Department with ID ${createPositionDto.departmentId} not found`);
    }

    // Validate reports to position exists if provided
    if (createPositionDto.reportsToId) {
      const reportsTo = await this.positionRepository.findOne({
        where: { id: createPositionDto.reportsToId, tenantId },
      });

      if (!reportsTo) {
        throw new NotFoundException(`Position with ID ${createPositionDto.reportsToId} not found`);
      }
    }

    // Validate salary range
    if (createPositionDto.minSalary && createPositionDto.maxSalary) {
      if (createPositionDto.minSalary > createPositionDto.maxSalary) {
        throw new ConflictException('Minimum salary cannot be greater than maximum salary');
      }
    }

    // Create position
    const position = this.positionRepository.create({
      ...createPositionDto,
      tenantId,
      createdBy,
      updatedBy: createdBy,
    });

    const savedPosition = await this.positionRepository.save(position);

    // Emit event
    this.eventEmitter.emit('position.created', {
      positionId: savedPosition.id,
      tenantId,
      createdBy,
    });

    return this.mapToResponseDto(savedPosition);
  }

  async findAll(
    query: PositionQueryDto,
    tenantId: string,
  ): Promise<PaginatedPositionResponseDto> {
    const { page = 1, limit = 10, search, sortBy = 'title', sortOrder = 'ASC' } = query;
    const skip = (page - 1) * limit;

    const queryBuilder = this.createQueryBuilder(tenantId);

    // Apply filters
    this.applyFilters(queryBuilder, query);

    // Apply search
    if (search) {
      queryBuilder.andWhere(
        '(position.title ILIKE :search OR position.code ILIKE :search OR position.description ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    // Apply sorting
    queryBuilder.orderBy(`position.${sortBy}`, sortOrder);

    // Apply pagination
    queryBuilder.skip(skip).take(limit);

    const [positions, total] = await queryBuilder.getManyAndCount();

    const data = await Promise.all(
      positions.map(position => this.mapToResponseDto(position))
    );

    return {
      data,
      meta: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPreviousPage: page > 1,
      },
    };
  }

  async findOne(id: string, tenantId: string): Promise<PositionResponseDto> {
    const position = await this.positionRepository.findOne({
      where: { id, tenantId },
      relations: ['department', 'reportsTo'],
    });

    if (!position) {
      throw new NotFoundException(`Position with ID ${id} not found`);
    }

    return this.mapToResponseDto(position);
  }

  async update(
    id: string,
    updatePositionDto: UpdatePositionDto,
    updatedBy: string,
    tenantId: string,
  ): Promise<PositionResponseDto> {
    const position = await this.positionRepository.findOne({
      where: { id, tenantId },
    });

    if (!position) {
      throw new NotFoundException(`Position with ID ${id} not found`);
    }

    // Validate department if being updated
    if (updatePositionDto.departmentId && updatePositionDto.departmentId !== position.departmentId) {
      const department = await this.departmentRepository.findOne({
        where: { id: updatePositionDto.departmentId, tenantId },
      });

      if (!department) {
        throw new NotFoundException(`Department with ID ${updatePositionDto.departmentId} not found`);
      }
    }

    // Validate reports to position if being updated
    if (updatePositionDto.reportsToId && updatePositionDto.reportsToId !== position.reportsToId) {
      const reportsTo = await this.positionRepository.findOne({
        where: { id: updatePositionDto.reportsToId, tenantId },
      });

      if (!reportsTo) {
        throw new NotFoundException(`Position with ID ${updatePositionDto.reportsToId} not found`);
      }

      // Prevent circular reference
      if (updatePositionDto.reportsToId === id) {
        throw new ConflictException('Position cannot report to itself');
      }
    }

    // Validate salary range
    const minSalary = updatePositionDto.minSalary ?? position.minSalary;
    const maxSalary = updatePositionDto.maxSalary ?? position.maxSalary;
    
    if (minSalary && maxSalary && minSalary > maxSalary) {
      throw new ConflictException('Minimum salary cannot be greater than maximum salary');
    }

    // Update position
    Object.assign(position, updatePositionDto, { updatedBy });
    const savedPosition = await this.positionRepository.save(position);

    // Emit event
    this.eventEmitter.emit('position.updated', {
      positionId: savedPosition.id,
      tenantId,
      updatedBy,
      changes: updatePositionDto,
    });

    return this.mapToResponseDto(savedPosition);
  }

  async remove(id: string, deletedBy: string, tenantId: string): Promise<void> {
    const position = await this.positionRepository.findOne({
      where: { id, tenantId },
    });

    if (!position) {
      throw new NotFoundException(`Position with ID ${id} not found`);
    }

    // Check if position has employees
    const employeeCount = await this.employeeRepository.count({
      where: { positionId: id, tenantId, isActive: true },
    });

    if (employeeCount > 0) {
      throw new ConflictException('Cannot delete position with active employees');
    }

    // Soft delete
    position.isActive = false;
    position.updatedBy = deletedBy;
    position.deletedAt = new Date();

    await this.positionRepository.save(position);

    // Emit event
    this.eventEmitter.emit('position.deleted', {
      positionId: id,
      tenantId,
      deletedBy,
    });
  }

  async getPositionEmployees(id: string, tenantId: string): Promise<any[]> {
    const position = await this.positionRepository.findOne({
      where: { id, tenantId },
    });

    if (!position) {
      throw new NotFoundException(`Position with ID ${id} not found`);
    }

    const employees = await this.employeeRepository.find({
      where: { positionId: id, tenantId, isActive: true },
      relations: ['department'],
      order: { lastName: 'ASC' },
    });

    return employees.map(employee => ({
      id: employee.id,
      employeeId: employee.employeeId,
      fullName: `${employee.firstName} ${employee.lastName}`,
      email: employee.email,
      department: employee.department ? {
        id: employee.department.id,
        name: employee.department.name,
        code: employee.department.code,
      } : null,
      employmentStatus: employee.employmentStatus,
      hireDate: employee.hireDate.toISOString(),
    }));
  }

  async getPositionsByDepartment(departmentId: string, tenantId: string): Promise<any[]> {
    const department = await this.departmentRepository.findOne({
      where: { id: departmentId, tenantId },
    });

    if (!department) {
      throw new NotFoundException(`Department with ID ${departmentId} not found`);
    }

    const positions = await this.positionRepository.find({
      where: { departmentId, tenantId, isActive: true },
      order: { title: 'ASC' },
    });

    return Promise.all(
      positions.map(async position => {
        const employeeCount = await this.employeeRepository.count({
          where: { positionId: position.id, tenantId, isActive: true },
        });

        return {
          id: position.id,
          title: position.title,
          code: position.code,
          level: position.level,
          minSalary: position.minSalary,
          maxSalary: position.maxSalary,
          employeeCount,
        };
      })
    );
  }

  private createQueryBuilder(tenantId: string): SelectQueryBuilder<Position> {
    return this.positionRepository
      .createQueryBuilder('position')
      .leftJoinAndSelect('position.department', 'department')
      .leftJoinAndSelect('position.reportsTo', 'reportsTo')
      .where('position.tenantId = :tenantId', { tenantId });
  }

  private applyFilters(queryBuilder: SelectQueryBuilder<Position>, query: PositionQueryDto): void {
    if (query.departmentId) {
      queryBuilder.andWhere('position.departmentId = :departmentId', {
        departmentId: query.departmentId,
      });
    }

    if (query.level) {
      queryBuilder.andWhere('position.level = :level', {
        level: query.level,
      });
    }

    if (query.reportsToId) {
      queryBuilder.andWhere('position.reportsToId = :reportsToId', {
        reportsToId: query.reportsToId,
      });
    }

    if (!query.includeInactive) {
      queryBuilder.andWhere('position.isActive = :isActive', { isActive: true });
    }
  }

  private async mapToResponseDto(position: Position): Promise<PositionResponseDto> {
    // Get employee count
    const employeeCount = await this.employeeRepository.count({
      where: { positionId: position.id, isActive: true },
    });

    return {
      id: position.id,
      title: position.title,
      code: position.code,
      description: position.description,
      department: position.department ? {
        id: position.department.id,
        name: position.department.name,
        code: position.department.code,
      } : null,
      level: position.level,
      reportsTo: position.reportsTo ? {
        id: position.reportsTo.id,
        title: position.reportsTo.title,
        code: position.reportsTo.code,
      } : null,
      minSalary: position.minSalary,
      maxSalary: position.maxSalary,
      skills: position.skills,
      qualifications: position.qualifications,
      experienceYears: position.experienceYears,
      responsibilities: position.responsibilities,
      employeeCount,
      isActive: position.isActive,
      createdAt: position.createdAt.toISOString(),
      updatedAt: position.updatedAt.toISOString(),
      metadata: position.metadata,
    };
  }
}
