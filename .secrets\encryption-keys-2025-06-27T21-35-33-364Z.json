{"ENCRYPTION_KEY": "29018730ca123d90b2433a4e5b2fbc00ae1cf61bd4848a8f77d7d080ee425d0e", "ENCRYPTION_MASTER_KEY": "e4bc8949052f2fc9e35674e9a454b372c4968265c35ba43c7c682b5447accb9e82f53be664cde050eff7fbd007b3bd37b6175dd7f5def1b9ff524d8d447aced5", "INDEX_HASH_SALT": "7e6b4993ee9d3d909be264f5d993fe113b8dc0a407fc3b767ef5fe8d4e845b2e", "generated_at": "2025-06-27T21-35-33-364Z", "algorithm": "aes-256-gcm", "key_lengths": {"ENCRYPTION_KEY": "32 bytes (256 bits)", "ENCRYPTION_MASTER_KEY": "64 bytes (512 bits)", "INDEX_HASH_SALT": "32 bytes (256 bits)"}, "entropy_bits": {"ENCRYPTION_KEY": 256, "ENCRYPTION_MASTER_KEY": 512, "INDEX_HASH_SALT": 256, "total": 1024}, "note": "Encryption keys generated automatically. Keep this file secure!", "usage": {"ENCRYPTION_KEY": "Primary encryption key for AES-256-GCM data encryption", "ENCRYPTION_MASTER_KEY": "Master key for tenant-specific key derivation", "INDEX_HASH_SALT": "Salt for searchable encryption index hashing"}, "security_notes": ["These keys are used for data encryption and must be kept secure", "Changing these keys will make existing encrypted data unreadable", "Use proper key rotation procedures in production", "Store keys in secure key management systems for production use"]}