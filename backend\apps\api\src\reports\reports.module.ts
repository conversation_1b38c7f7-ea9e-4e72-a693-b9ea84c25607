import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// Entities
import {
  Report,
  ReportTemplate,
  Employee,
  Department,
  Position,
  PayrollPeriod,
  Payslip,
  TimeEntry,
  LeaveRequest,
  PerformanceReview,
  User,
  Tenant,
  AuditLog,
} from '@app/database';

// Controllers
import { ReportController } from './controllers/report.controller';
import { ReportTemplateController } from './controllers/report-template.controller';
import { AnalyticsController } from './controllers/analytics.controller';

// Services
import { ReportService } from './services/report.service';
import { ReportTemplateService } from './services/report-template.service';
import { AnalyticsService } from './services/analytics.service';
import { ReportGenerationService } from './services/report-generation.service';
import { DataExportService } from './services/data-export.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Report,
      ReportTemplate,
      Employee,
      Department,
      Position,
      PayrollPeriod,
      Payslip,
      TimeEntry,
      LeaveRequest,
      PerformanceReview,
      User,
      Tenant,
      AuditLog,
    ]),
  ],
  controllers: [
    ReportController,
    ReportTemplateController,
    AnalyticsController,
  ],
  providers: [
    ReportService,
    ReportTemplateService,
    AnalyticsService,
    ReportGenerationService,
    DataExportService,
  ],
  exports: [
    ReportService,
    ReportTemplateService,
    AnalyticsService,
    ReportGenerationService,
    DataExportService,
  ],
})
export class ReportsModule {}
