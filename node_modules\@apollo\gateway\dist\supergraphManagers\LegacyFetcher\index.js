"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LegacyFetcher = void 0;
const resolvable_1 = __importDefault(require("@josephg/resolvable"));
const config_1 = require("../../config");
const composition_1 = require("@apollo/composition");
class LegacyFetcher {
    constructor(options) {
        this.timerRef = null;
        this.config = options;
        this.state = { phase: 'initialized' };
        this.issueDeprecationWarnings();
    }
    issueDeprecationWarnings() {
        var _a, _b;
        if ('experimental_updateSupergraphSdl' in this.config.gatewayConfig) {
            (_a = this.config.logger) === null || _a === void 0 ? void 0 : _a.warn('The `experimental_updateSupergraphSdl` option is deprecated and will be removed in a future version of `@apollo/gateway`. Please migrate to the function form of the `supergraphSdl` configuration option.');
        }
        if ('experimental_updateServiceDefinitions' in this.config.gatewayConfig) {
            (_b = this.config.logger) === null || _b === void 0 ? void 0 : _b.warn('The `experimental_updateServiceDefinitions` option is deprecated and will be removed in a future version of `@apollo/gateway`. Please migrate to the function form of the `supergraphSdl` configuration option.');
        }
    }
    async initialize({ update, healthCheck, getDataSource, }) {
        this.update = update;
        this.getDataSource = getDataSource;
        if (this.config.subgraphHealthCheck) {
            this.healthCheck = healthCheck;
        }
        let initialSupergraphSdl = null;
        try {
            initialSupergraphSdl = await this.updateSupergraphSdl();
        }
        catch (e) {
            this.logUpdateFailure(e);
            throw e;
        }
        if (this.config.pollIntervalInMs) {
            this.beginPolling();
        }
        return {
            supergraphSdl: initialSupergraphSdl,
            cleanup: async () => {
                if (this.state.phase === 'polling') {
                    await this.state.pollingPromise;
                }
                this.state = { phase: 'stopped' };
                if (this.timerRef) {
                    clearTimeout(this.timerRef);
                    this.timerRef = null;
                }
            },
        };
    }
    async updateSupergraphSdl() {
        var _a, _b;
        const result = await this.config.updateServiceDefinitions(this.config.gatewayConfig);
        if ((0, config_1.isSupergraphSdlUpdate)(result)) {
            if (this.compositionId === result.id)
                return null;
            await ((_a = this.healthCheck) === null || _a === void 0 ? void 0 : _a.call(this, result.supergraphSdl));
            this.compositionId = result.id;
            return result.supergraphSdl;
        }
        else if ((0, config_1.isServiceDefinitionUpdate)(result)) {
            const supergraphSdl = this.updateByComposition(result);
            if (!supergraphSdl)
                return null;
            await ((_b = this.healthCheck) === null || _b === void 0 ? void 0 : _b.call(this, supergraphSdl));
            return supergraphSdl;
        }
        else {
            throw new Error('Programming error: unexpected result type from `updateServiceDefinitions`');
        }
    }
    updateByComposition(result) {
        var _a, _b;
        if (!result.serviceDefinitions ||
            JSON.stringify(this.serviceDefinitions) ===
                JSON.stringify(result.serviceDefinitions)) {
            (_a = this.config.logger) === null || _a === void 0 ? void 0 : _a.debug('No change in service definitions since last check.');
            return null;
        }
        if (this.serviceDefinitions) {
            (_b = this.config.logger) === null || _b === void 0 ? void 0 : _b.info('New service definitions were found.');
        }
        this.serviceDefinitions = result.serviceDefinitions;
        const supergraphSdl = this.createSupergraphFromServiceList(result.serviceDefinitions);
        if (!supergraphSdl) {
            throw new Error("A valid schema couldn't be composed. Falling back to previous schema.");
        }
        else {
            return supergraphSdl;
        }
    }
    createSupergraphFromServiceList(serviceList) {
        var _a, _b, _c;
        (_a = this.config.logger) === null || _a === void 0 ? void 0 : _a.debug(`Composing schema from service list: \n${serviceList
            .map(({ name, url }) => `  ${url || 'local'}: ${name}`)
            .join('\n')}`);
        const compositionResult = (0, composition_1.composeServices)(serviceList);
        if (compositionResult.errors) {
            const { errors } = compositionResult;
            throw Error("A valid schema couldn't be composed. The following composition errors were found:\n" +
                errors.map((e) => '\t' + e.message).join('\n'));
        }
        else {
            const { supergraphSdl } = compositionResult;
            for (const service of serviceList) {
                (_b = this.getDataSource) === null || _b === void 0 ? void 0 : _b.call(this, service);
            }
            (_c = this.config.logger) === null || _c === void 0 ? void 0 : _c.debug('Schema loaded and ready for execution');
            return supergraphSdl;
        }
    }
    beginPolling() {
        this.state = { phase: 'polling' };
        this.poll();
    }
    poll() {
        this.timerRef = setTimeout(async () => {
            var _a;
            if (this.state.phase === 'polling') {
                const pollingPromise = (0, resolvable_1.default)();
                this.state.pollingPromise = pollingPromise;
                try {
                    const maybeNewSupergraphSdl = await this.updateSupergraphSdl();
                    if (maybeNewSupergraphSdl) {
                        (_a = this.update) === null || _a === void 0 ? void 0 : _a.call(this, maybeNewSupergraphSdl);
                    }
                }
                catch (e) {
                    this.logUpdateFailure(e);
                }
                pollingPromise.resolve();
            }
            this.poll();
        }, this.config.pollIntervalInMs);
    }
    logUpdateFailure(e) {
        var _a, _b;
        (_a = this.config.logger) === null || _a === void 0 ? void 0 : _a.error('LegacyFetcher failed to update supergraph with the following error: ' +
            ((_b = e.message) !== null && _b !== void 0 ? _b : e));
    }
}
exports.LegacyFetcher = LegacyFetcher;
//# sourceMappingURL=index.js.map