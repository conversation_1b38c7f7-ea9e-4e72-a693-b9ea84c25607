import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { PayrollPeriod } from '@app/database/entities/payroll-period.entity';
import { Payslip } from '@app/database/entities/payslip.entity';
import { PayrollItem } from '@app/database/entities/payroll-item.entity';
import { Employee } from '@app/database/entities/employee.entity';
import { TaxConfiguration } from '@app/database/entities/tax-configuration.entity';
import { BenefitPlan } from '@app/database/entities/benefit-plan.entity';
import { EmployeeBenefit } from '@app/database/entities/employee-benefit.entity';
import { Deduction } from '@app/database/entities/deduction.entity';
import { EmployeeDeduction } from '@app/database/entities/employee-deduction.entity';
import { CalculatePayrollDto, PayrollCalculationResultDto } from '../dto/payroll-calculation.dto';
import { PayrollRunStatus, PayrollStatus, PayrollItemType, EmployeeStatus } from '@app/common/enums/status.enum';
import { PayrollPeriodService } from './payroll-period.service';

@Injectable()
export class PayrollCalculationService {
  private readonly logger = new Logger(PayrollCalculationService.name);

  constructor(
    @InjectRepository(PayrollPeriod)
    private readonly payrollPeriodRepository: Repository<PayrollPeriod>,
    @InjectRepository(Payslip)
    private readonly payslipRepository: Repository<Payslip>,
    @InjectRepository(PayrollItem)
    private readonly payrollItemRepository: Repository<PayrollItem>,
    @InjectRepository(Employee)
    private readonly employeeRepository: Repository<Employee>,
    @InjectRepository(TaxConfiguration)
    private readonly taxConfigurationRepository: Repository<TaxConfiguration>,
    @InjectRepository(BenefitPlan)
    private readonly benefitPlanRepository: Repository<BenefitPlan>,
    @InjectRepository(EmployeeBenefit)
    private readonly employeeBenefitRepository: Repository<EmployeeBenefit>,
    @InjectRepository(Deduction)
    private readonly deductionRepository: Repository<Deduction>,
    @InjectRepository(EmployeeDeduction)
    private readonly employeeDeductionRepository: Repository<EmployeeDeduction>,
    private readonly payrollPeriodService: PayrollPeriodService,
  ) {}

  async calculatePayroll(tenantId: string, calculatePayrollDto: CalculatePayrollDto): Promise<PayrollCalculationResultDto[]> {
    this.logger.log(`Starting payroll calculation for tenant ${tenantId}, period ${calculatePayrollDto.payrollPeriodId}`);

    // Get payroll period
    const payrollPeriod = await this.payrollPeriodService.findOne(tenantId, calculatePayrollDto.payrollPeriodId);

    // Validate payroll period status
    if (![PayrollRunStatus.DRAFT, PayrollRunStatus.CALCULATED].includes(payrollPeriod.status)) {
      throw new BadRequestException('Payroll period is not in a calculable state');
    }

    // Update payroll period status
    payrollPeriod.status = PayrollRunStatus.CALCULATING;
    payrollPeriod.calculationStartedAt = new Date();
    await this.payrollPeriodRepository.save(payrollPeriod);

    try {
      // Get employees to calculate
      const employees = await this.getEligibleEmployees(tenantId, calculatePayrollDto);

      // Get tax configurations
      const taxConfigurations = await this.getActiveTaxConfigurations(tenantId);

      const results: PayrollCalculationResultDto[] = [];
      let processedCount = 0;
      let failedCount = 0;

      for (const employee of employees) {
        try {
          this.logger.log(`Calculating payroll for employee ${employee.id}`);

          // Check if payslip already exists
          let payslip = await this.payslipRepository.findOne({
            where: {
              tenantId,
              employeeId: employee.id,
              payrollPeriodId: payrollPeriod.id,
            },
            relations: ['payrollItems'],
          });

          // Create or update payslip
          if (!payslip || calculatePayrollDto.recalculate) {
            payslip = await this.createOrUpdatePayslip(employee, payrollPeriod, payslip);
          }

          // Calculate payroll items
          const calculationResult = await this.calculateEmployeePayroll(
            employee,
            payrollPeriod,
            taxConfigurations,
            calculatePayrollDto.customItems || []
          );

          // Update payslip with calculated values
          await this.updatePayslipWithCalculation(payslip, calculationResult);

          results.push(calculationResult);
          processedCount++;

        } catch (error) {
          this.logger.error(`Failed to calculate payroll for employee ${employee.id}: ${error.message}`);
          failedCount++;

          // Add error result
          results.push({
            employeeId: employee.id,
            employeeName: `${employee.firstName} ${employee.lastName}`,
            grossPay: 0,
            netPay: 0,
            totalTaxes: 0,
            totalDeductions: 0,
            totalBenefits: 0,
            regularHours: 0,
            overtimeHours: 0,
            payrollItems: [],
            errors: [error.message],
          });
        }
      }

      // Update payroll period with calculation results
      payrollPeriod.status = PayrollRunStatus.CALCULATED;
      payrollPeriod.calculationCompletedAt = new Date();
      payrollPeriod.employeeCount = employees.length;
      payrollPeriod.processedCount = processedCount;
      payrollPeriod.failedCount = failedCount;

      // Calculate totals
      const totals = this.calculatePeriodTotals(results);
      payrollPeriod.totalGrossPay = totals.grossPay;
      payrollPeriod.totalNetPay = totals.netPay;
      payrollPeriod.totalTaxes = totals.taxes;
      payrollPeriod.totalDeductions = totals.deductions;
      payrollPeriod.totalBenefits = totals.benefits;

      await this.payrollPeriodRepository.save(payrollPeriod);

      this.logger.log(`Completed payroll calculation for tenant ${tenantId}. Processed: ${processedCount}, Failed: ${failedCount}`);

      return results;

    } catch (error) {
      // Update payroll period status on error
      payrollPeriod.status = PayrollRunStatus.FAILED;
      payrollPeriod.calculationCompletedAt = new Date();
      await this.payrollPeriodRepository.save(payrollPeriod);

      this.logger.error(`Payroll calculation failed for tenant ${tenantId}: ${error.message}`);
      throw error;
    }
  }

  private async getEligibleEmployees(tenantId: string, calculatePayrollDto: CalculatePayrollDto): Promise<Employee[]> {
    const where: any = { tenantId };

    // Filter by specific employee IDs if provided
    if (calculatePayrollDto.employeeIds && calculatePayrollDto.employeeIds.length > 0) {
      where.id = In(calculatePayrollDto.employeeIds);
    }

    // Filter by employment status
    const statuses = [EmployeeStatus.ACTIVE];
    if (calculatePayrollDto.includeInactive) {
      statuses.push(EmployeeStatus.INACTIVE);
    }
    if (calculatePayrollDto.includeProbationary) {
      statuses.push(EmployeeStatus.ACTIVE); // Use ACTIVE instead of non-existent PROBATION
    }
    where.status = In(statuses);

    return this.employeeRepository.find({
      where,
      relations: ['department', 'position'],
    });
  }

  private async getActiveTaxConfigurations(tenantId: string): Promise<TaxConfiguration[]> {
    return this.taxConfigurationRepository.find({
      where: {
        tenantId,
        isActive: true,
      },
      order: { priority: 'ASC' },
    });
  }

  private async createOrUpdatePayslip(employee: Employee, payrollPeriod: PayrollPeriod, existingPayslip?: Payslip): Promise<Payslip> {
    const payslipData = {
      tenantId: payrollPeriod.tenantId,
      employeeId: employee.id,
      payrollPeriodId: payrollPeriod.id,
      payslipNumber: existingPayslip?.payslipNumber || this.generatePayslipNumber(payrollPeriod, employee),
      status: PayrollStatus.DRAFT,
      payDate: payrollPeriod.payDate,
      periodStartDate: payrollPeriod.startDate,
      periodEndDate: payrollPeriod.endDate,
      currency: payrollPeriod.currency,
      exchangeRate: 1.0, // TODO: Implement currency conversion
      grossPay: 0,
      netPay: 0,
      totalTaxes: 0,
      totalDeductions: 0,
      totalBenefits: 0,
      totalReimbursements: 0,
      regularHours: 0,
      overtimeHours: 0,
      totalHours: 0,
      regularPay: 0,
      overtimePay: 0,
      paymentMethod: employee.bankingInfo ? 'DIRECT_DEPOSIT' : 'CHECK',
      isCorrection: false,
      isViewed: false,
      isLocked: false,
    };

    if (existingPayslip) {
      Object.assign(existingPayslip, payslipData);
      return this.payslipRepository.save(existingPayslip);
    } else {
      const payslip = this.payslipRepository.create(payslipData as any);
      return this.payslipRepository.save(payslip);
    }
  }

  private async calculateEmployeePayroll(
    employee: Employee,
    payrollPeriod: PayrollPeriod,
    taxConfigurations: TaxConfiguration[],
    customItems: any[]
  ): Promise<PayrollCalculationResultDto> {
    const result: PayrollCalculationResultDto = {
      employeeId: employee.id,
      employeeName: `${employee.firstName} ${employee.lastName}`,
      grossPay: 0,
      netPay: 0,
      totalTaxes: 0,
      totalDeductions: 0,
      totalBenefits: 0,
      regularHours: 0,
      overtimeHours: 0,
      payrollItems: [],
      errors: [],
      warnings: [],
    };

    try {
      // Calculate base salary/wages
      await this.calculateBasePay(employee, payrollPeriod, result);

      // Calculate benefits
      await this.calculateBenefits(employee, payrollPeriod, result);

      // Calculate deductions
      await this.calculateDeductions(employee, payrollPeriod, result);

      // Calculate taxes
      await this.calculateTaxes(employee, payrollPeriod, taxConfigurations, result);

      // Add custom items
      this.addCustomItems(customItems, result);

      // Calculate final totals
      this.calculateFinalTotals(result);

    } catch (error) {
      result.errors?.push(`Calculation error: ${error.message}`);
    }

    return result;
  }

  private async calculateBasePay(employee: Employee, payrollPeriod: PayrollPeriod, result: PayrollCalculationResultDto): Promise<void> {
    if (!employee.baseSalary) {
      result.warnings?.push('No base salary configured for employee');
      return;
    }

    // Calculate hours based on pay frequency and period
    const { regularHours, overtimeHours } = this.calculateWorkingHours(employee, payrollPeriod);
    result.regularHours = regularHours;
    result.overtimeHours = overtimeHours;

    // Calculate regular pay
    const hourlyRate = this.calculateHourlyRate(employee.baseSalary, employee.salaryFrequency);
    result.regularPay = regularHours * hourlyRate;

    // Calculate overtime pay (1.5x rate)
    const overtimeRate = hourlyRate * 1.5;5;
    result.overtimePay = overtimeHours * overtimeRate;

    // Add salary item
    result.payrollItems.push({
      code: 'SALARY',
      name: 'Base Salary',
      category: 'earnings',
      amount: result.regularPay,
      currency: payrollPeriod.currency,
      rate: hourlyRate,
      units: regularHours,
      isTaxable: true,
      isPreTax: false,
    });

    // Add overtime item if applicable
    if (result.overtimePay > 0) {
      result.payrollItems.push({
        code: 'OVERTIME',
        name: 'Overtime Pay',
        category: 'earnings',
        amount: result.overtimePay,
        currency: payrollPeriod.currency,
        rate: overtimeRate,
        units: overtimeHours,
        isTaxable: true,
        isPreTax: false,
      });
    }

    result.grossPay = result.regularPay + result.overtimePay;
  }

  private async calculateBenefits(employee: Employee, payrollPeriod: PayrollPeriod, result: PayrollCalculationResultDto): Promise<void> {
    const employeeBenefits = await this.employeeBenefitRepository.find({
      where: {
        tenantId: payrollPeriod.tenantId,
        employeeId: employee.id,
        status: 'ACTIVE',
      },
      relations: ['benefitPlan'],
    });

    for (const employeeBenefit of employeeBenefits) {
      if (!employeeBenefit.benefitPlan.isCurrentlyEffective) continue;

      const employeeContribution = employeeBenefit.employeeContribution;
      const employerContribution = employeeBenefit.employerContribution;

      // Add employee contribution as deduction
      if (employeeContribution > 0) {
        result.payrollItems.push({
          code: `BEN_${employeeBenefit.benefitPlan.code}`,
          name: `${employeeBenefit.benefitPlan.name} - Employee`,
          category: 'deductions',
          amount: employeeContribution,
          currency: payrollPeriod.currency,
          isTaxable: !employeeBenefit.isPreTax,
          isPreTax: employeeBenefit.isPreTax,
        });

        result.totalDeductions += employeeContribution;
      }

      // Add employer contribution as benefit
      if (employerContribution > 0) {
        result.payrollItems.push({
          code: `BEN_EMP_${employeeBenefit.benefitPlan.code}`,
          name: `${employeeBenefit.benefitPlan.name} - Employer`,
          category: 'benefits',
          amount: employerContribution,
          currency: payrollPeriod.currency,
          isTaxable: employeeBenefit.benefitPlan.isTaxable,
          isPreTax: false,
        });

        result.totalBenefits += employerContribution;
      }
    }
  }

  private async calculateDeductions(employee: Employee, payrollPeriod: PayrollPeriod, result: PayrollCalculationResultDto): Promise<void> {
    const employeeDeductions = await this.employeeDeductionRepository.find({
      where: {
        tenantId: payrollPeriod.tenantId,
        employeeId: employee.id,
        status: 'ACTIVE',
      },
      relations: ['deduction'],
    });

    for (const employeeDeduction of employeeDeductions) {
      if (!employeeDeduction.deduction.isCurrentlyEffective) continue;
      if (!employeeDeduction.isCurrentlyEffective) continue;

      const amount = employeeDeduction.calculateCurrentAmount(result.grossPay);
      if (amount <= 0) continue;

      result.payrollItems.push({
        code: employeeDeduction.deduction.code,
        name: employeeDeduction.deduction.name,
        category: 'deductions',
        amount,
        currency: payrollPeriod.currency,
        isTaxable: false,
        isPreTax: employeeDeduction.isPreTax,
      });

      result.totalDeductions += amount;
    }
  }

  private async calculateTaxes(
    employee: Employee,
    payrollPeriod: PayrollPeriod,
    taxConfigurations: TaxConfiguration[],
    result: PayrollCalculationResultDto
  ): Promise<void> {
    // Calculate taxable income (gross pay minus pre-tax deductions)
    const preTaxDeductions = result.payrollItems
      .filter(item => item.category === 'deductions' && item.isPreTax)
      .reduce((sum, item) => sum + item.amount, 0);

    const taxableIncome = result.grossPay - preTaxDeductions;

    for (const taxConfig of taxConfigurations) {
      if (!taxConfig.isCurrentlyEffective) continue;

      const taxAmount = taxConfig.calculateTax(taxableIncome);
      if (taxAmount <= 0) continue;

      result.payrollItems.push({
        code: taxConfig.code,
        name: taxConfig.name,
        category: 'taxes',
        amount: taxAmount,
        currency: payrollPeriod.currency,
        isTaxable: false,
        isPreTax: false,
      });

      result.totalTaxes += taxAmount;
    }
  }

  private addCustomItems(customItems: any[], result: PayrollCalculationResultDto): void {
    for (const item of customItems) {
      result.payrollItems.push(item);

      switch (item.category) {
        case 'earnings':
          result.grossPay += item.amount;
          break;
        case 'deductions':
          result.totalDeductions += item.amount;
          break;
        case 'taxes':
          result.totalTaxes += item.amount;
          break;
        case 'benefits':
          result.totalBenefits += item.amount;
          break;
      }
    }
  }

  private calculateFinalTotals(result: PayrollCalculationResultDto): void {
    // Net pay = Gross pay - Total deductions - Total taxes
    result.netPay = result.grossPay - result.totalDeductions - result.totalTaxes;

    // Ensure net pay is not negative
    if (result.netPay < 0) {
      result.warnings?.push('Net pay is negative - please review deductions and taxes');
      result.netPay = 0;
    }
  }

  private calculateWorkingHours(employee: Employee, payrollPeriod: PayrollPeriod): { regularHours: number; overtimeHours: number } {
    // TODO: Integrate with time tracking system
    // For now, use standard hours based on work schedule
    const standardHours = employee.workSchedule?.hoursPerWeek || 40;
    const periodDays = Math.ceil((payrollPeriod.endDate.getTime() - payrollPeriod.startDate.getTime()) / (1000 * 60 * 60 * 24));
    const periodWeeks = periodDays / 7;
    
    const totalHours = standardHours * periodWeeks;
    const overtimeThreshold = payrollPeriod.configuration?.overtimeThreshold || 40;
    const weeklyOvertimeThreshold = overtimeThreshold * periodWeeks;

    const regularHours = Math.min(totalHours, weeklyOvertimeThreshold);
    const overtimeHours = Math.max(0, totalHours - weeklyOvertimeThreshold);

    return { regularHours, overtimeHours };
  }

  private calculateHourlyRate(annualSalary: number, frequency: string): number {
    const annualHours = 2080; // Standard 40 hours/week * 52 weeks

    switch (frequency) {
      case 'hourly':
        return annualSalary;
      case 'weekly':
        return (annualSalary * 52) / annualHours;
      case 'bi_weekly':
        return (annualSalary * 26) / annualHours;
      case 'semi_monthly':
        return (annualSalary * 24) / annualHours;
      case 'monthly':
        return (annualSalary * 12) / annualHours;
      case 'annually':
      default:
        return annualSalary / annualHours;
    }
  }

  private calculatePeriodTotals(results: PayrollCalculationResultDto[]): {
    grossPay: number;
    netPay: number;
    taxes: number;
    deductions: number;
    benefits: number;
  } {
    return results.reduce(
      (totals, result) => ({
        grossPay: totals.grossPay + result.grossPay,
        netPay: totals.netPay + result.netPay,
        taxes: totals.taxes + result.totalTaxes,
        deductions: totals.deductions + result.totalDeductions,
        benefits: totals.benefits + result.totalBenefits,
      }),
      { grossPay: 0, netPay: 0, taxes: 0, deductions: 0, benefits: 0 }
    );
  }

  private async updatePayslipWithCalculation(payslip: Payslip, calculation: PayrollCalculationResultDto): Promise<void> {
    // Update payslip totals
    payslip.grossPay = calculation.grossPay;
    payslip.netPay = calculation.netPay;
    payslip.totalTaxes = calculation.totalTaxes;
    payslip.totalDeductions = calculation.totalDeductions;
    payslip.totalBenefits = calculation.totalBenefits;
    payslip.regularHours = calculation.regularHours;
    payslip.overtimeHours = calculation.overtimeHours;
    payslip.totalHours = calculation.regularHours + calculation.overtimeHours;
    payslip.regularPay = calculation.regularPay || 0;
    payslip.overtimePay = calculation.overtimePay || 0;
    payslip.status = PayrollStatus.PENDING;

    // Add processing issues if any
    if (calculation.errors?.length || calculation.warnings?.length) {
      payslip.processingIssues = {
        errors: calculation.errors?.map(error => ({
          code: 'CALC_ERROR',
          message: error,
          field: 'calculation',
          severity: 'error' as const,
          timestamp: new Date(),
        })) || [],
        warnings: calculation.warnings?.map(warning => ({
          code: 'CALC_WARNING',
          message: warning,
          field: 'calculation',
          timestamp: new Date(),
        })) || [],
      };
    }

    await this.payslipRepository.save(payslip);

    // Delete existing payroll items
    await this.payrollItemRepository.delete({
      payslipId: payslip.id,
    });

    // Create new payroll items
    const payrollItems = calculation.payrollItems.map(item => 
      this.payrollItemRepository.create({
        tenantId: payslip.tenantId,
        payslipId: payslip.id,
        employeeId: payslip.employeeId,
        ...item,
        calculationMethod: 'fixed',
        sortOrder: 0,
        isActive: true,
        isManualAdjustment: false,
      })
    );

    await this.payrollItemRepository.save(payrollItems);
  }

  private generatePayslipNumber(payrollPeriod: PayrollPeriod, employee: Employee): string {
    const year = payrollPeriod.startDate.getFullYear();
    const month = String(payrollPeriod.startDate.getMonth() + 1).padStart(2, '0');
    const employeeNumber = employee.employeeId.slice(-4);
    return `PS-${year}${month}-${employeeNumber}`;
  }
}
