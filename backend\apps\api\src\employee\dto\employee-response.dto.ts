import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { EmploymentStatus, EmploymentType, Gender } from '@app/database';

export class EmployeeResponseDto {
  @ApiProperty({ description: 'Employee UUID' })
  id: string;

  @ApiProperty({ description: 'Employee ID/Number' })
  employeeId: string;

  @ApiProperty({ description: 'First name' })
  firstName: string;

  @ApiProperty({ description: 'Last name' })
  lastName: string;

  @ApiPropertyOptional({ description: 'Middle name' })
  middleName?: string;

  @ApiProperty({ description: 'Full name' })
  fullName: string;

  @ApiProperty({ description: 'Email address' })
  email: string;

  @ApiPropertyOptional({ description: 'Phone number' })
  phone?: string;

  @ApiProperty({ description: 'Date of birth' })
  dateOfBirth: string;

  @ApiProperty({ description: 'Gender', enum: Gender })
  gender: Gender;

  @ApiProperty({ description: 'Hire date' })
  hireDate: string;

  @ApiProperty({ description: 'Department information' })
  department: {
    id: string;
    name: string;
    code: string;
  };

  @ApiProperty({ description: 'Position information' })
  position: {
    id: string;
    title: string;
    code: string;
    level: string;
  };

  @ApiPropertyOptional({ description: 'Manager information' })
  manager?: {
    id: string;
    fullName: string;
    employeeId: string;
  };

  @ApiProperty({ description: 'Employment status', enum: EmploymentStatus })
  employmentStatus: EmploymentStatus;

  @ApiProperty({ description: 'Employment type', enum: EmploymentType })
  employmentType: EmploymentType;

  @ApiPropertyOptional({ description: 'Salary amount' })
  salary?: number;

  @ApiProperty({ description: 'Active status' })
  isActive: boolean;

  @ApiProperty({ description: 'Creation date' })
  createdAt: string;

  @ApiProperty({ description: 'Last update date' })
  updatedAt: string;

  @ApiPropertyOptional({ description: 'Additional metadata' })
  metadata?: Record<string, any>;
}

export class PaginatedEmployeeResponseDto {
  @ApiProperty({ description: 'Employee data', type: [EmployeeResponseDto] })
  data: EmployeeResponseDto[];

  @ApiProperty({ description: 'Pagination metadata' })
  meta: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}
