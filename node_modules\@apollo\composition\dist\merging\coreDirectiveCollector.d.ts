import { DirectiveCompositionSpecification, DirectiveDefinition, FeatureUrl, Subgraphs } from "@apollo/federation-internals";
export type CoreDirectiveInSubgraphs = {
    url: FeatureUrl;
    name: string;
    definitionsPerSubgraph: Map<string, DirectiveDefinition>;
    compositionSpec: DirectiveCompositionSpecification;
};
export declare function collectCoreDirectivesToCompose(subgraphs: Subgraphs): CoreDirectiveInSubgraphs[];
//# sourceMappingURL=coreDirectiveCollector.d.ts.map