"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MultiMap = void 0;
class MultiMap extends Map {
    add(key, value) {
        const values = this.get(key);
        if (values) {
            values.push(value);
        }
        else {
            this.set(key, [value]);
        }
        return this;
    }
}
exports.MultiMap = MultiMap;
//# sourceMappingURL=MultiMap.js.map