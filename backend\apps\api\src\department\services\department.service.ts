import {
  Injectable,
  NotFoundException,
  ConflictException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';

import {
  Department,
  Employee,
  Position,
  User,
} from '@app/database';

import {
  CreateDepartmentDto,
  UpdateDepartmentDto,
  DepartmentQueryDto,
  DepartmentResponseDto,
  PaginatedDepartmentResponseDto,
} from '../dto';

@Injectable()
export class DepartmentService {
  private readonly logger = new Logger(DepartmentService.name);

  constructor(
    @InjectRepository(Department)
    private readonly departmentRepository: Repository<Department>,
    @InjectRepository(Employee)
    private readonly employeeRepository: Repository<Employee>,
    @InjectRepository(Position)
    private readonly positionRepository: Repository<Position>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async create(
    createDepartmentDto: CreateDepartmentDto,
    createdBy: string,
    tenantId: string,
  ): Promise<DepartmentResponseDto> {
    this.logger.log(`Creating department: ${createDepartmentDto.name}`);

    // Check if department code already exists
    const existingDepartment = await this.departmentRepository.findOne({
      where: { code: createDepartmentDto.code, tenantId },
    });

    if (existingDepartment) {
      throw new ConflictException(`Department with code ${createDepartmentDto.code} already exists`);
    }

    // Validate parent department exists if provided
    let parentDepartment: Department | undefined;
    if (createDepartmentDto.parentId) {
      parentDepartment = await this.departmentRepository.findOne({
        where: { id: createDepartmentDto.parentId, tenantId },
      }) || undefined;

      if (!parentDepartment) {
        throw new NotFoundException(`Parent department with ID ${createDepartmentDto.parentId} not found`);
      }
    }

    // Validate department head exists if provided
    if (createDepartmentDto.headId) {
      const head = await this.employeeRepository.findOne({
        where: { id: createDepartmentDto.headId, tenantId },
      });

      if (!head) {
        throw new NotFoundException(`Employee with ID ${createDepartmentDto.headId} not found`);
      }
    }

    // Create department
    const department = this.departmentRepository.create({
      name: createDepartmentDto.name,
      code: createDepartmentDto.code,
      description: createDepartmentDto.description,
      budget: createDepartmentDto.budget,
      location: createDepartmentDto.location,
      metadata: createDepartmentDto.metadata,
      parent: parentDepartment,
      tenantId,
      createdBy,
      updatedBy: createdBy,
    });

    const savedDepartment = await this.departmentRepository.save(department);

    // Emit event
    this.eventEmitter.emit('department.created', {
      departmentId: savedDepartment.id,
      tenantId,
      createdBy,
    });

    return this.mapToResponseDto(savedDepartment);
  }

  async findAll(
    query: DepartmentQueryDto,
    tenantId: string,
  ): Promise<PaginatedDepartmentResponseDto> {
    const { page = 1, limit = 10, search, sortBy = 'name', sortOrder = 'ASC' } = query;
    const skip = (page - 1) * limit;

    const queryBuilder = this.createQueryBuilder(tenantId);

    // Apply filters
    this.applyFilters(queryBuilder, query);

    // Apply search
    if (search) {
      queryBuilder.andWhere(
        '(department.name ILIKE :search OR department.code ILIKE :search OR department.description ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    // Apply sorting
    queryBuilder.orderBy(`department.${sortBy}`, sortOrder);

    // Apply pagination
    queryBuilder.skip(skip).take(limit);

    const [departments, total] = await queryBuilder.getManyAndCount();

    const data = await Promise.all(
      departments.map(department => this.mapToResponseDto(department))
    );

    return {
      data,
      meta: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPreviousPage: page > 1,
      },
    };
  }

  async findOne(id: string, tenantId: string): Promise<DepartmentResponseDto> {
    const department = await this.departmentRepository.findOne({
      where: { id, tenantId },
      relations: ['parent', 'head'],
    });

    if (!department) {
      throw new NotFoundException(`Department with ID ${id} not found`);
    }

    return this.mapToResponseDto(department);
  }

  async update(
    id: string,
    updateDepartmentDto: UpdateDepartmentDto,
    updatedBy: string,
    tenantId: string,
  ): Promise<DepartmentResponseDto> {
    const department = await this.departmentRepository.findOne({
      where: { id, tenantId },
    });

    if (!department) {
      throw new NotFoundException(`Department with ID ${id} not found`);
    }

    // Validate parent department if being updated
    let newParent: Department | undefined;
    if (updateDepartmentDto.parentId && updateDepartmentDto.parentId !== department.parent?.id) {
      newParent = await this.departmentRepository.findOne({
        where: { id: updateDepartmentDto.parentId, tenantId },
      }) || undefined;

      if (!newParent) {
        throw new NotFoundException(`Parent department with ID ${updateDepartmentDto.parentId} not found`);
      }

      // Prevent circular reference
      if (updateDepartmentDto.parentId === id) {
        throw new ConflictException('Department cannot be its own parent');
      }
    }

    // Update department
    if (updateDepartmentDto.name !== undefined) department.name = updateDepartmentDto.name;
    // Note: code is omitted from UpdateDepartmentDto and cannot be updated
    if (updateDepartmentDto.description !== undefined) department.description = updateDepartmentDto.description;
    if (updateDepartmentDto.budget !== undefined) department.budget = updateDepartmentDto.budget;
    if (updateDepartmentDto.location !== undefined) department.location = updateDepartmentDto.location;
    if (updateDepartmentDto.metadata !== undefined) department.metadata = updateDepartmentDto.metadata;
    if (newParent !== undefined) department.parent = newParent;
    department.updatedBy = updatedBy;
    const savedDepartment = await this.departmentRepository.save(department);

    // Emit event
    this.eventEmitter.emit('department.updated', {
      departmentId: savedDepartment.id,
      tenantId,
      updatedBy,
      changes: updateDepartmentDto,
    });

    return this.mapToResponseDto(savedDepartment);
  }

  async remove(id: string, deletedBy: string, tenantId: string): Promise<void> {
    const department = await this.departmentRepository.findOne({
      where: { id, tenantId },
    });

    if (!department) {
      throw new NotFoundException(`Department with ID ${id} not found`);
    }

    // Check if department has employees
    const employeeCount = await this.employeeRepository.count({
      where: { departmentId: id, tenantId, isActive: true },
    });

    if (employeeCount > 0) {
      throw new ConflictException('Cannot delete department with active employees');
    }

    // Soft delete
    department.isActive = false;
    department.updatedBy = deletedBy;
    department.deletedAt = new Date();

    await this.departmentRepository.save(department);

    // Emit event
    this.eventEmitter.emit('department.deleted', {
      departmentId: id,
      tenantId,
      deletedBy,
    });
  }

  async getDepartmentEmployees(id: string, tenantId: string): Promise<any[]> {
    const department = await this.departmentRepository.findOne({
      where: { id, tenantId },
    });

    if (!department) {
      throw new NotFoundException(`Department with ID ${id} not found`);
    }

    const employees = await this.employeeRepository.find({
      where: { departmentId: id, tenantId, isActive: true },
      relations: ['position'],
      order: { lastName: 'ASC' },
    });

    return employees.map(employee => ({
      id: employee.id,
      employeeId: employee.employeeId,
      fullName: `${employee.firstName} ${employee.lastName}`,
      email: employee.email,
      position: employee.position ? {
        id: employee.position.id,
        title: employee.position.title,
        level: employee.position.level,
      } : null,
      employmentStatus: employee.status,
      hireDate: employee.dateOfJoining.toISOString(),
    }));
  }

  async getDepartmentPositions(id: string, tenantId: string): Promise<any[]> {
    const department = await this.departmentRepository.findOne({
      where: { id, tenantId },
    });

    if (!department) {
      throw new NotFoundException(`Department with ID ${id} not found`);
    }

    const positions = await this.positionRepository.find({
      where: { departmentId: id, tenantId, isActive: true },
      order: { title: 'ASC' },
    });

    return positions.map(position => ({
      id: position.id,
      title: position.title,
      code: position.code,
      level: position.level,
      minSalary: position.minSalary,
      maxSalary: position.maxSalary,
      employeeCount: 0, // This would need a separate query
    }));
  }

  private createQueryBuilder(tenantId: string): SelectQueryBuilder<Department> {
    return this.departmentRepository
      .createQueryBuilder('department')
      .leftJoinAndSelect('department.parent', 'parent')
      .leftJoinAndSelect('department.head', 'head')
      .where('department.tenantId = :tenantId', { tenantId });
  }

  private applyFilters(queryBuilder: SelectQueryBuilder<Department>, query: DepartmentQueryDto): void {
    if (query.parentId) {
      queryBuilder.andWhere('parent.id = :parentId', {
        parentId: query.parentId,
      });
    }

    if (query.headId) {
      queryBuilder.andWhere('department.headId = :headId', {
        headId: query.headId,
      });
    }

    if (!query.includeInactive) {
      queryBuilder.andWhere('department.isActive = :isActive', { isActive: true });
    }
  }

  private async mapToResponseDto(department: Department): Promise<DepartmentResponseDto> {
    // Get employee count
    const employeeCount = await this.employeeRepository.count({
      where: { departmentId: department.id, isActive: true },
    });

    // Get position count
    const positionCount = await this.positionRepository.count({
      where: { departmentId: department.id, isActive: true },
    });

    return {
      id: department.id,
      name: department.name,
      code: department.code,
      description: department.description,
      parent: department.parent ? {
        id: department.parent.id,
        name: department.parent.name,
        code: department.parent.code,
      } : undefined,
      head: department.head ? {
        id: department.head.id,
        fullName: `${department.head.firstName} ${department.head.lastName}`,
        employeeId: department.head.employeeId,
      } : undefined,

      budget: department.budget,
      location: department.location,
      employeeCount,
      positionCount,
      isActive: department.isActive,
      createdAt: department.createdAt.toISOString(),
      updatedAt: department.updatedAt.toISOString(),
      metadata: department.metadata,
    };
  }
}
