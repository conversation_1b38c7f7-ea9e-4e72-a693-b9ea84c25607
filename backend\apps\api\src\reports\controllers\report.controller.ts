import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards, ParseUUIDPipe } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard, RolesGuard, TenantGuard } from '@app/security';
import { Roles, CurrentUser, CurrentTenant } from '@app/common';
import { ReportService } from '../services/report.service';

@ApiTags('Reports')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard, TenantGuard, RolesGuard)
@Controller('reports')
export class ReportController {
  constructor(private readonly reportService: ReportService) {}

  @Post()
  @Roles('admin', 'hr_manager', 'hr_user', 'manager')
  @ApiOperation({ summary: 'Create report' })
  async create(@Body() createReportDto: any, @CurrentUser() user: any, @CurrentTenant() tenantId: string) {
    return this.reportService.create(createReportDto, user.id, tenantId);
  }

  @Get()
  @Roles('admin', 'hr_manager', 'hr_user', 'manager')
  @ApiOperation({ summary: 'Get reports' })
  async findAll(@Query() query: any, @CurrentUser() user: any, @CurrentTenant() tenantId: string) {
    return this.reportService.findAll(query, user, tenantId);
  }

  @Get(':id')
  @Roles('admin', 'hr_manager', 'hr_user', 'manager')
  @ApiOperation({ summary: 'Get report by ID' })
  async findOne(@Param('id', ParseUUIDPipe) id: string, @CurrentUser() user: any, @CurrentTenant() tenantId: string) {
    return this.reportService.findOne(id, user, tenantId);
  }

  @Post('generate/:templateId')
  @Roles('admin', 'hr_manager', 'hr_user', 'manager')
  @ApiOperation({ summary: 'Generate report from template' })
  async generateReport(@Param('templateId', ParseUUIDPipe) templateId: string, @Body() parameters: any, @CurrentTenant() tenantId: string) {
    return this.reportService.generateReport(templateId, parameters, tenantId);
  }

  @Put(':id')
  @Roles('admin', 'hr_manager', 'hr_user', 'manager')
  @ApiOperation({ summary: 'Update report' })
  async update(@Param('id', ParseUUIDPipe) id: string, @Body() updateReportDto: any, @CurrentUser() user: any, @CurrentTenant() tenantId: string) {
    return this.reportService.update(id, updateReportDto, user.id, tenantId);
  }

  @Delete(':id')
  @Roles('admin', 'hr_manager', 'hr_user', 'manager')
  @ApiOperation({ summary: 'Delete report' })
  async remove(@Param('id', ParseUUIDPipe) id: string, @CurrentUser() user: any, @CurrentTenant() tenantId: string) {
    return this.reportService.remove(id, user.id, tenantId);
  }
}
