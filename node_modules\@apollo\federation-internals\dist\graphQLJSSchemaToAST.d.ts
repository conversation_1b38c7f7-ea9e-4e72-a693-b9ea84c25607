import { DirectiveDefinitionNode, DocumentNode, GraphQLDirective, GraphQLNamedType, GraphQLSchema, TypeDefinitionNode, TypeExtensionNode } from "graphql";
export declare function graphQLJSSchemaToAST(schema: GraphQLSchema): DocumentNode;
export declare function graphQLJSNamedTypeToAST(type: GraphQLNamedType): {
    definition?: TypeDefinitionNode;
    extensions: readonly TypeExtensionNode[];
};
export declare function graphQLJSDirectiveToAST(directive: GraphQLDirective): DirectiveDefinitionNode;
//# sourceMappingURL=graphQLJSSchemaToAST.d.ts.map