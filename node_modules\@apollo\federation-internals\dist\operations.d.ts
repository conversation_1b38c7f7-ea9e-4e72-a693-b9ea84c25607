import { ArgumentNode, DocumentNode, FieldNode, FragmentDefinitionNode, FragmentSpreadNode, InlineFragmentNode, OperationDefinitionNode, SelectionNode, SelectionSetNode } from "graphql";
import { Directive, DirectiveTargetElement, FieldDefinition, Schema, SchemaRootKind, VariableCollector, VariableDefinitions, CompositeType, DeferDirectiveArgs, Variable, Type, Variables, NamedType } from "./definitions";
import { MultiMap, SetMultiMap } from "./utils";
export declare const DEFAULT_MIN_USAGES_TO_OPTIMIZE = 2;
declare abstract class AbstractOperationElement<T extends AbstractOperationElement<T>> extends DirectiveTargetElement<T> {
    private attachments?;
    constructor(schema: Schema, directives?: readonly Directive<any>[]);
    collectVariables(collector: VariableCollector): void;
    abstract key(): string;
    abstract asPathElement(): string | undefined;
    abstract rebaseOn(args: {
        parentType: CompositeType;
        errorIfCannotRebase: boolean;
    }): T | undefined;
    rebaseOnOrError(parentType: CompositeType): T;
    abstract withUpdatedDirectives(newDirectives: readonly Directive<any>[]): T;
    protected abstract collectVariablesInElement(collector: VariableCollector): void;
    addAttachment(key: string, value: string): void;
    getAttachment(key: string): string | undefined;
    protected copyAttachmentsTo(elt: AbstractOperationElement<any>): void;
    protected keyForDirectives(): string;
}
export declare class Field<TArgs extends {
    [key: string]: any;
} = {
    [key: string]: any;
}> extends AbstractOperationElement<Field<TArgs>> {
    readonly definition: FieldDefinition<CompositeType>;
    readonly args?: TArgs | undefined;
    readonly alias?: string | undefined;
    readonly kind: "Field";
    constructor(definition: FieldDefinition<CompositeType>, args?: TArgs | undefined, directives?: readonly Directive<any>[], alias?: string | undefined);
    protected collectVariablesInElement(collector: VariableCollector): void;
    get name(): string;
    argumentValue(name: string): any;
    responseName(): string;
    key(): string;
    asPathElement(): string;
    get parentType(): CompositeType;
    isLeafField(): boolean;
    baseType(): NamedType;
    copy(): Field<TArgs>;
    withUpdatedArguments(newArgs: TArgs): Field<TArgs>;
    withUpdatedDefinition(newDefinition: FieldDefinition<any>): Field<TArgs>;
    withUpdatedAlias(newAlias: string | undefined): Field<TArgs>;
    withUpdatedDirectives(newDirectives: readonly Directive<any>[]): Field<TArgs>;
    argumentsToNodes(): ArgumentNode[] | undefined;
    selects(definition: FieldDefinition<any>, assumeValid?: boolean, variableDefinitions?: VariableDefinitions, contextualArguments?: string[]): boolean;
    validate(variableDefinitions: VariableDefinitions, validateContextualArgs: boolean): void;
    rebaseOn({ parentType, errorIfCannotRebase }: {
        parentType: CompositeType;
        errorIfCannotRebase: boolean;
    }): Field<TArgs> | undefined;
    private canRebaseOn;
    typeIfAddedTo(parentType: CompositeType): Type | undefined;
    hasDefer(): boolean;
    deferDirectiveArgs(): undefined;
    withoutDefer(): Field<TArgs>;
    equals(that: OperationElement): boolean;
    toString(): string;
}
export declare class FragmentElement extends AbstractOperationElement<FragmentElement> {
    private readonly sourceType;
    readonly kind: "FragmentElement";
    readonly typeCondition?: CompositeType;
    private computedKey;
    constructor(sourceType: CompositeType, typeCondition?: string | CompositeType, directives?: readonly Directive<any>[]);
    protected collectVariablesInElement(_: VariableCollector): void;
    get parentType(): CompositeType;
    key(): string;
    castedType(): CompositeType;
    asPathElement(): string | undefined;
    withUpdatedSourceType(newSourceType: CompositeType): FragmentElement;
    withUpdatedCondition(newCondition: CompositeType | undefined): FragmentElement;
    withUpdatedTypes(newSourceType: CompositeType, newCondition: CompositeType | undefined): FragmentElement;
    withUpdatedDirectives(newDirectives: Directive<OperationElement>[]): FragmentElement;
    rebaseOn({ parentType, errorIfCannotRebase }: {
        parentType: CompositeType;
        errorIfCannotRebase: boolean;
    }): FragmentElement | undefined;
    private canRebaseOn;
    castedTypeIfAddedTo(parentType: CompositeType): CompositeType | undefined;
    hasDefer(): boolean;
    hasStream(): boolean;
    deferDirectiveArgs(): DeferDirectiveArgs | undefined;
    withoutDefer(): FragmentElement | undefined;
    withNormalizedDefer(normalizer: DeferNormalizer): FragmentElement | undefined;
    equals(that: OperationElement): boolean;
    toString(): string;
}
export type OperationElement = Field<any> | FragmentElement;
export type OperationPath = OperationElement[];
export declare function operationPathToStringPath(path: OperationPath): string[];
export declare function sameOperationPaths(p1: OperationPath, p2: OperationPath): boolean;
export declare function conditionalDirectivesInOperationPath(path: OperationPath): Directive<any, any>[];
export declare function concatOperationPaths(head: OperationPath, tail: OperationPath): OperationPath;
export type RootOperationPath = {
    rootKind: SchemaRootKind;
    path: OperationPath;
};
export declare class Operation extends DirectiveTargetElement<Operation> {
    readonly rootKind: SchemaRootKind;
    readonly selectionSet: SelectionSet;
    readonly variableDefinitions: VariableDefinitions;
    readonly fragments?: NamedFragments | undefined;
    readonly name?: string | undefined;
    constructor(schema: Schema, rootKind: SchemaRootKind, selectionSet: SelectionSet, variableDefinitions: VariableDefinitions, fragments?: NamedFragments | undefined, name?: string | undefined, directives?: readonly Directive<any>[]);
    private withUpdatedSelectionSet;
    private collectUndefinedVariablesFromFragments;
    private withUpdatedSelectionSetAndFragments;
    optimize(fragments?: NamedFragments, minUsagesToOptimize?: number, allAvailableVariables?: VariableDefinitions): Operation;
    generateQueryFragments(): Operation;
    expandAllFragments(): Operation;
    normalize(): Operation;
    withoutDefer(labelsToRemove?: Set<string>): Operation;
    withNormalizedDefer(): {
        operation: Operation;
        hasDefers: boolean;
        assignedDeferLabels: Set<string>;
        deferConditions: SetMultiMap<string, string>;
    };
    collectDefaultedVariableValues(): Record<string, any>;
    toString(expandFragments?: boolean, prettyPrint?: boolean): string;
}
export type FragmentRestrictionAtType = {
    selectionSet: SelectionSet;
    validator?: FieldsConflictValidator;
};
export declare class NamedFragmentDefinition extends DirectiveTargetElement<NamedFragmentDefinition> {
    readonly name: string;
    readonly typeCondition: CompositeType;
    private _selectionSet;
    private _expandedSelectionSet;
    private _fragmentUsages;
    private _includedFragmentNames;
    private readonly expandedSelectionSetsAtTypesCache;
    constructor(schema: Schema, name: string, typeCondition: CompositeType, directives?: Directive<NamedFragmentDefinition>[]);
    setSelectionSet(selectionSet: SelectionSet): NamedFragmentDefinition;
    get selectionSet(): SelectionSet;
    withUpdatedSelectionSet(newSelectionSet: SelectionSet): NamedFragmentDefinition;
    fragmentUsages(): ReadonlyMap<string, number>;
    collectUsedFragmentNames(collector: Map<string, number>): void;
    collectVariables(collector: VariableCollector): void;
    toFragmentDefinitionNode(): FragmentDefinitionNode;
    canApplyDirectlyAtType(type: CompositeType): boolean;
    private expandedSelectionSet;
    expandedSelectionSetAtType(type: CompositeType): FragmentRestrictionAtType;
    private computeExpandedSelectionSetAtType;
    includes(otherFragment: string): boolean;
    private computeIncludedFragmentNames;
    toString(indent?: string): string;
}
export declare class NamedFragments {
    private readonly fragments;
    isEmpty(): boolean;
    get size(): number;
    names(): readonly string[];
    add(fragment: NamedFragmentDefinition): void;
    addIfNotExist(fragment: NamedFragmentDefinition): void;
    maybeApplyingDirectlyAtType(type: CompositeType): NamedFragmentDefinition[];
    get(name: string): NamedFragmentDefinition | undefined;
    has(name: string): boolean;
    definitions(): readonly NamedFragmentDefinition[];
    collectUsedFragmentNames(collector: Map<string, number>): void;
    map(mapper: (def: NamedFragmentDefinition) => NamedFragmentDefinition): NamedFragments;
    mapInDependencyOrder(mapper: (fragment: NamedFragmentDefinition, newFragments: NamedFragments) => NamedFragmentDefinition | undefined): NamedFragments | undefined;
    mapToExpandedSelectionSets(mapper: (selectionSet: SelectionSet) => SelectionSet | undefined): NamedFragments | undefined;
    rebaseOn(schema: Schema): NamedFragments | undefined;
    filter(predicate: (fragment: NamedFragmentDefinition) => boolean): NamedFragments | undefined;
    validate(variableDefinitions: VariableDefinitions): void;
    toFragmentDefinitionNodes(): FragmentDefinitionNode[];
    toString(indent?: string): string;
}
declare class DeferNormalizer {
    private index;
    readonly assignedLabels: Set<string>;
    readonly deferConditions: SetMultiMap<string, string>;
    private readonly usedLabels;
    init(selectionSet: SelectionSet): {
        hasDefers: boolean;
        hasNonLabelledOrConditionalDefers: boolean;
    };
    private nextLabel;
    newLabel(): string;
    registerCondition(label: string, condition: Variable): void;
}
export declare enum ContainsResult {
    NOT_CONTAINED = 0,
    STRICTLY_CONTAINED = 1,
    EQUAL = 2
}
export type CollectedFieldsInSet = {
    path: string[];
    field: FieldSelection;
}[];
export declare class SelectionSet {
    readonly parentType: CompositeType;
    private readonly _keyedSelections;
    private readonly _selections;
    constructor(parentType: CompositeType, keyedSelections?: Map<string, Selection>);
    minimizeSelectionSet(namedFragments?: NamedFragments, seenSelections?: Map<string, [SelectionSet, NamedFragmentDefinition][]>): [SelectionSet, NamedFragments];
    selectionsInReverseOrder(): readonly Selection[];
    selections(): readonly Selection[];
    hasTopLevelTypenameField(): boolean;
    withoutTopLevelTypenameField(): SelectionSet;
    fieldsInSet(): CollectedFieldsInSet;
    fieldsByResponseName(): MultiMap<string, FieldSelection>;
    private collectFieldsByResponseName;
    usedVariables(): Variables;
    collectVariables(collector: VariableCollector): void;
    collectUsedFragmentNames(collector: Map<string, number>): void;
    optimize(fragments?: NamedFragments): SelectionSet;
    optimizeSelections(fragments: NamedFragments, validator: FieldsConflictMultiBranchValidator): SelectionSet;
    expandFragments(updatedFragments?: NamedFragments): SelectionSet;
    normalize({ parentType, recursive }: {
        parentType: CompositeType;
        recursive?: boolean;
    }): SelectionSet;
    lazyMap(mapper: (selection: Selection) => Selection | readonly Selection[] | SelectionSet | undefined, options?: {
        parentType?: CompositeType;
    }): SelectionSet;
    withoutDefer(labelsToRemove?: Set<string>): SelectionSet;
    withNormalizedDefer(normalizer: DeferNormalizer): SelectionSet;
    hasDefer(): boolean;
    filter(predicate: (selection: Selection) => boolean): SelectionSet;
    filterRecursiveDepthFirst(predicate: (selection: Selection) => boolean): SelectionSet;
    withoutEmptyBranches(): SelectionSet | undefined;
    rebaseOn({ parentType, fragments, errorIfCannotRebase, }: {
        parentType: CompositeType;
        fragments: NamedFragments | undefined;
        errorIfCannotRebase: boolean;
    }): SelectionSet;
    equals(that: SelectionSet): boolean;
    contains(that: SelectionSet, options?: {
        ignoreMissingTypename?: boolean;
    }): ContainsResult;
    containsTopLevelField(field: Field): boolean;
    minus(that: SelectionSet): SelectionSet;
    intersectionWith(that: SelectionSet): SelectionSet;
    canRebaseOn(parentTypeToTest: CompositeType): boolean;
    validate(variableDefinitions: VariableDefinitions, validateContextualArgs?: boolean): void;
    isEmpty(): boolean;
    toSelectionSetNode(): SelectionSetNode;
    private selectionsInPrintOrder;
    toOperationPaths(): OperationPath[];
    private toOperationPathsInternal;
    forEachElement(callback: (elt: OperationElement) => void): void;
    some(predicate: (elt: OperationElement) => boolean): boolean;
    toOperationString(rootKind: SchemaRootKind, variableDefinitions: VariableDefinitions, fragments: NamedFragments | undefined, operationName?: string, directives?: readonly Directive<any>[], expandFragments?: boolean, prettyPrint?: boolean): string;
    toString(expandFragments?: boolean, includeExternalBrackets?: boolean, indent?: string): string;
    isWorthUsing(): boolean;
}
export declare class SelectionSetUpdates {
    private readonly keyedUpdates;
    isEmpty(): boolean;
    add(selections: Selection | SelectionSet | readonly Selection[]): SelectionSetUpdates;
    addAtPath(path: OperationPath, selections?: Selection | SelectionSet | readonly Selection[]): SelectionSetUpdates;
    clone(): SelectionSetUpdates;
    clear(): void;
    toSelectionSet(parentType: CompositeType, fragments?: NamedFragments): SelectionSet;
    toString(): string;
}
export declare class MutableSelectionSet<TMemoizedValue extends {
    [key: string]: any;
} = {}> {
    readonly parentType: CompositeType;
    private readonly _updates;
    private readonly memoizer;
    private computed;
    private _memoized;
    private constructor();
    static empty(parentType: CompositeType): MutableSelectionSet;
    static emptyWithMemoized<TMemoizedValue extends {
        [key: string]: any;
    }>(parentType: CompositeType, memoizer: (s: SelectionSet) => TMemoizedValue): MutableSelectionSet<TMemoizedValue>;
    static of(selectionSet: SelectionSet): MutableSelectionSet;
    static ofWithMemoized<TMemoizedValue extends {
        [key: string]: any;
    }>(selectionSet: SelectionSet, memoizer: (s: SelectionSet) => TMemoizedValue): MutableSelectionSet<TMemoizedValue>;
    isEmpty(): boolean;
    get(): SelectionSet;
    updates(): SelectionSetUpdates;
    clone(): MutableSelectionSet<TMemoizedValue>;
    rebaseOn(parentType: CompositeType): MutableSelectionSet<TMemoizedValue>;
    memoized(): TMemoizedValue;
    toString(): string;
}
export declare function allFieldDefinitionsInSelectionSet(selection: SelectionSet): FieldDefinition<CompositeType>[];
export declare function selectionSetOf(parentType: CompositeType, selection: Selection): SelectionSet;
export declare function selectionSetOfElement(element: OperationElement, subSelection?: SelectionSet): SelectionSet;
export declare function selectionOfElement(element: OperationElement, subSelection?: SelectionSet): Selection;
export type Selection = FieldSelection | FragmentSelection;
declare abstract class AbstractSelection<TElement extends OperationElement, TIsLeaf extends undefined | never, TOwnType extends AbstractSelection<TElement, TIsLeaf, TOwnType>> {
    readonly element: TElement;
    constructor(element: TElement);
    abstract get selectionSet(): SelectionSet | TIsLeaf;
    protected abstract us(): TOwnType;
    abstract key(): string;
    abstract optimize(fragments: NamedFragments, validator: FieldsConflictMultiBranchValidator): Selection;
    abstract toSelectionNode(): SelectionNode;
    abstract validate(variableDefinitions: VariableDefinitions, validateContextualArgs: boolean): void;
    abstract rebaseOn(args: {
        parentType: CompositeType;
        fragments: NamedFragments | undefined;
        errorIfCannotRebase: boolean;
    }): TOwnType | undefined;
    rebaseOnOrError({ parentType, fragments }: {
        parentType: CompositeType;
        fragments: NamedFragments | undefined;
    }): TOwnType;
    get parentType(): CompositeType;
    isTypenameField(): boolean;
    collectVariables(collector: VariableCollector): void;
    collectUsedFragmentNames(collector: Map<string, number>): void;
    abstract withUpdatedComponents(element: TElement, selectionSet: SelectionSet | TIsLeaf): TOwnType;
    withUpdatedSelectionSet(selectionSet: SelectionSet | TIsLeaf): TOwnType;
    withUpdatedElement(element: TElement): TOwnType;
    mapToSelectionSet(mapper: (s: SelectionSet) => SelectionSet): TOwnType;
    abstract withoutDefer(labelsToRemove?: Set<string>): TOwnType | SelectionSet;
    abstract withNormalizedDefer(normalizer: DeferNormalizer): TOwnType | SelectionSet;
    abstract hasDefer(): boolean;
    abstract expandFragments(updatedFragments: NamedFragments | undefined): TOwnType | readonly Selection[];
    abstract normalize(args: {
        parentType: CompositeType;
        recursive?: boolean;
    }): TOwnType | SelectionSet | undefined;
    isFragmentSpread(): boolean;
    minus(that: Selection): TOwnType | undefined;
    intersectionWith(that: Selection): TOwnType | undefined;
    protected tryOptimizeSubselectionWithFragments({ parentType, subSelection, fragments, validator, canUseFullMatchingFragment, }: {
        parentType: CompositeType;
        subSelection: SelectionSet;
        fragments: NamedFragments;
        validator: FieldsConflictMultiBranchValidator;
        canUseFullMatchingFragment: (match: NamedFragmentDefinition) => boolean;
    }): SelectionSet | NamedFragmentDefinition;
}
declare class FieldsConflictMultiBranchValidator {
    private readonly validators;
    private usedSpreadTrimmedPartAtLevel?;
    constructor(validators: FieldsConflictValidator[]);
    static ofInitial(validator: FieldsConflictValidator): FieldsConflictMultiBranchValidator;
    forField(field: Field): FieldsConflictMultiBranchValidator;
    checkCanReuseFragmentAndTrackIt(fragment: FragmentRestrictionAtType): boolean;
}
declare class FieldsConflictValidator {
    private readonly byResponseName;
    private constructor();
    static build(s: SelectionSet): FieldsConflictValidator;
    private static forLevel;
    forField(field: Field): FieldsConflictValidator[];
    doMergeWith(that: FieldsConflictValidator): boolean;
    hasSameResponseShapeThan(that: FieldsConflictValidator): boolean;
    toString(indent?: string): string;
}
export declare class FieldSelection extends AbstractSelection<Field<any>, undefined, FieldSelection> {
    private readonly _selectionSet?;
    readonly kind: "FieldSelection";
    constructor(field: Field<any>, _selectionSet?: SelectionSet | undefined);
    get selectionSet(): SelectionSet | undefined;
    protected us(): FieldSelection;
    isTypenameField(): boolean;
    isPlainTypenameField(): boolean;
    withAttachment(key: string, value: string): FieldSelection;
    withUpdatedComponents(field: Field<any>, selectionSet: SelectionSet | undefined): FieldSelection;
    key(): string;
    optimize(fragments: NamedFragments, validator: FieldsConflictMultiBranchValidator): Selection;
    filterRecursiveDepthFirst(predicate: (selection: Selection) => boolean): FieldSelection | undefined;
    validate(variableDefinitions: VariableDefinitions, validateContextualArgs: boolean): void;
    rebaseOn({ parentType, fragments, errorIfCannotRebase, }: {
        parentType: CompositeType;
        fragments: NamedFragments | undefined;
        errorIfCannotRebase: boolean;
    }): FieldSelection | undefined;
    canAddTo(parentType: CompositeType): boolean;
    toSelectionNode(): FieldNode;
    withoutDefer(labelsToRemove?: Set<string>): FieldSelection;
    withNormalizedDefer(normalizer: DeferNormalizer): FieldSelection;
    hasDefer(): boolean;
    normalize({ parentType, recursive }: {
        parentType: CompositeType;
        recursive?: boolean;
    }): FieldSelection;
    expandFragments(updatedFragments?: NamedFragments): FieldSelection;
    equals(that: Selection): boolean;
    contains(that: Selection, options?: {
        ignoreMissingTypename?: boolean;
    }): ContainsResult;
    toString(expandFragments?: boolean, indent?: string): string;
}
export declare abstract class FragmentSelection extends AbstractSelection<FragmentElement, never, FragmentSelection> {
    readonly kind: "FragmentSelection";
    abstract canAddTo(parentType: CompositeType): boolean;
    protected us(): FragmentSelection;
    protected validateDeferAndStream(): void;
    filterRecursiveDepthFirst(predicate: (selection: Selection) => boolean): FragmentSelection | undefined;
    hasDefer(): boolean;
    abstract equals(that: Selection): boolean;
    abstract contains(that: Selection, options?: {
        ignoreMissingTypename?: boolean;
    }): ContainsResult;
    normalize({ parentType, recursive }: {
        parentType: CompositeType;
        recursive?: boolean;
    }): FragmentSelection | SelectionSet | undefined;
    protected abstract normalizeKnowingItIntersects({ parentType, recursive }: {
        parentType: CompositeType;
        recursive?: boolean;
    }): FragmentSelection | SelectionSet | undefined;
}
declare class InlineFragmentSelection extends FragmentSelection {
    private readonly _selectionSet;
    constructor(fragment: FragmentElement, _selectionSet: SelectionSet);
    get selectionSet(): SelectionSet;
    key(): string;
    withUpdatedComponents(fragment: FragmentElement, selectionSet: SelectionSet): InlineFragmentSelection;
    validate(variableDefinitions: VariableDefinitions): void;
    rebaseOn({ parentType, fragments, errorIfCannotRebase, }: {
        parentType: CompositeType;
        fragments: NamedFragments | undefined;
        errorIfCannotRebase: boolean;
    }): FragmentSelection | undefined;
    canAddTo(parentType: CompositeType): boolean;
    toSelectionNode(): InlineFragmentNode;
    optimize(fragments: NamedFragments, validator: FieldsConflictMultiBranchValidator): FragmentSelection;
    withoutDefer(labelsToRemove?: Set<string>): InlineFragmentSelection | SelectionSet;
    withNormalizedDefer(normalizer: DeferNormalizer): InlineFragmentSelection | SelectionSet;
    protected normalizeKnowingItIntersects({ parentType, recursive }: {
        parentType: CompositeType;
        recursive?: boolean;
    }): FragmentSelection | SelectionSet | undefined;
    expandFragments(updatedFragments: NamedFragments | undefined): FragmentSelection;
    equals(that: Selection): boolean;
    contains(that: Selection, options?: {
        ignoreMissingTypename?: boolean;
    }): ContainsResult;
    toString(expandFragments?: boolean, indent?: string): string;
}
export declare class FragmentSpreadSelection extends FragmentSelection {
    private readonly fragments;
    readonly namedFragment: NamedFragmentDefinition;
    private readonly spreadDirectives;
    private computedKey;
    constructor(sourceType: CompositeType, fragments: NamedFragments, namedFragment: NamedFragmentDefinition, spreadDirectives: readonly Directive<any>[]);
    isFragmentSpread(): boolean;
    get selectionSet(): SelectionSet;
    key(): string;
    withUpdatedComponents(_fragment: FragmentElement, _selectionSet: SelectionSet): InlineFragmentSelection;
    normalizeKnowingItIntersects({ parentType }: {
        parentType: CompositeType;
    }): FragmentSelection;
    validate(): void;
    toSelectionNode(): FragmentSpreadNode;
    optimize(_1: NamedFragments, _2: FieldsConflictMultiBranchValidator): FragmentSelection;
    rebaseOn({ parentType, fragments, errorIfCannotRebase, }: {
        parentType: CompositeType;
        fragments: NamedFragments | undefined;
        errorIfCannotRebase: boolean;
    }): FragmentSelection | undefined;
    canAddTo(_: CompositeType): boolean;
    expandFragments(updatedFragments: NamedFragments | undefined): FragmentSelection | readonly Selection[];
    collectUsedFragmentNames(collector: Map<string, number>): void;
    withoutDefer(_labelsToRemove?: Set<string>): FragmentSpreadSelection;
    withNormalizedDefer(_normalizer: DeferNormalizer): FragmentSpreadSelection;
    minus(that: Selection): undefined;
    equals(that: Selection): boolean;
    contains(that: Selection, options?: {
        ignoreMissingTypename?: boolean;
    }): ContainsResult;
    toString(expandFragments?: boolean, indent?: string): string;
}
export declare function operationFromDocument(schema: Schema, document: DocumentNode, options?: {
    operationName?: string;
    validate?: boolean;
}): Operation;
export declare function parseOperation(schema: Schema, operation: string, options?: {
    operationName?: string;
    validate?: boolean;
}): Operation;
export declare function parseSelectionSet({ parentType, source, variableDefinitions, fragments, fieldAccessor, validate, }: {
    parentType: CompositeType;
    source: string | SelectionSetNode;
    variableDefinitions?: VariableDefinitions;
    fragments?: NamedFragments;
    fieldAccessor?: (type: CompositeType, fieldName: string) => (FieldDefinition<any> | undefined);
    validate?: boolean;
}): SelectionSet;
export declare function parseOperationAST(source: string): OperationDefinitionNode;
export declare function operationToDocument(operation: Operation): DocumentNode;
export declare function hasSelectionWithPredicate(selectionSet: SelectionSet, predicate: (s: Selection) => boolean): boolean;
export {};
//# sourceMappingURL=operations.d.ts.map