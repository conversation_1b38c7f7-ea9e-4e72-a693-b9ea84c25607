import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere, Between } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';

import { Goal, GoalType, GoalStatus, GoalPriority, MeasurementType } from '@app/database/entities/goal.entity';
import { Employee } from '@app/database/entities/employee.entity';
import { TenantService } from '../../tenant/tenant.service';

export interface CreateGoalDto {
  employeeId: string;
  managerId?: string;
  parentGoalId?: string;
  title: string;
  description: string;
  goalType: GoalType;
  priority: GoalPriority;
  targetValue?: number;
  currentValue?: number;
  measurementType?: MeasurementType;
  unit?: string;
  startDate: Date;
  targetDate: Date;
  keyResults?: Array<{
    description: string;
    targetValue: number;
    currentValue?: number;
    unit?: string;
    weight?: number;
  }>;
  milestones?: Array<{
    title: string;
    description?: string;
    targetDate: string;
    completed?: boolean;
  }>;
  tags?: string[];
  metadata?: any;
}

export interface UpdateGoalDto {
  title?: string;
  description?: string;
  priority?: GoalPriority;
  status?: GoalStatus;
  targetValue?: number;
  currentValue?: number;
  measurementType?: MeasurementType;
  unit?: string;
  startDate?: Date;
  targetDate?: Date;
  keyResults?: Array<{
    description: string;
    targetValue: number;
    currentValue?: number;
    unit?: string;
    weight?: number;
  }>;
  milestones?: Array<{
    title: string;
    description?: string;
    targetDate: string;
    completed?: boolean;
  }>;
  progressNotes?: string;
  achievementDate?: Date;
  aiInsights?: any;
  tags?: string[];
  metadata?: any;
}

export interface GoalFilters {
  employeeId?: string;
  managerId?: string;
  parentGoalId?: string;
  goalType?: GoalType;
  status?: GoalStatus;
  priority?: GoalPriority;
  startDate?: Date;
  targetDate?: Date;
  tags?: string[];
}

@Injectable()
export class GoalService {
  constructor(
    @InjectRepository(Goal)
    private goalRepository: Repository<Goal>,
    @InjectRepository(Employee)
    private employeeRepository: Repository<Employee>,
    private tenantService: TenantService,
    private eventEmitter: EventEmitter2,
  ) {}

  async create(createDto: CreateGoalDto, userId: string): Promise<Goal> {
    const tenantId = this.tenantService.getCurrentTenantId();

    // Validate employee exists
    const employee = await this.employeeRepository.findOne({
      where: { id: createDto.employeeId, tenantId },
    });

    if (!employee) {
      throw new NotFoundException('Employee not found');
    }

    // Validate manager if provided
    if (createDto.managerId) {
      const manager = await this.employeeRepository.findOne({
        where: { id: createDto.managerId, tenantId },
      });
      if (!manager) {
        throw new NotFoundException('Manager not found');
      }
    }

    // Validate parent goal if provided
    if (createDto.parentGoalId) {
      const parentGoal = await this.goalRepository.findOne({
        where: { id: createDto.parentGoalId, tenantId },
      });
      if (!parentGoal) {
        throw new NotFoundException('Parent goal not found');
      }
    }

    const goal = this.goalRepository.create({
      ...createDto,
      tenantId,
      createdBy: userId,
      status: GoalStatus.DRAFT,
      currentValue: createDto.currentValue || 0,
    });

    const savedGoal = await this.goalRepository.save(goal);

    // Emit event
    this.eventEmitter.emit('goal.created', {
      goal: savedGoal,
      tenantId,
      userId,
    });

    return savedGoal;
  }

  async findAll(
    filters: GoalFilters = {},
    page: number = 1,
    limit: number = 10,
  ): Promise<{ data: Goal[]; total: number; page: number; limit: number }> {
    const tenantId = this.tenantService.getCurrentTenantId();

    const where: FindOptionsWhere<Goal> = {
      tenantId,
      ...filters,
    };

    const [data, total] = await this.goalRepository.findAndCount({
      where,
      relations: ['employee', 'manager', 'parentGoal', 'subGoals'],
      order: { createdAt: 'DESC' },
      skip: (page - 1) * limit,
      take: limit,
    });

    return { data, total, page, limit };
  }

  async findOne(id: string): Promise<Goal> {
    const tenantId = this.tenantService.getCurrentTenantId();

    const goal = await this.goalRepository.findOne({
      where: { id, tenantId },
      relations: ['employee', 'manager', 'parentGoal', 'subGoals'],
    });

    if (!goal) {
      throw new NotFoundException('Goal not found');
    }

    return goal;
  }

  async update(id: string, updateDto: UpdateGoalDto, userId: string): Promise<Goal> {
    const goal = await this.findOne(id);

    // Validate status transitions
    if (updateDto.status) {
      this.validateStatusTransition(goal.status, updateDto.status);
    }

    // Update achievement date if status is achieved
    if (updateDto.status === GoalStatus.ACHIEVED && !updateDto.achievementDate) {
      updateDto.achievementDate = new Date();
    }

    // Calculate progress percentage if current value is updated
    if (updateDto.currentValue !== undefined && goal.targetValue) {
      const progressPercentage = Math.min((updateDto.currentValue / goal.targetValue) * 100, 100);
      updateDto['progressPercentage'] = progressPercentage;
    }

    Object.assign(goal, updateDto);
    goal.updatedBy = userId;

    const savedGoal = await this.goalRepository.save(goal);

    // Emit event
    this.eventEmitter.emit('goal.updated', {
      goal: savedGoal,
      tenantId: this.tenantService.getCurrentTenantId(),
      userId,
      changes: updateDto,
    });

    return savedGoal;
  }

  async delete(id: string, userId: string): Promise<void> {
    const goal = await this.findOne(id);

    if (goal.status === GoalStatus.ACHIEVED) {
      throw new BadRequestException('Cannot delete achieved goal');
    }

    // Check if goal has sub-goals
    const subGoalsCount = await this.goalRepository.count({
      where: { parentGoalId: id, tenantId: this.tenantService.getCurrentTenantId() },
    });

    if (subGoalsCount > 0) {
      throw new BadRequestException('Cannot delete goal with sub-goals');
    }

    await this.goalRepository.remove(goal);

    // Emit event
    this.eventEmitter.emit('goal.deleted', {
      goalId: id,
      tenantId: this.tenantService.getCurrentTenantId(),
      userId,
    });
  }

  async getEmployeeGoals(
    employeeId: string,
    page: number = 1,
    limit: number = 10,
  ): Promise<{ data: Goal[]; total: number; page: number; limit: number }> {
    return this.findAll({ employeeId }, page, limit);
  }

  async getGoalsByManager(
    managerId: string,
    page: number = 1,
    limit: number = 10,
  ): Promise<{ data: Goal[]; total: number; page: number; limit: number }> {
    return this.findAll({ managerId }, page, limit);
  }

  async getOverdueGoals(): Promise<Goal[]> {
    const tenantId = this.tenantService.getCurrentTenantId();
    const today = new Date();

    return this.goalRepository.find({
      where: {
        tenantId,
        dueDate: Between(new Date('1900-01-01'), today),
        status: GoalStatus.IN_PROGRESS,
      },
      relations: ['employee', 'manager'],
    });
  }

  async updateProgress(
    id: string,
    currentValue: number,
    progressNotes?: string,
    userId?: string,
  ): Promise<Goal> {
    const goal = await this.findOne(id);

    const progressPercentage = goal.targetValue 
      ? Math.min((currentValue / goal.targetValue) * 100, 100)
      : 0;

    // Auto-update status based on progress
    let newStatus = goal.status;
    if (progressPercentage >= 100 && goal.status === GoalStatus.IN_PROGRESS) {
      newStatus = GoalStatus.ACHIEVED;
    } else if (progressPercentage > 0 && goal.status === GoalStatus.DRAFT) {
      newStatus = GoalStatus.IN_PROGRESS;
    }

    const updateDto: UpdateGoalDto = {
      currentValue,
      progressNotes,
      status: newStatus,
    };

    if (newStatus === GoalStatus.ACHIEVED) {
      updateDto.achievementDate = new Date();
    }

    return this.update(id, updateDto, userId || goal.createdBy);
  }

  async getGoalHierarchy(parentGoalId?: string): Promise<Goal[]> {
    const tenantId = this.tenantService.getCurrentTenantId();

    return this.goalRepository.find({
      where: {
        tenantId,
        parentGoalId: parentGoalId || null,
      },
      relations: ['employee', 'manager', 'subGoals'],
      order: { priority: 'DESC', createdAt: 'ASC' },
    });
  }

  private validateStatusTransition(currentStatus: GoalStatus, newStatus: GoalStatus): void {
    const validTransitions: Record<GoalStatus, GoalStatus[]> = {
      [GoalStatus.DRAFT]: [GoalStatus.IN_PROGRESS, GoalStatus.CANCELLED],
      [GoalStatus.IN_PROGRESS]: [GoalStatus.ACHIEVED, GoalStatus.ON_HOLD, GoalStatus.CANCELLED],
      [GoalStatus.ON_HOLD]: [GoalStatus.IN_PROGRESS, GoalStatus.CANCELLED],
      [GoalStatus.ACHIEVED]: [],
      [GoalStatus.CANCELLED]: [GoalStatus.DRAFT, GoalStatus.IN_PROGRESS],
    };

    if (!validTransitions[currentStatus]?.includes(newStatus)) {
      throw new BadRequestException(`Invalid status transition from ${currentStatus} to ${newStatus}`);
    }
  }
}
