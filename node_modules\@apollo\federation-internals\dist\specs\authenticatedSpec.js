"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AUTHENTICATED_VERSIONS = exports.AuthenticatedSpecDefinition = void 0;
const graphql_1 = require("graphql");
const coreSpec_1 = require("./coreSpec");
const directiveAndTypeSpecification_1 = require("../directiveAndTypeSpecification");
const knownCoreFeatures_1 = require("../knownCoreFeatures");
class AuthenticatedSpecDefinition extends coreSpec_1.FeatureDefinition {
    constructor(version, minimumFederationVersion) {
        super(new coreSpec_1.FeatureUrl(AuthenticatedSpecDefinition.identity, AuthenticatedSpecDefinition.directiveName, version), minimumFederationVersion);
        this.registerDirective((0, directiveAndTypeSpecification_1.createDirectiveSpecification)({
            name: AuthenticatedSpecDefinition.directiveName,
            locations: [
                graphql_1.DirectiveLocation.FIELD_DEFINITION,
                graphql_1.DirectiveLocation.OBJECT,
                graphql_1.DirectiveLocation.INTERFACE,
                graphql_1.DirectiveLocation.SCALAR,
                graphql_1.DirectiveLocation.ENUM,
            ],
            composes: true,
            supergraphSpecification: () => exports.AUTHENTICATED_VERSIONS.latest(),
        }));
    }
    get defaultCorePurpose() {
        return 'SECURITY';
    }
}
exports.AuthenticatedSpecDefinition = AuthenticatedSpecDefinition;
AuthenticatedSpecDefinition.directiveName = "authenticated";
AuthenticatedSpecDefinition.identity = `https://specs.apollo.dev/${AuthenticatedSpecDefinition.directiveName}`;
exports.AUTHENTICATED_VERSIONS = new coreSpec_1.FeatureDefinitions(AuthenticatedSpecDefinition.identity).add(new AuthenticatedSpecDefinition(new coreSpec_1.FeatureVersion(0, 1), new coreSpec_1.FeatureVersion(2, 5)));
(0, knownCoreFeatures_1.registerKnownFeature)(exports.AUTHENTICATED_VERSIONS);
//# sourceMappingURL=authenticatedSpec.js.map