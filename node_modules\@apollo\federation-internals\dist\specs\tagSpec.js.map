{"version": 3, "file": "tagSpec.js", "sourceRoot": "", "sources": ["../../src/specs/tagSpec.ts"], "names": [], "mappings": ";;;AAAA,qCAA0D;AAC1D,yCAA+F;AAC/F,gDAAkE;AAClE,oFAAwG;AACxG,oCAAkC;AAClC,4DAA4D;AAC5D,oCAAoC;AAEvB,QAAA,WAAW,GAAG,8BAA8B,CAAC;AAE1D,MAAa,iBAAkB,SAAQ,4BAAiB;IAKtD,YAAY,OAAuB,EAAE,wBAAyC;QAC5E,KAAK,CAAC,IAAI,qBAAU,CAAC,mBAAW,EAAE,KAAK,EAAE,OAAO,CAAC,EAAE,wBAAwB,CAAC,CAAC;QAC7E,IAAI,CAAC,YAAY,GAAG;YAClB,2BAAiB,CAAC,gBAAgB;YAClC,2BAAiB,CAAC,MAAM;YACxB,2BAAiB,CAAC,SAAS;YAC3B,2BAAiB,CAAC,KAAK;SACxB,CAAC;QACF,IAAI,CAAC,oBAAoB,GAAG,2FAA2F,CAAC;QACxH,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC;YAClB,IAAI,CAAC,YAAY,CAAC,IAAI,CACpB,2BAAiB,CAAC,mBAAmB,EACrC,2BAAiB,CAAC,MAAM,EACxB,2BAAiB,CAAC,IAAI,EACtB,2BAAiB,CAAC,UAAU,EAC5B,2BAAiB,CAAC,YAAY,EAC9B,2BAAiB,CAAC,sBAAsB,CACzC,CAAC;YACF,IAAI,CAAC,oBAAoB,GAAG,sLAAsL,CAAC;YACnN,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC;gBAClB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,2BAAiB,CAAC,MAAM,CAAC,CAAC;gBACjD,IAAI,CAAC,oBAAoB,GAAG,+LAA+L,CAAC;YAC9N,CAAC;QACH,CAAC;QACD,IAAI,CAAC,gBAAgB,GAAG,IAAA,4DAA4B,EAAC;YACnD,IAAI,EAAC,KAAK;YACV,SAAS,EAAE,IAAI,CAAC,YAAY;YAC5B,UAAU,EAAE,IAAI;YAChB,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,yBAAW,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC;YAChF,QAAQ,EAAE,IAAI;YACd,uBAAuB,EAAE,CAAC,UAAU,EAAE,EAAE,CAAC,oBAAY,CAAC,yBAAyB,CAAC,UAAU,CAAC;SAC5F,CAAC,CAAC;QACH,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;IAChD,CAAC;IAEO,KAAK;QACX,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,yBAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACvD,CAAC;IAEO,KAAK;QACX,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,yBAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;IACtD,CAAC;IAED,wBAAwB,CAAC,UAA+B;QACtD,MAAM,mBAAmB,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;QAC3E,MAAM,OAAO,GAAG,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC5C,MAAM,eAAe,GAAG,OAAO,IAAI,IAAA,gBAAQ,EAAC,OAAO,CAAC,IAAK,EAAE,IAAI,yBAAW,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;QAC9G,MAAM,iBAAiB,GAAG,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;QAC7F,IAAI,mBAAmB,IAAI,CAAC,eAAe,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAClE,OAAO,cAAM,CAAC,4BAA4B,CAAC,GAAG,CAC5C,0IAA0I,IAAI,CAAC,oBAAoB,EAAE,CACtK,CAAC;QACJ,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;CACF;AA5DD,8CA4DC;AAEY,QAAA,YAAY,GAAG,IAAI,6BAAkB,CAAoB,mBAAW,CAAC;KAC/E,GAAG,CAAC,IAAI,iBAAiB,CAAC,IAAI,yBAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KACpD,GAAG,CAAC,IAAI,iBAAiB,CAAC,IAAI,yBAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KACpD,GAAG,CAAC,IAAI,iBAAiB,CAAC,IAAI,yBAAc,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,yBAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAElF,IAAA,wCAAoB,EAAC,oBAAY,CAAC,CAAC"}