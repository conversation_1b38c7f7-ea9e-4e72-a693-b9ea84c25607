import { <PERSON>, Post, Param, UseGuards, Request, ParseUUIDPipe } from '@nestjs/common';
import { ApiTags, ApiBearerAuth } from '@nestjs/swagger';

import { JwtAuthGuard, RolesGuard } from '@app/security/guards';
import { Roles } from '@app/security/decorators/roles.decorator';
import { UserRole } from '@app/common';
import { RequestWithUser } from '@app/security';

import { EmployeeOffboardingService } from '../services/employee-offboarding.service';

@ApiTags('Employee Offboarding')
@Controller('employee-offboarding')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
export class EmployeeOffboardingController {
  constructor(private readonly offboardingService: EmployeeOffboardingService) {}

  @Post(':employeeId/start')
  @Roles(UserRole.ADMIN, UserRole.HR, UserRole.SUPER_ADMIN)
  async startOffboarding(
    @Param('employeeId', ParseUUIDPipe) employeeId: string,
    @Request() req: RequestWithUser,
  ) {
    return this.offboardingService.startOffboardingProcess(employeeId, req.user.tenantId);
  }
}
