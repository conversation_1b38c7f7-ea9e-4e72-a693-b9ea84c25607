import { type HeadersInit } from 'node-fetch';
import { GraphQLDataSource } from '../../datasources/types';
import { ServiceDefinitionUpdate, ServiceEndpointDefinition } from '../../config';
export type Service = ServiceEndpointDefinition & {
    dataSource: GraphQLDataSource;
};
export declare function loadServicesFromRemoteEndpoint({ serviceList, getServiceIntrospectionHeaders, serviceSdlCache, }: {
    serviceList: Service[];
    getServiceIntrospectionHeaders: (service: ServiceEndpointDefinition) => Promise<HeadersInit | undefined>;
    serviceSdlCache: Map<string, string>;
}): Promise<ServiceDefinitionUpdate>;
//# sourceMappingURL=loadServicesFromRemoteEndpoint.d.ts.map