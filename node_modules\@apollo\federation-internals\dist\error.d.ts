import { ASTNode, GraphQLError, GraphQLErrorOptions, GraphQLFormattedError } from "graphql";
type SchemaRootKind = 'query' | 'mutation' | 'subscription';
export type ErrorCodeMetadata = {
    addedIn: string;
    replaces?: string[];
};
export type ErrorCodeDefinition = {
    code: string;
    description: string;
    metadata: ErrorCodeMetadata;
    err: (message: string, options?: GraphQLErrorOptions) => GraphQLError;
};
export declare function extractGraphQLErrorOptions(e: GraphQLError): GraphQLErrorOptions;
export declare function aggregateError(code: string, message: string, causes: GraphQLError[]): GraphQLError;
export declare function errorCauses(e: Error): GraphQLError[] | undefined;
export declare function printGraphQLErrorsOrRethrow(e: Error): string;
export declare function printErrors(errors: GraphQLError[]): string;
export type ErrorCodeCategory<TElement = string> = {
    get(element: TElement): ErrorCodeDefinition;
};
export declare function errorCode(e: GraphQLError | GraphQLFormattedError): string | undefined;
export declare function errorCodeDef(e: GraphQLError | GraphQLFormattedError | string): ErrorCodeDefinition | undefined;
export declare function withModifiedErrorMessage(e: GraphQLError, newMessage: string): GraphQLError;
export declare function withModifiedErrorNodes(e: GraphQLError, newNodes: readonly ASTNode[] | ASTNode | undefined): GraphQLError;
export declare const ERROR_CATEGORIES: {
    DIRECTIVE_FIELDS_MISSING_EXTERNAL: ErrorCodeCategory<string> & {
        createCode(element: string): ErrorCodeDefinition;
    };
    DIRECTIVE_UNSUPPORTED_ON_INTERFACE: ErrorCodeCategory<string> & {
        createCode(element: string): ErrorCodeDefinition;
    };
    DIRECTIVE_INVALID_FIELDS_TYPE: ErrorCodeCategory<string> & {
        createCode(element: string): ErrorCodeDefinition;
    };
    DIRECTIVE_INVALID_FIELDS: ErrorCodeCategory<string> & {
        createCode(element: string): ErrorCodeDefinition;
    };
    FIELDS_HAS_ARGS: ErrorCodeCategory<string> & {
        createCode(element: string): ErrorCodeDefinition;
    };
    ROOT_TYPE_USED: ErrorCodeCategory<SchemaRootKind> & {
        createCode(element: SchemaRootKind): ErrorCodeDefinition;
    };
    DIRECTIVE_IN_FIELDS_ARG: ErrorCodeCategory<string> & {
        createCode(element: string): ErrorCodeDefinition;
    };
};
export declare const ERRORS: {
    INVALID_GRAPHQL: ErrorCodeDefinition;
    DIRECTIVE_DEFINITION_INVALID: ErrorCodeDefinition;
    TYPE_DEFINITION_INVALID: ErrorCodeDefinition;
    UNSUPPORTED_LINKED_FEATURE: ErrorCodeDefinition;
    UNKNOWN_FEDERATION_LINK_VERSION: ErrorCodeDefinition;
    UNKNOWN_LINK_VERSION: ErrorCodeDefinition;
    KEY_FIELDS_HAS_ARGS: ErrorCodeDefinition;
    PROVIDES_FIELDS_HAS_ARGS: ErrorCodeDefinition;
    PROVIDES_MISSING_EXTERNAL: ErrorCodeDefinition;
    REQUIRES_MISSING_EXTERNAL: ErrorCodeDefinition;
    KEY_UNSUPPORTED_ON_INTERFACE: ErrorCodeDefinition;
    PROVIDES_UNSUPPORTED_ON_INTERFACE: ErrorCodeDefinition;
    REQUIRES_UNSUPPORTED_ON_INTERFACE: ErrorCodeDefinition;
    EXTERNAL_UNUSED: ErrorCodeDefinition;
    EXTERNAL_COLLISION_WITH_ANOTHER_DIRECTIVE: ErrorCodeDefinition;
    TYPE_WITH_ONLY_UNUSED_EXTERNAL: ErrorCodeDefinition;
    PROVIDES_ON_NON_OBJECT_FIELD: ErrorCodeDefinition;
    KEY_INVALID_FIELDS_TYPE: ErrorCodeDefinition;
    PROVIDES_INVALID_FIELDS_TYPE: ErrorCodeDefinition;
    REQUIRES_INVALID_FIELDS_TYPE: ErrorCodeDefinition;
    KEY_INVALID_FIELDS: ErrorCodeDefinition;
    PROVIDES_INVALID_FIELDS: ErrorCodeDefinition;
    REQUIRES_INVALID_FIELDS: ErrorCodeDefinition;
    KEY_FIELDS_SELECT_INVALID_TYPE: ErrorCodeDefinition;
    ROOT_QUERY_USED: ErrorCodeDefinition;
    ROOT_MUTATION_USED: ErrorCodeDefinition;
    ROOT_SUBSCRIPTION_USED: ErrorCodeDefinition;
    INVALID_SUBGRAPH_NAME: ErrorCodeDefinition;
    NO_QUERIES: ErrorCodeDefinition;
    INTERFACE_FIELD_NO_IMPLEM: ErrorCodeDefinition;
    TYPE_KIND_MISMATCH: ErrorCodeDefinition;
    CONTEXT_NOT_SET: ErrorCodeDefinition;
    CONTEXT_INVALID_SELECTION: ErrorCodeDefinition;
    NO_CONTEXT_IN_SELECTION: ErrorCodeDefinition;
    CONTEXT_NO_RESOLVABLE_KEY: ErrorCodeDefinition;
    CONTEXT_NAME_INVALID: ErrorCodeDefinition;
    EXTERNAL_TYPE_MISMATCH: ErrorCodeDefinition;
    EXTERNAL_ARGUMENT_MISSING: ErrorCodeDefinition;
    EXTERNAL_ARGUMENT_TYPE_MISMATCH: ErrorCodeDefinition;
    EXTERNAL_ARGUMENT_DEFAULT_MISMATCH: ErrorCodeDefinition;
    EXTERNAL_ON_INTERFACE: ErrorCodeDefinition;
    MERGED_DIRECTIVE_APPLICATION_ON_EXTERNAL: ErrorCodeDefinition;
    FIELD_TYPE_MISMATCH: ErrorCodeDefinition;
    ARGUMENT_TYPE_MISMATCH: ErrorCodeDefinition;
    INPUT_FIELD_DEFAULT_MISMATCH: ErrorCodeDefinition;
    ARGUMENT_DEFAULT_MISMATCH: ErrorCodeDefinition;
    EXTENSION_WITH_NO_BASE: ErrorCodeDefinition;
    EXTERNAL_MISSING_ON_BASE: ErrorCodeDefinition;
    INVALID_FIELD_SHARING: ErrorCodeDefinition;
    INVALID_SHAREABLE_USAGE: ErrorCodeDefinition;
    INVALID_LINK_DIRECTIVE_USAGE: ErrorCodeDefinition;
    INVALID_LINK_IDENTIFIER: ErrorCodeDefinition;
    LINK_IMPORT_NAME_MISMATCH: ErrorCodeDefinition;
    REFERENCED_INACCESSIBLE: ErrorCodeDefinition;
    DEFAULT_VALUE_USES_INACCESSIBLE: ErrorCodeDefinition;
    QUERY_ROOT_TYPE_INACCESSIBLE: ErrorCodeDefinition;
    REQUIRED_INACCESSIBLE: ErrorCodeDefinition;
    DISALLOWED_INACCESSIBLE: ErrorCodeDefinition;
    IMPLEMENTED_BY_INACCESSIBLE: ErrorCodeDefinition;
    ONLY_INACCESSIBLE_CHILDREN: ErrorCodeDefinition;
    REQUIRED_ARGUMENT_MISSING_IN_SOME_SUBGRAPH: ErrorCodeDefinition;
    REQUIRED_INPUT_FIELD_MISSING_IN_SOME_SUBGRAPH: ErrorCodeDefinition;
    EMPTY_MERGED_INPUT_TYPE: ErrorCodeDefinition;
    ENUM_VALUE_MISMATCH: ErrorCodeDefinition;
    EMPTY_MERGED_ENUM_TYPE: ErrorCodeDefinition;
    SHAREABLE_HAS_MISMATCHED_RUNTIME_TYPES: ErrorCodeDefinition;
    SATISFIABILITY_ERROR: ErrorCodeDefinition;
    OVERRIDE_COLLISION_WITH_ANOTHER_DIRECTIVE: ErrorCodeDefinition;
    OVERRIDE_FROM_SELF_ERROR: ErrorCodeDefinition;
    OVERRIDE_SOURCE_HAS_OVERRIDE: ErrorCodeDefinition;
    OVERRIDE_ON_INTERFACE: ErrorCodeDefinition;
    OVERRIDE_LABEL_INVALID: ErrorCodeDefinition;
    UNSUPPORTED_FEATURE: ErrorCodeDefinition;
    INVALID_FEDERATION_SUPERGRAPH: ErrorCodeDefinition;
    DOWNSTREAM_SERVICE_ERROR: ErrorCodeDefinition;
    KEY_HAS_DIRECTIVE_IN_FIELDS_ARGS: ErrorCodeDefinition;
    PROVIDES_HAS_DIRECTIVE_IN_FIELDS_ARGS: ErrorCodeDefinition;
    REQUIRES_HAS_DIRECTIVE_IN_FIELDS_ARGS: ErrorCodeDefinition;
    DIRECTIVE_COMPOSITION_ERROR: ErrorCodeDefinition;
    INTERFACE_OBJECT_USAGE_ERROR: ErrorCodeDefinition;
    INTERFACE_KEY_NOT_ON_IMPLEMENTATION: ErrorCodeDefinition;
    INTERFACE_KEY_MISSING_IMPLEMENTATION_TYPE: ErrorCodeDefinition;
    CONTEXTUAL_ARGUMENT_NOT_CONTEXTUAL_IN_ALL_SUBGRAPHS: ErrorCodeDefinition;
    COST_APPLIED_TO_INTERFACE_FIELD: ErrorCodeDefinition;
    LIST_SIZE_APPLIED_TO_NON_LIST: ErrorCodeDefinition;
    LIST_SIZE_INVALID_ASSUMED_SIZE: ErrorCodeDefinition;
    LIST_SIZE_INVALID_SIZED_FIELD: ErrorCodeDefinition;
    LIST_SIZE_INVALID_SLICING_ARGUMENT: ErrorCodeDefinition;
    MAX_VALIDATION_SUBGRAPH_PATHS_EXCEEDED: ErrorCodeDefinition;
};
export declare const REMOVED_ERRORS: string[][];
export {};
//# sourceMappingURL=error.d.ts.map