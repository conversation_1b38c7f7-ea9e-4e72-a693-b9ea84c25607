import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseU<PERSON><PERSON>ipe,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard, RolesGuard, TenantGuard } from '@app/security';
import { Roles, CurrentUser, CurrentTenant } from '@app/common';
import { UserRole } from '@app/common/enums/user-role.enum';
import { PerformanceMetricService } from '../services/performance-metric.service';

@ApiTags('performance-metrics')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('performance-metrics')
export class PerformanceMetricController {
  constructor(private readonly performanceMetricService: PerformanceMetricService) {}

  @Post()
  @Roles(UserRole.ADMIN, UserRole.HR, UserRole.MANAGER)
  @ApiOperation({ summary: 'Create performance metric' })
  @ApiResponse({ status: 201, description: 'Performance metric created successfully' })
  async create(
    @Body() createPerformanceMetricDto: any,
    @CurrentUser() user: any,
    @CurrentTenant() tenantId: string,
  ) {
    return this.performanceMetricService.create(createPerformanceMetricDto, user.id, tenantId);
  }

  @Get()
  @Roles(UserRole.ADMIN, UserRole.HR, UserRole.MANAGER, UserRole.EMPLOYEE)
  @ApiOperation({ summary: 'Get all performance metrics' })
  @ApiResponse({ status: 200, description: 'Performance metrics retrieved successfully' })
  async findAll(
    @Query() filters: any,
    @CurrentTenant() tenantId: string,
  ) {
    return this.performanceMetricService.findAll(tenantId, filters);
  }

  @Get(':id')
  @Roles(UserRole.ADMIN, UserRole.HR, UserRole.MANAGER, UserRole.EMPLOYEE)
  @ApiOperation({ summary: 'Get performance metric by ID' })
  @ApiResponse({ status: 200, description: 'Performance metric retrieved successfully' })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentTenant() tenantId: string,
  ) {
    return this.performanceMetricService.findOne(id, tenantId);
  }

  @Patch(':id')
  @Roles(UserRole.ADMIN, UserRole.HR, UserRole.MANAGER)
  @ApiOperation({ summary: 'Update performance metric' })
  @ApiResponse({ status: 200, description: 'Performance metric updated successfully' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updatePerformanceMetricDto: any,
    @CurrentUser() user: any,
    @CurrentTenant() tenantId: string,
  ) {
    return this.performanceMetricService.update(id, updatePerformanceMetricDto, tenantId);
  }

  @Delete(':id')
  @Roles(UserRole.ADMIN, UserRole.HR)
  @ApiOperation({ summary: 'Delete performance metric' })
  @ApiResponse({ status: 200, description: 'Performance metric deleted successfully' })
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentTenant() tenantId: string,
  ) {
    return this.performanceMetricService.delete(id, tenantId);
  }

  @Get('employee/:employeeId')
  @Roles(UserRole.ADMIN, UserRole.HR, UserRole.MANAGER, UserRole.EMPLOYEE)
  @ApiOperation({ summary: 'Get performance metrics for employee' })
  @ApiResponse({ status: 200, description: 'Employee performance metrics retrieved successfully' })
  async getEmployeeMetrics(
    @Param('employeeId', ParseUUIDPipe) employeeId: string,
    @Query() filters: any,
    @CurrentTenant() tenantId: string,
  ) {
    return this.performanceMetricService.findByEmployee(employeeId, tenantId, filters);
  }

  @Post(':id/update-value')
  @Roles(UserRole.ADMIN, UserRole.HR, UserRole.MANAGER, UserRole.EMPLOYEE)
  @ApiOperation({ summary: 'Update performance metric value' })
  @ApiResponse({ status: 200, description: 'Performance metric value updated successfully' })
  async updateValue(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateValueDto: { value: number; notes?: string },
    @CurrentUser() user: any,
    @CurrentTenant() tenantId: string,
  ) {
    // For now, just update the metric with the new value
    return this.performanceMetricService.update(id, { value: updateValueDto.value, notes: updateValueDto.notes }, tenantId);
  }
}
