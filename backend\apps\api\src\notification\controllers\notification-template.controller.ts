import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards, ParseUUIDPipe } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard, RolesGuard, TenantGuard } from '@app/security';
import { Roles, CurrentUser, CurrentTenant } from '@app/common';
import { NotificationTemplateService } from '../services/notification-template.service';

@ApiTags('Notification Templates')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard, TenantGuard, RolesGuard)
@Controller('notification-templates')
export class NotificationTemplateController {
  constructor(private readonly notificationTemplateService: NotificationTemplateService) {}

  @Post()
  @Roles('admin', 'hr_manager')
  @ApiOperation({ summary: 'Create notification template' })
  async create(@Body() createTemplateDto: any, @CurrentUser() user: any, @CurrentTenant() tenantId: string) {
    return this.notificationTemplateService.create(createTemplateDto, user.id, tenantId);
  }

  @Get()
  @Roles('admin', 'hr_manager', 'hr_user', 'manager')
  @ApiOperation({ summary: 'Get notification templates' })
  async findAll(@Query() query: any, @CurrentTenant() tenantId: string) {
    return this.notificationTemplateService.findAll(query, tenantId);
  }

  @Get(':id')
  @Roles('admin', 'hr_manager', 'hr_user', 'manager')
  @ApiOperation({ summary: 'Get notification template by ID' })
  async findOne(@Param('id', ParseUUIDPipe) id: string, @CurrentTenant() tenantId: string) {
    return this.notificationTemplateService.findOne(id, tenantId);
  }

  @Put(':id')
  @Roles('admin', 'hr_manager')
  @ApiOperation({ summary: 'Update notification template' })
  async update(@Param('id', ParseUUIDPipe) id: string, @Body() updateTemplateDto: any, @CurrentUser() user: any, @CurrentTenant() tenantId: string) {
    return this.notificationTemplateService.update(id, updateTemplateDto, user.id, tenantId);
  }

  @Delete(':id')
  @Roles('admin', 'hr_manager')
  @ApiOperation({ summary: 'Delete notification template' })
  async remove(@Param('id', ParseUUIDPipe) id: string, @CurrentUser() user: any, @CurrentTenant() tenantId: string) {
    return this.notificationTemplateService.remove(id, user.id, tenantId);
  }
}
