import React, { useState } from 'react';
import { useAppDispatch, useAppSelector } from '@/store';
import { loginUser } from '@/store/slices/auth-slice';

const DebugLogin: React.FC = () => {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('Password1234');
  const [logs, setLogs] = useState<string[]>([]);
  
  const dispatch = useAppDispatch();
  const auth = useAppSelector((state) => state.auth);
  
  const addLog = (message: string) => {
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const handleDirectApiTest = async () => {
    addLog('Testing direct API call...');
    try {
      const response = await fetch('/api/v1/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });
      
      addLog(`Response status: ${response.status}`);
      
      if (response.ok) {
        const data = await response.json();
        addLog(`Response data: ${JSON.stringify(data, null, 2)}`);
      } else {
        const error = await response.text();
        addLog(`Error response: ${error}`);
      }
    } catch (error) {
      addLog(`Network error: ${error}`);
    }
  };

  const handleReduxLogin = async () => {
    addLog('Testing Redux login...');
    try {
      const result = await dispatch(loginUser({ email, password }));
      addLog(`Redux result: ${JSON.stringify(result, null, 2)}`);
      addLog(`Result type: ${result.type}`);
      addLog(`Is fulfilled: ${result.type === 'auth/login/fulfilled'}`);
    } catch (error) {
      addLog(`Redux error: ${error}`);
    }
  };

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Debug Login</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div>
          <h2 className="text-lg font-semibold mb-4">Test Controls</h2>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-1">Email:</label>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full p-2 border rounded"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-1">Password:</label>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full p-2 border rounded"
              />
            </div>
            
            <div className="space-y-2">
              <button
                onClick={handleDirectApiTest}
                className="w-full p-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              >
                Test Direct API Call
              </button>
              
              <button
                onClick={handleReduxLogin}
                className="w-full p-2 bg-green-500 text-white rounded hover:bg-green-600"
              >
                Test Redux Login
              </button>
              
              <button
                onClick={() => setLogs([])}
                className="w-full p-2 bg-gray-500 text-white rounded hover:bg-gray-600"
              >
                Clear Logs
              </button>
            </div>
          </div>
          
          <div className="mt-6">
            <h3 className="text-md font-semibold mb-2">Auth State:</h3>
            <pre className="bg-gray-100 p-3 rounded text-xs overflow-auto">
              {JSON.stringify(auth, null, 2)}
            </pre>
          </div>
        </div>
        
        <div>
          <h2 className="text-lg font-semibold mb-4">Logs</h2>
          <div className="bg-black text-green-400 p-4 rounded h-96 overflow-auto font-mono text-sm">
            {logs.map((log, index) => (
              <div key={index}>{log}</div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DebugLogin;
