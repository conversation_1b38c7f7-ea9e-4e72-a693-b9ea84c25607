{"hash": "b31a57cf", "configHash": "6c1c09a6", "lockfileHash": "a3d3228f", "browserHash": "faf48d63", "optimized": {"react": {"src": "../../../../node_modules/react/index.js", "file": "react.js", "fileHash": "729261cd", "needsInterop": true}, "react-dom": {"src": "../../../../node_modules/react-dom/index.js", "file": "react-dom.js", "fileHash": "1f712dbb", "needsInterop": true}, "react-router-dom": {"src": "../../../../node_modules/react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "f3f03678", "needsInterop": false}, "@reduxjs/toolkit": {"src": "../../../../node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs", "file": "@reduxjs_toolkit.js", "fileHash": "272fa52d", "needsInterop": false}, "react-redux": {"src": "../../../../node_modules/react-redux/dist/react-redux.mjs", "file": "react-redux.js", "fileHash": "c5fb71a7", "needsInterop": false}, "@apollo/client": {"src": "../../../../node_modules/@apollo/client/index.js", "file": "@apollo_client.js", "fileHash": "679e9ef2", "needsInterop": false}, "graphql": {"src": "../../../../node_modules/graphql/index.mjs", "file": "graphql.js", "fileHash": "b85478ba", "needsInterop": false}, "react/jsx-dev-runtime": {"src": "../../../../node_modules/react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "037433ef", "needsInterop": true}, "@apollo/client/link/context": {"src": "../../../../node_modules/@apollo/client/link/context/index.js", "file": "@apollo_client_link_context.js", "fileHash": "a2ce8761", "needsInterop": false}, "@apollo/client/link/error": {"src": "../../../../node_modules/@apollo/client/link/error/index.js", "file": "@apollo_client_link_error.js", "fileHash": "4d37868f", "needsInterop": false}, "@apollo/client/link/retry": {"src": "../../../../node_modules/@apollo/client/link/retry/index.js", "file": "@apollo_client_link_retry.js", "fileHash": "b6425324", "needsInterop": false}, "@radix-ui/react-progress": {"src": "../../../../node_modules/@radix-ui/react-progress/dist/index.mjs", "file": "@radix-ui_react-progress.js", "fileHash": "d47ca693", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../../../node_modules/@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "c4958b4d", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../../../node_modules/@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "ecabdf39", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../../../node_modules/@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "7e5e0f8a", "needsInterop": false}, "@tanstack/react-query": {"src": "../../../../node_modules/@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "c82b8c04", "needsInterop": false}, "@tanstack/react-query-devtools": {"src": "../../../../node_modules/@tanstack/react-query-devtools/build/modern/index.js", "file": "@tanstack_react-query-devtools.js", "fileHash": "f1abdc1b", "needsInterop": false}, "@tanstack/react-table": {"src": "../../../../node_modules/@tanstack/react-table/build/lib/index.mjs", "file": "@tanstack_react-table.js", "fileHash": "8b88f805", "needsInterop": false}, "class-variance-authority": {"src": "../../../../node_modules/class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "2b42f93b", "needsInterop": false}, "clsx": {"src": "../../../../node_modules/clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "5382abce", "needsInterop": false}, "lucide-react": {"src": "../../../../node_modules/lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "dba61fee", "needsInterop": false}, "react-dom/client": {"src": "../../../../node_modules/react-dom/client.js", "file": "react-dom_client.js", "fileHash": "73596a39", "needsInterop": true}, "react/jsx-runtime": {"src": "../../../../node_modules/react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "514b3c73", "needsInterop": true}, "tailwind-merge": {"src": "../../../../node_modules/tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "a203f62c", "needsInterop": false}, "axios": {"src": "../../../../node_modules/axios/index.js", "file": "axios.js", "fileHash": "3ab88089", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../../../node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "ec5e07e3", "needsInterop": false}, "@radix-ui/react-avatar": {"src": "../../../../node_modules/@radix-ui/react-avatar/dist/index.mjs", "file": "@radix-ui_react-avatar.js", "fileHash": "6a157155", "needsInterop": false}}, "chunks": {"HH7B3BHX-Q4YOC5WZ": {"file": "HH7B3BHX-Q4YOC5WZ.js"}, "JZI2RDCT-DWJ3VECO": {"file": "JZI2RDCT-DWJ3VECO.js"}, "chunk-LBMSAYCB": {"file": "chunk-LBMSAYCB.js"}, "chunk-2U47SVD6": {"file": "chunk-2U47SVD6.js"}, "chunk-PZ64A3AQ": {"file": "chunk-PZ64A3AQ.js"}, "chunk-5OZCVLYU": {"file": "chunk-5OZCVLYU.js"}, "chunk-MKYRKYXK": {"file": "chunk-MKYRKYXK.js"}, "chunk-K7WKLXQ3": {"file": "chunk-K7WKLXQ3.js"}, "chunk-CKPZGGGH": {"file": "chunk-CKPZGGGH.js"}, "chunk-OCAIBKXN": {"file": "chunk-OCAIBKXN.js"}, "chunk-TMGRVX2R": {"file": "chunk-TMGRVX2R.js"}, "chunk-WLN5YRVE": {"file": "chunk-WLN5YRVE.js"}, "chunk-VXCDF2R5": {"file": "chunk-VXCDF2R5.js"}, "chunk-G4GORMM2": {"file": "chunk-G4GORMM2.js"}, "chunk-6I5JPKR6": {"file": "chunk-6I5JPKR6.js"}, "chunk-AIZAQUPQ": {"file": "chunk-AIZAQUPQ.js"}, "chunk-BXIS6N7B": {"file": "chunk-BXIS6N7B.js"}, "chunk-UJHYEV4N": {"file": "chunk-UJHYEV4N.js"}, "chunk-76T5642S": {"file": "chunk-76T5642S.js"}, "chunk-G3PMV62Z": {"file": "chunk-G3PMV62Z.js"}}}