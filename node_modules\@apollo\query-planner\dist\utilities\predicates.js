"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isNotNullOrUndefined = exports.isObject = void 0;
function isObject(value) {
    return (value !== undefined &&
        value !== null &&
        typeof value === 'object' &&
        !Array.isArray(value));
}
exports.isObject = isObject;
function isNotNullOrUndefined(value) {
    return value !== null && typeof value !== 'undefined';
}
exports.isNotNullOrUndefined = isNotNullOrUndefined;
//# sourceMappingURL=predicates.js.map