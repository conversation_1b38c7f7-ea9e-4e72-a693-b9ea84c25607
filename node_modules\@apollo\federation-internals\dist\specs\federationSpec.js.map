{"version": 3, "file": "federationSpec.js", "sourceRoot": "", "sources": ["../../src/specs/federationSpec.ts"], "names": [], "mappings": ";;;AAAA,gDAIwB;AACxB,yCAA+F;AAC/F,oFAI0C;AAC1C,qCAA4C;AAC5C,oCAAkC;AAClC,uCAAyC;AACzC,8CAAmD;AACnD,4DAA4D;AAC5D,yDAA2D;AAC3D,2DAA6D;AAC7D,6DAAgE;AAChE,6CAA+C;AAC/C,+CAAiD;AACjD,yCAA2C;AAE9B,QAAA,kBAAkB,GAAG,qCAAqC,CAAC;AAExE,IAAY,kBAGX;AAHD,WAAY,kBAAkB;IAC5B,4CAAsB,CAAA;IACtB,+DAAyC,CAAA;AAC3C,CAAC,EAHW,kBAAkB,kCAAlB,kBAAkB,QAG7B;AAED,IAAY,uBAmBX;AAnBD,WAAY,uBAAuB;IACjC,sCAAW,CAAA;IACX,gDAAqB,CAAA;IACrB,gDAAqB,CAAA;IACrB,gDAAqB,CAAA;IACrB,8CAAmB,CAAA;IACnB,kDAAuB,CAAA;IACvB,gDAAqB,CAAA;IACrB,sCAAW,CAAA;IACX,wDAA6B,CAAA;IAC7B,iEAAsC,CAAA;IACtC,+DAAoC,CAAA;IACpC,0DAA+B,CAAA;IAC/B,6DAAkC,CAAA;IAClC,4CAAiB,CAAA;IACjB,8CAAmB,CAAA;IACnB,uDAA4B,CAAA;IAC5B,wCAAa,CAAA;IACb,iDAAsB,CAAA;AACxB,CAAC,EAnBW,uBAAuB,uCAAvB,uBAAuB,QAmBlC;AAED,MAAM,gBAAgB,GAAG,IAAA,6DAA6B,EAAC,EAAE,IAAI,EAAE,kBAAkB,CAAC,SAAS,EAAE,CAAC,CAAC;AAE/F,MAAM,cAAc,GAA0B,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC;AAEzG,MAAM,gBAAgB,GAAG,IAAA,4DAA4B,EAAC;IACpD,IAAI,EAAE,uBAAuB,CAAC,GAAG;IACjC,SAAS,EAAE,CAAC,2BAAiB,CAAC,MAAM,EAAE,2BAAiB,CAAC,SAAS,CAAC;IAClE,UAAU,EAAE,IAAI;IAChB,IAAI,EAAE;QACJ,cAAc;QACd,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,WAAW,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE;KACnF;CACF,CAAC,CAAC;AAEH,MAAM,oBAAoB,GAAG,IAAA,4DAA4B,EAAC;IACxD,IAAI,EAAE,uBAAuB,CAAC,OAAO;IACrC,SAAS,EAAE,CAAC,2BAAiB,CAAC,MAAM,EAAE,2BAAiB,CAAC,SAAS,CAAC;CACnE,CAAC,CAAC;AAEH,MAAM,qBAAqB,GAAG,IAAA,4DAA4B,EAAC;IACzD,IAAI,EAAE,uBAAuB,CAAC,QAAQ;IACtC,SAAS,EAAE,CAAC,2BAAiB,CAAC,MAAM,EAAE,2BAAiB,CAAC,gBAAgB,CAAC;IACzE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE,CAAC;CAClE,CAAC,CAAC;AAEH,MAAM,qBAAqB,GAAG,IAAA,4DAA4B,EAAC;IACzD,IAAI,EAAE,uBAAuB,CAAC,QAAQ;IACtC,SAAS,EAAE,CAAC,2BAAiB,CAAC,gBAAgB,CAAC;IAC/C,IAAI,EAAE,CAAC,cAAc,CAAC;CACvB,CAAC,CAAC;AAEH,MAAM,qBAAqB,GAAG,IAAA,4DAA4B,EAAC;IACzD,IAAI,EAAE,uBAAuB,CAAC,QAAQ;IACtC,SAAS,EAAE,CAAC,2BAAiB,CAAC,gBAAgB,CAAC;IAC/C,IAAI,EAAE,CAAC,cAAc,CAAC;CACvB,CAAC,CAAC;AAEH,MAAM,qBAAqB,GAAG;IAC5B,gBAAgB;CACjB,CAAC;AAEF,MAAM,0BAA0B,GAAG;IACjC,gBAAgB;IAChB,qBAAqB;IACrB,qBAAqB;IACrB,qBAAqB;IAGrB,sBAAY,CAAC,IAAI,CAAC,IAAI,yBAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAE,CAAC,gBAAgB;IAC7D,oBAAoB;CACrB,CAAC;AAEW,QAAA,iBAAiB,GAAG,qBAAqB,CAAC;AAC1C,QAAA,sBAAsB,GAAG,0BAA0B,CAAC;AAGjE,SAAS,YAAY,CAAC,MAAc;IAClC,MAAM,QAAQ,GAAG,IAAA,+BAAkB,EAAC,MAAM,CAAC,CAAC;IAC5C,IAAA,cAAM,EAAC,QAAQ,EAAE,yCAAyC,CAAC,CAAC;IAC5D,OAAO,IAAI,yBAAW,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC,CAAC;AAClD,CAAC;AAED,MAAa,wBAAyB,SAAQ,4BAAiB;IAC7D,YAAY,OAAuB;QACjC,KAAK,CAAC,IAAI,qBAAU,CAAC,0BAAkB,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC,CAAC;QAEjE,KAAK,MAAM,IAAI,IAAI,qBAAqB,EAAE,CAAC;YACzC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QAC1B,CAAC;QAED,KAAK,MAAM,SAAS,IAAI,0BAA0B,EAAE,CAAC;YACnD,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;QACpC,CAAC;QAED,IAAI,CAAC,iBAAiB,CAAC,IAAA,4DAA4B,EAAC;YAClD,IAAI,EAAE,uBAAuB,CAAC,SAAS;YACvC,SAAS,EAAE,CAAC,2BAAiB,CAAC,MAAM,EAAE,2BAAiB,CAAC,gBAAgB,CAAC;YACzE,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,IAAI,yBAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SAClD,CAAC,CAAC,CAAC;QAEJ,IAAI,CAAC,kBAAkB,CAAC,wCAAqB,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC,CAAC;QAElF,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,yBAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YAC1C,IAAI,CAAC,iBAAiB,CAAC,IAAA,4DAA4B,EAAC;gBAClD,IAAI,EAAE,uBAAuB,CAAC,QAAQ;gBACtC,SAAS,EAAE,CAAC,2BAAiB,CAAC,gBAAgB,CAAC;gBAC/C,IAAI,EAAE;oBACJ,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,yBAAW,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,EAAE;oBACxE,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE;iBACzD;aACF,CAAC,CAAC,CAAC;QACN,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,iBAAiB,CAAC,IAAA,4DAA4B,EAAC;gBAClD,IAAI,EAAE,uBAAuB,CAAC,QAAQ;gBACtC,SAAS,EAAE,CAAC,2BAAiB,CAAC,gBAAgB,CAAC;gBAC/C,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,yBAAW,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC;aACjF,CAAC,CAAC,CAAC;QACN,CAAC;QAED,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,yBAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YAC1C,IAAI,CAAC,iBAAiB,CAAC,IAAA,4DAA4B,EAAC;gBAClD,IAAI,EAAE,uBAAuB,CAAC,iBAAiB;gBAC/C,SAAS,EAAE,CAAC,2BAAiB,CAAC,MAAM,CAAC;gBACrC,UAAU,EAAE,IAAI;gBAChB,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE,CAAC;aAChE,CAAC,CAAC,CAAC;QACN,CAAC;QAED,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,yBAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YAC1C,IAAI,CAAC,iBAAiB,CAAC,IAAA,4DAA4B,EAAC;gBAClD,IAAI,EAAE,uBAAuB,CAAC,gBAAgB;gBAC9C,SAAS,EAAE,CAAC,2BAAiB,CAAC,MAAM,CAAC;aACtC,CAAC,CAAC,CAAC;YACJ,IAAI,CAAC,kBAAkB,CAAC,sBAAY,CAAC,IAAI,CAAC,IAAI,yBAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAE,CAAC,CAAC;QACxE,CAAC;QAED,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,yBAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YAC1C,IAAI,CAAC,kBAAkB,CAAC,0CAAsB,CAAC,IAAI,CAAC,IAAI,yBAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAE,CAAC,CAAC;YAChF,IAAI,CAAC,kBAAkB,CAAC,6CAAwB,CAAC,IAAI,CAAC,IAAI,yBAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAE,CAAC,CAAC;QACpF,CAAC;QAED,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,yBAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YAC1C,IAAI,CAAC,kBAAkB,CAAC,4BAAe,CAAC,IAAI,CAAC,IAAI,yBAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAE,CAAC,CAAC;QAC3E,CAAC;QAED,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,yBAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YAC1C,IAAI,CAAC,kBAAkB,CAAC,8BAAgB,CAAC,IAAI,CAAC,IAAI,yBAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAE,CAAC,CAAC;QAC5E,CAAC;QAED,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,yBAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YAC1C,IAAI,CAAC,kBAAkB,CAAC,wBAAa,CAAC,IAAI,CAAC,IAAI,yBAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAE,CAAC,CAAC;QACzE,CAAC;IACH,CAAC;CACF;AAvED,4DAuEC;AAEY,QAAA,mBAAmB,GAAG,IAAI,6BAAkB,CAA2B,0BAAkB,CAAC;KACpG,GAAG,CAAC,IAAI,wBAAwB,CAAC,IAAI,yBAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KAC3D,GAAG,CAAC,IAAI,wBAAwB,CAAC,IAAI,yBAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KAC3D,GAAG,CAAC,IAAI,wBAAwB,CAAC,IAAI,yBAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KAC3D,GAAG,CAAC,IAAI,wBAAwB,CAAC,IAAI,yBAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KAC3D,GAAG,CAAC,IAAI,wBAAwB,CAAC,IAAI,yBAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KAC3D,GAAG,CAAC,IAAI,wBAAwB,CAAC,IAAI,yBAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KAC3D,GAAG,CAAC,IAAI,wBAAwB,CAAC,IAAI,yBAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KAC3D,GAAG,CAAC,IAAI,wBAAwB,CAAC,IAAI,yBAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KAC3D,GAAG,CAAC,IAAI,wBAAwB,CAAC,IAAI,yBAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KAC3D,GAAG,CAAC,IAAI,wBAAwB,CAAC,IAAI,yBAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KAC3D,GAAG,CAAC,IAAI,wBAAwB,CAAC,IAAI,yBAAc,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;KAC5D,GAAG,CAAC,IAAI,wBAAwB,CAAC,IAAI,yBAAc,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AAEhE,IAAA,wCAAoB,EAAC,2BAAmB,CAAC,CAAC"}