import { NonEmptyArray, SelectionSet, Variable, VariableDefinitions } from "@apollo/federation-internals";
import { ConditionNode } from ".";
export type VariableCondition = {
    variable: Variable;
    negated: boolean;
};
export type Condition = VariableCondition | boolean;
export type Conditions = NonEmptyArray<VariableCondition> | boolean;
export declare function isConstantCondition(cond: Condition | Conditions): cond is boolean;
export declare function mergeConditions(conditions1: Conditions, conditions2: Conditions): Conditions;
export declare function conditionsOfSelectionSet(selectionSet: SelectionSet): Conditions;
export declare function updatedConditions(newConditions: Conditions, handledConditions: Conditions): Conditions;
export declare function removeConditionsFromSelectionSet(selectionSet: SelectionSet, conditions: Conditions): SelectionSet;
export declare function evaluateCondition(condition: ConditionNode, variables: VariableDefinitions, values: Record<string, any> | undefined): boolean;
//# sourceMappingURL=conditions.d.ts.map