{"version": 3, "file": "coreDirectiveCollector.js", "sourceRoot": "", "sources": ["../../src/merging/coreDirectiveCollector.ts"], "names": [], "mappings": ";;;AAAA,uEAAyL;AASzL,SAAgB,8BAA8B,CAC5C,SAAoB;IAIpB,MAAM,8BAA8B,GAAG,IAAI,GAAG,EAA0E,CAAC;IAEzH,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;QACjC,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC;QAC9C,IAAA,6BAAM,EAAC,QAAQ,EAAE,kCAAkC,CAAC,CAAC;QACrD,KAAK,MAAM,SAAS,IAAI,QAAQ,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE,CAAC;YACrD,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;YAGjD,IAAI,CAAC,MAAM,IAAI,SAAS,CAAC,YAAY,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;gBACnD,SAAS;YACX,CAAC;YAED,MAAM,GAAG,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC;YAC/B,MAAM,GAAG,GAAG,GAAG,MAAM,CAAC,aAAa,IAAI,GAAG,CAAC,QAAQ,EAAE,CAAA;YACrD,IAAI,UAAU,GAAG,8BAA8B,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACzD,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,UAAU,GAAG,IAAI,GAAG,EAAE,CAAC;gBACvB,8BAA8B,CAAC,GAAG,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;YACtD,CAAC;YAED,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC;YAC7E,IAAI,UAAU,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YACvC,IAAI,UAAU,EAAE,CAAC;gBAEf,IAAI,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;oBACtD,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC;gBACvB,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,UAAU,GAAG;oBACX,GAAG;oBACH,IAAI,EAAE,MAAM,CAAC,aAAa;oBAC1B,sBAAsB,EAAE,IAAI,GAAG,EAAE;iBAClC,CAAA;gBACD,UAAU,CAAC,GAAG,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;YACpC,CAAC;YACD,UAAU,CAAC,sBAAsB,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;IAED,OAAO,IAAA,gCAAS,EAAC,8BAA8B,CAAC;SAC7C,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAA,gCAAS,EAAC,UAAU,CAAC,CAAC;SAC9C,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;QACT,MAAM,iBAAiB,GAAG,IAAA,mDAA4B,EAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC9D,MAAM,eAAe,GAAG,iBAAiB,aAAjB,iBAAiB,uBAAjB,iBAAiB,CAAE,wBAAwB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAC5E,IAAI,CAAC,eAAe,EAAE,CAAC;YACrB,OAAO,SAAS,CAAC;QACnB,CAAC;QACD,OAAO;YACL,GAAG,CAAC;YACJ,eAAe;SAChB,CAAC;IACJ,CAAC,CAAC;SACD,MAAM,CAAC,gCAAS,CAAC,CAAC;AACvB,CAAC;AA3DD,wEA2DC"}