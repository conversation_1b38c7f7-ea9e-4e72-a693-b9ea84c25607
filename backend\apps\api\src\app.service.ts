import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class AppService {
  constructor(private readonly configService: ConfigService) {}

  getApplicationInfo() {
    const environment = this.configService.get<string>('NODE_ENV', 'development');
    const port = this.configService.get<number>('API_PORT', 3002);
    
    return {
      name: 'PeopleNest HRMS API',
      version: '1.0.0',
      description: 'Human Resource Management System API',
      environment,
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      endpoints: {
        health: `/api/v1/health`,
        docs: environment === 'development' ? `/api/docs` : null,
        api: `/api/v1`,
      },
      features: [
        'Employee Management',
        'Payroll Processing',
        'Performance Management',
        'Time & Attendance',
        'Benefits Administration',
        'Reporting & Analytics',
        'Multi-tenant Support',
        'Role-based Access Control',
      ],
    };
  }

  getVersion() {
    return {
      version: '1.0.0',
      buildDate: new Date().toISOString(),
      nodeVersion: process.version,
      environment: this.configService.get<string>('NODE_ENV', 'development'),
    };
  }
}
