"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocalCompose = void 0;
const composition_1 = require("@apollo/composition");
class LocalCompose {
    constructor(options) {
        this.config = options;
        this.issueDeprecationWarnings();
    }
    issueDeprecationWarnings() {
        var _a;
        (_a = this.config.logger) === null || _a === void 0 ? void 0 : _a.warn('The `localServiceList` option is deprecated and will be removed in a future version of `@apollo/gateway`. Please migrate to the `LocalCompose` supergraph manager exported by `@apollo/gateway`.');
    }
    async initialize({ getDataSource }) {
        this.getDataSource = getDataSource;
        let supergraphSdl = null;
        try {
            supergraphSdl = this.createSupergraphFromServiceList(this.config.localServiceList);
        }
        catch (e) {
            this.logUpdateFailure(e);
            throw e;
        }
        return {
            supergraphSdl,
        };
    }
    createSupergraphFromServiceList(serviceList) {
        var _a, _b, _c;
        (_a = this.config.logger) === null || _a === void 0 ? void 0 : _a.debug(`Composing schema from service list: \n${serviceList
            .map(({ name, url }) => `  ${url || 'local'}: ${name}`)
            .join('\n')}`);
        const compositionResult = (0, composition_1.composeServices)(serviceList);
        const errors = compositionResult.errors;
        if (errors) {
            throw Error("A valid schema couldn't be composed. The following composition errors were found:\n" +
                errors.map((e) => '\t' + e.message).join('\n'));
        }
        else {
            const { supergraphSdl } = compositionResult;
            for (const service of serviceList) {
                (_b = this.getDataSource) === null || _b === void 0 ? void 0 : _b.call(this, service);
            }
            (_c = this.config.logger) === null || _c === void 0 ? void 0 : _c.debug('Schema loaded and ready for execution');
            return supergraphSdl;
        }
    }
    logUpdateFailure(e) {
        var _a, _b;
        (_a = this.config.logger) === null || _a === void 0 ? void 0 : _a.error('LocalCompose failed to update supergraph with the following error: ' +
            ((_b = e.message) !== null && _b !== void 0 ? _b : e));
    }
}
exports.LocalCompose = LocalCompose;
//# sourceMappingURL=index.js.map