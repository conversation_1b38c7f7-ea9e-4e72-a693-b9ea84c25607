import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';

/**
 * Decorator to extract the current tenant ID from the request
 * 
 * @example
 * ```typescript
 * @Get('data')
 * getData(@CurrentTenant() tenantId: string) {
 *   return this.service.getDataForTenant(tenantId);
 * }
 * ```
 */
export const CurrentTenant = createParamDecorator(
  (data: unknown, context: ExecutionContext) => {
    // Handle HTTP requests
    if (context.getType() === 'http') {
      const request = context.switchToHttp().getRequest();
      return request.user?.tenantId;
    }

    // Handle GraphQL requests
    if (context.getType<any>() === 'graphql') {
      const ctx = GqlExecutionContext.create(context);
      const request = ctx.getContext().req;
      return request.user?.tenantId;
    }

    // Handle other request types
    const request = context.switchToHttp().getRequest();
    return request.user?.tenantId;
  },
);
