import { CorePurpose, FeatureDefinition, FeatureDefinitions, FeatureVersion } from "./coreSpec";
import { DirectiveDefinition, Schema } from "../definitions";
import { GraphQLError, DirectiveLocation } from "graphql";
import { DirectiveSpecification } from "../directiveAndTypeSpecification";
export declare const inaccessibleIdentity = "https://specs.apollo.dev/inaccessible";
export declare class InaccessibleSpecDefinition extends FeatureDefinition {
    readonly inaccessibleLocations: DirectiveLocation[];
    readonly inaccessibleDirectiveSpec: DirectiveSpecification;
    private readonly printedInaccessibleDefinition;
    constructor(version: FeatureVersion, minimumFederationVersion?: FeatureVersion);
    isV01(): boolean;
    inaccessibleDirective(schema: Schema): DirectiveDefinition<Record<string, never>> | undefined;
    checkCompatibleDirective(definition: DirectiveDefinition): GraphQLError | undefined;
    get defaultCorePurpose(): CorePurpose | undefined;
}
export declare const INACCESSIBLE_VERSIONS: FeatureDefinitions<InaccessibleSpecDefinition>;
export declare function removeInaccessibleElements(schema: Schema): void;
//# sourceMappingURL=inaccessibleSpec.d.ts.map