import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class DepartmentResponseDto {
  @ApiProperty({ description: 'Department UUID' })
  id: string;

  @ApiProperty({ description: 'Department name' })
  name: string;

  @ApiProperty({ description: 'Department code' })
  code: string;

  @ApiPropertyOptional({ description: 'Department description' })
  description?: string;

  @ApiPropertyOptional({ description: 'Parent department information' })
  parent?: {
    id: string;
    name: string;
    code: string;
  };

  @ApiPropertyOptional({ description: 'Department head information' })
  head?: {
    id: string;
    fullName: string;
    employeeId: string;
  };

  @ApiPropertyOptional({ description: 'Cost center code' })
  costCenter?: string;

  @ApiPropertyOptional({ description: 'Budget amount' })
  budget?: number;

  @ApiPropertyOptional({ description: 'Location information' })
  location?: string;

  @ApiProperty({ description: 'Employee count' })
  employeeCount: number;

  @ApiProperty({ description: 'Position count' })
  positionCount: number;

  @ApiProperty({ description: 'Active status' })
  isActive: boolean;

  @ApiProperty({ description: 'Creation date' })
  createdAt: string;

  @ApiProperty({ description: 'Last update date' })
  updatedAt: string;

  @ApiPropertyOptional({ description: 'Additional metadata' })
  metadata?: Record<string, any>;
}

export class PaginatedDepartmentResponseDto {
  @ApiProperty({ description: 'Department data', type: [DepartmentResponseDto] })
  data: DepartmentResponseDto[];

  @ApiProperty({ description: 'Pagination metadata' })
  meta: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}
