# =============================================================================
# PeopleNest HRMS - Service Status Check Script (PowerShell)
# =============================================================================
# This script checks the status of all PeopleNest HRMS services
# =============================================================================

param(
    [switch]$Detailed,
    [switch]$Json,
    [switch]$Watch
)

# Colors for output
$Green = "Green"
$Red = "Red"
$Yellow = "Yellow"
$Blue = "Blue"
$Cyan = "Cyan"

function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    if (-not $Json) {
        Write-Host $Message -ForegroundColor $Color
    }
}

function Write-Header {
    param([string]$Title)
    if (-not $Json) {
        Write-Host ""
        Write-ColorOutput ("=" * 80) $Blue
        Write-ColorOutput "  $Title" $Cyan
        Write-ColorOutput ("=" * 80) $Blue
        Write-Host ""
    }
}

function Test-ServiceHealth {
    param([string]$ServiceName, [string]$Url, [int]$TimeoutSeconds = 5)
    
    try {
        $response = Invoke-WebRequest -Uri $Url -TimeoutSec $TimeoutSeconds -UseBasicParsing -ErrorAction SilentlyContinue
        return @{
            Name = $ServiceName
            Status = "Running"
            StatusCode = $response.StatusCode
            Url = $Url
            Healthy = $true
        }
    }
    catch {
        return @{
            Name = $ServiceName
            Status = "Down"
            StatusCode = $null
            Url = $Url
            Healthy = $false
            Error = $_.Exception.Message
        }
    }
}

function Test-DatabaseConnection {
    param([string]$ServiceName, [string]$HostName, [int]$Port)

    try {
        $connection = Test-NetConnection -ComputerName $HostName -Port $Port -WarningAction SilentlyContinue
        return @{
            Name = $ServiceName
            Status = if ($connection.TcpTestSucceeded) { "Running" } else { "Down" }
            Host = $HostName
            Port = $Port
            Healthy = $connection.TcpTestSucceeded
        }
    }
    catch {
        return @{
            Name = $ServiceName
            Status = "Down"
            Host = $HostName
            Port = $Port
            Healthy = $false
            Error = $_.Exception.Message
        }
    }
}

function Get-ServiceStatus {
    $services = @()
    
    # Application Services
    $services += Test-ServiceHealth -ServiceName "Frontend (React)" -Url "http://localhost:3000"
    $services += Test-ServiceHealth -ServiceName "Backend (NestJS)" -Url "http://localhost:4000/health"
    $services += Test-ServiceHealth -ServiceName "AI Services (FastAPI)" -Url "http://localhost:8000/health"
    
    # Infrastructure Services (HTTP)
    $services += Test-ServiceHealth -ServiceName "Elasticsearch" -Url "http://localhost:9200"
    $services += Test-ServiceHealth -ServiceName "Kibana" -Url "http://localhost:5601"
    $services += Test-ServiceHealth -ServiceName "MinIO" -Url "http://localhost:9000"
    $services += Test-ServiceHealth -ServiceName "Grafana" -Url "http://localhost:3001"
    $services += Test-ServiceHealth -ServiceName "Prometheus" -Url "http://localhost:9090"
    $services += Test-ServiceHealth -ServiceName "Jaeger" -Url "http://localhost:16686"
    
    # Database Services (TCP)
    $services += Test-DatabaseConnection -ServiceName "PostgreSQL" -HostName "localhost" -Port 5432
    $services += Test-DatabaseConnection -ServiceName "Redis" -HostName "localhost" -Port 6379
    $services += Test-DatabaseConnection -ServiceName "MongoDB" -HostName "localhost" -Port 27017
    $services += Test-DatabaseConnection -ServiceName "Kafka" -HostName "localhost" -Port 9092
    
    return $services
}

function Get-DockerStatus {
    try {
        $containers = docker ps --format "{{.Names}},{{.Status}},{{.Ports}}" | Where-Object { $_ -like "*peoplenest*" }
        $dockerServices = @()
        
        foreach ($container in $containers) {
            $parts = $container -split ","
            $dockerServices += @{
                Name = $parts[0]
                Status = $parts[1]
                Ports = $parts[2]
            }
        }
        
        return $dockerServices
    }
    catch {
        return @()
    }
}

function Get-ProcessStatus {
    $processes = @()
    
    # Check Node.js processes
    try {
        $nodeProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue
        foreach ($process in $nodeProcesses) {
            $commandLine = (Get-WmiObject Win32_Process -Filter "ProcessId = $($process.Id)").CommandLine
            if ($commandLine -and ($commandLine -like "*peoplenest*" -or $commandLine -like "*start:dev*" -or $commandLine -like "*npm run dev*")) {
                $processes += @{
                    Type = "Node.js"
                    PID = $process.Id
                    Name = $process.ProcessName
                    Command = $commandLine
                }
            }
        }
    }
    catch { }
    
    # Check Python processes
    try {
        $pythonProcesses = Get-Process -Name "python*" -ErrorAction SilentlyContinue
        foreach ($process in $pythonProcesses) {
            $commandLine = (Get-WmiObject Win32_Process -Filter "ProcessId = $($process.Id)").CommandLine
            if ($commandLine -and ($commandLine -like "*uvicorn*" -or $commandLine -like "*ai-services*")) {
                $processes += @{
                    Type = "Python"
                    PID = $process.Id
                    Name = $process.ProcessName
                    Command = $commandLine
                }
            }
        }
    }
    catch { }
    
    return $processes
}

function Show-StatusTable {
    param([array]$Services)
    
    Write-Header "PeopleNest HRMS - Service Status"
    
    $runningCount = ($Services | Where-Object { $_.Healthy }).Count
    $totalCount = $Services.Count
    
    Write-ColorOutput "📊 Overall Status: $runningCount/$totalCount services running" $(if ($runningCount -eq $totalCount) { $Green } else { $Yellow })
    Write-Host ""
    
    # Application Services
    Write-ColorOutput "🚀 Application Services:" $Blue
    $appServices = $Services | Where-Object { $_.Name -like "*React*" -or $_.Name -like "*NestJS*" -or $_.Name -like "*FastAPI*" }
    foreach ($service in $appServices) {
        $status = if ($service.Healthy) { "✅ Running" } else { "❌ Down" }
        $color = if ($service.Healthy) { $Green } else { $Red }
        
        if ($service.Url) {
            Write-ColorOutput ("  {0,-25} {1,-15} {2}" -f $service.Name, $status, $service.Url) $color
        } else {
            Write-ColorOutput ("  {0,-25} {1,-15} {2}:{3}" -f $service.Name, $status, $service.Host, $service.Port) $color
        }
        
        if ($Detailed -and $service.Error) {
            Write-ColorOutput ("    Error: {0}" -f $service.Error) $Red
        }
    }
    
    Write-Host ""
    
    # Infrastructure Services
    Write-ColorOutput "🏗️  Infrastructure Services:" $Blue
    $infraServices = $Services | Where-Object { $_.Name -notlike "*React*" -and $_.Name -notlike "*NestJS*" -and $_.Name -notlike "*FastAPI*" }
    foreach ($service in $infraServices) {
        $status = if ($service.Healthy) { "✅ Running" } else { "❌ Down" }
        $color = if ($service.Healthy) { $Green } else { $Red }
        
        if ($service.Url) {
            Write-ColorOutput ("  {0,-25} {1,-15} {2}" -f $service.Name, $status, $service.Url) $color
        } else {
            Write-ColorOutput ("  {0,-25} {1,-15} {2}:{3}" -f $service.Name, $status, $service.Host, $service.Port) $color
        }
        
        if ($Detailed -and $service.Error) {
            Write-ColorOutput ("    Error: {0}" -f $service.Error) $Red
        }
    }
    
    if ($Detailed) {
        # Show Docker containers
        Write-Host ""
        Write-ColorOutput "🐳 Docker Containers:" $Blue
        $dockerServices = Get-DockerStatus
        if ($dockerServices.Count -gt 0) {
            foreach ($container in $dockerServices) {
                Write-ColorOutput ("  {0,-25} {1}" -f $container.Name, $container.Status) $Cyan
                if ($container.Ports) {
                    Write-ColorOutput ("    Ports: {0}" -f $container.Ports) $Yellow
                }
            }
        } else {
            Write-ColorOutput "  No PeopleNest containers running" $Yellow
        }
        
        # Show processes
        Write-Host ""
        Write-ColorOutput "⚙️  Application Processes:" $Blue
        $processes = Get-ProcessStatus
        if ($processes.Count -gt 0) {
            foreach ($process in $processes) {
                $processType = $process.Type
                $processPID = $process.PID
                $processInfo = "  $processType (Process ID: $processPID)"
                Write-ColorOutput $processInfo $Cyan
                if ($process.Command -and $process.Command.Length -gt 80) {
                    $commandText = $process.Command.Substring(0, 77)
                    $commandInfo = "    $commandText..."
                    Write-ColorOutput $commandInfo $Yellow
                } elseif ($process.Command) {
                    $commandText = $process.Command
                    $commandInfo = "    $commandText"
                    Write-ColorOutput $commandInfo $Yellow
                }
            }
        } else {
            Write-ColorOutput "  No application processes found" $Yellow
        }
    }
    
    Write-Host ""
    Write-ColorOutput "🔑 Default Admin Credentials:" $Yellow
    Write-ColorOutput "   Username: awadhesh" $Cyan
    Write-ColorOutput "   Password: awadhesh123" $Cyan
    Write-ColorOutput "   Email: <EMAIL>" $Cyan
    
    Write-Host ""
    Write-ColorOutput "⏰ Last checked: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" $Yellow
}

function Show-JsonOutput {
    param([array]$Services)
    
    $output = @{
        timestamp = Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ"
        summary = @{
            total_services = $Services.Count
            running_services = ($Services | Where-Object { $_.Healthy }).Count
            down_services = ($Services | Where-Object { -not $_.Healthy }).Count
        }
        services = $Services
        docker_containers = Get-DockerStatus
        processes = Get-ProcessStatus
    }
    
    $output | ConvertTo-Json -Depth 10
}

# Main execution
do {
    if ($Json) {
        Clear-Host
    }
    
    $services = Get-ServiceStatus
    
    if ($Json) {
        Show-JsonOutput -Services $services
    } else {
        if ($Watch) {
            Clear-Host
        }
        Show-StatusTable -Services $services
    }
    
    if ($Watch) {
        Write-ColorOutput "`n🔄 Refreshing in 5 seconds... (Press Ctrl+C to stop)" $Yellow
        Start-Sleep -Seconds 5
    }
} while ($Watch)
