import type { Logger } from '@apollo/utils.logger';
import type { Fetcher } from '@apollo/utils.fetcher';
import { SupergraphManager, SupergraphSdlHookOptions } from '../../config';
export type FailureToFetchSupergraphSdlFunctionParams = {
    error: Error;
    graphRef: string;
    logger: Logger;
    fetchCount: number;
};
export type FailureToFetchSupergraphSdlDuringInit = ({ error, graphRef, logger, fetchCount, }: FailureToFetchSupergraphSdlFunctionParams) => Promise<string>;
export type FailureToFetchSupergraphSdlAfterInit = ({ error, graphRef, logger, fetchCount, mostRecentSuccessfulFetchAt, }: FailureToFetchSupergraphSdlFunctionParams & {
    mostRecentSuccessfulFetchAt?: Date;
}) => Promise<string | null>;
export declare class UplinkSupergraphManager implements SupergraphManager {
    static readonly DEFAULT_REQUEST_TIMEOUT_MS = 30000;
    static readonly MIN_POLL_INTERVAL_MS = 10000;
    static readonly DEFAULT_UPLINK_ENDPOINTS: string[];
    readonly uplinkEndpoints: string[];
    private apiKey;
    private graphRef;
    private fetcher;
    private maxRetries;
    private requestTimeoutMs;
    private initialMaxRetries;
    private pollIntervalMs;
    private fallbackPollIntervalInMs?;
    private logger;
    private update?;
    private shouldRunSubgraphHealthcheck;
    private healthCheck?;
    private onFailureToFetchSupergraphSdlDuringInit?;
    private onFailureToFetchSupergraphSdlAfterInit?;
    private timerRef;
    private state;
    private compositionId?;
    private fetchCount;
    private mostRecentSuccessfulFetchAt?;
    constructor({ apiKey, graphRef, debug, logger, uplinkEndpoints, fallbackPollIntervalInMs, maxRetries, initialMaxRetries, fetcher, shouldRunSubgraphHealthcheck, onFailureToFetchSupergraphSdlDuringInit, onFailureToFetchSupergraphSdlAfterInit, }: {
        apiKey: string;
        graphRef: string;
        debug?: boolean;
        logger?: Logger;
        uplinkEndpoints?: string[];
        fallbackPollIntervalInMs?: number;
        maxRetries?: number;
        initialMaxRetries?: number;
        fetcher?: Fetcher;
        shouldRunSubgraphHealthcheck?: boolean;
        onFailureToFetchSupergraphSdlDuringInit?: FailureToFetchSupergraphSdlDuringInit;
        onFailureToFetchSupergraphSdlAfterInit?: FailureToFetchSupergraphSdlAfterInit;
    });
    initialize({ update, healthCheck }: SupergraphSdlHookOptions): Promise<{
        supergraphSdl: string;
        cleanup: () => Promise<void>;
    }>;
    nextFetch(): Promise<void | null>;
    static getUplinkEndpoints(): string[];
    private updateSupergraphSdl;
    private beginPolling;
    private poll;
    private logUpdateFailure;
}
//# sourceMappingURL=index.d.ts.map