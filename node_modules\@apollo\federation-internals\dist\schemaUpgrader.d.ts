import { GraphQLError } from "graphql";
import { Subgraphs } from "./federation";
import { MultiMap } from "./utils";
export type UpgradeResult = UpgradeSuccess | UpgradeFailure;
type UpgradeChanges = MultiMap<UpgradeChangeID, UpgradeChange>;
export type UpgradeSuccess = {
    subgraphs: Subgraphs;
    changes: Map<string, UpgradeChanges>;
    errors?: never;
};
export type UpgradeFailure = {
    subgraphs?: never;
    changes?: never;
    errors: GraphQLError[];
};
export type UpgradeChangeID = UpgradeChange['id'];
export type UpgradeChange = ExternalOnTypeExtensionRemoval | TypeExtensionRemoval | UnusedExternalRemoval | TypeWithOnlyUnusedExternalRemoval | ExternalOnInterfaceRemoval | ExternalOnObjectTypeRemoval | InactiveProvidesOrRequiresRemoval | InactiveProvidesOrRequiresFieldsRemoval | ShareableFieldAddition | ShareableTypeAddition | KeyOnInterfaceRemoval | ProvidesOrRequiresOnInterfaceFieldRemoval | ProvidesOnNonCompositeRemoval | FieldsArgumentCoercionToString | RemovedTagOnExternal;
export declare class ExternalOnTypeExtensionRemoval {
    readonly field: string;
    readonly id: "EXTERNAL_ON_TYPE_EXTENSION_REMOVAL";
    constructor(field: string);
    toString(): string;
}
export declare class TypeExtensionRemoval {
    readonly type: string;
    readonly id: "TYPE_EXTENSION_REMOVAL";
    constructor(type: string);
    toString(): string;
}
export declare class ExternalOnInterfaceRemoval {
    readonly field: string;
    readonly id: "EXTERNAL_ON_INTERFACE_REMOVAL";
    constructor(field: string);
    toString(): string;
}
export declare class ExternalOnObjectTypeRemoval {
    readonly type: string;
    readonly id: "EXTERNAL_ON_OBJECT_TYPE_REMOVAL";
    constructor(type: string);
    toString(): string;
}
export declare class UnusedExternalRemoval {
    readonly field: string;
    readonly id: "UNUSED_EXTERNAL_REMOVAL";
    constructor(field: string);
    toString(): string;
}
export declare class TypeWithOnlyUnusedExternalRemoval {
    readonly type: string;
    readonly id: "TYPE_WITH_ONLY_UNUSED_EXTERNAL_REMOVAL";
    constructor(type: string);
    toString(): string;
}
export declare class InactiveProvidesOrRequiresRemoval {
    readonly parent: string;
    readonly removed: string;
    readonly id: "INACTIVE_PROVIDES_OR_REQUIRES_REMOVAL";
    constructor(parent: string, removed: string);
    toString(): string;
}
export declare class InactiveProvidesOrRequiresFieldsRemoval {
    readonly parent: string;
    readonly original: string;
    readonly updated: string;
    readonly id: "INACTIVE_PROVIDES_OR_REQUIRES_FIELDS_REMOVAL";
    constructor(parent: string, original: string, updated: string);
    toString(): string;
}
export declare class ShareableFieldAddition {
    readonly field: string;
    readonly declaringSubgraphs: string[];
    readonly id: "SHAREABLE_FIELD_ADDITION";
    constructor(field: string, declaringSubgraphs: string[]);
    toString(): string;
}
export declare class ShareableTypeAddition {
    readonly type: string;
    readonly declaringSubgraphs: string[];
    readonly id: "SHAREABLE_TYPE_ADDITION";
    constructor(type: string, declaringSubgraphs: string[]);
    toString(): string;
}
export declare class KeyOnInterfaceRemoval {
    readonly type: string;
    readonly id: "KEY_ON_INTERFACE_REMOVAL";
    constructor(type: string);
    toString(): string;
}
export declare class ProvidesOrRequiresOnInterfaceFieldRemoval {
    readonly field: string;
    readonly directive: string;
    readonly id: "PROVIDES_OR_REQUIRES_ON_INTERFACE_FIELD_REMOVAL";
    constructor(field: string, directive: string);
    toString(): string;
}
export declare class ProvidesOnNonCompositeRemoval {
    readonly field: string;
    readonly type: string;
    readonly id: "PROVIDES_ON_NON_COMPOSITE_REMOVAL";
    constructor(field: string, type: string);
    toString(): string;
}
export declare class FieldsArgumentCoercionToString {
    readonly element: string;
    readonly directive: string;
    readonly before: string;
    readonly after: string;
    readonly id: "FIELDS_ARGUMENT_COERCION_TO_STRING";
    constructor(element: string, directive: string, before: string, after: string);
    toString(): string;
}
export declare class RemovedTagOnExternal {
    readonly application: string;
    readonly element: string;
    readonly id: "REMOVED_TAG_ON_EXTERNAL";
    constructor(application: string, element: string);
    toString(): string;
}
export declare function upgradeSubgraphsIfNecessary(inputs: Subgraphs): UpgradeResult;
export {};
//# sourceMappingURL=schemaUpgrader.d.ts.map