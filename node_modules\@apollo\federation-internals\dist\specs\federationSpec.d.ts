import { FeatureDefinition, FeatureDefinitions, FeatureVersion } from "./coreSpec";
export declare const federationIdentity = "https://specs.apollo.dev/federation";
export declare enum FederationTypeName {
    FIELD_SET = "FieldSet",
    CONTEXT_FIELD_VALUE = "ContextFieldValue"
}
export declare enum FederationDirectiveName {
    KEY = "key",
    EXTERNAL = "external",
    REQUIRES = "requires",
    PROVIDES = "provides",
    EXTENDS = "extends",
    SHAREABLE = "shareable",
    OVERRIDE = "override",
    TAG = "tag",
    INACCESSIBLE = "inaccessible",
    COMPOSE_DIRECTIVE = "composeDirective",
    INTERFACE_OBJECT = "interfaceObject",
    AUTHENTICATED = "authenticated",
    REQUIRES_SCOPES = "requiresScopes",
    POLICY = "policy",
    CONTEXT = "context",
    FROM_CONTEXT = "fromContext",
    COST = "cost",
    LIST_SIZE = "listSize"
}
export declare const FEDERATION1_TYPES: import("../directiveAndTypeSpecification").TypeSpecification[];
export declare const FEDERATION1_DIRECTIVES: import("../directiveAndTypeSpecification").DirectiveSpecification[];
export declare class FederationSpecDefinition extends FeatureDefinition {
    constructor(version: FeatureVersion);
}
export declare const FEDERATION_VERSIONS: FeatureDefinitions<FederationSpecDefinition>;
//# sourceMappingURL=federationSpec.d.ts.map