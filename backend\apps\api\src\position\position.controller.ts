import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  ParseUUIDPipe,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
} from '@nestjs/swagger';

import { JwtAuthGuard, RolesGuard, TenantGuard } from '@app/security';
import { Roles, CurrentUser, CurrentTenant } from '@app/common';
import { PositionService } from './services/position.service';
import { CreatePositionDto, UpdatePositionDto, PositionQueryDto } from './dto';

@ApiTags('Positions')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard, TenantGuard, RolesGuard)
@Controller('positions')
export class PositionController {
  constructor(private readonly positionService: PositionService) {}

  @Post()
  @Roles('admin', 'hr_manager')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ 
    summary: 'Create new position',
    description: 'Create a new position in the organization'
  })
  @ApiResponse({ 
    status: 201, 
    description: 'Position created successfully'
  })
  @ApiResponse({ 
    status: 400, 
    description: 'Invalid input data'
  })
  @ApiResponse({ 
    status: 403, 
    description: 'Insufficient permissions'
  })
  async create(
    @Body() createPositionDto: CreatePositionDto,
    @CurrentUser() user: any,
    @CurrentTenant() tenantId: string,
  ) {
    return this.positionService.create(createPositionDto, user.id, tenantId);
  }

  @Get()
  @Roles('admin', 'hr_manager', 'hr_user', 'manager', 'employee')
  @ApiOperation({ 
    summary: 'Get all positions',
    description: 'Retrieve a list of all positions with optional filtering'
  })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'departmentId', required: false, type: String })
  @ApiQuery({ name: 'level', required: false, type: String })
  @ApiQuery({ name: 'includeInactive', required: false, type: Boolean })
  @ApiResponse({ 
    status: 200, 
    description: 'Positions retrieved successfully'
  })
  async findAll(
    @Query() query: PositionQueryDto,
    @CurrentTenant() tenantId: string,
  ) {
    return this.positionService.findAll(query, tenantId);
  }

  @Get(':id')
  @Roles('admin', 'hr_manager', 'hr_user', 'manager', 'employee')
  @ApiOperation({ 
    summary: 'Get position by ID',
    description: 'Retrieve detailed information about a specific position'
  })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiResponse({ 
    status: 200, 
    description: 'Position found successfully'
  })
  @ApiResponse({ 
    status: 404, 
    description: 'Position not found'
  })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentTenant() tenantId: string,
  ) {
    return this.positionService.findOne(id, tenantId);
  }

  @Put(':id')
  @Roles('admin', 'hr_manager')
  @ApiOperation({ 
    summary: 'Update position',
    description: 'Update position information'
  })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiResponse({ 
    status: 200, 
    description: 'Position updated successfully'
  })
  @ApiResponse({ 
    status: 404, 
    description: 'Position not found'
  })
  @ApiResponse({ 
    status: 403, 
    description: 'Insufficient permissions'
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updatePositionDto: UpdatePositionDto,
    @CurrentUser() user: any,
    @CurrentTenant() tenantId: string,
  ) {
    return this.positionService.update(id, updatePositionDto, user.id, tenantId);
  }

  @Delete(':id')
  @Roles('admin', 'hr_manager')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ 
    summary: 'Delete position',
    description: 'Soft delete a position'
  })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiResponse({ 
    status: 204, 
    description: 'Position deleted successfully'
  })
  @ApiResponse({ 
    status: 404, 
    description: 'Position not found'
  })
  @ApiResponse({ 
    status: 403, 
    description: 'Insufficient permissions'
  })
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() user: any,
    @CurrentTenant() tenantId: string,
  ) {
    return this.positionService.remove(id, user.id, tenantId);
  }

  @Get(':id/employees')
  @Roles('admin', 'hr_manager', 'hr_user', 'manager')
  @ApiOperation({ 
    summary: 'Get position employees',
    description: 'Retrieve all employees in a specific position'
  })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiResponse({ 
    status: 200, 
    description: 'Position employees retrieved successfully'
  })
  async getPositionEmployees(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentTenant() tenantId: string,
  ) {
    return this.positionService.getPositionEmployees(id, tenantId);
  }

  @Get('department/:departmentId')
  @Roles('admin', 'hr_manager', 'hr_user', 'manager')
  @ApiOperation({ 
    summary: 'Get positions by department',
    description: 'Retrieve all positions in a specific department'
  })
  @ApiParam({ name: 'departmentId', type: 'string', format: 'uuid' })
  @ApiResponse({ 
    status: 200, 
    description: 'Department positions retrieved successfully'
  })
  async getPositionsByDepartment(
    @Param('departmentId', ParseUUIDPipe) departmentId: string,
    @CurrentTenant() tenantId: string,
  ) {
    return this.positionService.getPositionsByDepartment(departmentId, tenantId);
  }
}
