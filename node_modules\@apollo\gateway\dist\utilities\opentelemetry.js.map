{"version": 3, "file": "opentelemetry.js", "sourceRoot": "", "sources": ["../../src/utilities/opentelemetry.ts"], "names": [], "mappings": ";;;;;;AAAA,6DAA+C;AA4B/C,IAAY,sBAOX;AAPD,WAAY,sBAAsB;IAChC,qDAA2B,CAAA;IAC3B,+CAAqB,CAAA;IACrB,iDAAuB,CAAA;IACvB,oEAA0C,CAAA;IAC1C,qDAA2B,CAAA;IAC3B,uDAA6B,CAAA;AAC/B,CAAC,EAPW,sBAAsB,sCAAtB,sBAAsB,QAOjC;AAOD,IAAY,2BAKX;AALD,WAAY,2BAA2B;IACrC,oEAAqC,CAAA;IACrC,gFAAiD,CAAA;IACjD,kFAAmD,CAAA;IACnD,gFAAiD,CAAA;AACnD,CAAC,EALW,2BAA2B,2CAA3B,2BAA2B,QAKtC;AAED,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAC3C,QAAA,MAAM,GAAG,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,IAAI,IAAI,OAAO,EAAE,CAAC,CAAC;AAY1E,SAAgB,4BAA4B,CAC1C,cAA4C,EAC5C,MAAuC;IAEvC,MAAM,cAAc,GAAmB,EAAE,CAAC;IAE1C,IAAI,cAAc,CAAC,aAAa,EAAE,CAAC;QACjC,cAAc,CACZ,2BAA2B,CAAC,iCAAiC,CAC9D,GAAG,cAAc,CAAC,aAAa,CAAC;QACjC,cAAc,CAAC,2BAA2B,CAAC,sBAAsB,CAAC;YAChE,cAAc,CAAC,aAAa,CAAC;IACjC,CAAC;IACD,IAAI,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,eAAe,KAAI,cAAc,CAAC,MAAM,EAAE,CAAC;QACrD,cAAc,CAAC,2BAA2B,CAAC,gBAAgB,CAAC;YAC1D,cAAc,CAAC,MAAM,CAAC;IAC1B,CAAC;IAED,OAAO,cAAc,CAAC;AACxB,CAAC;AAnBD,oEAmBC;AAED,SAAgB,8BAA8B,CAC5C,gBAAkC;IAElC,MAAM,cAAc,GAAmB,EAAE,CAAC;IAE1C,IAAI,gBAAgB,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;QACzC,cAAc,CAAC,2BAA2B,CAAC,sBAAsB,CAAC;YAChE,gBAAgB,CAAC,SAAS,CAAC,SAAS,CAAC;IACzC,CAAC;IAED,OAAO,cAAc,CAAC;AACxB,CAAC;AAXD,wEAWC;AAED,SAAgB,gBAAgB,CAC9B,IAAU,EACV,UAAgC,EAChC,MAAuC;IAEvC,MAAM,gBAAgB,GAAG,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,gBAAgB,CAAC;IAElD,IAAI,gBAAgB,KAAK,SAAS,IAAI,gBAAgB,KAAK,KAAK,EAAE,CAAC;QACjE,OAAO;IACT,CAAC;IAED,IAAI,kBAAkB,CAAC;IAEvB,IAAI,gBAAgB,KAAK,IAAI,EAAE,CAAC;QAC9B,kBAAkB,GAAG,UAAU,CAAC;IAClC,CAAC;SAAM,IAAI,gBAAgB,IAAI,CAAC,EAAE,CAAC;QACjC,OAAO;IACT,CAAC;SAAM,CAAC;QACN,kBAAkB,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAA;IAC5D,CAAC;IAED,KAAK,MAAM,SAAS,IAAI,kBAAkB,EAAE,CAAC;QAC3C,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;IAClC,CAAC;AACH,CAAC;AAxBD,4CAwBC"}