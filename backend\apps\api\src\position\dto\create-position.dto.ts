import {
  IsString,
  IsOptional,
  IsUUID,
  IsObject,
  IsN<PERSON>ber,
  IsEnum,
  Length,
  Matches,
  Min,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export enum PositionLevel {
  ENTRY = 'entry',
  JUNIOR = 'junior',
  MID = 'mid',
  SENIOR = 'senior',
  LEAD = 'lead',
  MANAGER = 'manager',
  DIRECTOR = 'director',
  VP = 'vp',
  C_LEVEL = 'c_level',
}

export class CreatePositionDto {
  @ApiProperty({ description: 'Position title', example: 'Software Engineer' })
  @IsString()
  @Length(2, 100)
  title: string;

  @ApiProperty({ description: 'Position code', example: 'SE001' })
  @IsString()
  @Length(2, 20)
  @Matches(/^[A-Z0-9]+$/, { message: 'Position code must contain only uppercase letters and numbers' })
  code: string;

  @ApiPropertyOptional({ description: 'Position description' })
  @IsOptional()
  @IsString()
  @Length(0, 1000)
  description?: string;

  @ApiProperty({ description: 'Department ID' })
  @IsUUID()
  departmentId: string;

  @ApiProperty({ description: 'Position level', enum: PositionLevel })
  @IsEnum(PositionLevel)
  level: PositionLevel;

  @ApiPropertyOptional({ description: 'Reports to position ID' })
  @IsOptional()
  @IsUUID()
  reportsToId?: string;

  @ApiPropertyOptional({ description: 'Minimum salary' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  minSalary?: number;

  @ApiPropertyOptional({ description: 'Maximum salary' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  maxSalary?: number;

  @ApiPropertyOptional({ description: 'Required skills' })
  @IsOptional()
  @IsString({ each: true })
  skills?: string[];

  @ApiPropertyOptional({ description: 'Required qualifications' })
  @IsOptional()
  @IsString({ each: true })
  qualifications?: string[];

  @ApiPropertyOptional({ description: 'Years of experience required' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  experienceYears?: number;

  @ApiPropertyOptional({ description: 'Job responsibilities' })
  @IsOptional()
  @IsString({ each: true })
  responsibilities?: string[];

  @ApiPropertyOptional({ description: 'Additional metadata' })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}
