import { CorePurpose, FeatureDefinition, FeatureDefinitions, FeatureVersion } from "./coreSpec";
export declare enum RequiresScopesTypeName {
    SCOPE = "Scope"
}
export declare class RequiresScopesSpecDefinition extends FeatureDefinition {
    static readonly directiveName = "requiresScopes";
    static readonly identity: string;
    constructor(version: FeatureVersion);
    get defaultCorePurpose(): CorePurpose;
}
export declare const REQUIRES_SCOPES_VERSIONS: FeatureDefinitions<RequiresScopesSpecDefinition>;
//# sourceMappingURL=requiresScopesSpec.d.ts.map