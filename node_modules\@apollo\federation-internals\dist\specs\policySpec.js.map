{"version": 3, "file": "policySpec.js", "sourceRoot": "", "sources": ["../../src/specs/policySpec.ts"], "names": [], "mappings": ";;;AAAA,qCAA4C;AAC5C,yCAMoB;AACpB,gDAAuD;AACvD,oFAA+G;AAC/G,4DAA4D;AAC5D,oFAAmF;AACnF,oCAAkC;AAElC,IAAY,cAEX;AAFD,WAAY,cAAc;IACxB,mCAAiB,CAAA;AACnB,CAAC,EAFW,cAAc,8BAAd,cAAc,QAEzB;AACD,MAAa,oBAAqB,SAAQ,4BAAiB;IAKzD,YAAY,OAAuB;QACjC,KAAK,CACH,IAAI,qBAAU,CACZ,oBAAoB,CAAC,QAAQ,EAC7B,oBAAoB,CAAC,aAAa,EAClC,OAAO,CACR,CACF,CAAC;QAEF,IAAI,CAAC,YAAY,CAAC,IAAA,6DAA6B,EAAC,EAAE,IAAI,EAAE,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAElF,IAAI,CAAC,iBAAiB,CAAC,IAAA,4DAA4B,EAAC;YAClD,IAAI,EAAE,oBAAoB,CAAC,aAAa;YACxC,IAAI,EAAE,CAAC;oBACL,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE;wBACxB,IAAA,cAAM,EAAC,OAAO,EAAE,2DAA2D,CAAC,CAAC;wBAC7E,MAAM,UAAU,GAAG,OAAO,CAAC,gBAAgB,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;wBACnE,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;wBAC3C,IAAA,cAAM,EAAC,UAAU,EAAE,GAAG,EAAE,CAAC,aAAa,UAAU,iBAAiB,CAAC,CAAC;wBACnE,OAAO,IAAI,yBAAW,CAAC,IAAI,sBAAQ,CAAC,IAAI,yBAAW,CAAC,IAAI,sBAAQ,CAAC,IAAI,yBAAW,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACnG,CAAC;oBACD,mBAAmB,EAAE,+DAA+B,CAAC,KAAK;iBAC3D,CAAC;YACF,SAAS,EAAE;gBACT,2BAAiB,CAAC,gBAAgB;gBAClC,2BAAiB,CAAC,MAAM;gBACxB,2BAAiB,CAAC,SAAS;gBAC3B,2BAAiB,CAAC,MAAM;gBACxB,2BAAiB,CAAC,IAAI;aACvB;YACD,QAAQ,EAAE,IAAI;YACd,uBAAuB,EAAE,GAAG,EAAE,CAAC,uBAAe,CAAC,MAAM,EAAE;SACxD,CAAC,CAAC,CAAC;IACN,CAAC;IAED,IAAI,kBAAkB;QACpB,OAAO,UAAU,CAAC;IACpB,CAAC;;AA3CH,oDA4CC;AA3CwB,kCAAa,GAAG,QAAQ,CAAC;AACzB,6BAAQ,GAC7B,4BAA4B,oBAAoB,CAAC,aAAa,EAAE,CAAC;AA2CxD,QAAA,eAAe,GAC1B,IAAI,6BAAkB,CACpB,oBAAoB,CAAC,QAAQ,CAC9B,CAAC,GAAG,CAAC,IAAI,oBAAoB,CAAC,IAAI,yBAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAE5D,IAAA,wCAAoB,EAAC,uBAAe,CAAC,CAAC"}