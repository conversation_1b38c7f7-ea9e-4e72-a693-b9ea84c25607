"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.graphQLJSDirectiveToAST = exports.graphQLJSNamedTypeToAST = exports.graphQLJSSchemaToAST = void 0;
const graphql_1 = require("graphql");
const definitions_1 = require("./definitions");
const allOperationTypeNode = [graphql_1.OperationTypeNode.QUERY, graphql_1.OperationTypeNode.MUTATION, graphql_1.OperationTypeNode.SUBSCRIPTION];
function graphQLJSSchemaToAST(schema) {
    const types = Object.values(schema.getTypeMap()).filter((type) => !(0, graphql_1.isIntrospectionType)(type) && !(0, graphql_1.isSpecifiedScalarType)(type));
    const directives = schema.getDirectives().filter((directive) => !(0, graphql_1.isSpecifiedDirective)(directive));
    const schemaASTs = toNodeArray(graphQLJSSchemaToSchemaDefinitionAST(schema));
    const typesASTs = types.map((type) => toNodeArray(graphQLJSNamedTypeToAST(type))).flat();
    const directivesASTs = directives.map((directive) => graphQLJSDirectiveToAST(directive));
    return {
        kind: graphql_1.Kind.DOCUMENT,
        definitions: [...schemaASTs, ...typesASTs, ...directivesASTs],
    };
}
exports.graphQLJSSchemaToAST = graphQLJSSchemaToAST;
function toNodeArray({ definition, extensions, }) {
    return definition ? [definition, ...extensions] : extensions;
}
function maybe(v) {
    return v ? v : undefined;
}
function graphQLJSSchemaToSchemaDefinitionAST(schema) {
    if (schema.astNode || schema.extensionASTNodes.length > 0) {
        return {
            definition: maybe(schema.astNode),
            extensions: schema.extensionASTNodes,
        };
    }
    else {
        let definition = undefined;
        if (hasNonDefaultRootNames(schema)) {
            const operationTypes = [];
            for (const operation of allOperationTypeNode) {
                const type = schema.getRootType(operation);
                if (type) {
                    operationTypes.push({
                        kind: graphql_1.Kind.OPERATION_TYPE_DEFINITION,
                        operation,
                        type: { kind: graphql_1.Kind.NAMED_TYPE, name: { kind: graphql_1.Kind.NAME, value: type.name } },
                    });
                }
            }
            definition = {
                kind: graphql_1.Kind.SCHEMA_DEFINITION,
                description: schema.description ? {
                    kind: graphql_1.Kind.STRING,
                    value: schema.description,
                } : undefined,
                operationTypes,
            };
        }
        return {
            definition,
            extensions: [],
        };
    }
}
function hasNonDefaultRootNames(schema) {
    return allOperationTypeNode.some((t) => isNonDefaultRootName(schema.getRootType(t), t));
}
function isNonDefaultRootName(type, operation) {
    return !!type && type.name !== (0, definitions_1.defaultRootName)(operation);
}
function graphQLJSNamedTypeToAST(type) {
    if (type.astNode || type.extensionASTNodes.length > 0) {
        return {
            definition: maybe(type.astNode),
            extensions: type.extensionASTNodes,
        };
    }
    else {
        return {
            definition: (0, graphql_1.parse)((0, graphql_1.printType)(type)).definitions[0],
            extensions: [],
        };
    }
}
exports.graphQLJSNamedTypeToAST = graphQLJSNamedTypeToAST;
function graphQLJSDirectiveToAST(directive) {
    if (directive.astNode) {
        return directive.astNode;
    }
    else {
        const fakeSchema = new graphql_1.GraphQLSchema({
            directives: [directive],
            assumeValid: true,
        });
        const reparsed = (0, graphql_1.parse)((0, graphql_1.printSchema)(fakeSchema));
        return reparsed.definitions.find((def) => def.kind === graphql_1.Kind.DIRECTIVE_DEFINITION);
    }
}
exports.graphQLJSDirectiveToAST = graphQLJSDirectiveToAST;
//# sourceMappingURL=graphQLJSSchemaToAST.js.map