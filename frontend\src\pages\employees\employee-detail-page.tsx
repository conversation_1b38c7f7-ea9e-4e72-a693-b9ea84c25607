import React from 'react';
import { useParams } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { PageContainer, PageHeader, PageSection, GridContainer } from '@/components/layout/page-container';
import { ArrowLeft, Edit, Mail, Phone, MapPin, User, Building, Calendar } from 'lucide-react';

const EmployeeDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();

  // Mock employee data - replace with actual data fetching
  const employee = {
    id: id,
    firstName: '<PERSON>',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+****************',
    position: 'Software Engineer',
    department: 'Engineering',
    status: 'active',
    startDate: '2023-01-15',
    manager: '<PERSON>',
    salary: 85000,
    address: {
      street: '123 Main St',
      city: 'San Francisco',
      state: 'CA',
      zipCode: '94105',
      country: 'USA',
    },
  };

  return (
    <PageContainer>
      <PageHeader
        title={`${employee.firstName} ${employee.lastName}`}
        description={employee.position}
        actions={
          <div className="flex items-center space-x-3">
            <Button variant="outline" size="sm" className="bg-white/80 backdrop-blur-sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Employees
            </Button>
            <Button className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70">
              <Edit className="h-4 w-4 mr-2" />
              Edit Employee
            </Button>
          </div>
        }
      />

      <PageSection>
        <GridContainer cols={3} gap="lg">
          {/* Basic Information */}
          <div className="lg:col-span-2 space-y-6">
            <Card className="shadow-lg border-0 bg-white/95 backdrop-blur-sm">
              <CardHeader className="bg-gradient-to-r from-blue-50/80 to-white/80 border-b border-blue-200/50">
                <CardTitle className="text-xl font-semibold text-blue-900 flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Basic Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6 p-6">
                <div className="grid grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <label className="text-sm font-semibold text-gray-600 uppercase tracking-wide">First Name</label>
                    <p className="text-lg font-medium text-gray-900">{employee.firstName}</p>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-semibold text-gray-600 uppercase tracking-wide">Last Name</label>
                    <p className="text-lg font-medium text-gray-900">{employee.lastName}</p>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-semibold text-gray-600 uppercase tracking-wide">Position</label>
                    <p className="text-lg font-medium text-gray-900">{employee.position}</p>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-semibold text-gray-600 uppercase tracking-wide">Department</label>
                    <p className="text-lg font-medium text-gray-900 flex items-center gap-2">
                      <Building className="h-4 w-4 text-blue-600" />
                      {employee.department}
                    </p>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-semibold text-gray-600 uppercase tracking-wide">Start Date</label>
                    <p className="text-lg font-medium text-gray-900 flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-green-600" />
                      {new Date(employee.startDate).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-semibold text-gray-600 uppercase tracking-wide">Manager</label>
                    <p className="text-lg font-medium text-gray-900 flex items-center gap-2">
                      <User className="h-4 w-4 text-purple-600" />
                      {employee.manager}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-lg border-0 bg-white/95 backdrop-blur-sm">
              <CardHeader className="bg-gradient-to-r from-green-50/80 to-white/80 border-b border-green-200/50">
                <CardTitle className="text-xl font-semibold text-green-900 flex items-center gap-2">
                  <Mail className="h-5 w-5" />
                  Contact Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6 p-6">
                <div className="flex items-center space-x-4 p-3 rounded-lg bg-gray-50/50">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Mail className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <label className="text-sm font-semibold text-gray-600 uppercase tracking-wide">Email</label>
                    <p className="text-lg font-medium text-gray-900">{employee.email}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-4 p-3 rounded-lg bg-gray-50/50">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <Phone className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <label className="text-sm font-semibold text-gray-600 uppercase tracking-wide">Phone</label>
                    <p className="text-lg font-medium text-gray-900">{employee.phone}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-4 p-3 rounded-lg bg-gray-50/50">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <MapPin className="h-5 w-5 text-purple-600" />
                  </div>
                  <div>
                    <label className="text-sm font-semibold text-gray-600 uppercase tracking-wide">Address</label>
                    <p className="text-lg font-medium text-gray-900">
                      {employee.address.street}, {employee.address.city}, {employee.address.state} {employee.address.zipCode}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
        </div>

          {/* Sidebar */}
          <div className="space-y-6">
            <Card className="shadow-lg border-0 bg-gradient-to-br from-emerald-50 to-emerald-100/50 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-semibold text-emerald-900">Status</CardTitle>
              </CardHeader>
              <CardContent>
                <Badge
                  variant={employee.status === 'active' ? 'default' : 'secondary'}
                  className={`text-sm font-semibold px-4 py-2 ${
                    employee.status === 'active'
                      ? 'bg-emerald-500 hover:bg-emerald-600 text-white'
                      : 'bg-gray-200 text-gray-700'
                  }`}
                >
                  {employee.status.toUpperCase()}
                </Badge>
              </CardContent>
            </Card>

            <Card className="shadow-lg border-0 bg-gradient-to-br from-blue-50 to-blue-100/50 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-semibold text-blue-900">Salary Information</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-3xl font-bold text-blue-900 mb-2">
                  ${employee.salary.toLocaleString()}
                </p>
                <p className="text-sm text-blue-700 font-medium">Annual salary</p>
              </CardContent>
            </Card>

            <Card className="shadow-lg border-0 bg-gradient-to-br from-purple-50 to-purple-100/50 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-semibold text-purple-900">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button variant="outline" className="w-full bg-white/80 hover:bg-white border-purple-200 hover:border-purple-300">
                  View Performance
                </Button>
                <Button variant="outline" className="w-full bg-white/80 hover:bg-white border-purple-200 hover:border-purple-300">
                  View Payroll
                </Button>
                  Send Message
                </Button>
              </CardContent>
            </Card>
          </div>
        </GridContainer>
      </PageSection>
    </PageContainer>
  );
};

export default EmployeeDetailPage;
