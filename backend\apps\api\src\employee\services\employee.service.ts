import {
  Injectable,
  NotFoundException,
  ConflictException,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';

import {
  Employee,
  Department,
  Position,
  User,
  EmploymentStatus,
} from '@app/database';

import {
  CreateEmployeeDto,
  UpdateEmployeeDto,
  EmployeeQueryDto,
  EmployeeResponseDto,
  PaginatedEmployeeResponseDto,
} from '../dto';

@Injectable()
export class EmployeeService {
  private readonly logger = new Logger(EmployeeService.name);

  constructor(
    @InjectRepository(Employee)
    private readonly employeeRepository: Repository<Employee>,
    @InjectRepository(Department)
    private readonly departmentRepository: Repository<Department>,
    @InjectRepository(Position)
    private readonly positionRepository: Repository<Position>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async create(
    createEmployeeDto: CreateEmployeeDto,
    createdBy: string,
    tenantId: string,
  ): Promise<EmployeeResponseDto> {
    this.logger.log(`Creating employee: ${createEmployeeDto.employeeId}`);

    // Check if employee ID already exists
    const existingEmployee = await this.employeeRepository.findOne({
      where: { employeeId: createEmployeeDto.employeeId, tenantId },
    });

    if (existingEmployee) {
      throw new ConflictException(`Employee with ID ${createEmployeeDto.employeeId} already exists`);
    }

    // Check if email already exists
    const existingEmail = await this.employeeRepository.findOne({
      where: { email: createEmployeeDto.email, tenantId },
    });

    if (existingEmail) {
      throw new ConflictException(`Employee with email ${createEmployeeDto.email} already exists`);
    }

    // Validate department exists
    const department = await this.departmentRepository.findOne({
      where: { id: createEmployeeDto.departmentId, tenantId },
    });

    if (!department) {
      throw new NotFoundException(`Department with ID ${createEmployeeDto.departmentId} not found`);
    }

    // Validate position exists
    const position = await this.positionRepository.findOne({
      where: { id: createEmployeeDto.positionId, tenantId },
    });

    if (!position) {
      throw new NotFoundException(`Position with ID ${createEmployeeDto.positionId} not found`);
    }

    // Validate manager exists if provided
    if (createEmployeeDto.managerId) {
      const manager = await this.employeeRepository.findOne({
        where: { id: createEmployeeDto.managerId, tenantId },
      });

      if (!manager) {
        throw new NotFoundException(`Manager with ID ${createEmployeeDto.managerId} not found`);
      }
    }

    // Create employee
    const employee = this.employeeRepository.create({
      ...createEmployeeDto,
      tenantId,
      createdBy,
      updatedBy: createdBy,
    });

    const savedEmployee = await this.employeeRepository.save(employee);

    // Emit event
    this.eventEmitter.emit('employee.created', {
      employeeId: savedEmployee.id,
      tenantId,
      createdBy,
    });

    return this.mapToResponseDto(savedEmployee);
  }

  async findAll(
    query: EmployeeQueryDto,
    tenantId: string,
  ): Promise<PaginatedEmployeeResponseDto> {
    const { page = 1, limit = 10, search, sortBy = 'lastName', sortOrder = 'ASC' } = query;
    const skip = (page - 1) * limit;

    const queryBuilder = this.createQueryBuilder(tenantId);

    // Apply filters
    this.applyFilters(queryBuilder, query);

    // Apply search
    if (search) {
      queryBuilder.andWhere(
        '(employee.firstName ILIKE :search OR employee.lastName ILIKE :search OR employee.email ILIKE :search OR employee.employeeId ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    // Apply sorting
    queryBuilder.orderBy(`employee.${sortBy}`, sortOrder);

    // Apply pagination
    queryBuilder.skip(skip).take(limit);

    const [employees, total] = await queryBuilder.getManyAndCount();

    const data = employees.map(employee => this.mapToResponseDto(employee));

    return {
      data,
      meta: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNextPage: page < Math.ceil(total / limit),
        hasPreviousPage: page > 1,
      },
    };
  }

  async findOne(
    id: string,
    user: any,
    tenantId: string,
  ): Promise<EmployeeResponseDto> {
    const employee = await this.employeeRepository.findOne({
      where: { id, tenantId },
      relations: ['department', 'position', 'manager'],
    });

    if (!employee) {
      throw new NotFoundException(`Employee with ID ${id} not found`);
    }

    // Check permissions - employees can only view their own data unless they have elevated permissions
    if (user.role === 'employee' && employee.userId !== user.id) {
      throw new ForbiddenException('You can only view your own employee data');
    }

    return this.mapToResponseDto(employee);
  }

  async update(
    id: string,
    updateEmployeeDto: UpdateEmployeeDto,
    updatedBy: string,
    tenantId: string,
  ): Promise<EmployeeResponseDto> {
    const employee = await this.employeeRepository.findOne({
      where: { id, tenantId },
    });

    if (!employee) {
      throw new NotFoundException(`Employee with ID ${id} not found`);
    }

    // Validate email uniqueness if being updated
    if (updateEmployeeDto.email && updateEmployeeDto.email !== employee.email) {
      const existingEmail = await this.employeeRepository.findOne({
        where: { email: updateEmployeeDto.email, tenantId },
      });

      if (existingEmail) {
        throw new ConflictException(`Employee with email ${updateEmployeeDto.email} already exists`);
      }
    }

    // Update employee
    Object.assign(employee, updateEmployeeDto, { updatedBy });
    const savedEmployee = await this.employeeRepository.save(employee);

    // Emit event
    this.eventEmitter.emit('employee.updated', {
      employeeId: savedEmployee.id,
      tenantId,
      updatedBy,
      changes: updateEmployeeDto,
    });

    return this.mapToResponseDto(savedEmployee);
  }

  async remove(id: string, deletedBy: string, tenantId: string): Promise<void> {
    const employee = await this.employeeRepository.findOne({
      where: { id, tenantId },
    });

    if (!employee) {
      throw new NotFoundException(`Employee with ID ${id} not found`);
    }

    // Soft delete
    employee.isActive = false;
    employee.employmentStatus = EmploymentStatus.TERMINATED;
    employee.updatedBy = deletedBy;
    employee.deletedAt = new Date();

    await this.employeeRepository.save(employee);

    // Emit event
    this.eventEmitter.emit('employee.deleted', {
      employeeId: id,
      tenantId,
      deletedBy,
    });
  }

  async activate(id: string, activatedBy: string, tenantId: string): Promise<EmployeeResponseDto> {
    const employee = await this.employeeRepository.findOne({
      where: { id, tenantId },
    });

    if (!employee) {
      throw new NotFoundException(`Employee with ID ${id} not found`);
    }

    employee.isActive = true;
    employee.employmentStatus = EmploymentStatus.ACTIVE;
    employee.updatedBy = activatedBy;

    const savedEmployee = await this.employeeRepository.save(employee);

    // Emit event
    this.eventEmitter.emit('employee.activated', {
      employeeId: id,
      tenantId,
      activatedBy,
    });

    return this.mapToResponseDto(savedEmployee);
  }

  async deactivate(id: string, deactivatedBy: string, tenantId: string): Promise<EmployeeResponseDto> {
    const employee = await this.employeeRepository.findOne({
      where: { id, tenantId },
    });

    if (!employee) {
      throw new NotFoundException(`Employee with ID ${id} not found`);
    }

    employee.isActive = false;
    employee.employmentStatus = EmploymentStatus.INACTIVE;
    employee.updatedBy = deactivatedBy;

    const savedEmployee = await this.employeeRepository.save(employee);

    // Emit event
    this.eventEmitter.emit('employee.deactivated', {
      employeeId: id,
      tenantId,
      deactivatedBy,
    });

    return this.mapToResponseDto(savedEmployee);
  }

  private createQueryBuilder(tenantId: string): SelectQueryBuilder<Employee> {
    return this.employeeRepository
      .createQueryBuilder('employee')
      .leftJoinAndSelect('employee.department', 'department')
      .leftJoinAndSelect('employee.position', 'position')
      .leftJoinAndSelect('employee.manager', 'manager')
      .where('employee.tenantId = :tenantId', { tenantId });
  }

  private applyFilters(queryBuilder: SelectQueryBuilder<Employee>, query: EmployeeQueryDto): void {
    if (query.departmentId) {
      queryBuilder.andWhere('employee.departmentId = :departmentId', {
        departmentId: query.departmentId,
      });
    }

    if (query.positionId) {
      queryBuilder.andWhere('employee.positionId = :positionId', {
        positionId: query.positionId,
      });
    }

    if (query.managerId) {
      queryBuilder.andWhere('employee.managerId = :managerId', {
        managerId: query.managerId,
      });
    }

    if (query.employmentStatus) {
      queryBuilder.andWhere('employee.employmentStatus = :employmentStatus', {
        employmentStatus: query.employmentStatus,
      });
    }

    if (query.employmentType) {
      queryBuilder.andWhere('employee.employmentType = :employmentType', {
        employmentType: query.employmentType,
      });
    }

    if (!query.includeInactive) {
      queryBuilder.andWhere('employee.isActive = :isActive', { isActive: true });
    }
  }

  private mapToResponseDto(employee: Employee): EmployeeResponseDto {
    return {
      id: employee.id,
      employeeId: employee.employeeId,
      firstName: employee.firstName,
      lastName: employee.lastName,
      middleName: employee.middleName,
      fullName: `${employee.firstName} ${employee.lastName}`,
      email: employee.email,
      phone: employee.phone,
      dateOfBirth: employee.dateOfBirth?.toISOString(),
      gender: employee.gender,
      hireDate: employee.hireDate.toISOString(),
      department: employee.department ? {
        id: employee.department.id,
        name: employee.department.name,
        code: employee.department.code,
      } : null,
      position: employee.position ? {
        id: employee.position.id,
        title: employee.position.title,
        code: employee.position.code,
        level: employee.position.level,
      } : null,
      manager: employee.manager ? {
        id: employee.manager.id,
        fullName: `${employee.manager.firstName} ${employee.manager.lastName}`,
        employeeId: employee.manager.employeeId,
      } : null,
      employmentStatus: employee.employmentStatus,
      employmentType: employee.employmentType,
      salary: employee.salary,
      isActive: employee.isActive,
      createdAt: employee.createdAt.toISOString(),
      updatedAt: employee.updatedAt.toISOString(),
      metadata: employee.metadata,
    };
  }
}
