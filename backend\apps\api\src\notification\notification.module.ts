import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// Entities
import {
  Notification,
  NotificationTemplate,
  NotificationPreference,
  Employee,
  User,
  Tenant,
  AuditLog,
} from '@app/database';

// Controllers
import { NotificationController } from './controllers/notification.controller';
import { NotificationTemplateController } from './controllers/notification-template.controller';
import { NotificationPreferenceController } from './controllers/notification-preference.controller';

// Services
import { NotificationService } from './services/notification.service';
import { NotificationTemplateService } from './services/notification-template.service';
import { NotificationPreferenceService } from './services/notification-preference.service';
import { EmailNotificationService } from './services/email-notification.service';
import { PushNotificationService } from './services/push-notification.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Notification,
      NotificationTemplate,
      NotificationPreference,
      Employee,
      User,
      Tenant,
      AuditLog,
    ]),
  ],
  controllers: [
    NotificationController,
    NotificationTemplateController,
    NotificationPreferenceController,
  ],
  providers: [
    NotificationService,
    NotificationTemplateService,
    NotificationPreferenceService,
    EmailNotificationService,
    PushNotificationService,
  ],
  exports: [
    NotificationService,
    NotificationTemplateService,
    NotificationPreferenceService,
    EmailNotificationService,
    PushNotificationService,
  ],
})
export class NotificationModule {}
