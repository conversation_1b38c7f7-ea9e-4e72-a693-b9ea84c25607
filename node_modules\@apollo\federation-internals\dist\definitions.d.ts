import { ConstArgumentNode, ASTNode, DirectiveLocation, ConstDirectiveNode, DocumentNode, GraphQLError, GraphQLSchema, Kind, TypeNode, VariableDefinitionNode, VariableNode, DirectiveDefinitionNode, DirectiveNode } from "graphql";
import { CoreImport, CoreSpecDefinition, FeatureUrl, FeatureVersion } from "./specs/coreSpec";
import { MapWithCachedArrays } from "./utils";
import { SDLValidationRule } from "graphql/validation/ValidationContext";
export declare const ErrGraphQLValidationFailed: (causes: GraphQLError[], message?: string) => GraphQLError;
export declare const ErrGraphQLAPISchemaValidationFailed: (causes: GraphQLError[]) => GraphQLError;
export declare const typenameFieldName = "__typename";
export type QueryRootKind = 'query';
export type MutationRootKind = 'mutation';
export type SubscriptionRootKind = 'subscription';
export type SchemaRootKind = QueryRootKind | MutationRootKind | SubscriptionRootKind;
export declare const allSchemaRootKinds: SchemaRootKind[];
export declare function defaultRootName(rootKind: SchemaRootKind): string;
export declare function isSchemaRootType(type: NamedType): boolean;
export type Type = NamedType | WrapperType;
export type NamedType = ScalarType | ObjectType | InterfaceType | UnionType | EnumType | InputObjectType;
export type OutputType = ScalarType | ObjectType | InterfaceType | UnionType | EnumType | ListType<any> | NonNullType<any>;
export type InputType = ScalarType | EnumType | InputObjectType | ListType<any> | NonNullType<any>;
export type WrapperType = ListType<any> | NonNullType<any>;
export type AbstractType = InterfaceType | UnionType;
export type CompositeType = ObjectType | InterfaceType | UnionType;
export type OutputTypeReferencer = FieldDefinition<any>;
export type InputTypeReferencer = InputFieldDefinition | ArgumentDefinition<any>;
export type ObjectTypeReferencer = OutputTypeReferencer | UnionType | SchemaDefinition;
export type InterfaceTypeReferencer = OutputTypeReferencer | ObjectType | InterfaceType;
export type NullableType = NamedType | ListType<any>;
export type NamedTypeKind = NamedType['kind'];
export declare function isNamedType(type: Type): type is NamedType;
export declare function isWrapperType(type: Type): type is WrapperType;
export declare function isListType(type: Type): type is ListType<any>;
export declare function isNonNullType(type: Type): type is NonNullType<any>;
export declare function isScalarType(type: Type): type is ScalarType;
export declare function isCustomScalarType(type: Type): boolean;
export declare function isIntType(type: Type): boolean;
export declare function isStringType(type: Type): boolean;
export declare function isFloatType(type: Type): boolean;
export declare function isBooleanType(type: Type): boolean;
export declare function isIDType(type: Type): boolean;
export declare function isObjectType(type: Type): type is ObjectType;
export declare function isInterfaceType(type: Type): type is InterfaceType;
export declare function isEnumType(type: Type): type is EnumType;
export declare function isUnionType(type: Type): type is UnionType;
export declare function isInputObjectType(type: Type): type is InputObjectType;
export declare function isOutputType(type: Type): type is OutputType;
export declare function isInputType(type: Type): type is InputType;
export declare function isTypeOfKind<T extends Type>(type: Type, kind: T['kind']): type is T;
export declare function filterTypesOfKind<T extends Type>(types: readonly Type[], kind: T['kind']): T[];
export declare function baseType(type: Type): NamedType;
export declare function isNullableType(type: Type): boolean;
export declare function isAbstractType(type: Type): type is AbstractType;
export declare function isCompositeType(type: Type): type is CompositeType;
export declare function possibleRuntimeTypes(type: CompositeType): readonly ObjectType[];
export declare function runtimeTypesIntersects(t1: CompositeType, t2: CompositeType): boolean;
export declare function supertypes(type: CompositeType): readonly CompositeType[];
export declare function isConditionalDirective(directive: Directive<any, any> | DirectiveDefinition<any>): boolean;
export declare const executableDirectiveLocations: DirectiveLocation[];
export declare function isExecutableDirectiveLocation(loc: DirectiveLocation): boolean;
export declare const typeSystemDirectiveLocations: DirectiveLocation[];
export declare function isTypeSystemDirectiveLocation(loc: DirectiveLocation): boolean;
export declare function typeToAST(type: Type): TypeNode;
export declare function typeFromAST(schema: Schema, node: TypeNode): Type;
export type LeafType = ScalarType | EnumType;
export declare function isLeafType(type: Type): type is LeafType;
export interface Named {
    readonly name: string;
}
export type ExtendableElement = SchemaDefinition | NamedType;
export declare class DirectiveTargetElement<T extends DirectiveTargetElement<T>> {
    private readonly _schema;
    readonly appliedDirectives: Directive<T>[];
    constructor(_schema: Schema, directives?: readonly Directive<any>[]);
    schema(): Schema;
    private attachDirective;
    appliedDirectivesOf<TApplicationArgs extends {
        [key: string]: any;
    } = {
        [key: string]: any;
    }>(nameOrDefinition: string | DirectiveDefinition<TApplicationArgs>): Directive<T, TApplicationArgs>[];
    hasAppliedDirective(nameOrDefinition: string | DirectiveDefinition): boolean;
    appliedDirectivesToDirectiveNodes(): ConstDirectiveNode[] | undefined;
    appliedDirectivesToString(): string;
    collectVariablesInAppliedDirectives(collector: VariableCollector): void;
}
export declare function sourceASTs<TNode extends ASTNode = ASTNode>(...elts: ({
    sourceAST?: TNode;
} | undefined)[]): TNode[];
declare abstract class Element<TParent extends SchemaElement<any, any> | Schema | DirectiveTargetElement<any>> {
    protected _parent?: TParent;
    sourceAST?: ASTNode;
    schema(): Schema;
    protected schemaInternal(): Schema | undefined;
    get parent(): TParent;
    isAttached(): boolean;
    private setParent;
    protected onAttached(): void;
    protected checkUpdate(): void;
}
export declare class Extension<TElement extends ExtendableElement> {
    protected _extendedElement?: TElement;
    sourceAST?: ASTNode;
    get extendedElement(): TElement | undefined;
    private setExtendedElement;
}
type UnappliedDirective = {
    nameOrDef: DirectiveDefinition<Record<string, any>> | string;
    args: Record<string, any>;
    extension?: Extension<any>;
    directive: DirectiveNode;
};
export declare abstract class SchemaElement<TOwnType extends SchemaElement<any, TParent>, TParent extends SchemaElement<any, any> | Schema> extends Element<TParent> {
    protected _appliedDirectives: Directive<TOwnType>[] | undefined;
    protected _unappliedDirectives: UnappliedDirective[] | undefined;
    description?: string;
    addUnappliedDirective({ nameOrDef, args, extension, directive }: UnappliedDirective): void;
    processUnappliedDirectives(): void;
    get appliedDirectives(): readonly Directive<TOwnType>[];
    appliedDirectivesOf<TApplicationArgs extends {
        [key: string]: any;
    } = {
        [key: string]: any;
    }>(nameOrDefinition: string | DirectiveDefinition<TApplicationArgs>): Directive<TOwnType, TApplicationArgs>[];
    hasAppliedDirective(nameOrDefinition: string | DirectiveDefinition<any>): boolean;
    applyDirective<TApplicationArgs extends {
        [key: string]: any;
    } = {
        [key: string]: any;
    }>(nameOrDef: DirectiveDefinition<TApplicationArgs> | string, args?: TApplicationArgs, asFirstDirective?: boolean): Directive<TOwnType, TApplicationArgs>;
    protected removeAppliedDirectives(): void;
    protected onModification(): void;
    protected isElementBuiltIn(): boolean;
    protected removeTypeReferenceInternal(type: BaseNamedType<any, any>): void;
    protected abstract removeTypeReference(type: NamedType): void;
    protected checkRemoval(): void;
    protected checkUpdate(addedElement?: {
        schema(): Schema;
        isAttached(): boolean;
    }): void;
}
export declare abstract class NamedSchemaElement<TOwnType extends NamedSchemaElement<TOwnType, TParent, TReferencer>, TParent extends NamedSchemaElement<any, any, any> | Schema, TReferencer> extends SchemaElement<TOwnType, TParent> implements Named {
    protected _name: string;
    constructor(name: string);
    get name(): string;
    abstract coordinate: string;
    abstract remove(): TReferencer[];
}
declare abstract class BaseNamedType<TReferencer, TOwnType extends NamedType & NamedSchemaElement<TOwnType, Schema, TReferencer>> extends NamedSchemaElement<TOwnType, Schema, TReferencer> {
    readonly isBuiltIn: boolean;
    protected _referencers?: Set<TReferencer>;
    protected _extensions?: Extension<TOwnType>[];
    preserveEmptyDefinition: boolean;
    constructor(name: string, isBuiltIn?: boolean);
    private addReferencer;
    private removeReferencer;
    get coordinate(): string;
    allChildElements(): Generator<NamedSchemaElement<any, TOwnType, any>, void, undefined>;
    extensions(): readonly Extension<TOwnType>[];
    hasExtension(extension: Extension<any>): boolean;
    newExtension(): Extension<TOwnType>;
    addExtension(extension: Extension<TOwnType>): Extension<TOwnType>;
    removeExtensions(): void;
    isIntrospectionType(): boolean;
    hasExtensionElements(): boolean;
    hasNonExtensionElements(): boolean;
    protected abstract hasNonExtensionInnerElements(): boolean;
    protected abstract removeInnerElementsExtensions(): void;
    protected isElementBuiltIn(): boolean;
    rename(newName: string): void;
    remove(): TReferencer[];
    removeRecursive(): void;
    protected abstract removeReferenceRecursive(ref: TReferencer): void;
    referencers(): ReadonlySet<TReferencer>;
    isReferenced(): boolean;
    protected abstract removeInnerElements(): void;
    toString(): string;
}
export declare abstract class NamedSchemaElementWithType<TType extends Type, TOwnType extends NamedSchemaElementWithType<TType, TOwnType, P, Referencer>, P extends NamedSchemaElement<any, any, any> | Schema, Referencer> extends NamedSchemaElement<TOwnType, P, Referencer> {
    private _type?;
    get type(): TType | undefined;
    set type(type: TType | undefined);
    protected removeTypeReference(type: NamedType): void;
}
declare abstract class BaseExtensionMember<TExtended extends ExtendableElement> extends Element<TExtended> {
    private _extension?;
    ofExtension(): Extension<TExtended> | undefined;
    removeOfExtension(): void;
    setOfExtension(extension: Extension<TExtended> | undefined): void;
    remove(): void;
    protected abstract removeInner(): void;
}
export declare class SchemaBlueprint {
    onMissingDirectiveDefinition(_schema: Schema, _directive: Directive): DirectiveDefinition | GraphQLError[] | undefined;
    onDirectiveDefinitionAndSchemaParsed(_: Schema): GraphQLError[];
    ignoreParsedField(_type: NamedType, _fieldName: string): boolean;
    onConstructed(_: Schema): void;
    onAddedCoreFeature(_schema: Schema, _feature: CoreFeature): void;
    onInvalidation(_: Schema): void;
    onValidation(_schema: Schema): GraphQLError[];
    validationRules(): readonly SDLValidationRule[];
    onGraphQLJSValidationError(schema: Schema, error: GraphQLError): GraphQLError;
    onUnknownDirectiveValidationError(_schema: Schema, _unknownDirectiveName: string, error: GraphQLError): GraphQLError;
    applyDirectivesAfterParsing(): boolean;
}
export declare const defaultSchemaBlueprint: SchemaBlueprint;
export declare class CoreFeature {
    readonly url: FeatureUrl;
    readonly nameInSchema: string;
    readonly directive: Directive<SchemaDefinition>;
    readonly imports: CoreImport[];
    readonly purpose?: string | undefined;
    constructor(url: FeatureUrl, nameInSchema: string, directive: Directive<SchemaDefinition>, imports: CoreImport[], purpose?: string | undefined);
    isFeatureDefinition(element: NamedType | DirectiveDefinition): boolean;
    directiveNameInSchema(name: string): string;
    static directiveNameInSchemaForCoreArguments(specUrl: FeatureUrl, specNameInSchema: string, imports: CoreImport[], directiveNameInSpec: string): string;
    typeNameInSchema(name: string): string;
    minimumFederationVersion(): FeatureVersion | undefined;
}
export declare class CoreFeatures {
    readonly coreItself: CoreFeature;
    readonly coreDefinition: CoreSpecDefinition;
    private readonly byAlias;
    private readonly byIdentity;
    constructor(coreItself: CoreFeature);
    getByIdentity(identity: string): CoreFeature | undefined;
    allFeatures(): IterableIterator<CoreFeature>;
    private removeFeature;
    private maybeAddFeature;
    private add;
    sourceFeature(element: DirectiveDefinition | Directive | NamedType): {
        feature: CoreFeature;
        nameInFeature: string;
        isImported: boolean;
    } | undefined;
}
export type DeferDirectiveArgs = {
    label?: string;
    if?: boolean | Variable;
};
export type StreamDirectiveArgs = {
    label?: string;
    initialCount: number;
    if?: boolean;
};
export type SchemaConfig = {
    cacheAST?: boolean;
};
export declare class Schema {
    readonly blueprint: SchemaBlueprint;
    readonly config: SchemaConfig;
    private _schemaDefinition;
    private readonly _builtInTypes;
    private readonly _types;
    private readonly _builtInDirectives;
    private readonly _directives;
    private _coreFeatures?;
    private isConstructed;
    isValidated: boolean;
    private cachedDocument?;
    private apiSchema?;
    constructor(blueprint?: SchemaBlueprint, config?: SchemaConfig);
    private canModifyBuiltIn;
    private runWithBuiltInModificationAllowed;
    private renameTypeInternal;
    private removeTypeInternal;
    private removeDirectiveInternal;
    private markAsCoreSchema;
    private unmarkAsCoreSchema;
    private onModification;
    isCoreSchema(): boolean;
    get coreFeatures(): CoreFeatures | undefined;
    toAST(): DocumentNode;
    toAPISchema(): Schema;
    private emptyASTDefinitionsForExtensionsWithoutDefinition;
    toGraphQLJSSchema(config?: {
        includeDefer?: boolean;
        includeStream?: boolean;
    }): GraphQLSchema;
    get schemaDefinition(): SchemaDefinition;
    types(): readonly NamedType[];
    interfaceTypes(): readonly InterfaceType[];
    objectTypes(): readonly ObjectType[];
    unionTypes(): readonly UnionType[];
    scalarTypes(): readonly ScalarType[];
    inputTypes(): readonly InputObjectType[];
    enumTypes(): readonly EnumType[];
    builtInTypes(includeShadowed?: boolean): readonly NamedType[];
    private isShadowedBuiltInType;
    allTypes(): readonly NamedType[];
    type(name: string): NamedType | undefined;
    typeOfKind<T extends NamedType>(name: string, kind: T['kind']): T | undefined;
    intType(): ScalarType;
    floatType(): ScalarType;
    stringType(): ScalarType;
    booleanType(): ScalarType;
    idType(): ScalarType;
    builtInScalarTypes(): ScalarType[];
    addType<T extends NamedType>(type: T): T;
    directives(): readonly DirectiveDefinition[];
    builtInDirectives(includeShadowed?: boolean): readonly DirectiveDefinition[];
    allDirectives(): readonly DirectiveDefinition[];
    private isShadowedBuiltInDirective;
    directive(name: string): DirectiveDefinition | undefined;
    builtInDirective(name: string): DirectiveDefinition | undefined;
    allNamedSchemaElement(): Generator<NamedSchemaElement<any, any, any>, void, undefined>;
    allSchemaElement(): Generator<SchemaElement<any, any>, void, undefined>;
    addDirectiveDefinition(name: string): DirectiveDefinition;
    addDirectiveDefinition(directive: DirectiveDefinition): DirectiveDefinition;
    invalidate(): void;
    assumeValid(): void;
    validate(): void;
    clone(builtIns?: SchemaBlueprint, cloneJoinDirectives?: boolean): Schema;
    private getBuiltInDirective;
    includeDirective(): DirectiveDefinition<{
        if: boolean | Variable;
    }>;
    skipDirective(): DirectiveDefinition<{
        if: boolean | Variable;
    }>;
    deprecatedDirective(): DirectiveDefinition<{
        reason?: string;
    }>;
    specifiedByDirective(): DirectiveDefinition<{
        url: string;
    }>;
    deferDirective(): DirectiveDefinition<DeferDirectiveArgs>;
    streamDirective(): DirectiveDefinition<StreamDirectiveArgs>;
    elementByCoordinate(coordinate: string): NamedSchemaElement<any, any, any> | undefined;
}
export declare class RootType extends BaseExtensionMember<SchemaDefinition> {
    readonly rootKind: SchemaRootKind;
    readonly type: ObjectType;
    constructor(rootKind: SchemaRootKind, type: ObjectType);
    isDefaultRootName(): boolean;
    protected removeInner(): void;
}
export declare class SchemaDefinition extends SchemaElement<SchemaDefinition, Schema> {
    readonly kind: "SchemaDefinition";
    protected readonly _roots: MapWithCachedArrays<SchemaRootKind, RootType>;
    protected _extensions: Extension<SchemaDefinition>[] | undefined;
    preserveEmptyDefinition: boolean;
    roots(): readonly RootType[];
    applyDirective<TApplicationArgs extends {
        [key: string]: any;
    } = {
        [key: string]: any;
    }>(nameOrDef: DirectiveDefinition<TApplicationArgs> | string, args?: TApplicationArgs, asFirstDirective?: boolean): Directive<SchemaDefinition, TApplicationArgs>;
    root(rootKind: SchemaRootKind): RootType | undefined;
    rootType(rootKind: SchemaRootKind): ObjectType | undefined;
    setRoot(rootKind: SchemaRootKind, nameOrType: ObjectType | string): RootType;
    extensions(): Extension<SchemaDefinition>[];
    hasExtension(extension: Extension<any>): boolean;
    newExtension(): Extension<SchemaDefinition>;
    addExtension(extension: Extension<SchemaDefinition>): Extension<SchemaDefinition>;
    hasExtensionElements(): boolean;
    hasNonExtensionElements(): boolean;
    private removeRootType;
    protected removeTypeReference(toRemove: NamedType): void;
    toString(): string;
}
export declare class ScalarType extends BaseNamedType<OutputTypeReferencer | InputTypeReferencer, ScalarType> {
    readonly kind: "ScalarType";
    readonly astDefinitionKind = Kind.SCALAR_TYPE_DEFINITION;
    protected removeTypeReference(type: NamedType): void;
    protected hasNonExtensionInnerElements(): boolean;
    protected removeInnerElementsExtensions(): void;
    protected removeInnerElements(): void;
    protected removeReferenceRecursive(ref: OutputTypeReferencer | InputTypeReferencer): void;
}
export declare class InterfaceImplementation<T extends ObjectType | InterfaceType> extends BaseExtensionMember<T> {
    readonly interface: InterfaceType;
    constructor(itf: InterfaceType);
    protected removeInner(): void;
    toString(): string;
}
declare abstract class FieldBasedType<T extends (ObjectType | InterfaceType) & NamedSchemaElement<T, Schema, R>, R> extends BaseNamedType<R, T> {
    private _interfaceImplementations;
    private readonly _fields;
    private _cachedNonBuiltInFields?;
    protected onAttached(): void;
    private removeFieldInternal;
    interfaceImplementations(): readonly InterfaceImplementation<T>[];
    interfaceImplementation(type: string | InterfaceType): InterfaceImplementation<T> | undefined;
    interfaces(): readonly InterfaceType[];
    implementsInterface(type: string | InterfaceType): boolean;
    addImplementedInterface(nameOrItfOrItfImpl: InterfaceImplementation<T> | InterfaceType | string): InterfaceImplementation<T>;
    fields(): readonly FieldDefinition<T>[];
    hasFields(): boolean;
    builtInFields(): FieldDefinition<T>[];
    allFields(): readonly FieldDefinition<T>[];
    field(name: string): FieldDefinition<T> | undefined;
    typenameField(): FieldDefinition<T> | undefined;
    addField(nameOrField: string | FieldDefinition<T>, type?: Type): FieldDefinition<T>;
    allChildElements(): Generator<NamedSchemaElement<any, any, any>, void, undefined>;
    private removeInterfaceImplementation;
    protected removeTypeReference(type: NamedType): void;
    protected removeInnerElements(): void;
    protected hasNonExtensionInnerElements(): boolean;
    protected removeInnerElementsExtensions(): void;
}
export declare class ObjectType extends FieldBasedType<ObjectType, ObjectTypeReferencer> {
    readonly kind: "ObjectType";
    readonly astDefinitionKind = Kind.OBJECT_TYPE_DEFINITION;
    isRootType(): boolean;
    isQueryRootType(): boolean;
    isSubscriptionRootType(): boolean;
    protected removeReferenceRecursive(ref: ObjectTypeReferencer): void;
    unionsWhereMember(): readonly UnionType[];
}
export declare class InterfaceType extends FieldBasedType<InterfaceType, InterfaceTypeReferencer> {
    readonly kind: "InterfaceType";
    readonly astDefinitionKind = Kind.INTERFACE_TYPE_DEFINITION;
    allImplementations(): (ObjectType | InterfaceType)[];
    possibleRuntimeTypes(): readonly ObjectType[];
    isPossibleRuntimeType(type: string | NamedType): boolean;
    protected removeReferenceRecursive(ref: InterfaceTypeReferencer): void;
}
export declare class UnionMember extends BaseExtensionMember<UnionType> {
    readonly type: ObjectType;
    constructor(type: ObjectType);
    protected removeInner(): void;
}
export declare class UnionType extends BaseNamedType<OutputTypeReferencer, UnionType> {
    readonly kind: "UnionType";
    readonly astDefinitionKind = Kind.UNION_TYPE_DEFINITION;
    protected readonly _members: MapWithCachedArrays<string, UnionMember>;
    private _typenameField?;
    protected onAttached(): void;
    types(): ObjectType[];
    members(): readonly UnionMember[];
    membersCount(): number;
    hasTypeMember(type: string | ObjectType): boolean;
    addType(nameOrTypeOrMember: ObjectType | string | UnionMember): UnionMember;
    clearTypes(): void;
    field(name: string): FieldDefinition<UnionType> | undefined;
    typenameField(): FieldDefinition<UnionType> | undefined;
    private removeMember;
    protected removeTypeReference(type: NamedType): void;
    protected removeInnerElements(): void;
    protected hasNonExtensionInnerElements(): boolean;
    protected removeReferenceRecursive(ref: OutputTypeReferencer): void;
    protected removeInnerElementsExtensions(): void;
}
export declare class EnumType extends BaseNamedType<OutputTypeReferencer, EnumType> {
    readonly kind: "EnumType";
    readonly astDefinitionKind = Kind.ENUM_TYPE_DEFINITION;
    private _values;
    get values(): readonly EnumValue[];
    value(name: string): EnumValue | undefined;
    addValue(value: EnumValue): EnumValue;
    addValue(name: string): EnumValue;
    protected removeTypeReference(type: NamedType): void;
    private removeValueInternal;
    protected removeInnerElements(): void;
    protected hasNonExtensionInnerElements(): boolean;
    protected removeReferenceRecursive(ref: OutputTypeReferencer): void;
    protected removeInnerElementsExtensions(): void;
}
export declare class InputObjectType extends BaseNamedType<InputTypeReferencer, InputObjectType> {
    readonly kind: "InputObjectType";
    readonly astDefinitionKind = Kind.INPUT_OBJECT_TYPE_DEFINITION;
    private readonly _fields;
    private _cachedFieldsArray?;
    fields(): InputFieldDefinition[];
    field(name: string): InputFieldDefinition | undefined;
    addField(field: InputFieldDefinition): InputFieldDefinition;
    addField(name: string, type?: Type): InputFieldDefinition;
    hasFields(): boolean;
    allChildElements(): Generator<NamedSchemaElement<any, any, any>, void, undefined>;
    protected removeTypeReference(type: NamedType): void;
    protected removeInnerElements(): void;
    private removeFieldInternal;
    protected hasNonExtensionInnerElements(): boolean;
    protected removeReferenceRecursive(ref: InputTypeReferencer): void;
    protected removeInnerElementsExtensions(): void;
}
declare class BaseWrapperType<T extends Type> {
    protected _type: T;
    protected constructor(_type: T);
    schema(): Schema;
    isAttached(): boolean;
    get ofType(): T;
    baseType(): NamedType;
}
export declare class ListType<T extends Type> extends BaseWrapperType<T> {
    readonly kind: "ListType";
    constructor(type: T);
    toString(): string;
}
export declare class NonNullType<T extends NullableType> extends BaseWrapperType<T> {
    readonly kind: "NonNullType";
    constructor(type: T);
    toString(): string;
}
export declare class FieldDefinition<TParent extends CompositeType> extends NamedSchemaElementWithType<OutputType, FieldDefinition<TParent>, TParent, never> {
    readonly isBuiltIn: boolean;
    readonly kind: "FieldDefinition";
    private _args;
    private _extension?;
    constructor(name: string, isBuiltIn?: boolean);
    protected isElementBuiltIn(): boolean;
    get coordinate(): string;
    hasArguments(): boolean;
    arguments(): readonly ArgumentDefinition<FieldDefinition<TParent>>[];
    argument(name: string): ArgumentDefinition<FieldDefinition<TParent>> | undefined;
    addArgument(arg: ArgumentDefinition<FieldDefinition<TParent>>): ArgumentDefinition<FieldDefinition<TParent>>;
    addArgument(name: string, type?: Type, defaultValue?: any): ArgumentDefinition<FieldDefinition<TParent>>;
    ofExtension(): Extension<TParent> | undefined;
    removeOfExtension(): void;
    setOfExtension(extension: Extension<TParent> | undefined): void;
    isIntrospectionField(): boolean;
    isSchemaIntrospectionField(): boolean;
    private removeArgumentInternal;
    private removeParent;
    isDeprecated(): boolean;
    remove(): never[];
    removeRecursive(): void;
    toString(): string;
}
export declare class InputFieldDefinition extends NamedSchemaElementWithType<InputType, InputFieldDefinition, InputObjectType, never> {
    readonly kind: "InputFieldDefinition";
    private _extension?;
    defaultValue?: any;
    get coordinate(): string;
    isRequired(): boolean;
    ofExtension(): Extension<InputObjectType> | undefined;
    removeOfExtension(): void;
    setOfExtension(extension: Extension<InputObjectType> | undefined): void;
    isDeprecated(): boolean;
    remove(): never[];
    removeRecursive(): void;
    toString(): string;
}
export declare class ArgumentDefinition<TParent extends FieldDefinition<any> | DirectiveDefinition> extends NamedSchemaElementWithType<InputType, ArgumentDefinition<TParent>, TParent, never> {
    readonly kind: "ArgumentDefinition";
    defaultValue?: any;
    constructor(name: string);
    get coordinate(): string;
    isRequired(): boolean;
    isDeprecated(): boolean;
    remove(): never[];
    toString(): string;
}
export declare class EnumValue extends NamedSchemaElement<EnumValue, EnumType, never> {
    readonly kind: "EnumValue";
    private _extension?;
    get coordinate(): string;
    ofExtension(): Extension<EnumType> | undefined;
    removeOfExtension(): void;
    setOfExtension(extension: Extension<EnumType> | undefined): void;
    isDeprecated(): boolean;
    remove(): never[];
    protected removeTypeReference(type: NamedType): void;
    toString(): string;
}
export declare class DirectiveDefinition<TApplicationArgs extends {
    [key: string]: any;
} = {
    [key: string]: any;
}> extends NamedSchemaElement<DirectiveDefinition<TApplicationArgs>, Schema, Directive> {
    readonly isBuiltIn: boolean;
    readonly kind: "DirectiveDefinition";
    private _args?;
    repeatable: boolean;
    private readonly _locations;
    private _referencers?;
    constructor(name: string, isBuiltIn?: boolean);
    get coordinate(): string;
    arguments(): readonly ArgumentDefinition<DirectiveDefinition>[];
    argument(name: string): ArgumentDefinition<DirectiveDefinition> | undefined;
    addArgument(arg: ArgumentDefinition<DirectiveDefinition>): ArgumentDefinition<DirectiveDefinition>;
    addArgument(name: string, type?: InputType, defaultValue?: any): ArgumentDefinition<DirectiveDefinition>;
    private removeArgumentInternal;
    get locations(): readonly DirectiveLocation[];
    addLocations(...locations: DirectiveLocation[]): DirectiveDefinition;
    addAllLocations(): DirectiveDefinition;
    addAllTypeLocations(): DirectiveDefinition;
    removeLocations(...locations: DirectiveLocation[]): DirectiveDefinition;
    hasExecutableLocations(): boolean;
    hasTypeSystemLocations(): boolean;
    applications(): ReadonlySet<Directive<SchemaElement<any, any>, TApplicationArgs>>;
    private addReferencer;
    private removeReferencer;
    protected removeTypeReference(type: NamedType): void;
    remove(): Directive[];
    removeRecursive(): void;
    toAST(): DirectiveDefinitionNode;
    toString(): string;
}
export declare class Directive<TParent extends SchemaElement<any, any> | DirectiveTargetElement<any> = SchemaElement<any, any>, TArgs extends {
    [key: string]: any;
} = {
    [key: string]: any;
}> extends Element<TParent> implements Named {
    readonly name: string;
    private _args;
    private _extension?;
    constructor(name: string, _args?: TArgs);
    schema(): Schema;
    get definition(): DirectiveDefinition | undefined;
    arguments(includeDefaultValues?: boolean): Readonly<TArgs>;
    private onModification;
    private isAttachedToSchemaElement;
    setArguments(args: TArgs): void;
    argumentType(name: string): InputType | undefined;
    matchArguments(expectedArgs: Record<string, any>): boolean;
    ofExtension(): Extension<any> | undefined;
    removeOfExtension(): void;
    setOfExtension(extension: Extension<any> | undefined): void;
    argumentsToAST(): ConstArgumentNode[] | undefined;
    remove(): boolean;
    private removeInternal;
    toString(): string;
}
export declare function directivesToString(directives?: readonly Directive<any>[]): string;
export declare function directivesToDirectiveNodes(directives?: readonly Directive<any>[]): ConstDirectiveNode[] | undefined;
export declare function sameDirectiveApplication(application1: Directive<any, any>, application2: Directive<any, any>, directivesNeverEqualToThemselves?: string[]): boolean;
export declare function sameDirectiveApplications(applications1: readonly Directive<any, any>[], applications2: readonly Directive<any, any>[], directivesNeverEqualToThemselves?: string[]): boolean;
export declare function isDirectiveApplicationsSubset(applications: readonly Directive<any, any>[], maybeSubset: readonly Directive<any, any>[]): boolean;
export declare function directiveApplicationsSubstraction(baseApplications: readonly Directive<any, any>[], toRemove: readonly Directive<any, any>[]): Directive<any, any>[];
export declare class Variable {
    readonly name: string;
    constructor(name: string);
    toVariableNode(): VariableNode;
    toString(): string;
}
export type Variables = readonly Variable[];
export declare class VariableCollector {
    private readonly _variables;
    add(variable: Variable): void;
    addAll(variables: Variables): void;
    collectInArguments(args: {
        [key: string]: any;
    }): void;
    variables(): Variable[];
    toString(): string;
}
export declare function isVariable(v: any): v is Variable;
export declare class VariableDefinition extends DirectiveTargetElement<VariableDefinition> {
    readonly variable: Variable;
    readonly type: InputType;
    readonly defaultValue?: any;
    constructor(schema: Schema, variable: Variable, type: InputType, defaultValue?: any);
    toVariableDefinitionNode(): VariableDefinitionNode;
    toString(): string;
}
export declare class VariableDefinitions {
    private readonly _definitions;
    add(definition: VariableDefinition): boolean;
    addAll(definitions: VariableDefinitions): void;
    definition(variable: Variable | string): VariableDefinition | undefined;
    isEmpty(): boolean;
    definitions(): readonly VariableDefinition[];
    filter(variables: Variables): VariableDefinitions;
    toVariableDefinitionNodes(): readonly VariableDefinitionNode[] | undefined;
    toString(): string;
}
export declare function variableDefinitionsFromAST(schema: Schema, definitionNodes: readonly VariableDefinitionNode[]): VariableDefinitions;
export declare function variableDefinitionFromAST(schema: Schema, definitionNode: VariableDefinitionNode): VariableDefinition;
export declare function newNamedType(kind: NamedTypeKind, name: string): NamedType;
export declare function copyDirectiveDefinitionToSchema({ definition, schema, copyDirectiveApplicationsInArguments, locationFilter, }: {
    definition: DirectiveDefinition;
    schema: Schema;
    copyDirectiveApplicationsInArguments: boolean;
    locationFilter?: (loc: DirectiveLocation) => boolean;
}): void;
export declare function isFieldDefinition(elem: SchemaElement<any, any>): elem is FieldDefinition<any>;
export {};
//# sourceMappingURL=definitions.d.ts.map