import { Injectable, LoggerService as NestLoggerService } from '@nestjs/common';

@Injectable()
export class LoggerService implements NestLoggerService {
  log(message: any, context?: string) {
    console.log(`[${context || 'Application'}] ${message}`);
  }

  error(message: any, trace?: string, context?: string) {
    console.error(`[${context || 'Application'}] ${message}`, trace);
  }

  warn(message: any, context?: string) {
    console.warn(`[${context || 'Application'}] ${message}`);
  }

  debug(message: any, context?: string) {
    console.debug(`[${context || 'Application'}] ${message}`);
  }

  verbose(message: any, context?: string) {
    console.log(`[VERBOSE][${context || 'Application'}] ${message}`);
  }
}
