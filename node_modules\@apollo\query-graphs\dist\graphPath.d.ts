import { NamedType, OperationElement, Schema, SchemaRootKind, SelectionSet, ObjectType, DeferDirectiveArgs, Type } from "@apollo/federation-internals";
import { OpPathTree } from "./pathTree";
import { Vertex, QueryGraph, Edge, RootVertex } from "./querygraph";
import { Transition } from "./transition";
import { PathContext } from "./pathContext";
export type ContextAtUsageEntry = {
    contextId: string;
    relativePath: string[];
    selectionSet: SelectionSet;
    subgraphArgType: Type;
};
export declare class GraphPath<TTrigger, RV extends Vertex = Vertex, TNullEdge extends null | never = never> implements Iterable<[Edge | TNullEdge, TTrigger, OpPathTree | null, Set<string> | null, Map<string, ContextAtUsageEntry> | null]> {
    private readonly props;
    private constructor();
    get graph(): QueryGraph;
    get root(): RV;
    get tail(): Vertex;
    get deferOnTail(): DeferDirectiveArgs | undefined;
    get subgraphEnteringEdge(): {
        index: number;
        edge: Edge;
        cost: number;
    } | undefined;
    static create<TTrigger, RV extends Vertex = Vertex, TNullEdge extends null | never = never>(graph: QueryGraph, root: RV): GraphPath<TTrigger, RV, TNullEdge>;
    static fromGraphRoot<TTrigger, TNullEdge extends null | never = never>(graph: QueryGraph, rootKind: SchemaRootKind): RootPath<TTrigger, TNullEdge> | undefined;
    get size(): number;
    countSubgraphJumpsAfterLastCommonVertex(that: GraphPath<TTrigger, RV, TNullEdge>): {
        thisJumps: number;
        thatJumps: number;
    };
    private findLastCommonVertex;
    private subgraphJumpsAtIdx;
    subgraphJumps(): number;
    isEquivalentSaveForTypeExplosionTo(that: GraphPath<TTrigger, RV, TNullEdge>): boolean;
    [Symbol.iterator](): PathIterator<TTrigger, TNullEdge>;
    lastEdge(): Edge | TNullEdge | undefined;
    lastTrigger(): TTrigger | undefined;
    tailPossibleRuntimeTypes(): readonly ObjectType[];
    lastIsIntefaceObjectFakeDownCastAfterEnteringSubgraph(): boolean;
    private lastIsInterfaceObjectFakeDownCast;
    add(trigger: TTrigger, edge: Edge | TNullEdge, conditionsResolution: ConditionResolution, defer?: DeferDirectiveArgs): GraphPath<TTrigger, RV, TNullEdge>;
    private mergeEdgeConditionsWithResolution;
    concat(tailPath: GraphPath<TTrigger, Vertex, TNullEdge>): GraphPath<TTrigger, RV, TNullEdge>;
    checkDirectPathFromPreviousSubgraphTo(typeName: string, triggerToEdge: (graph: QueryGraph, vertex: Vertex, t: TTrigger, overrideConditions: Map<string, boolean>) => Edge | null | undefined, overrideConditions: Map<string, boolean>, prevSubgraphStartingVertex?: Vertex): Vertex | undefined;
    nextEdges(): readonly Edge[];
    isTerminal(): boolean;
    isRootPath(): this is RootPath<TTrigger, TNullEdge>;
    mapMainPath<T>(mapper: (e: Edge | TNullEdge, pathIdx: number) => T): T[];
    private edgeAt;
    reduceMainPath<T>(reducer: (accumulator: T, edge: Edge | TNullEdge, pathIdx: number) => T, initialValue: T): T;
    hasJustCycled(): boolean;
    hasAnyEdgeConditions(): boolean;
    isOnTopLevelQueryRoot(): boolean;
    truncateTrailingDowncasts(): GraphPath<TTrigger, RV, TNullEdge>;
    markOverridding(otherOptions: GraphPath<TTrigger, RV, TNullEdge>[][]): {
        thisPath: GraphPath<TTrigger, RV, TNullEdge>;
        otherOptions: GraphPath<TTrigger, RV, TNullEdge>[][];
    };
    isOverriddenBy(otherPath: GraphPath<TTrigger, RV, TNullEdge>): boolean;
    tailIsInterfaceObject(): boolean;
    toString(): string;
}
export interface PathIterator<TTrigger, TNullEdge extends null | never = never> extends Iterator<[Edge | TNullEdge, TTrigger, OpPathTree | null, Set<string> | null, Map<string, ContextAtUsageEntry> | null]> {
    currentIndex: number;
    currentVertex: Vertex;
}
export type RootPath<TTrigger, TNullEdge extends null | never = never> = GraphPath<TTrigger, RootVertex, TNullEdge>;
export type OpTrigger = OperationElement | PathContext;
export type OpGraphPath<RV extends Vertex = Vertex> = GraphPath<OpTrigger, RV, null>;
export type OpRootPath = OpGraphPath<RootVertex>;
export declare function isRootPath(path: OpGraphPath<any>): path is OpRootPath;
export declare function terminateWithNonRequestedTypenameField<V extends Vertex>(path: OpGraphPath<V>, overrideConditions: Map<string, boolean>): OpGraphPath<V>;
export declare function traversePath(path: GraphPath<any>, onEdges: (edge: Edge) => void): void;
export type ConditionResolver = (edge: Edge, context: PathContext, excludedDestinations: ExcludedDestinations, excludedConditions: ExcludedConditions, extraConditions?: SelectionSet) => ConditionResolution;
type ContextMapEntry = {
    levelsInDataPath: number;
    levelsInQueryPath: number;
    pathTree?: OpPathTree;
    selectionSet: SelectionSet;
    inboundEdge: Edge;
    paramName: string;
    argType: Type;
    id: string;
};
export type ConditionResolution = {
    satisfied: boolean;
    cost: number;
    pathTree?: OpPathTree;
    contextMap?: Map<string, ContextMapEntry>;
    unsatisfiedConditionReason?: UnsatisfiedConditionReason;
};
export declare enum UnsatisfiedConditionReason {
    NO_POST_REQUIRE_KEY = 0,
    NO_CONTEXT_SET = 1
}
export declare const noConditionsResolution: ConditionResolution;
export declare const unsatisfiedConditionsResolution: ConditionResolution;
export declare enum UnadvanceableReason {
    UNSATISFIABLE_KEY_CONDITION = 0,
    UNSATISFIABLE_REQUIRES_CONDITION = 1,
    UNRESOLVABLE_INTERFACE_OBJECT = 2,
    NO_MATCHING_TRANSITION = 3,
    UNREACHABLE_TYPE = 4,
    IGNORED_INDIRECT_PATH = 5,
    UNSATISFIABLE_OVERRIDE_CONDITION = 6
}
export type Unadvanceable = {
    sourceSubgraph: string;
    destSubgraph: string;
    reason: UnadvanceableReason;
    details: string;
};
export declare class Unadvanceables {
    readonly reasons: Unadvanceable[];
    constructor(reasons: Unadvanceable[]);
    toString(): string;
}
export type UnadvanceableClosure = () => Unadvanceable | Unadvanceable[];
export declare class UnadvanceableClosures {
    private _unadvanceables;
    readonly closures: UnadvanceableClosure[];
    constructor(closures: UnadvanceableClosure | UnadvanceableClosure[]);
    toUnadvanceables(): Unadvanceables;
}
export declare function isUnadvanceableClosures(result: any[] | UnadvanceableClosures): result is UnadvanceableClosures;
export declare class TransitionPathWithLazyIndirectPaths<V extends Vertex = Vertex> {
    readonly path: GraphPath<Transition, V>;
    readonly conditionResolver: ConditionResolver;
    readonly overrideConditions: Map<string, boolean>;
    private lazilyComputedIndirectPaths;
    constructor(path: GraphPath<Transition, V>, conditionResolver: ConditionResolver, overrideConditions: Map<string, boolean>);
    static initial<V extends Vertex = Vertex>(initialPath: GraphPath<Transition, V>, conditionResolver: ConditionResolver, overrideConditions: Map<string, boolean>): TransitionPathWithLazyIndirectPaths<V>;
    indirectOptions(): IndirectPaths<Transition, V>;
    private computeIndirectPaths;
    toString(): string;
}
export declare function advancePathWithTransition<V extends Vertex>(subgraphPath: TransitionPathWithLazyIndirectPaths<V>, transition: Transition, targetType: NamedType, overrideConditions: Map<string, boolean>): TransitionPathWithLazyIndirectPaths<V>[] | UnadvanceableClosures;
export type ExcludedDestinations = readonly string[];
export declare function sameExcludedDestinations(ex1: ExcludedDestinations, ex2: ExcludedDestinations): boolean;
export type ExcludedConditions = readonly SelectionSet[];
export declare function addConditionExclusion(excluded: ExcludedConditions, newExclusion: SelectionSet | undefined): ExcludedConditions;
export type IndirectPaths<TTrigger, V extends Vertex = Vertex, TNullEdge extends null | never = never, TDeadEnds extends UnadvanceableClosures | never = UnadvanceableClosures> = {
    paths: GraphPath<TTrigger, V, TNullEdge>[];
    deadEnds: TDeadEnds;
};
export declare function getLocallySatisfiableKey(graph: QueryGraph, typeVertex: Vertex): SelectionSet | undefined;
export type SimultaneousPaths<V extends Vertex = Vertex> = OpGraphPath<V>[];
type OpIndirectPaths<V extends Vertex> = IndirectPaths<OpTrigger, V, null, never>;
export declare class SimultaneousPathsWithLazyIndirectPaths<V extends Vertex = Vertex> {
    readonly paths: SimultaneousPaths<V>;
    readonly context: PathContext;
    readonly conditionResolver: ConditionResolver;
    readonly excludedNonCollectingEdges: ExcludedDestinations;
    readonly excludedConditionsOnNonCollectingEdges: ExcludedConditions;
    readonly overrideConditions: Map<string, boolean>;
    private lazilyComputedIndirectPaths;
    constructor(paths: SimultaneousPaths<V>, context: PathContext, conditionResolver: ConditionResolver, excludedNonCollectingEdges: ExcludedDestinations, excludedConditionsOnNonCollectingEdges: ExcludedConditions, overrideConditions: Map<string, boolean>);
    indirectOptions(updatedContext: PathContext, pathIdx: number): OpIndirectPaths<V>;
    private computeIndirectPaths;
    toString(): string;
}
export declare function simultaneousPathsToString(simultaneousPaths: SimultaneousPaths<any> | SimultaneousPathsWithLazyIndirectPaths<any>, indentOnNewLine?: string): string;
export declare function advanceOptionsToString(options: (SimultaneousPaths<any> | SimultaneousPathsWithLazyIndirectPaths<any> | GraphPath<any>)[] | undefined): string;
export declare function advanceSimultaneousPathsWithOperation<V extends Vertex>(supergraphSchema: Schema, subgraphSimultaneousPaths: SimultaneousPathsWithLazyIndirectPaths<V>, operation: OperationElement, overrideConditions: Map<string, boolean>): SimultaneousPathsWithLazyIndirectPaths<V>[] | undefined;
export declare function createInitialOptions<V extends Vertex>(initialPath: OpGraphPath<V>, initialContext: PathContext, conditionResolver: ConditionResolver, excludedEdges: ExcludedDestinations, excludedConditions: ExcludedConditions, overrideConditions: Map<string, boolean>): SimultaneousPathsWithLazyIndirectPaths<V>[];
export {};
//# sourceMappingURL=graphPath.d.ts.map