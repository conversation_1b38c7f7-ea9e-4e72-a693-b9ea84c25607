import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TaxConfiguration } from '@app/database/entities/tax-configuration.entity';
import { Employee } from '@app/database/entities/employee.entity';
import { Country } from '@app/common/enums/status.enum';
import { TaxType, FilingStatus } from '@app/common/enums/status.enum';

export interface TaxCalculationInput {
  grossIncome: number;
  preTaxDeductions: number;
  filingStatus: FilingStatus;
  exemptions: number;
  state?: string;
  locality?: string;
  country: string;
}

export interface TaxCalculationResult {
  taxType: TaxType;
  taxableIncome: number;
  taxAmount: number;
  effectiveRate: number;
  marginalRate: number;
  brackets: Array<{
    min: number;
    max: number;
    rate: number;
    amount: number;
  }>;
}

export interface ComprehensiveTaxResult {
  totalTaxAmount: number;
  totalTaxableIncome: number;
  effectiveRate: number;
  taxes: TaxCalculationResult[];
  deductions: Array<{
    type: string;
    amount: number;
    description: string;
  }>;
}

@Injectable()
export class TaxCalculationService {
  private readonly logger = new Logger(TaxCalculationService.name);

  constructor(
    @InjectRepository(TaxConfiguration)
    private readonly taxConfigurationRepository: Repository<TaxConfiguration>,
    @InjectRepository(Employee)
    private readonly employeeRepository: Repository<Employee>,
  ) {}

  async calculateEmployeeTaxes(
    tenantId: string,
    employeeId: string,
    input: TaxCalculationInput
  ): Promise<ComprehensiveTaxResult> {
    this.logger.log(`Calculating taxes for employee ${employeeId} in tenant ${tenantId}`);

    // Get employee details
    const employee = await this.employeeRepository.findOne({
      where: { id: employeeId, tenantId },
    });

    if (!employee) {
      throw new Error(`Employee ${employeeId} not found`);
    }

    // Get applicable tax configurations
    const taxConfigurations = await this.taxConfigurationRepository.find({
      where: {
        tenantId,
        country: input.country as any,
        state: input.state || undefined,
        locality: input.locality || undefined,
        isActive: true,
      },
    });

    const result: ComprehensiveTaxResult = {
      totalTaxAmount: 0,
      totalTaxableIncome: input.grossIncome - input.preTaxDeductions,
      effectiveRate: 0,
      taxes: [],
      deductions: [],
    };

    // Calculate each tax type
    for (const taxConfig of taxConfigurations) {
      const taxResult = await this.calculateTaxForConfiguration(taxConfig, input);
      result.taxes.push(taxResult);
      result.totalTaxAmount += taxResult.taxAmount;
    }

    // Calculate effective rate
    if (result.totalTaxableIncome > 0) {
      result.effectiveRate = (result.totalTaxAmount / result.totalTaxableIncome) * 100;
    }

    // Add standard deductions
    result.deductions.push({
      type: 'pre_tax',
      amount: input.preTaxDeductions,
      description: 'Pre-tax deductions (benefits, retirement contributions, etc.)',
    });

    this.logger.log(`Calculated total tax amount: ${result.totalTaxAmount} for employee ${employeeId}`);
    return result;
  }

  async calculateTaxForConfiguration(
    taxConfig: TaxConfiguration,
    input: TaxCalculationInput
  ): Promise<TaxCalculationResult> {
    const taxableIncome = this.calculateTaxableIncome(taxConfig, input);
    
    const result: TaxCalculationResult = {
      taxType: taxConfig.taxType,
      taxableIncome,
      taxAmount: 0,
      effectiveRate: 0,
      marginalRate: 0,
      brackets: [],
    };

    if (taxableIncome <= 0) {
      return result;
    }

    // Calculate tax using brackets
    if (taxConfig.taxBrackets && taxConfig.taxBrackets.length > 0) {
      result.taxAmount = this.calculateProgressiveTax(taxConfig.taxBrackets, taxableIncome, result);
    } else {
      // Flat rate calculation using taxRate
      result.taxAmount = taxableIncome * (taxConfig.taxRate / 100);
      result.marginalRate = taxConfig.taxRate;
    }

    // Apply minimum and maximum tax using annualCap
    if (taxConfig.annualCap && result.taxAmount > taxConfig.annualCap) {
      result.taxAmount = taxConfig.annualCap;
    }

    // Calculate effective rate
    if (taxableIncome > 0) {
      result.effectiveRate = (result.taxAmount / taxableIncome) * 100;
    }

    return result;
  }

  private calculateTaxableIncome(taxConfig: TaxConfiguration, input: TaxCalculationInput): number {
    let taxableIncome = input.grossIncome;

    // Apply pre-tax deductions
    taxableIncome -= input.preTaxDeductions;

    // Apply standard deduction
    if (taxConfig.standardDeduction) {
      const standardDeduction = this.getStandardDeduction(taxConfig, input.filingStatus);
      taxableIncome -= standardDeduction;
    }

    // Apply personal exemptions using exemptions configuration
    if (taxConfig.exemptions && input.filingStatus) {
      let exemptionAmount = 0;
      switch (input.filingStatus) {
        case FilingStatus.SINGLE:
          exemptionAmount = taxConfig.exemptions.single || 0;
          break;
        case FilingStatus.MARRIED_FILING_JOINTLY:
          exemptionAmount = taxConfig.exemptions.marriedFilingJointly || 0;
          break;
        case FilingStatus.MARRIED_FILING_SEPARATELY:
          exemptionAmount = taxConfig.exemptions.marriedFilingSeparately || 0;
          break;
        case FilingStatus.HEAD_OF_HOUSEHOLD:
          exemptionAmount = taxConfig.exemptions.headOfHousehold || 0;
          break;
      }
      exemptionAmount += (taxConfig.exemptions.dependents || 0) * input.exemptions;
      taxableIncome -= exemptionAmount;
    }

    return Math.max(0, taxableIncome);
  }

  private calculateProgressiveTax(
    brackets: any[],
    taxableIncome: number,
    result: TaxCalculationResult
  ): number {
    let totalTax = 0;
    let remainingIncome = taxableIncome;

    for (const bracket of brackets.sort((a, b) => a.min - b.min)) {
      if (remainingIncome <= 0) break;

      const bracketMin = bracket.min;
      const bracketMax = bracket.max || Infinity;
      const bracketRate = bracket.rate;

      if (taxableIncome <= bracketMin) continue;

      const taxableInBracket = Math.min(remainingIncome, bracketMax - bracketMin);
      const taxInBracket = taxableInBracket * (bracketRate / 100);

      totalTax += taxInBracket;
      remainingIncome -= taxableInBracket;

      // Track bracket details
      result.brackets.push({
        min: bracketMin,
        max: bracketMax === Infinity ? taxableIncome : bracketMax,
        rate: bracketRate,
        amount: taxInBracket,
      });

      // Set marginal rate (rate of the highest bracket used)
      if (taxableIncome > bracketMin) {
        result.marginalRate = bracketRate;
      }
    }

    return totalTax;
  }

  private getStandardDeduction(taxConfig: TaxConfiguration, filingStatus: FilingStatus): number {
    if (!taxConfig.standardDeduction) return 0;

    // standardDeduction is a number, not an object
    return taxConfig.standardDeduction;
  }

  private isDeductionApplicable(deduction: any, input: TaxCalculationInput): boolean {
    // Check income limits
    if (deduction.incomeLimit && input.grossIncome > deduction.incomeLimit) {
      return false;
    }

    // Check filing status requirements
    if (deduction.filingStatus && !deduction.filingStatus.includes(input.filingStatus)) {
      return false;
    }

    // Check other conditions
    if (deduction.conditions) {
      for (const condition of deduction.conditions) {
        if (!this.evaluateCondition(condition, input)) {
          return false;
        }
      }
    }

    return true;
  }

  private evaluateCondition(condition: any, input: TaxCalculationInput): boolean {
    // Simple condition evaluation - can be extended
    switch (condition.type) {
      case 'income_range':
        return input.grossIncome >= condition.min && input.grossIncome <= condition.max;
      case 'filing_status':
        return condition.values.includes(input.filingStatus);
      case 'exemptions_min':
        return input.exemptions >= condition.value;
      default:
        return true;
    }
  }

  async getEstimatedAnnualTax(
    tenantId: string,
    employeeId: string,
    annualSalary: number
  ): Promise<ComprehensiveTaxResult> {
    const employee = await this.employeeRepository.findOne({
      where: { id: employeeId, tenantId },
      relations: ['addresses'],
    });

    if (!employee) {
      throw new Error(`Employee ${employeeId} not found`);
    }

    // Use default values for estimation
    const input: TaxCalculationInput = {
      grossIncome: annualSalary,
      preTaxDeductions: annualSalary * 0.1, // Assume 10% pre-tax deductions
      filingStatus: FilingStatus.SINGLE, // Default filing status
      exemptions: 1,
      country: employee.addresses?.find(addr => addr.isPrimary)?.country || Country.US,
      state: employee.addresses?.find(addr => addr.isPrimary)?.state,
    };

    return this.calculateEmployeeTaxes(tenantId, employeeId, input);
  }

  // Removed duplicate method - implementation below is the correct one

  async validateTaxConfiguration(taxConfig: TaxConfiguration): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
  }> {
    const result = {
      isValid: true,
      errors: [] as string[],
      warnings: [] as string[],
    };

    // Validate brackets
    if (taxConfig.taxBrackets && taxConfig.taxBrackets.length > 0) {
      const brackets = taxConfig.taxBrackets.sort((a: any, b: any) => a.min - b.min);
      
      for (let i = 0; i < brackets.length; i++) {
        const bracket = brackets[i];
        
        // Check bracket structure
        if (bracket.minIncome < 0) {
          result.errors.push(`Bracket ${i + 1}: Minimum income cannot be negative`);
          result.isValid = false;
        }
        
        if (bracket.maxIncome && bracket.maxIncome <= bracket.minIncome) {
          result.errors.push(`Bracket ${i + 1}: Maximum income must be greater than minimum`);
          result.isValid = false;
        }
        
        if (bracket.rate < 0 || bracket.rate > 100) {
          result.errors.push(`Bracket ${i + 1}: Tax rate must be between 0 and 100`);
          result.isValid = false;
        }
        
        // Check for gaps between brackets
        if (i > 0 && brackets[i - 1].maxIncome && brackets[i - 1].maxIncome < bracket.minIncome) {
          result.warnings.push(`Gap between bracket ${i} and ${i + 1}`);
        }
        
        // Check for overlaps
        if (i > 0 && brackets[i - 1].maxIncome && brackets[i - 1].maxIncome > bracket.minIncome) {
          result.errors.push(`Overlap between bracket ${i} and ${i + 1}`);
          result.isValid = false;
        }
      }
    }

    // Validate flat rate
    if (taxConfig.taxRate && (taxConfig.taxRate < 0 || taxConfig.taxRate > 100)) {
      result.errors.push('Flat rate must be between 0 and 100');
      result.isValid = false;
    }

    // Validate minimum/maximum tax
    if (taxConfig.annualCap && taxConfig.annualCap < 0) {
      result.errors.push('Annual cap cannot be negative');
      result.isValid = false;
    }

    // Validate effective dates
    if (taxConfig.effectiveStartDate && taxConfig.effectiveEndDate && taxConfig.effectiveStartDate > taxConfig.effectiveEndDate) {
      result.errors.push('Effective date cannot be after expiry date');
      result.isValid = false;
    }

    return result;
  }

  async calculateQuarterlyEstimate(
    tenantId: string,
    employeeId: string,
    quarterlyIncome: number
  ): Promise<ComprehensiveTaxResult> {
    // Calculate annual estimate and divide by 4
    const annualEstimate = await this.getEstimatedAnnualTax(tenantId, employeeId, quarterlyIncome * 4);
    
    return {
      ...annualEstimate,
      totalTaxAmount: annualEstimate.totalTaxAmount / 4,
      totalTaxableIncome: annualEstimate.totalTaxableIncome / 4,
      taxes: annualEstimate.taxes.map(tax => ({
        ...tax,
        taxAmount: tax.taxAmount / 4,
        taxableIncome: tax.taxableIncome / 4,
        brackets: tax.brackets.map(bracket => ({
          ...bracket,
          amount: bracket.amount / 4,
        })),
      })),
      deductions: annualEstimate.deductions.map(deduction => ({
        ...deduction,
        amount: deduction.amount / 4,
      })),
    };
  }
}
