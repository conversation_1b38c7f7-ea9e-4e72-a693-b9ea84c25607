import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  ParseUUI<PERSON>ipe,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
} from '@nestjs/swagger';

import { JwtAuthGuard, RolesGuard, TenantGuard } from '@app/security';
import { Roles, CurrentUser, CurrentTenant, UserRole } from '@app/common';
import { EmployeeService } from './services/employee.service';
import { CreateEmployeeDto, UpdateEmployeeDto, EmployeeQueryDto } from './dto';

@ApiTags('Employees')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard, TenantGuard, RolesGuard)
@Controller('employees')
export class EmployeeController {
  constructor(private readonly employeeService: EmployeeService) {}

  @Post()
  @Roles(UserRole.ADMIN, UserRole.HR)
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ 
    summary: 'Create new employee',
    description: 'Create a new employee record in the system'
  })
  @ApiResponse({ 
    status: 201, 
    description: 'Employee created successfully'
  })
  @ApiResponse({ 
    status: 400, 
    description: 'Invalid input data'
  })
  @ApiResponse({ 
    status: 403, 
    description: 'Insufficient permissions'
  })
  async create(
    @Body() createEmployeeDto: CreateEmployeeDto,
    @CurrentUser() user: any,
    @CurrentTenant() tenantId: string,
  ) {
    return this.employeeService.create(createEmployeeDto, user.id, tenantId);
  }

  @Get()
  @Roles(UserRole.ADMIN, UserRole.HR, UserRole.MANAGER)
  @ApiOperation({ 
    summary: 'Get all employees',
    description: 'Retrieve a paginated list of employees with optional filtering'
  })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'department', required: false, type: String })
  @ApiQuery({ name: 'position', required: false, type: String })
  @ApiQuery({ name: 'status', required: false, type: String })
  @ApiResponse({ 
    status: 200, 
    description: 'Employees retrieved successfully'
  })
  async findAll(
    @Query() query: EmployeeQueryDto,
    @CurrentTenant() tenantId: string,
  ) {
    return this.employeeService.findAll(query, tenantId);
  }

  @Get(':id')
  @Roles(UserRole.ADMIN, UserRole.HR, UserRole.MANAGER, UserRole.EMPLOYEE)
  @ApiOperation({ 
    summary: 'Get employee by ID',
    description: 'Retrieve detailed information about a specific employee'
  })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiResponse({ 
    status: 200, 
    description: 'Employee found successfully'
  })
  @ApiResponse({ 
    status: 404, 
    description: 'Employee not found'
  })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() user: any,
    @CurrentTenant() tenantId: string,
  ) {
    return this.employeeService.findOne(id, user, tenantId);
  }

  @Put(':id')
  @Roles(UserRole.ADMIN, UserRole.HR)
  @ApiOperation({ 
    summary: 'Update employee',
    description: 'Update employee information'
  })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiResponse({ 
    status: 200, 
    description: 'Employee updated successfully'
  })
  @ApiResponse({ 
    status: 404, 
    description: 'Employee not found'
  })
  @ApiResponse({ 
    status: 403, 
    description: 'Insufficient permissions'
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateEmployeeDto: UpdateEmployeeDto,
    @CurrentUser() user: any,
    @CurrentTenant() tenantId: string,
  ) {
    return this.employeeService.update(id, updateEmployeeDto, user.id, tenantId);
  }

  @Delete(':id')
  @Roles(UserRole.ADMIN, UserRole.HR)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ 
    summary: 'Delete employee',
    description: 'Soft delete an employee record'
  })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiResponse({ 
    status: 204, 
    description: 'Employee deleted successfully'
  })
  @ApiResponse({ 
    status: 404, 
    description: 'Employee not found'
  })
  @ApiResponse({ 
    status: 403, 
    description: 'Insufficient permissions'
  })
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() user: any,
    @CurrentTenant() tenantId: string,
  ) {
    return this.employeeService.remove(id, user.id, tenantId);
  }

  @Post(':id/activate')
  @Roles(UserRole.ADMIN, UserRole.HR)
  @ApiOperation({ 
    summary: 'Activate employee',
    description: 'Activate a deactivated employee'
  })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiResponse({ 
    status: 200, 
    description: 'Employee activated successfully'
  })
  async activate(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() user: any,
    @CurrentTenant() tenantId: string,
  ) {
    return this.employeeService.activate(id, user.id, tenantId);
  }

  @Post(':id/deactivate')
  @Roles(UserRole.ADMIN, UserRole.HR)
  @ApiOperation({ 
    summary: 'Deactivate employee',
    description: 'Deactivate an active employee'
  })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiResponse({ 
    status: 200, 
    description: 'Employee deactivated successfully'
  })
  async deactivate(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() user: any,
    @CurrentTenant() tenantId: string,
  ) {
    return this.employeeService.deactivate(id, user.id, tenantId);
  }
}
