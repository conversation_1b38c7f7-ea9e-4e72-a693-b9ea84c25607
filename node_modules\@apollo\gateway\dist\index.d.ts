import { GraphQLSchema } from 'graphql';
import { buildOperationContext } from './operationContext';
import { executeQueryPlan, ServiceMap } from './executeQueryPlan';
import { GraphQLDataSource } from './datasources/types';
import { QueryPlanner } from '@apollo/query-planner';
import { ServiceEndpointDefinition, Experimental_DidFailCompositionCallback, Experimental_DidResolveQueryPlanCallback, Experimental_DidUpdateSupergraphCallback, Experimental_UpdateComposition, CompositionInfo, GatewayConfig, SupergraphManager } from './config';
import { IntrospectAndCompose, UplinkSupergraphManager, LocalCompose } from './supergraphManagers';
import { ServiceDefinition } from '@apollo/federation-internals';
import { GatewayInterface, GatewayUnsubscriber, GatewayGraphQLRequestContext, GatewayExecutionResult } from '@apollo/server-gateway-interface';
type DataSourceMap = {
    [serviceName: string]: {
        url?: string;
        dataSource: GraphQLDataSource;
    };
};
export declare const HEALTH_CHECK_QUERY = "query __ApolloServiceHealthCheck__ { __typename }";
export declare const SERVICE_DEFINITION_QUERY = "query __ApolloGetServiceDefinition__ { _service { sdl } }";
type GatewayState = {
    phase: 'initialized';
} | {
    phase: 'failed to load';
} | {
    phase: 'loaded';
} | {
    phase: 'stopping';
    stoppingDonePromise: Promise<void>;
} | {
    phase: 'stopped';
} | {
    phase: 'updating schema';
};
interface ApolloConfigFromAS2Or3 {
    key?: string;
    keyHash?: string;
    graphRef?: string;
    graphId?: string;
    graphVariant?: string;
}
interface GraphQLServiceEngineConfig {
    apiKeyHash: string;
    graphId: string;
    graphVariant?: string;
}
export declare class ApolloGateway implements GatewayInterface {
    schema?: GraphQLSchema;
    private apiSchema?;
    private serviceMap;
    private config;
    private logger;
    private queryPlanStore;
    private apolloConfig?;
    private onSchemaChangeListeners;
    private onSchemaLoadOrUpdateListeners;
    private warnedStates;
    private queryPlanner?;
    private supergraphSdl?;
    private supergraphSchema?;
    private compositionId?;
    private state;
    private _supergraphManager?;
    private experimental_didResolveQueryPlan?;
    private experimental_didUpdateSupergraph?;
    private recursiveSelectionsLimitDisabled;
    private nonLocalSelectionsLimitDisabled;
    private pollIntervalInMs?;
    private toDispose;
    constructor(config?: GatewayConfig);
    get supergraphManager(): SupergraphManager | undefined;
    private initQueryPlanStore;
    private validateConfigAndEmitWarnings;
    load(options?: {
        apollo?: ApolloConfigFromAS2Or3;
        engine?: GraphQLServiceEngineConfig;
    }): Promise<{
        schema: GraphQLSchema;
        executor: (requestContext: GatewayGraphQLRequestContext<Record<string, any>>) => Promise<GatewayExecutionResult>;
    }>;
    private getIdForSupergraphSdl;
    private initializeSupergraphManager;
    private externalSupergraphUpdateCallback;
    private externalSubgraphHealthCheckCallback;
    private externalGetDataSourceCallback;
    private updateWithSupergraphSdl;
    private updateWithSchemaAndNotify;
    serviceHealthCheck(serviceMap?: DataSourceMap): Promise<{
        name: string;
        response: import("@apollo/server-gateway-interface").GatewayGraphQLResponse;
    }[]>;
    private serviceListFromSupergraphSdl;
    private createSchemaFromSupergraphSdl;
    onSchemaChange(callback: (schema: GraphQLSchema) => void): GatewayUnsubscriber;
    onSchemaLoadOrUpdate(callback: (schemaContext: {
        apiSchema: GraphQLSchema;
        coreSupergraphSdl: string;
    }) => void): GatewayUnsubscriber;
    private getOrCreateDataSource;
    private createDataSource;
    private createServices;
    private maybeWarnOnConflictingConfig;
    executor: (requestContext: GatewayGraphQLRequestContext) => Promise<GatewayExecutionResult>;
    private validateIncomingRequest;
    private performCleanupAndLogErrors;
    stop(): Promise<void>;
    __testing(): {
        state: GatewayState;
        compositionId: string | undefined;
        supergraphSdl: string | undefined;
        queryPlanner: QueryPlanner | undefined;
    };
}
export { executeQueryPlan, buildOperationContext, ServiceMap, Experimental_DidFailCompositionCallback, Experimental_DidResolveQueryPlanCallback, Experimental_DidUpdateSupergraphCallback, Experimental_UpdateComposition, GatewayConfig, ServiceEndpointDefinition, ServiceDefinition, CompositionInfo, IntrospectAndCompose, LocalCompose, UplinkSupergraphManager, };
export * from './datasources';
export { SupergraphSdlUpdateFunction, SubgraphHealthCheckFunction, GetDataSourceFunction, SupergraphSdlHook, SupergraphManager, } from './config';
export { UplinkFetcherError, FailureToFetchSupergraphSdlAfterInit, FailureToFetchSupergraphSdlDuringInit, FailureToFetchSupergraphSdlFunctionParams, } from './supergraphManagers';
//# sourceMappingURL=index.d.ts.map