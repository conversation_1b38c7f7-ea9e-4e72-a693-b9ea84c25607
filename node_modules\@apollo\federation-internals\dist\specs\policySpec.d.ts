import { CorePurpose, FeatureDefinition, FeatureDefinitions, FeatureVersion } from "./coreSpec";
export declare enum PolicyTypeName {
    POLICY = "Policy"
}
export declare class PolicySpecDefinition extends FeatureDefinition {
    static readonly directiveName = "policy";
    static readonly identity: string;
    constructor(version: FeatureVersion);
    get defaultCorePurpose(): CorePurpose;
}
export declare const POLICY_VERSIONS: FeatureDefinitions<PolicySpecDefinition>;
//# sourceMappingURL=policySpec.d.ts.map