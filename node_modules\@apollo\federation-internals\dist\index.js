"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.coreFeatureDefinitionIfKnown = void 0;
__exportStar(require("./definitions"), exports);
__exportStar(require("./buildSchema"), exports);
__exportStar(require("./print"), exports);
__exportStar(require("./values"), exports);
__exportStar(require("./federation"), exports);
__exportStar(require("./types"), exports);
__exportStar(require("./operations"), exports);
__exportStar(require("./utils"), exports);
__exportStar(require("./debug"), exports);
__exportStar(require("./specs/coreSpec"), exports);
__exportStar(require("./specs/joinSpec"), exports);
__exportStar(require("./specs/tagSpec"), exports);
__exportStar(require("./specs/inaccessibleSpec"), exports);
__exportStar(require("./specs/federationSpec"), exports);
__exportStar(require("./specs/contextSpec"), exports);
__exportStar(require("./supergraphs"), exports);
__exportStar(require("./error"), exports);
__exportStar(require("./schemaUpgrader"), exports);
__exportStar(require("./suggestions"), exports);
__exportStar(require("./graphQLJSSchemaToAST"), exports);
__exportStar(require("./directiveAndTypeSpecification"), exports);
var knownCoreFeatures_1 = require("./knownCoreFeatures");
Object.defineProperty(exports, "coreFeatureDefinitionIfKnown", { enumerable: true, get: function () { return knownCoreFeatures_1.coreFeatureDefinitionIfKnown; } });
__exportStar(require("./argumentCompositionStrategies"), exports);
__exportStar(require("./specs/authenticatedSpec"), exports);
__exportStar(require("./specs/requiresScopesSpec"), exports);
__exportStar(require("./specs/policySpec"), exports);
__exportStar(require("./specs/connectSpec"), exports);
__exportStar(require("./specs/costSpec"), exports);
//# sourceMappingURL=index.js.map