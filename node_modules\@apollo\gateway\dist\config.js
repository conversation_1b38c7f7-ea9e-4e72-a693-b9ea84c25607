"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isStaticConfig = exports.isManagedConfig = exports.isManuallyManagedConfig = exports.isSupergraphManagerConfig = exports.isSupergraphSdlHookConfig = exports.isStaticSupergraphSdlConfig = exports.isServiceListConfig = exports.isLocalConfig = exports.isManuallyManagedSupergraphSdlGatewayConfig = exports.isServiceDefinitionUpdate = exports.isSupergraphSdlUpdate = void 0;
const supergraphManagers_1 = require("./supergraphManagers");
function isSupergraphSdlUpdate(update) {
    return 'supergraphSdl' in update;
}
exports.isSupergraphSdlUpdate = isSupergraphSdlUpdate;
function isServiceDefinitionUpdate(update) {
    return 'isNewSchema' in update;
}
exports.isServiceDefinitionUpdate = isServiceDefinitionUpdate;
function isManuallyManagedSupergraphSdlGatewayConfig(config) {
    return isSupergraphSdlHookConfig(config) || isSupergraphManagerConfig(config);
}
exports.isManuallyManagedSupergraphSdlGatewayConfig = isManuallyManagedSupergraphSdlGatewayConfig;
function isLocalConfig(config) {
    return 'localServiceList' in config;
}
exports.isLocalConfig = isLocalConfig;
function isServiceListConfig(config) {
    return 'serviceList' in config;
}
exports.isServiceListConfig = isServiceListConfig;
function isStaticSupergraphSdlConfig(config) {
    return 'supergraphSdl' in config && typeof config.supergraphSdl === 'string';
}
exports.isStaticSupergraphSdlConfig = isStaticSupergraphSdlConfig;
function isSupergraphSdlHookConfig(config) {
    return ('supergraphSdl' in config && typeof config.supergraphSdl === 'function');
}
exports.isSupergraphSdlHookConfig = isSupergraphSdlHookConfig;
function isSupergraphManagerConfig(config) {
    return ('supergraphSdl' in config &&
        typeof config.supergraphSdl === 'object' &&
        'initialize' in config.supergraphSdl);
}
exports.isSupergraphManagerConfig = isSupergraphManagerConfig;
function isManuallyManagedConfig(config) {
    return (isManuallyManagedSupergraphSdlGatewayConfig(config) ||
        'experimental_updateServiceDefinitions' in config ||
        'experimental_updateSupergraphSdl' in config ||
        isServiceListConfig(config));
}
exports.isManuallyManagedConfig = isManuallyManagedConfig;
function isManagedConfig(config) {
    return ('schemaConfigDeliveryEndpoint' in config ||
        'uplinkEndpoints' in config ||
        'fallbackPollIntervalInMs' in config ||
        (isSupergraphManagerConfig(config) && config.supergraphSdl instanceof supergraphManagers_1.UplinkSupergraphManager) ||
        (!isLocalConfig(config) &&
            !isStaticSupergraphSdlConfig(config) &&
            !isManuallyManagedConfig(config)));
}
exports.isManagedConfig = isManagedConfig;
function isStaticConfig(config) {
    return isLocalConfig(config) || isStaticSupergraphSdlConfig(config);
}
exports.isStaticConfig = isStaticConfig;
//# sourceMappingURL=config.js.map