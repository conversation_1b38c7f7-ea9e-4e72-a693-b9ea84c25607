{"version": 3, "file": "querygraph.d.ts", "sourceRoot": "", "sources": ["../src/querygraph.ts"], "names": [], "mappings": "AAAA,OAAO,EAEL,QAAQ,EAMR,SAAS,EAET,MAAM,EACN,cAAc,EACd,IAAI,EAGJ,YAAY,EAQZ,mBAAmB,EAYnB,UAAU,EAIX,MAAM,8BAA8B,CAAC;AAEtC,OAAO,EAAqF,UAAU,EAAkE,MAAM,cAAc,CAAC;AAE7L,OAAO,EAAE,0BAA0B,EAAE,MAAM,gCAAgC,CAAC;AAK5E,eAAO,MAAM,2BAA2B,MAAoC,CAAC;AAG7E,wBAAgB,0BAA0B,CAAC,QAAQ,EAAE,cAAc,GAAG,MAAM,CAE3E;AAED,wBAAgB,wBAAwB,CAAC,IAAI,EAAE,SAAS,WAEvD;AAQD,qBAAa,MAAM;IAYf,QAAQ,CAAC,KAAK,EAAE,MAAM;IAEtB,QAAQ,CAAC,IAAI,EAAE,SAAS;IAKxB,QAAQ,CAAC,MAAM,EAAG,MAAM;IAlB1B,8BAA8B,EAAE,OAAO,CAAS;IAOhD,SAAS,EAAE,MAAM,GAAG,SAAS,CAAC;gBAInB,KAAK,EAAE,MAAM,EAEb,IAAI,EAAE,SAAS,EAKf,MAAM,EAAG,MAAM;IAG1B,QAAQ,IAAI,MAAM;CAInB;AAQD,qBAAa,UAAW,SAAQ,MAAM;IAElC,QAAQ,CAAC,QAAQ,EAAE,cAAc;gBAAxB,QAAQ,EAAE,cAAc,EACjC,KAAK,EAAE,MAAM,EACb,IAAI,EAAE,SAAS,EACf,MAAM,EAAG,MAAM;IAKjB,QAAQ,IAAI,MAAM;CAGnB;AAMD,wBAAgB,YAAY,CAAC,MAAM,EAAE,MAAM,GAAG,MAAM,IAAI,UAAU,CAEjE;AAED,MAAM,WAAW,iBAAiB;IAChC,KAAK,EAAE,MAAM,CAAC;IACd,SAAS,EAAE,OAAO,CAAC;CACpB;AAED,wBAAgB,sBAAsB,CACpC,iBAAiB,EAAE,iBAAiB,EACpC,iBAAiB,EAAE,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,GACtC,OAAO,CAGT;AAED,MAAM,MAAM,gBAAgB,GAAG;IAC7B,OAAO,EAAE,MAAM,CAAC;IAChB,YAAY,EAAE,MAAM,CAAC;IACrB,cAAc,EAAE,MAAM,CAAC;IACvB,SAAS,EAAE,MAAM,CAAC;IAClB,mBAAmB,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;IACjC,OAAO,EAAE,IAAI,CAAC;IACd,UAAU,EAAE,MAAM,CAAC;CACpB,CAAA;AAQD,qBAAa,IAAI;aAYG,KAAK,EAAE,MAAM;aAIb,IAAI,EAAE,MAAM;aAIZ,IAAI,EAAE,MAAM;aAQZ,UAAU,EAAE,UAAU;IAsB/B,iBAAiB,CAAC;IAjD3B,OAAO,CAAC,WAAW,CAAC,CAAe;IAE5B,gBAAgB,EAAE,gBAAgB,EAAE,CAAM;gBAS/B,KAAK,EAAE,MAAM,EAIb,IAAI,EAAE,MAAM,EAIZ,IAAI,EAAE,MAAM,EAQZ,UAAU,EAAE,UAAU,EActC,UAAU,CAAC,EAAE,YAAY,EAQlB,iBAAiB,CAAC,+BAAmB,EAM5C,gBAAgB,CAAC,EAAE,gBAAgB,EAAE;IAQvC,IAAI,UAAU,IAAI,YAAY,GAAG,SAAS,CAEzC;IAED,cAAc,CAAC,IAAI,EAAE,MAAM,GAAG,OAAO;IAIrC,2BAA2B,CAAC,eAAe,EAAE,UAAU,GAAG,OAAO;IAWjE,eAAe,IAAI,OAAO;IAI1B,KAAK,IAAI,MAAM;IAgBf,WAAW,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI;IAYlC,eAAe,CAAC,aAAa,EAAE,YAAY;IAM3C,sBAAsB,CAAC,iBAAiB,EAAE,gBAAgB,EAAE;IAI5D,yBAAyB,IAAI,OAAO;IAIpC,2BAA2B,CAAC,iBAAiB,EAAE,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC;IAQnE,QAAQ,IAAI,MAAM;CAGnB;AA4BD,qBAAa,UAAU;IAqCnB,QAAQ,CAAC,IAAI,EAAE,MAAM;IAErB,QAAQ,CAAC,QAAQ,EAAE,MAAM,EAAE;IAK3B,OAAO,CAAC,QAAQ,CAAC,SAAS;IAM1B,OAAO,CAAC,QAAQ,CAAC,eAAe;IAEhC,OAAO,CAAC,QAAQ,CAAC,YAAY;IAM7B,QAAQ,CAAC,OAAO,EAAE,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC;IAE7C,QAAQ,CAAC,cAAc,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC;IAE9C,QAAQ,CAAC,oBAAoB,EAAE,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAE/D,QAAQ,CAAC,MAAM,EAAE,MAAM;IA1CzB,QAAQ,CAAC,uBAAuB,EAAE,CAAC,IAAI,EAAE,IAAI,KAAK,SAAS,IAAI,EAAE,CAAC;IAMlE,QAAQ,CAAC,0BAA0B,EAAE,0BAA0B,GAAG,IAAI,CAAC;gBAS5D,IAAI,EAAE,MAAM,EAEZ,QAAQ,EAAE,MAAM,EAAE,EAKV,SAAS,EAAE,IAAI,EAAE,EAAE,EAMnB,eAAe,EAAE,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC,EAEzC,YAAY,EAAE,mBAAmB,CAAC,cAAc,EAAE,UAAU,CAAC,EAMrE,OAAO,EAAE,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC,EAEpC,cAAc,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,EAErC,oBAAoB,EAAE,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,EAEtD,MAAM,EAAE,MAAM,EAEvB,8BAA8B,CAAC,EAAE,OAAO;IAS1C,aAAa,IAAI,MAAM;IAKvB,UAAU,IAAI,MAAM;IAUpB,SAAS,IAAI,SAAS,cAAc,EAAE;IAOtC,KAAK,IAAI,SAAS,UAAU,EAAE;IAU9B,IAAI,CAAC,IAAI,EAAE,cAAc,GAAG,UAAU,GAAG,SAAS;IAgBlD,QAAQ,CAAC,MAAM,EAAE,MAAM,EAAE,gCAAgC,GAAE,OAAe,GAAG,SAAS,IAAI,EAAE;IAa5F,aAAa,CAAC,MAAM,EAAE,MAAM,GAAG,MAAM;IAarC,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS;IAI5D,WAAW,IAAI,QAAQ,CAAC,MAAM,CAAC;IAI9B,QAAQ,IAAI,QAAQ,CAAC,IAAI,CAAC;IAc3B,UAAU,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO;IAOnC,eAAe,CAAC,QAAQ,EAAE,MAAM,GAAG,MAAM,EAAE;CAI5C;AASD,qBAAa,eAAe,CAAC,WAAW,EAAE,SAAS,GAAG,SAAS;IAE7D,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAuC;IACtE,OAAO,CAAC,QAAQ,CAAC,iBAAiB,CAAkD;IAUpF,cAAc,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,WAAW;IAWjD,iBAAiB,CAAC,MAAM,EAAE,MAAM;IAYhC,cAAc,CAAC,MAAM,EAAE,MAAM,GAAG,WAAW,GAAG,SAAS;IAYvD,YAAY,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS;IAgBzC,eAAe,CAAC,IAAI,EAAE,IAAI;IAkB1B,YAAY,CAAC,IAAI,EAAE,IAAI,GAAG,SAAS,GAAG,SAAS;IAI/C,aAAa,CACX,YAAY,EAAE,CAAC,CAAC,EAAE,WAAW,KAAK,MAAM,EACxC,UAAU,EAAE,CAAC,CAAC,EAAE,SAAS,KAAK,MAAM,GACnC,MAAM;CAWV;AAiBD,wBAAgB,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,0BAA0B,CAAC,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,UAAU,CAE1H;AAwCD,wBAAgB,4BAA4B,CAAC,UAAU,EAAE,UAAU,GAAG,UAAU,CAgB/E;AAgBD,wBAAgB,wBAAwB,CAAC,UAAU,EAAE,UAAU,EAAE,gBAAgB,EAAE,OAAO,GAAG,UAAU,CAOtG;AAgjCD,wBAAgB,eAAe,CAC7B,KAAK,EAAE,UAAU,EACjB,QAAQ,EAAE,CAAC,CAAC,EAAE,MAAM,KAAK,IAAI,EAC7B,OAAO,EAAE,CAAC,CAAC,EAAE,IAAI,KAAK,OAAO,QAyB9B"}