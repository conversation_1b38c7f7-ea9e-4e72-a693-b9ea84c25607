import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { ColumnDef } from '@tanstack/react-table';
import {
  Plus,
  Search,
  Filter,
  Download,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Building,
  User,
} from 'lucide-react';
import { Link } from 'react-router-dom';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { DataTable } from '@/components/ui/data-table';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Header, PageSection } from '@/components/layout/page-container';
import { useToast } from '@/hooks/use-toast';
import { employeeService } from '@/services/employee-service';

interface Employee {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  position: {
    title: string;
    department: {
      name: string;
    };
  };
  employeeId: string;
  hireDate: string;
  status: 'active' | 'inactive' | 'terminated';
  profilePicture?: string;
  location?: string;
  manager?: {
    firstName: string;
    lastName: string;
  };
}

export default function EmployeesPage() {
  console.log('🚀 EmployeesPage component is rendering!');
  console.log('🌐 Current window location:', window.location.href);

  const [searchTerm, setSearchTerm] = useState('');
  const [departmentFilter, setDepartmentFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const { toast } = useToast();
  const queryClient = useQueryClient();

  console.log('🏢 EmployeesPage rendered with state:', {
    searchTerm,
    departmentFilter,
    statusFilter,
    currentPage,
    pageSize
  });

  const {
    data: employeesData,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['employees', currentPage, pageSize, searchTerm, departmentFilter, statusFilter],
    queryFn: () => {
      console.log('🚀 React Query executing employees query');
      return employeeService.getEmployees({
        page: currentPage,
        limit: pageSize,
        search: searchTerm,
        department: departmentFilter !== 'all' ? departmentFilter : undefined,
        status: statusFilter !== 'all' ? statusFilter : undefined,
      });
    },
  });

  console.log('📊 React Query state:', {
    isLoading,
    error: error?.message,
    hasData: !!employeesData,
    dataLength: employeesData?.data?.length
  });

  const { data: departments = [] } = useQuery({
    queryKey: ['departments'],
    queryFn: () => {
      console.log('🏢 React Query executing departments query');
      return employeeService.getDepartments();
    },
    onError: (error) => {
      console.error('❌ Departments query error:', error);
    },
  });

  const deleteEmployeeMutation = useMutation({
    mutationFn: employeeService.deleteEmployee,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['employees'] });
      toast({
        title: 'Success',
        description: 'Employee deleted successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete employee',
        variant: 'destructive',
      });
    },
  });

  const handleDeleteEmployee = (id: string) => {
    if (window.confirm('Are you sure you want to delete this employee?')) {
      deleteEmployeeMutation.mutate(id);
    }
  };

  const handleExport = () => {
    // Implementation for exporting employee data
    toast({
      title: 'Export Started',
      description: 'Employee data export has been initiated',
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="default">Active</Badge>;
      case 'inactive':
        return <Badge variant="secondary">Inactive</Badge>;
      case 'terminated':
        return <Badge variant="destructive">Terminated</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const columns: ColumnDef<Employee>[] = [
    {
      accessorKey: 'employee',
      header: 'Employee',
      cell: ({ row }) => {
        const employee = row.original;
        return (
          <div className="flex items-center space-x-4">
            <Avatar className="h-12 w-12 ring-2 ring-blue-100 ring-offset-2">
              <AvatarImage src={employee.profilePicture} />
              <AvatarFallback className="bg-gradient-to-br from-blue-500 to-blue-600 text-white font-semibold">
                {employee.firstName[0]}{employee.lastName[0]}
              </AvatarFallback>
            </Avatar>
            <div className="space-y-1">
              <div className="font-semibold text-gray-900 text-base">
                {employee.firstName} {employee.lastName}
              </div>
              <div className="text-sm text-gray-500 font-medium">
                ID: {employee.employeeId}
              </div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'email',
      header: 'Contact',
      cell: ({ row }) => {
        const employee = row.original;
        return (
          <div className="space-y-2">
            <div className="flex items-center space-x-3 p-2 rounded-lg bg-gray-50/50">
              <div className="p-1 bg-blue-100 rounded">
                <Mail className="h-4 w-4 text-blue-600" />
              </div>
              <span className="text-sm font-medium text-gray-900">{employee.email}</span>
            </div>
            {employee.phone && (
              <div className="flex items-center space-x-3 p-2 rounded-lg bg-gray-50/50">
                <div className="p-1 bg-green-100 rounded">
                  <Phone className="h-4 w-4 text-green-600" />
                </div>
                <span className="text-sm font-medium text-gray-900">{employee.phone}</span>
              </div>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'position',
      header: 'Position',
      cell: ({ row }) => {
        const employee = row.original;
        return (
          <div className="space-y-2">
            <div className="font-semibold text-gray-900">{employee.position.title}</div>
            <div className="flex items-center space-x-2 p-2 rounded-lg bg-purple-50/50">
              <div className="p-1 bg-purple-100 rounded">
                <Building className="h-4 w-4 text-purple-600" />
              </div>
              <span className="text-sm font-medium text-purple-900">{employee.position.department.name}</span>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'manager',
      header: 'Manager',
      cell: ({ row }) => {
        const employee = row.original;
        return employee.manager ? (
          <div className="flex items-center space-x-3 p-2 rounded-lg bg-orange-50/50">
            <div className="p-1 bg-orange-100 rounded">
              <User className="h-4 w-4 text-orange-600" />
            </div>
            <span className="text-sm font-medium text-orange-900">
              {employee.manager.firstName} {employee.manager.lastName}
            </span>
          </div>
        ) : (
          <span className="text-sm text-gray-400 italic">No manager assigned</span>
        );
      },
    },
    {
      accessorKey: 'hireDate',
      header: 'Hire Date',
      cell: ({ row }) => {
        const employee = row.original;
        return (
          <div className="flex items-center space-x-3 p-2 rounded-lg bg-emerald-50/50">
            <div className="p-1 bg-emerald-100 rounded">
              <Calendar className="h-4 w-4 text-emerald-600" />
            </div>
            <span className="text-sm font-medium text-emerald-900">
              {new Date(employee.hireDate).toLocaleDateString()}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: 'location',
      header: 'Location',
      cell: ({ row }) => {
        const employee = row.original;
        return employee.location ? (
          <div className="flex items-center space-x-2">
            <MapPin className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm">{employee.location}</span>
          </div>
        ) : (
          <span className="text-sm text-muted-foreground">Not specified</span>
        );
      },
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => {
        const employee = row.original;
        return getStatusBadge(employee.status);
      },
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => {
        const employee = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem asChild>
                <Link to={`/employees/${employee.id}`}>
                  <Eye className="mr-2 h-4 w-4" />
                  View Details
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link to={`/employees/${employee.id}/edit`}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Employee
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => handleDeleteEmployee(employee.id)}
                className="text-destructive"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete Employee
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  const filters = (
    <div className="flex items-center space-x-2">
      <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
        <SelectTrigger className="w-[180px]">
          <SelectValue placeholder="All Departments" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Departments</SelectItem>
          {departments?.map((dept: any) => (
            <SelectItem key={dept.id} value={dept.id}>
              {dept.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      <Select value={statusFilter} onValueChange={setStatusFilter}>
        <SelectTrigger className="w-[140px]">
          <SelectValue placeholder="All Status" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Status</SelectItem>
          <SelectItem value="active">Active</SelectItem>
          <SelectItem value="inactive">Inactive</SelectItem>
          <SelectItem value="terminated">Terminated</SelectItem>
        </SelectContent>
      </Select>
    </div>
  );

  const actions = (
    <div className="flex items-center space-x-2">
      <Button onClick={handleExport} variant="outline" size="sm">
        <Download className="h-4 w-4 mr-2" />
        Export
      </Button>
      <Button asChild>
        <Link to="/employees/new">
          <Plus className="h-4 w-4 mr-2" />
          Add Employee
        </Link>
      </Button>
    </div>
  );

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <h3 className="text-lg font-semibold mb-2">Failed to load employees</h3>
          <p className="text-muted-foreground">Please try refreshing the page</p>
        </div>
      </div>
    );
  }

  return (
    <PageContainer>
      <PageHeader
        title="Employees"
        description="Manage your organization's employees and their information."
      />

      <PageSection>
        <DataTable
          columns={columns}
          data={employeesData?.data || []}
          title="Employee Directory"
          description={`${employeesData?.total || 0} employees found`}
          searchPlaceholder="Search employees..."
          isLoading={isLoading}
          onRefresh={() => queryClient.invalidateQueries({ queryKey: ['employees'] })}
          onExport={handleExport}
          enableColumnVisibility
          enableSearch
          enablePagination
          pageSize={pageSize}
          totalCount={employeesData?.total}
          currentPage={currentPage}
          onPageChange={setCurrentPage}
          onPageSizeChange={setPageSize}
          filters={filters}
          actions={actions}
        />
      </PageSection>
    </PageContainer>
  );
}
