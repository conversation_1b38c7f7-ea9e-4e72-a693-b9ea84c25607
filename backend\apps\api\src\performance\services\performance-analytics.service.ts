import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';

import { PerformanceReview, ReviewStatus, OverallRating } from '@app/database/entities/performance-review.entity';
import { Goal, GoalStatus } from '@app/database/entities/goal.entity';
import { Feedback, FeedbackType } from '@app/database/entities/feedback.entity';
import { PerformanceMetric, MetricCategory } from '@app/database/entities/performance-metric.entity';
import { Employee } from '@app/database/entities/employee.entity';
import { Department } from '@app/database/entities/department.entity';
// TenantService removed - tenantId passed as parameter

export interface PerformanceDashboardData {
  overview: {
    totalEmployees: number;
    completedReviews: number;
    pendingReviews: number;
    averageRating: number;
    goalAchievementRate: number;
  };
  reviewDistribution: Array<{
    rating: OverallRating;
    count: number;
    percentage: number;
  }>;
  goalProgress: Array<{
    status: GoalStatus;
    count: number;
    percentage: number;
  }>;
  departmentPerformance: Array<{
    departmentId: string;
    departmentName: string;
    averageRating: number;
    completedReviews: number;
    totalEmployees: number;
    goalAchievementRate: number;
  }>;
  performanceTrends: Array<{
    period: string;
    averageRating: number;
    completedReviews: number;
    goalAchievements: number;
  }>;
  topPerformers: Array<{
    employeeId: string;
    employeeName: string;
    departmentName: string;
    averageRating: number;
    goalAchievementRate: number;
  }>;
  feedbackInsights: {
    totalFeedback: number;
    averageSentiment: number;
    feedbackByType: Array<{
      type: FeedbackType;
      count: number;
    }>;
  };
}

export interface EmployeePerformanceAnalytics {
  employee: {
    id: string;
    name: string;
    department: string;
    position: string;
  };
  currentPeriod: {
    overallRating: OverallRating | null;
    goalAchievementRate: number;
    completedGoals: number;
    totalGoals: number;
    feedbackReceived: number;
    averageFeedbackRating: number;
  };
  historicalTrends: Array<{
    period: string;
    rating: number;
    goalAchievements: number;
    feedbackCount: number;
  }>;
  competencyAnalysis: Array<{
    competency: string;
    currentRating: number;
    previousRating: number;
    trend: 'improving' | 'declining' | 'stable';
  }>;
  developmentAreas: string[];
  strengths: string[];
  careerProgression: {
    promotionReadiness: number;
    recommendedActions: string[];
  };
}

@Injectable()
export class PerformanceAnalyticsService {
  constructor(
    @InjectRepository(PerformanceReview)
    private performanceReviewRepository: Repository<PerformanceReview>,
    @InjectRepository(Goal)
    private goalRepository: Repository<Goal>,
    @InjectRepository(Feedback)
    private feedbackRepository: Repository<Feedback>,
    @InjectRepository(PerformanceMetric)
    private performanceMetricRepository: Repository<PerformanceMetric>,
    @InjectRepository(Employee)
    private employeeRepository: Repository<Employee>,
    @InjectRepository(Department)
    private departmentRepository: Repository<Department>,
  ) {}

  async getDashboardData(
    tenantId: string,
    dateFrom?: Date,
    dateTo?: Date,
  ): Promise<PerformanceDashboardData> {
    const startDate = dateFrom || new Date(new Date().getFullYear(), 0, 1); // Start of current year
    const endDate = dateTo || new Date();

    // Overview metrics
    const totalEmployees = await this.employeeRepository.count({
      where: { tenantId, isActive: true },
    });

    const completedReviews = await this.performanceReviewRepository.count({
      where: {
        tenantId,
        status: ReviewStatus.COMPLETED,
        completedDate: Between(startDate, endDate),
      },
    });

    const pendingReviews = await this.performanceReviewRepository.count({
      where: {
        tenantId,
        status: ReviewStatus.PENDING_SELF_ASSESSMENT,
      },
    });

    // Average rating calculation
    const reviewsWithRatings = await this.performanceReviewRepository.find({
      where: {
        tenantId,
        status: ReviewStatus.COMPLETED,
        completedDate: Between(startDate, endDate),
      },
      select: ['overallScore'],
    });

    const averageRating = reviewsWithRatings.length > 0
      ? reviewsWithRatings.reduce((sum, review) => sum + (review.overallScore || 0), 0) / reviewsWithRatings.length
      : 0;

    // Goal achievement rate
    const totalGoals = await this.goalRepository.count({
      where: {
        tenantId,
        dueDate: Between(startDate, endDate),
      },
    });

    const achievedGoals = await this.goalRepository.count({
      where: {
        tenantId,
        status: GoalStatus.ACHIEVED,
        dueDate: Between(startDate, endDate),
      },
    });

    const goalAchievementRate = totalGoals > 0 ? (achievedGoals / totalGoals) * 100 : 0;

    // Review distribution
    const reviewDistribution = await this.getReviewDistribution(startDate, endDate);

    // Goal progress
    const goalProgress = await this.getGoalProgress(startDate, endDate);

    // Department performance
    const departmentPerformance = await this.getDepartmentPerformance(startDate, endDate);

    // Performance trends
    const performanceTrends = await this.getPerformanceTrends(startDate, endDate);

    // Top performers
    const topPerformers = await this.getTopPerformers(startDate, endDate);

    // Feedback insights
    const feedbackInsights = await this.getFeedbackInsights(startDate, endDate);

    return {
      overview: {
        totalEmployees,
        completedReviews,
        pendingReviews,
        averageRating,
        goalAchievementRate,
      },
      reviewDistribution,
      goalProgress,
      departmentPerformance,
      performanceTrends,
      topPerformers,
      feedbackInsights,
    };
  }

  async getEmployeeAnalytics(
    employeeId: string,
    dateFrom?: Date,
    dateTo?: Date,
  ): Promise<EmployeePerformanceAnalytics> {
    const tenantId = this.tenantService.getCurrentTenantId();
    const startDate = dateFrom || new Date(new Date().getFullYear(), 0, 1);
    const endDate = dateTo || new Date();

    // Get employee details
    const employee = await this.employeeRepository.findOne({
      where: { id: employeeId, tenantId },
      relations: ['department', 'position'],
    });

    if (!employee) {
      throw new Error('Employee not found');
    }

    // Current period performance
    const currentReview = await this.performanceReviewRepository.findOne({
      where: {
        tenantId,
        employeeId,
        status: ReviewStatus.COMPLETED,
        completedDate: Between(startDate, endDate),
      },
      order: { completedDate: 'DESC' },
    });

    const employeeGoals = await this.goalRepository.find({
      where: {
        tenantId,
        employeeId,
        dueDate: Between(startDate, endDate),
      },
    });

    const completedGoals = employeeGoals.filter(goal => goal.status === GoalStatus.ACHIEVED).length;
    const goalAchievementRate = employeeGoals.length > 0 ? (completedGoals / employeeGoals.length) * 100 : 0;

    const feedbackReceived = await this.feedbackRepository.count({
      where: {
        tenantId,
        recipientId: employeeId,
        submittedAt: Between(startDate, endDate),
      },
    });

    // Calculate average feedback rating
    const feedbackWithRatings = await this.feedbackRepository.find({
      where: {
        tenantId,
        recipientId: employeeId,
        submittedAt: Between(startDate, endDate),
      },
      select: ['competencyRatings'],
    });

    let averageFeedbackRating = 0;
    if (feedbackWithRatings.length > 0) {
      const allRatings: number[] = [];
      feedbackWithRatings.forEach(feedback => {
        if (feedback.competencyRatings) {
          Object.values(feedback.competencyRatings).forEach((rating: any) => {
            if (rating.rating) allRatings.push(rating.rating);
          });
        }
      });
      averageFeedbackRating = allRatings.length > 0 
        ? allRatings.reduce((sum, rating) => sum + rating, 0) / allRatings.length 
        : 0;
    }

    // Historical trends (last 12 months)
    const historicalTrends = await this.getEmployeeHistoricalTrends(employeeId);

    // Competency analysis
    const competencyAnalysis = await this.getCompetencyAnalysis(employeeId);

    // Development areas and strengths from latest review
    const developmentAreas = currentReview?.improvementAreas || [];
    const strengths = currentReview?.strengths || [];

    // Career progression analysis
    const careerProgression = await this.getCareerProgression(employeeId);

    return {
      employee: {
        id: employee.id,
        name: `${employee.firstName} ${employee.lastName}`,
        department: employee.department?.name || 'N/A',
        position: employee.position?.title || 'N/A',
      },
      currentPeriod: {
        overallRating: currentReview?.overallRating || null,
        goalAchievementRate,
        completedGoals,
        totalGoals: employeeGoals.length,
        feedbackReceived,
        averageFeedbackRating,
      },
      historicalTrends,
      competencyAnalysis,
      developmentAreas,
      strengths,
      careerProgression,
    };
  }

  private async getReviewDistribution(startDate: Date, endDate: Date) {
    const tenantId = this.tenantService.getCurrentTenantId();
    
    const reviews = await this.performanceReviewRepository.find({
      where: {
        tenantId,
        status: ReviewStatus.COMPLETED,
        completedDate: Between(startDate, endDate),
      },
      select: ['overallRating'],
    });

    const distribution = Object.values(OverallRating).map(rating => ({
      rating,
      count: reviews.filter(review => review.overallRating === rating).length,
      percentage: 0,
    }));

    const total = reviews.length;
    distribution.forEach(item => {
      item.percentage = total > 0 ? (item.count / total) * 100 : 0;
    });

    return distribution;
  }

  private async getGoalProgress(startDate: Date, endDate: Date) {
    const tenantId = this.tenantService.getCurrentTenantId();
    
    const goals = await this.goalRepository.find({
      where: {
        tenantId,
        dueDate: Between(startDate, endDate),
      },
      select: ['status'],
    });

    const progress = Object.values(GoalStatus).map(status => ({
      status,
      count: goals.filter(goal => goal.status === status).length,
      percentage: 0,
    }));

    const total = goals.length;
    progress.forEach(item => {
      item.percentage = total > 0 ? (item.count / total) * 100 : 0;
    });

    return progress;
  }

  private async getDepartmentPerformance(startDate: Date, endDate: Date) {
    const tenantId = this.tenantService.getCurrentTenantId();
    
    const departments = await this.departmentRepository.find({
      where: { tenantId },
      relations: ['employees'],
    });

    const departmentPerformance = [];

    for (const department of departments) {
      const employeeIds = department.employees.map(emp => emp.id);
      
      if (employeeIds.length === 0) continue;

      const departmentReviews = await this.performanceReviewRepository.find({
        where: {
          tenantId,
          employeeId: employeeIds as any, // TypeORM In operator
          status: ReviewStatus.COMPLETED,
          completedDate: Between(startDate, endDate),
        },
        select: ['overallScore'],
      });

      const averageRating = departmentReviews.length > 0
        ? departmentReviews.reduce((sum, review) => sum + (review.overallScore || 0), 0) / departmentReviews.length
        : 0;

      const departmentGoals = await this.goalRepository.find({
        where: {
          tenantId,
          employeeId: employeeIds as any,
          dueDate: Between(startDate, endDate),
        },
      });

      const achievedGoals = departmentGoals.filter(goal => goal.status === GoalStatus.ACHIEVED).length;
      const goalAchievementRate = departmentGoals.length > 0 ? (achievedGoals / departmentGoals.length) * 100 : 0;

      departmentPerformance.push({
        departmentId: department.id,
        departmentName: department.name,
        averageRating,
        completedReviews: departmentReviews.length,
        totalEmployees: employeeIds.length,
        goalAchievementRate,
      });
    }

    return departmentPerformance;
  }

  private async getPerformanceTrends(startDate: Date, endDate: Date) {
    // Implementation for monthly performance trends
    // This would aggregate data by month for the given period
    return [];
  }

  private async getTopPerformers(startDate: Date, endDate: Date, limit: number = 10) {
    // Implementation for identifying top performers
    // Based on review ratings and goal achievements
    return [];
  }

  private async getFeedbackInsights(startDate: Date, endDate: Date) {
    const tenantId = this.tenantService.getCurrentTenantId();
    
    const totalFeedback = await this.feedbackRepository.count({
      where: {
        tenantId,
        submittedAt: Between(startDate, endDate),
      },
    });

    const feedbackByType = await Promise.all(
      Object.values(FeedbackType).map(async type => ({
        type,
        count: await this.feedbackRepository.count({
          where: {
            tenantId,
            feedbackType: type,
            submittedAt: Between(startDate, endDate),
          },
        }),
      }))
    );

    return {
      totalFeedback,
      averageSentiment: 0, // Would be calculated from AI sentiment analysis
      feedbackByType,
    };
  }

  private async getEmployeeHistoricalTrends(employeeId: string) {
    // Implementation for employee historical performance trends
    return [];
  }

  private async getCompetencyAnalysis(employeeId: string) {
    // Implementation for competency trend analysis
    return [];
  }

  private async getCareerProgression(employeeId: string) {
    // Implementation for career progression analysis
    return {
      promotionReadiness: 0,
      recommendedActions: [],
    };
  }
}
