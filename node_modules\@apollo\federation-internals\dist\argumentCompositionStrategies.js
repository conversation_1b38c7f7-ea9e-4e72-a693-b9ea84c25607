"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ARGUMENT_COMPOSITION_STRATEGIES = void 0;
const definitions_1 = require("./definitions");
const types_1 = require("./types");
const values_1 = require("./values");
function supportFixedTypes(types) {
    return (schema, type) => {
        const supported = types(schema);
        return supported.some((t) => (0, types_1.sameType)(t, type))
            ? { valid: true }
            : { valid: false, supportedMsg: `type(s) ${supported.join(', ')}` };
    };
}
function supportAnyNonNullArray() {
    return (_, type) => (0, definitions_1.isNonNullType)(type) && (0, definitions_1.isListType)(type.ofType)
        ? { valid: true }
        : { valid: false, supportedMsg: 'non nullable list types of any type' };
}
function supportAnyArray() {
    return (_, type) => (0, definitions_1.isListType)(type) || ((0, definitions_1.isNonNullType)(type) && (0, definitions_1.isListType)(type.ofType))
        ? { valid: true }
        : { valid: false, supportedMsg: 'list types of any type' };
}
function mergeNullableValues(mergeValues) {
    return (values) => {
        const nonNullValues = values.filter((v) => v !== null && v !== undefined);
        return nonNullValues.length > 0
            ? mergeValues(nonNullValues)
            : undefined;
    };
}
function unionValues(values) {
    return values.reduce((acc, next) => {
        const newValues = next.filter((v1) => !acc.some((v2) => (0, values_1.valueEquals)(v1, v2)));
        return acc.concat(newValues);
    }, []);
}
exports.ARGUMENT_COMPOSITION_STRATEGIES = {
    MAX: {
        name: 'MAX',
        isTypeSupported: supportFixedTypes((schema) => [new definitions_1.NonNullType(schema.intType())]),
        mergeValues: (values) => Math.max(...values),
    },
    MIN: {
        name: 'MIN',
        isTypeSupported: supportFixedTypes((schema) => [new definitions_1.NonNullType(schema.intType())]),
        mergeValues: (values) => Math.min(...values),
    },
    INTERSECTION: {
        name: 'INTERSECTION',
        isTypeSupported: supportAnyNonNullArray(),
        mergeValues: (values) => {
            var _a;
            return (_a = values.reduce((acc, next) => {
                if (acc === undefined) {
                    return next;
                }
                else {
                    return acc.filter((v1) => next.some((v2) => (0, values_1.valueEquals)(v1, v2)));
                }
            }, undefined)) !== null && _a !== void 0 ? _a : [];
        },
    },
    UNION: {
        name: 'UNION',
        isTypeSupported: supportAnyNonNullArray(),
        mergeValues: unionValues,
    },
    NULLABLE_AND: {
        name: 'NULLABLE_AND',
        isTypeSupported: supportFixedTypes((schema) => [
            schema.booleanType(),
            new definitions_1.NonNullType(schema.booleanType())
        ]),
        mergeValues: mergeNullableValues((values) => values.every((v) => v)),
    },
    NULLABLE_MAX: {
        name: 'NULLABLE_MAX',
        isTypeSupported: supportFixedTypes((schema) => [
            schema.intType(),
            new definitions_1.NonNullType(schema.intType())
        ]),
        mergeValues: mergeNullableValues((values) => Math.max(...values))
    },
    NULLABLE_UNION: {
        name: 'NULLABLE_UNION',
        isTypeSupported: supportAnyArray(),
        mergeValues: mergeNullableValues(unionValues),
    }
};
//# sourceMappingURL=argumentCompositionStrategies.js.map