"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoreFeature = exports.defaultSchemaBlueprint = exports.SchemaBlueprint = exports.NamedSchemaElementWithType = exports.NamedSchemaElement = exports.SchemaElement = exports.Extension = exports.sourceASTs = exports.DirectiveTargetElement = exports.isLeafType = exports.typeFromAST = exports.typeToAST = exports.isTypeSystemDirectiveLocation = exports.typeSystemDirectiveLocations = exports.isExecutableDirectiveLocation = exports.executableDirectiveLocations = exports.isConditionalDirective = exports.supertypes = exports.runtimeTypesIntersects = exports.possibleRuntimeTypes = exports.isCompositeType = exports.isAbstractType = exports.isNullableType = exports.baseType = exports.filterTypesOfKind = exports.isTypeOfKind = exports.isInputType = exports.isOutputType = exports.isInputObjectType = exports.isUnionType = exports.isEnumType = exports.isInterfaceType = exports.isObjectType = exports.isIDType = exports.isBooleanType = exports.isFloatType = exports.isStringType = exports.isIntType = exports.isCustomScalarType = exports.isScalarType = exports.isNonNullType = exports.isListType = exports.isWrapperType = exports.isNamedType = exports.isSchemaRootType = exports.defaultRootName = exports.allSchemaRootKinds = exports.typenameFieldName = exports.ErrGraphQLAPISchemaValidationFailed = exports.ErrGraphQLValidationFailed = void 0;
exports.isFieldDefinition = exports.copyDirectiveDefinitionToSchema = exports.newNamedType = exports.variableDefinitionFromAST = exports.variableDefinitionsFromAST = exports.VariableDefinitions = exports.VariableDefinition = exports.isVariable = exports.VariableCollector = exports.Variable = exports.directiveApplicationsSubstraction = exports.isDirectiveApplicationsSubset = exports.sameDirectiveApplications = exports.sameDirectiveApplication = exports.directivesToDirectiveNodes = exports.directivesToString = exports.Directive = exports.DirectiveDefinition = exports.EnumValue = exports.ArgumentDefinition = exports.InputFieldDefinition = exports.FieldDefinition = exports.NonNullType = exports.ListType = exports.InputObjectType = exports.EnumType = exports.UnionType = exports.UnionMember = exports.InterfaceType = exports.ObjectType = exports.InterfaceImplementation = exports.ScalarType = exports.SchemaDefinition = exports.RootType = exports.Schema = exports.CoreFeatures = void 0;
const graphql_1 = require("graphql");
const coreSpec_1 = require("./specs/coreSpec");
const utils_1 = require("./utils");
const values_1 = require("./values");
const inaccessibleSpec_1 = require("./specs/inaccessibleSpec");
const print_1 = require("./print");
const types_1 = require("./types");
const introspection_1 = require("./introspection");
const validate_1 = require("graphql/validation/validate");
const specifiedRules_1 = require("graphql/validation/specifiedRules");
const validate_2 = require("./validate");
const directiveAndTypeSpecification_1 = require("./directiveAndTypeSpecification");
const suggestions_1 = require("./suggestions");
const error_1 = require("./error");
const knownCoreFeatures_1 = require("./knownCoreFeatures");
const validationErrorCode = 'GraphQLValidationFailed';
const DEFAULT_VALIDATION_ERROR_MESSAGE = 'The schema is not a valid GraphQL schema.';
const EMPTY_SET = new Set();
const ErrGraphQLValidationFailed = (causes, message = DEFAULT_VALIDATION_ERROR_MESSAGE) => (0, error_1.aggregateError)(validationErrorCode, message, causes);
exports.ErrGraphQLValidationFailed = ErrGraphQLValidationFailed;
const apiSchemaValidationErrorCode = 'GraphQLAPISchemaValidationFailed';
const ErrGraphQLAPISchemaValidationFailed = (causes) => (0, error_1.aggregateError)(apiSchemaValidationErrorCode, 'The supergraph schema failed to produce a valid API schema', causes);
exports.ErrGraphQLAPISchemaValidationFailed = ErrGraphQLAPISchemaValidationFailed;
exports.typenameFieldName = '__typename';
exports.allSchemaRootKinds = ['query', 'mutation', 'subscription'];
function defaultRootName(rootKind) {
    return rootKind.charAt(0).toUpperCase() + rootKind.slice(1);
}
exports.defaultRootName = defaultRootName;
function checkDefaultSchemaRoot(type) {
    if (type.kind !== 'ObjectType') {
        return undefined;
    }
    switch (type.name) {
        case 'Query': return 'query';
        case 'Mutation': return 'mutation';
        case 'Subscription': return 'subscription';
        default: return undefined;
    }
}
function isSchemaRootType(type) {
    return isObjectType(type) && type.isRootType();
}
exports.isSchemaRootType = isSchemaRootType;
function isNamedType(type) {
    return type instanceof BaseNamedType;
}
exports.isNamedType = isNamedType;
function isWrapperType(type) {
    return isListType(type) || isNonNullType(type);
}
exports.isWrapperType = isWrapperType;
function isListType(type) {
    return type.kind == 'ListType';
}
exports.isListType = isListType;
function isNonNullType(type) {
    return type.kind == 'NonNullType';
}
exports.isNonNullType = isNonNullType;
function isScalarType(type) {
    return type.kind == 'ScalarType';
}
exports.isScalarType = isScalarType;
function isCustomScalarType(type) {
    return isScalarType(type) && !graphQLBuiltInTypes.includes(type.name);
}
exports.isCustomScalarType = isCustomScalarType;
function isIntType(type) {
    return type === type.schema().intType();
}
exports.isIntType = isIntType;
function isStringType(type) {
    return type === type.schema().stringType();
}
exports.isStringType = isStringType;
function isFloatType(type) {
    return type === type.schema().floatType();
}
exports.isFloatType = isFloatType;
function isBooleanType(type) {
    return type === type.schema().booleanType();
}
exports.isBooleanType = isBooleanType;
function isIDType(type) {
    return type === type.schema().idType();
}
exports.isIDType = isIDType;
function isObjectType(type) {
    return type.kind == 'ObjectType';
}
exports.isObjectType = isObjectType;
function isInterfaceType(type) {
    return type.kind == 'InterfaceType';
}
exports.isInterfaceType = isInterfaceType;
function isEnumType(type) {
    return type.kind == 'EnumType';
}
exports.isEnumType = isEnumType;
function isUnionType(type) {
    return type.kind == 'UnionType';
}
exports.isUnionType = isUnionType;
function isInputObjectType(type) {
    return type.kind == 'InputObjectType';
}
exports.isInputObjectType = isInputObjectType;
function isOutputType(type) {
    switch (baseType(type).kind) {
        case 'ScalarType':
        case 'ObjectType':
        case 'UnionType':
        case 'EnumType':
        case 'InterfaceType':
            return true;
        default:
            return false;
    }
}
exports.isOutputType = isOutputType;
function isInputType(type) {
    switch (baseType(type).kind) {
        case 'ScalarType':
        case 'EnumType':
        case 'InputObjectType':
            return true;
        default:
            return false;
    }
}
exports.isInputType = isInputType;
function isTypeOfKind(type, kind) {
    return type.kind === kind;
}
exports.isTypeOfKind = isTypeOfKind;
function filterTypesOfKind(types, kind) {
    return types.reduce((acc, type) => {
        if (isTypeOfKind(type, kind)) {
            acc.push(type);
        }
        return acc;
    }, []);
}
exports.filterTypesOfKind = filterTypesOfKind;
function baseType(type) {
    return isWrapperType(type) ? type.baseType() : type;
}
exports.baseType = baseType;
function isNullableType(type) {
    return !isNonNullType(type);
}
exports.isNullableType = isNullableType;
function isAbstractType(type) {
    return isInterfaceType(type) || isUnionType(type);
}
exports.isAbstractType = isAbstractType;
function isCompositeType(type) {
    return isObjectType(type) || isInterfaceType(type) || isUnionType(type);
}
exports.isCompositeType = isCompositeType;
function possibleRuntimeTypes(type) {
    switch (type.kind) {
        case 'InterfaceType': return type.possibleRuntimeTypes();
        case 'UnionType': return type.types();
        case 'ObjectType': return [type];
    }
}
exports.possibleRuntimeTypes = possibleRuntimeTypes;
function runtimeTypesIntersects(t1, t2) {
    if (t1 === t2) {
        return true;
    }
    const rt1 = possibleRuntimeTypes(t1);
    const rt2 = possibleRuntimeTypes(t2);
    for (const obj1 of rt1) {
        if (rt2.some(obj2 => obj1.name === obj2.name)) {
            return true;
        }
    }
    return false;
}
exports.runtimeTypesIntersects = runtimeTypesIntersects;
function supertypes(type) {
    switch (type.kind) {
        case 'InterfaceType': return type.interfaces();
        case 'UnionType': return [];
        case 'ObjectType': return type.interfaces().concat(type.unionsWhereMember());
    }
}
exports.supertypes = supertypes;
function isConditionalDirective(directive) {
    return ['include', 'skip'].includes(directive.name);
}
exports.isConditionalDirective = isConditionalDirective;
exports.executableDirectiveLocations = [
    graphql_1.DirectiveLocation.QUERY,
    graphql_1.DirectiveLocation.MUTATION,
    graphql_1.DirectiveLocation.SUBSCRIPTION,
    graphql_1.DirectiveLocation.FIELD,
    graphql_1.DirectiveLocation.FRAGMENT_DEFINITION,
    graphql_1.DirectiveLocation.FRAGMENT_SPREAD,
    graphql_1.DirectiveLocation.INLINE_FRAGMENT,
    graphql_1.DirectiveLocation.VARIABLE_DEFINITION,
];
const executableDirectiveLocationsSet = new Set(exports.executableDirectiveLocations);
function isExecutableDirectiveLocation(loc) {
    return executableDirectiveLocationsSet.has(loc);
}
exports.isExecutableDirectiveLocation = isExecutableDirectiveLocation;
exports.typeSystemDirectiveLocations = [
    graphql_1.DirectiveLocation.SCHEMA,
    graphql_1.DirectiveLocation.SCALAR,
    graphql_1.DirectiveLocation.OBJECT,
    graphql_1.DirectiveLocation.FIELD_DEFINITION,
    graphql_1.DirectiveLocation.ARGUMENT_DEFINITION,
    graphql_1.DirectiveLocation.INTERFACE,
    graphql_1.DirectiveLocation.UNION,
    graphql_1.DirectiveLocation.ENUM,
    graphql_1.DirectiveLocation.ENUM_VALUE,
    graphql_1.DirectiveLocation.INPUT_OBJECT,
    graphql_1.DirectiveLocation.INPUT_FIELD_DEFINITION,
];
const typeSystemDirectiveLocationsSet = new Set(exports.typeSystemDirectiveLocations);
function isTypeSystemDirectiveLocation(loc) {
    return typeSystemDirectiveLocationsSet.has(loc);
}
exports.isTypeSystemDirectiveLocation = isTypeSystemDirectiveLocation;
function typeToAST(type) {
    switch (type.kind) {
        case 'ListType':
            return {
                kind: graphql_1.Kind.LIST_TYPE,
                type: typeToAST(type.ofType)
            };
        case 'NonNullType':
            return {
                kind: graphql_1.Kind.NON_NULL_TYPE,
                type: typeToAST(type.ofType)
            };
        default:
            return {
                kind: graphql_1.Kind.NAMED_TYPE,
                name: { kind: graphql_1.Kind.NAME, value: type.name }
            };
    }
}
exports.typeToAST = typeToAST;
function typeFromAST(schema, node) {
    switch (node.kind) {
        case graphql_1.Kind.LIST_TYPE:
            return new ListType(typeFromAST(schema, node.type));
        case graphql_1.Kind.NON_NULL_TYPE:
            return new NonNullType(typeFromAST(schema, node.type));
        default:
            const type = schema.type(node.name.value);
            if (!type) {
                throw error_1.ERRORS.INVALID_GRAPHQL.err(`Unknown type "${node.name.value}"`, { nodes: node });
            }
            return type;
    }
}
exports.typeFromAST = typeFromAST;
function isLeafType(type) {
    return isScalarType(type) || isEnumType(type);
}
exports.isLeafType = isLeafType;
class DirectiveTargetElement {
    constructor(_schema, directives = []) {
        this._schema = _schema;
        this.appliedDirectives = directives.map((d) => this.attachDirective(d));
    }
    schema() {
        return this._schema;
    }
    attachDirective(directive) {
        const toAdd = directive.isAttached()
            ? new Directive(directive.name, directive.arguments())
            : directive;
        Element.prototype['setParent'].call(toAdd, this);
        return toAdd;
    }
    appliedDirectivesOf(nameOrDefinition) {
        const directiveName = typeof nameOrDefinition === 'string' ? nameOrDefinition : nameOrDefinition.name;
        return this.appliedDirectives.filter(d => d.name == directiveName);
    }
    hasAppliedDirective(nameOrDefinition) {
        const directiveName = typeof nameOrDefinition === 'string' ? nameOrDefinition : nameOrDefinition.name;
        return this.appliedDirectives.some(d => d.name == directiveName);
    }
    appliedDirectivesToDirectiveNodes() {
        return directivesToDirectiveNodes(this.appliedDirectives);
    }
    appliedDirectivesToString() {
        return directivesToString(this.appliedDirectives);
    }
    collectVariablesInAppliedDirectives(collector) {
        for (const applied of this.appliedDirectives) {
            collector.collectInArguments(applied.arguments());
        }
    }
}
exports.DirectiveTargetElement = DirectiveTargetElement;
function sourceASTs(...elts) {
    return elts.map(elt => elt === null || elt === void 0 ? void 0 : elt.sourceAST).filter((elt) => elt !== undefined);
}
exports.sourceASTs = sourceASTs;
class Element {
    schema() {
        const schema = this.schemaInternal();
        (0, utils_1.assert)(schema, 'requested schema does not exist. Probably because the element is unattached');
        return schema;
    }
    schemaInternal() {
        if (!this._parent) {
            return undefined;
        }
        else if (this._parent instanceof Schema) {
            return this._parent;
        }
        else if (this._parent instanceof SchemaElement) {
            return this._parent.schemaInternal();
        }
        else if (this._parent instanceof DirectiveTargetElement) {
            return this._parent.schema();
        }
        (0, utils_1.assert)(false, 'unreachable code. parent is of unknown type');
    }
    get parent() {
        (0, utils_1.assert)(this._parent, 'trying to access non-existent parent');
        return this._parent;
    }
    isAttached() {
        return !!this._parent;
    }
    setParent(parent) {
        (0, utils_1.assert)(!this._parent, "Cannot set parent of an already attached element");
        this._parent = parent;
        this.onAttached();
    }
    onAttached() {
    }
    checkUpdate() {
        (0, utils_1.assert)(this.isAttached(), () => `Cannot modify detached element ${this}`);
    }
}
class Extension {
    get extendedElement() {
        return this._extendedElement;
    }
    setExtendedElement(element) {
        (0, utils_1.assert)(!this._extendedElement, "Cannot attached already attached extension");
        this._extendedElement = element;
    }
}
exports.Extension = Extension;
class SchemaElement extends Element {
    addUnappliedDirective({ nameOrDef, args, extension, directive }) {
        const toAdd = {
            nameOrDef,
            args: args !== null && args !== void 0 ? args : {},
            extension,
            directive,
        };
        if (this._unappliedDirectives) {
            this._unappliedDirectives.push(toAdd);
        }
        else {
            this._unappliedDirectives = [toAdd];
        }
    }
    processUnappliedDirectives() {
        var _a;
        for (const { nameOrDef, args, extension, directive } of (_a = this._unappliedDirectives) !== null && _a !== void 0 ? _a : []) {
            const d = this.applyDirective(nameOrDef, args);
            d.setOfExtension(extension);
            d.sourceAST = directive;
        }
        this._unappliedDirectives = undefined;
    }
    get appliedDirectives() {
        var _a;
        return (_a = this._appliedDirectives) !== null && _a !== void 0 ? _a : [];
    }
    appliedDirectivesOf(nameOrDefinition) {
        const directiveName = typeof nameOrDefinition === 'string' ? nameOrDefinition : nameOrDefinition.name;
        return this.appliedDirectives.filter(d => d.name == directiveName);
    }
    hasAppliedDirective(nameOrDefinition) {
        return (typeof nameOrDefinition === 'string'
            ? this.appliedDirectivesOf(nameOrDefinition)
            : this.appliedDirectivesOf(nameOrDefinition)).length !== 0;
    }
    applyDirective(nameOrDef, args, asFirstDirective = false) {
        var _a;
        let toAdd;
        if (typeof nameOrDef === 'string') {
            this.checkUpdate();
            toAdd = new Directive(nameOrDef, args !== null && args !== void 0 ? args : Object.create(null));
            const def = (_a = this.schema().directive(nameOrDef)) !== null && _a !== void 0 ? _a : this.schema().blueprint.onMissingDirectiveDefinition(this.schema(), toAdd);
            if (!def) {
                throw this.schema().blueprint.onGraphQLJSValidationError(this.schema(), error_1.ERRORS.INVALID_GRAPHQL.err(`Unknown directive "@${nameOrDef}".`));
            }
            if (Array.isArray(def)) {
                throw (0, exports.ErrGraphQLValidationFailed)(def);
            }
        }
        else {
            this.checkUpdate(nameOrDef);
            toAdd = new Directive(nameOrDef.name, args !== null && args !== void 0 ? args : Object.create(null));
        }
        Element.prototype['setParent'].call(toAdd, this);
        if (this._appliedDirectives) {
            if (asFirstDirective) {
                this._appliedDirectives.unshift(toAdd);
            }
            else {
                this._appliedDirectives.push(toAdd);
            }
        }
        else {
            this._appliedDirectives = [toAdd];
        }
        DirectiveDefinition.prototype['addReferencer'].call(toAdd.definition, toAdd);
        this.onModification();
        return toAdd;
    }
    removeAppliedDirectives() {
        if (!this._appliedDirectives) {
            return;
        }
        const applied = this._appliedDirectives.concat();
        applied.forEach(d => d.remove());
    }
    onModification() {
        const schema = this.schemaInternal();
        if (schema) {
            Schema.prototype['onModification'].call(schema);
        }
    }
    isElementBuiltIn() {
        return false;
    }
    removeTypeReferenceInternal(type) {
        this.removeTypeReference(type);
    }
    checkRemoval() {
        (0, utils_1.assert)(!this.isElementBuiltIn() || Schema.prototype['canModifyBuiltIn'].call(this.schema()), () => `Cannot modify built-in ${this}`);
    }
    checkUpdate(addedElement) {
        super.checkUpdate();
        if (!Schema.prototype['canModifyBuiltIn'].call(this.schema())) {
            let thisElement = this;
            while (thisElement && thisElement instanceof SchemaElement) {
                (0, utils_1.assert)(!thisElement.isElementBuiltIn(), () => `Cannot modify built-in (or part of built-in) ${this}`);
                thisElement = thisElement.parent;
            }
        }
        if (addedElement && addedElement.isAttached()) {
            const thatSchema = addedElement.schema();
            (0, utils_1.assert)(!thatSchema || thatSchema === this.schema(), () => `Cannot add element ${addedElement} to ${this} as it is attached to another schema`);
        }
    }
}
exports.SchemaElement = SchemaElement;
class NamedSchemaElement extends SchemaElement {
    constructor(name) {
        super();
        this._name = name;
    }
    get name() {
        return this._name;
    }
}
exports.NamedSchemaElement = NamedSchemaElement;
class BaseNamedType extends NamedSchemaElement {
    constructor(name, isBuiltIn = false) {
        super(name);
        this.isBuiltIn = isBuiltIn;
        this.preserveEmptyDefinition = false;
    }
    addReferencer(referencer) {
        var _a;
        (_a = this._referencers) !== null && _a !== void 0 ? _a : (this._referencers = new Set());
        this._referencers.add(referencer);
    }
    removeReferencer(referencer) {
        var _a;
        (_a = this._referencers) === null || _a === void 0 ? void 0 : _a.delete(referencer);
    }
    get coordinate() {
        return this.name;
    }
    *allChildElements() {
    }
    extensions() {
        var _a;
        return (_a = this._extensions) !== null && _a !== void 0 ? _a : [];
    }
    hasExtension(extension) {
        var _a, _b;
        return (_b = (_a = this._extensions) === null || _a === void 0 ? void 0 : _a.includes(extension)) !== null && _b !== void 0 ? _b : false;
    }
    newExtension() {
        return this.addExtension(new Extension());
    }
    addExtension(extension) {
        this.checkUpdate();
        if (this.hasExtension(extension)) {
            return extension;
        }
        (0, utils_1.assert)(!extension.extendedElement, () => `Cannot add extension to type ${this}: it is already added to another type`);
        if (this._extensions) {
            this._extensions.push(extension);
        }
        else {
            this._extensions = [extension];
        }
        Extension.prototype['setExtendedElement'].call(extension, this);
        this.onModification();
        return extension;
    }
    removeExtensions() {
        if (!this._extensions) {
            return;
        }
        this._extensions = undefined;
        for (const directive of this.appliedDirectives) {
            directive.removeOfExtension();
        }
        this.removeInnerElementsExtensions();
    }
    isIntrospectionType() {
        return (0, introspection_1.isIntrospectionName)(this.name);
    }
    hasExtensionElements() {
        return !!this._extensions;
    }
    hasNonExtensionElements() {
        return this.preserveEmptyDefinition
            || this.appliedDirectives.some(d => d.ofExtension() === undefined)
            || this.hasNonExtensionInnerElements();
    }
    isElementBuiltIn() {
        return this.isBuiltIn;
    }
    rename(newName) {
        this.checkUpdate();
        const oldName = this._name;
        this._name = newName;
        Schema.prototype['renameTypeInternal'].call(this._parent, oldName, newName);
        this.onModification();
    }
    remove() {
        var _a;
        if (!this._parent) {
            return [];
        }
        this.checkRemoval();
        this.onModification();
        this.sourceAST = undefined;
        this.removeAppliedDirectives();
        this.removeInnerElements();
        const toReturn = [];
        (_a = this._referencers) === null || _a === void 0 ? void 0 : _a.forEach(r => {
            SchemaElement.prototype['removeTypeReferenceInternal'].call(r, this);
            toReturn.push(r);
        });
        this._referencers = undefined;
        Schema.prototype['removeTypeInternal'].call(this._parent, this);
        this._parent = undefined;
        return toReturn;
    }
    removeRecursive() {
        this.remove().forEach(ref => this.removeReferenceRecursive(ref));
    }
    referencers() {
        var _a;
        return (_a = this._referencers) !== null && _a !== void 0 ? _a : EMPTY_SET;
    }
    isReferenced() {
        return !!this._referencers;
    }
    toString() {
        return this.name;
    }
}
class NamedSchemaElementWithType extends NamedSchemaElement {
    get type() {
        return this._type;
    }
    set type(type) {
        if (type) {
            this.checkUpdate(type);
        }
        else {
            this.checkRemoval();
        }
        if (this._type) {
            removeReferenceToType(this, this._type);
        }
        this._type = type;
        if (type) {
            addReferenceToType(this, type);
        }
    }
    removeTypeReference(type) {
        (0, utils_1.assert)(this._type && baseType(this._type) === type, () => `Cannot remove reference to type ${type} on ${this} as its type is ${this._type}`);
        this._type = undefined;
    }
}
exports.NamedSchemaElementWithType = NamedSchemaElementWithType;
class BaseExtensionMember extends Element {
    ofExtension() {
        return this._extension;
    }
    removeOfExtension() {
        this._extension = undefined;
    }
    setOfExtension(extension) {
        var _a;
        this.checkUpdate();
        (0, utils_1.assert)(!extension || ((_a = this._parent) === null || _a === void 0 ? void 0 : _a.hasExtension(extension)), () => `Cannot set object as part of the provided extension: it is not an extension of parent ${this.parent}`);
        this._extension = extension;
    }
    remove() {
        this.removeInner();
        Schema.prototype['onModification'].call(this.schema());
        this._extension = undefined;
        this._parent = undefined;
    }
}
class SchemaBlueprint {
    onMissingDirectiveDefinition(_schema, _directive) {
        return undefined;
    }
    onDirectiveDefinitionAndSchemaParsed(_) {
        return [];
    }
    ignoreParsedField(_type, _fieldName) {
        return false;
    }
    onConstructed(_) {
    }
    onAddedCoreFeature(_schema, _feature) {
    }
    onInvalidation(_) {
    }
    onValidation(_schema) {
        return [];
    }
    validationRules() {
        return specifiedRules_1.specifiedSDLRules;
    }
    onGraphQLJSValidationError(schema, error) {
        var _a;
        const matcher = /^Unknown directive "@(?<directive>[_A-Za-z][_0-9A-Za-z]*)"\.$/.exec(error.message);
        const name = (_a = matcher === null || matcher === void 0 ? void 0 : matcher.groups) === null || _a === void 0 ? void 0 : _a.directive;
        if (!name) {
            return error;
        }
        const allDefinedDirectiveNames = schema.allDirectives().map((d) => d.name);
        const suggestions = (0, suggestions_1.suggestionList)(name, allDefinedDirectiveNames);
        if (suggestions.length === 0) {
            return this.onUnknownDirectiveValidationError(schema, name, error);
        }
        else {
            return (0, error_1.withModifiedErrorMessage)(error, `${error.message}${(0, suggestions_1.didYouMean)(suggestions.map((s) => '@' + s))}`);
        }
    }
    onUnknownDirectiveValidationError(_schema, _unknownDirectiveName, error) {
        return error;
    }
    applyDirectivesAfterParsing() {
        return false;
    }
}
exports.SchemaBlueprint = SchemaBlueprint;
exports.defaultSchemaBlueprint = new SchemaBlueprint();
class CoreFeature {
    constructor(url, nameInSchema, directive, imports, purpose) {
        this.url = url;
        this.nameInSchema = nameInSchema;
        this.directive = directive;
        this.imports = imports;
        this.purpose = purpose;
    }
    isFeatureDefinition(element) {
        const importName = element.kind === 'DirectiveDefinition'
            ? '@' + element.name
            : element.name;
        return element.name.startsWith(this.nameInSchema + '__')
            || (element.kind === 'DirectiveDefinition' && element.name === this.nameInSchema)
            || !!this.imports.find((i) => { var _a; return importName === ((_a = i.as) !== null && _a !== void 0 ? _a : i.name); });
    }
    directiveNameInSchema(name) {
        return CoreFeature.directiveNameInSchemaForCoreArguments(this.url, this.nameInSchema, this.imports, name);
    }
    static directiveNameInSchemaForCoreArguments(specUrl, specNameInSchema, imports, directiveNameInSpec) {
        var _a, _b;
        const elementImport = imports.find((i) => i.name.charAt(0) === '@' && i.name.slice(1) === directiveNameInSpec);
        return elementImport
            ? ((_b = (_a = elementImport.as) === null || _a === void 0 ? void 0 : _a.slice(1)) !== null && _b !== void 0 ? _b : directiveNameInSpec)
            : (directiveNameInSpec === specUrl.name
                ? specNameInSchema
                : specNameInSchema + '__' + directiveNameInSpec);
    }
    typeNameInSchema(name) {
        var _a;
        const elementImport = this.imports.find((i) => i.name === name);
        return elementImport ? ((_a = elementImport.as) !== null && _a !== void 0 ? _a : name) : this.nameInSchema + '__' + name;
    }
    minimumFederationVersion() {
        var _a;
        return (_a = (0, knownCoreFeatures_1.coreFeatureDefinitionIfKnown)(this.url)) === null || _a === void 0 ? void 0 : _a.minimumFederationVersion;
    }
}
exports.CoreFeature = CoreFeature;
class CoreFeatures {
    constructor(coreItself) {
        this.coreItself = coreItself;
        this.byAlias = new Map();
        this.byIdentity = new Map();
        this.add(coreItself);
        const coreDef = (0, coreSpec_1.findCoreSpecVersion)(coreItself.url);
        if (!coreDef) {
            throw error_1.ERRORS.UNKNOWN_LINK_VERSION.err(`Schema uses unknown version ${coreItself.url.version} of the ${coreItself.url.name} spec`);
        }
        this.coreDefinition = coreDef;
    }
    getByIdentity(identity) {
        return this.byIdentity.get(identity);
    }
    allFeatures() {
        return this.byIdentity.values();
    }
    removeFeature(featureIdentity) {
        const feature = this.byIdentity.get(featureIdentity);
        if (feature) {
            this.byIdentity.delete(featureIdentity);
            this.byAlias.delete(feature.nameInSchema);
        }
    }
    maybeAddFeature(directive) {
        var _a, _b;
        if (((_a = directive.definition) === null || _a === void 0 ? void 0 : _a.name) !== this.coreItself.nameInSchema) {
            return undefined;
        }
        const typedDirective = directive;
        const args = typedDirective.arguments();
        const url = this.coreDefinition.extractFeatureUrl(args);
        const existing = this.byIdentity.get(url.identity);
        if (existing) {
            throw error_1.ERRORS.INVALID_LINK_DIRECTIVE_USAGE.err(`Duplicate inclusion of feature ${url.identity}`);
        }
        const imports = (0, coreSpec_1.extractCoreFeatureImports)(url, typedDirective);
        const feature = new CoreFeature(url, (_b = args.as) !== null && _b !== void 0 ? _b : url.name, directive, imports, args.for);
        this.add(feature);
        directive.schema().blueprint.onAddedCoreFeature(directive.schema(), feature);
        return feature;
    }
    add(feature) {
        this.byAlias.set(feature.nameInSchema, feature);
        this.byIdentity.set(feature.url.identity, feature);
    }
    sourceFeature(element) {
        const isDirective = element instanceof DirectiveDefinition || element instanceof Directive;
        const splitted = element.name.split('__');
        if (splitted.length > 1) {
            const feature = this.byAlias.get(splitted[0]);
            return feature ? {
                feature,
                nameInFeature: splitted.slice(1).join('__'),
                isImported: false,
            } : undefined;
        }
        else {
            const importName = isDirective ? '@' + element.name : element.name;
            const allFeatures = [this.coreItself, ...this.byIdentity.values()];
            for (const feature of allFeatures) {
                for (const { as, name } of feature.imports) {
                    if ((as !== null && as !== void 0 ? as : name) === importName) {
                        return {
                            feature,
                            nameInFeature: isDirective ? name.slice(1) : name,
                            isImported: true,
                        };
                    }
                }
            }
            const directFeature = this.byAlias.get(element.name);
            if (directFeature && isDirective) {
                return {
                    feature: directFeature,
                    nameInFeature: element.name,
                    isImported: false,
                };
            }
            return undefined;
        }
    }
}
exports.CoreFeatures = CoreFeatures;
const graphQLBuiltInTypes = ['Int', 'Float', 'String', 'Boolean', 'ID'];
const graphQLBuiltInTypesSpecifications = graphQLBuiltInTypes.map((name) => (0, directiveAndTypeSpecification_1.createScalarTypeSpecification)({ name }));
const graphQLBuiltInDirectivesSpecifications = [
    (0, directiveAndTypeSpecification_1.createDirectiveSpecification)({
        name: 'include',
        locations: [graphql_1.DirectiveLocation.FIELD, graphql_1.DirectiveLocation.FRAGMENT_SPREAD, graphql_1.DirectiveLocation.INLINE_FRAGMENT],
        args: [{ name: 'if', type: (schema) => new NonNullType(schema.booleanType()) }],
    }),
    (0, directiveAndTypeSpecification_1.createDirectiveSpecification)({
        name: 'skip',
        locations: [graphql_1.DirectiveLocation.FIELD, graphql_1.DirectiveLocation.FRAGMENT_SPREAD, graphql_1.DirectiveLocation.INLINE_FRAGMENT],
        args: [{ name: 'if', type: (schema) => new NonNullType(schema.booleanType()) }],
    }),
    (0, directiveAndTypeSpecification_1.createDirectiveSpecification)({
        name: 'deprecated',
        locations: [graphql_1.DirectiveLocation.FIELD_DEFINITION, graphql_1.DirectiveLocation.ENUM_VALUE, graphql_1.DirectiveLocation.ARGUMENT_DEFINITION, graphql_1.DirectiveLocation.INPUT_FIELD_DEFINITION],
        args: [{ name: 'reason', type: (schema) => schema.stringType(), defaultValue: 'No longer supported' }],
    }),
    (0, directiveAndTypeSpecification_1.createDirectiveSpecification)({
        name: 'specifiedBy',
        locations: [graphql_1.DirectiveLocation.SCALAR],
        args: [{ name: 'url', type: (schema) => new NonNullType(schema.stringType()) }],
    }),
    (0, directiveAndTypeSpecification_1.createDirectiveSpecification)({
        name: 'defer',
        locations: [graphql_1.DirectiveLocation.FRAGMENT_SPREAD, graphql_1.DirectiveLocation.INLINE_FRAGMENT],
        args: [
            { name: 'label', type: (schema) => schema.stringType() },
            { name: 'if', type: (schema) => new NonNullType(schema.booleanType()), defaultValue: true },
        ],
    }),
    (0, directiveAndTypeSpecification_1.createDirectiveSpecification)({
        name: 'stream',
        locations: [graphql_1.DirectiveLocation.FIELD],
        args: [
            { name: 'label', type: (schema) => schema.stringType() },
            { name: 'initialCount', type: (schema) => schema.intType(), defaultValue: 0 },
            { name: 'if', type: (schema) => new NonNullType(schema.booleanType()), defaultValue: true },
        ],
    }),
];
const coordinateRegexp = /^@?[_A-Za-z][_0-9A-Za-z]*(\.[_A-Za-z][_0-9A-Za-z]*)?(\([_A-Za-z][_0-9A-Za-z]*:\))?$/;
class Schema {
    constructor(blueprint = exports.defaultSchemaBlueprint, config = {}) {
        this.blueprint = blueprint;
        this.config = config;
        this._builtInTypes = new utils_1.MapWithCachedArrays();
        this._types = new utils_1.MapWithCachedArrays();
        this._builtInDirectives = new utils_1.MapWithCachedArrays();
        this._directives = new utils_1.MapWithCachedArrays();
        this.isConstructed = false;
        this.isValidated = false;
        this._schemaDefinition = new SchemaDefinition();
        Element.prototype['setParent'].call(this._schemaDefinition, this);
        graphQLBuiltInTypesSpecifications.forEach((spec) => spec.checkOrAdd(this, undefined, true));
        graphQLBuiltInDirectivesSpecifications.forEach((spec) => spec.checkOrAdd(this, undefined, true));
        blueprint.onConstructed(this);
        this.isConstructed = true;
    }
    canModifyBuiltIn() {
        return !this.isConstructed;
    }
    runWithBuiltInModificationAllowed(fct) {
        const wasConstructed = this.isConstructed;
        this.isConstructed = false;
        fct();
        this.isConstructed = wasConstructed;
    }
    renameTypeInternal(oldName, newName) {
        this._types.set(newName, this._types.get(oldName));
        this._types.delete(oldName);
    }
    removeTypeInternal(type) {
        this._types.delete(type.name);
    }
    removeDirectiveInternal(definition) {
        this._directives.delete(definition.name);
    }
    markAsCoreSchema(coreItself) {
        this._coreFeatures = new CoreFeatures(coreItself);
    }
    unmarkAsCoreSchema() {
        this._coreFeatures = undefined;
    }
    onModification() {
        if (this.isConstructed) {
            this.invalidate();
            this.cachedDocument = undefined;
            this.apiSchema = undefined;
        }
    }
    isCoreSchema() {
        return this.coreFeatures !== undefined;
    }
    get coreFeatures() {
        return this._coreFeatures;
    }
    toAST() {
        var _a;
        if (!this.cachedDocument) {
            const ast = (0, graphql_1.parse)((0, print_1.printSchema)(this), { noLocation: true });
            const shouldCache = (_a = this.config.cacheAST) !== null && _a !== void 0 ? _a : false;
            if (!shouldCache) {
                return ast;
            }
            this.cachedDocument = ast;
        }
        return this.cachedDocument;
    }
    toAPISchema() {
        if (!this.apiSchema) {
            this.validate();
            const apiSchema = this.clone(undefined, false);
            for (const toRemoveIfCustom of ['defer', 'stream']) {
                const directive = apiSchema.directive(toRemoveIfCustom);
                if (directive && !directive.isBuiltIn) {
                    directive.removeRecursive();
                }
            }
            (0, inaccessibleSpec_1.removeInaccessibleElements)(apiSchema);
            (0, coreSpec_1.removeAllCoreFeatures)(apiSchema);
            (0, utils_1.assert)(!apiSchema.isCoreSchema(), "The API schema shouldn't be a core schema");
            apiSchema.validate();
            this.apiSchema = apiSchema;
        }
        return this.apiSchema;
    }
    emptyASTDefinitionsForExtensionsWithoutDefinition() {
        const nodes = [];
        if (this.schemaDefinition.hasExtensionElements() && !this.schemaDefinition.hasNonExtensionElements()) {
            const node = { kind: graphql_1.Kind.SCHEMA_DEFINITION, operationTypes: [] };
            nodes.push(node);
        }
        for (const type of this.types()) {
            if (type.hasExtensionElements() && !type.hasNonExtensionElements()) {
                const node = {
                    kind: type.astDefinitionKind,
                    name: { kind: graphql_1.Kind.NAME, value: type.name },
                };
                nodes.push(node);
            }
        }
        return nodes;
    }
    toGraphQLJSSchema(config) {
        var _a, _b;
        const includeDefer = (_a = config === null || config === void 0 ? void 0 : config.includeDefer) !== null && _a !== void 0 ? _a : false;
        const includeStream = (_b = config === null || config === void 0 ? void 0 : config.includeStream) !== null && _b !== void 0 ? _b : false;
        let ast = this.toAST();
        const additionalNodes = this.emptyASTDefinitionsForExtensionsWithoutDefinition();
        if (includeDefer) {
            additionalNodes.push(this.deferDirective().toAST());
        }
        if (includeStream) {
            additionalNodes.push(this.streamDirective().toAST());
        }
        if (additionalNodes.length > 0) {
            ast = {
                kind: graphql_1.Kind.DOCUMENT,
                definitions: ast.definitions.concat(additionalNodes),
            };
        }
        const graphQLSchema = (0, graphql_1.buildASTSchema)(ast);
        if (additionalNodes.length > 0) {
            for (const node of additionalNodes) {
                switch (node.kind) {
                    case graphql_1.Kind.SCHEMA_DEFINITION:
                        graphQLSchema.astNode = undefined;
                        break;
                    case graphql_1.Kind.SCALAR_TYPE_DEFINITION:
                    case graphql_1.Kind.OBJECT_TYPE_DEFINITION:
                    case graphql_1.Kind.INTERFACE_TYPE_DEFINITION:
                    case graphql_1.Kind.ENUM_TYPE_DEFINITION:
                    case graphql_1.Kind.UNION_TYPE_DEFINITION:
                    case graphql_1.Kind.INPUT_OBJECT_TYPE_DEFINITION:
                        const type = graphQLSchema.getType(node.name.value);
                        if (type) {
                            type.astNode = undefined;
                        }
                }
            }
        }
        return graphQLSchema;
    }
    get schemaDefinition() {
        return this._schemaDefinition;
    }
    types() {
        return this._types.values();
    }
    interfaceTypes() {
        return filterTypesOfKind(this.types(), 'InterfaceType');
    }
    objectTypes() {
        return filterTypesOfKind(this.types(), 'ObjectType');
    }
    unionTypes() {
        return filterTypesOfKind(this.types(), 'UnionType');
    }
    scalarTypes() {
        return filterTypesOfKind(this.types(), 'ScalarType');
    }
    inputTypes() {
        return filterTypesOfKind(this.types(), 'InputObjectType');
    }
    enumTypes() {
        return filterTypesOfKind(this.types(), 'EnumType');
    }
    builtInTypes(includeShadowed = false) {
        const allBuiltIns = this._builtInTypes.values();
        return includeShadowed
            ? allBuiltIns
            : allBuiltIns.filter(t => !this.isShadowedBuiltInType(t));
    }
    isShadowedBuiltInType(type) {
        return type.isBuiltIn && this._types.has(type.name);
    }
    allTypes() {
        return this.builtInTypes().concat(this.types());
    }
    type(name) {
        const type = this._types.get(name);
        return type ? type : this._builtInTypes.get(name);
    }
    typeOfKind(name, kind) {
        const type = this.type(name);
        return type && type.kind === kind ? type : undefined;
    }
    intType() {
        return this._builtInTypes.get('Int');
    }
    floatType() {
        return this._builtInTypes.get('Float');
    }
    stringType() {
        return this._builtInTypes.get('String');
    }
    booleanType() {
        return this._builtInTypes.get('Boolean');
    }
    idType() {
        return this._builtInTypes.get('ID');
    }
    builtInScalarTypes() {
        return [
            this.intType(),
            this.floatType(),
            this.stringType(),
            this.booleanType(),
            this.idType(),
        ];
    }
    addType(type) {
        const existing = this.type(type.name);
        if (existing) {
            (0, utils_1.assert)(existing.isBuiltIn, () => `Type ${type} already exists in this schema`);
        }
        if (type.isAttached()) {
            (0, utils_1.assert)(type.parent == this, () => `Cannot add type ${type} to this schema; it is already attached to another schema`);
            return type;
        }
        if (type.isBuiltIn) {
            (0, utils_1.assert)(!this.isConstructed, `Cannot add built-in ${type} to this schema (built-ins can only be added at schema construction time)`);
            this._builtInTypes.set(type.name, type);
        }
        else {
            this._types.set(type.name, type);
        }
        Element.prototype['setParent'].call(type, this);
        const defaultSchemaRoot = checkDefaultSchemaRoot(type);
        if (defaultSchemaRoot && !this.schemaDefinition.root(defaultSchemaRoot)) {
            this.schemaDefinition.setRoot(defaultSchemaRoot, type);
        }
        this.onModification();
        return type;
    }
    directives() {
        return this._directives.values();
    }
    builtInDirectives(includeShadowed = false) {
        return includeShadowed
            ? this._builtInDirectives.values()
            : this._builtInDirectives.values().filter(d => !this.isShadowedBuiltInDirective(d));
    }
    allDirectives() {
        return this.builtInDirectives().concat(this.directives());
    }
    isShadowedBuiltInDirective(directive) {
        return directive.isBuiltIn && this._directives.has(directive.name);
    }
    directive(name) {
        const directive = this._directives.get(name);
        return directive ? directive : this.builtInDirective(name);
    }
    builtInDirective(name) {
        return this._builtInDirectives.get(name);
    }
    *allNamedSchemaElement() {
        for (const type of this.types()) {
            yield type;
            yield* type.allChildElements();
        }
        for (const directive of this.directives()) {
            yield directive;
            yield* directive.arguments();
        }
    }
    *allSchemaElement() {
        yield this._schemaDefinition;
        yield* this.allNamedSchemaElement();
    }
    addDirectiveDefinition(directiveOrName) {
        const definition = typeof directiveOrName === 'string' ? new DirectiveDefinition(directiveOrName) : directiveOrName;
        const existing = this.directive(definition.name);
        (0, utils_1.assert)(!existing || existing.isBuiltIn, () => `Directive ${definition} already exists in this schema`);
        if (definition.isAttached()) {
            (0, utils_1.assert)(definition.parent == this, () => `Cannot add directive ${definition} to this schema; it is already attached to another schema`);
            return definition;
        }
        if (definition.isBuiltIn) {
            (0, utils_1.assert)(!this.isConstructed, () => `Cannot add built-in ${definition} to this schema (built-ins can only be added at schema construction time)`);
            this._builtInDirectives.set(definition.name, definition);
        }
        else {
            this._directives.set(definition.name, definition);
        }
        Element.prototype['setParent'].call(definition, this);
        this.onModification();
        return definition;
    }
    invalidate() {
        if (this.isValidated) {
            this.blueprint.onInvalidation(this);
        }
        this.isValidated = false;
    }
    assumeValid() {
        this.runWithBuiltInModificationAllowed(() => {
            (0, introspection_1.addIntrospectionFields)(this);
        });
        this.isValidated = true;
    }
    validate() {
        if (this.isValidated) {
            return;
        }
        this.runWithBuiltInModificationAllowed(() => {
            (0, introspection_1.addIntrospectionFields)(this);
        });
        let errors = (0, validate_1.validateSDL)(this.toAST(), undefined, this.blueprint.validationRules()).map((e) => this.blueprint.onGraphQLJSValidationError(this, e));
        errors = errors.concat((0, validate_2.validateSchema)(this));
        if (errors.length === 0) {
            this.runWithBuiltInModificationAllowed(() => {
                errors = this.blueprint.onValidation(this);
            });
        }
        if (errors.length > 0) {
            throw (0, exports.ErrGraphQLValidationFailed)(errors);
        }
        this.isValidated = true;
    }
    clone(builtIns, cloneJoinDirectives = true) {
        const cloned = new Schema(builtIns !== null && builtIns !== void 0 ? builtIns : this.blueprint);
        copy(this, cloned, cloneJoinDirectives);
        if (this.isValidated) {
            cloned.assumeValid();
        }
        return cloned;
    }
    getBuiltInDirective(name) {
        const directive = this.directive(name);
        (0, utils_1.assert)(directive, `The provided schema has not be built with the ${name} directive built-in`);
        return directive;
    }
    includeDirective() {
        return this.getBuiltInDirective('include');
    }
    skipDirective() {
        return this.getBuiltInDirective('skip');
    }
    deprecatedDirective() {
        return this.getBuiltInDirective('deprecated');
    }
    specifiedByDirective() {
        return this.getBuiltInDirective('specifiedBy');
    }
    deferDirective() {
        return this.getBuiltInDirective('defer');
    }
    streamDirective() {
        return this.getBuiltInDirective('stream');
    }
    elementByCoordinate(coordinate) {
        if (!coordinate.match(coordinateRegexp)) {
            throw error_1.ERRORS.INVALID_GRAPHQL.err(`Invalid argument "${coordinate}: it is not a syntactically valid graphQL coordinate."`);
        }
        const argStartIdx = coordinate.indexOf('(');
        const start = argStartIdx < 0 ? coordinate : coordinate.slice(0, argStartIdx);
        const argName = argStartIdx < 0 ? undefined : coordinate.slice(argStartIdx + 1, coordinate.length - 2);
        const splittedStart = start.split('.');
        const typeOrDirectiveName = splittedStart[0];
        const fieldOrEnumName = splittedStart[1];
        const isDirective = typeOrDirectiveName.startsWith('@');
        if (isDirective) {
            if (fieldOrEnumName) {
                throw error_1.ERRORS.INVALID_GRAPHQL.err(`Invalid argument "${coordinate}: it is not a syntactically valid graphQL coordinate."`);
            }
            const directive = this.directive(typeOrDirectiveName.slice(1));
            return argName ? directive === null || directive === void 0 ? void 0 : directive.argument(argName) : directive;
        }
        else {
            const type = this.type(typeOrDirectiveName);
            if (!type || !fieldOrEnumName) {
                return type;
            }
            switch (type.kind) {
                case 'ObjectType':
                case 'InterfaceType':
                    const field = type.field(fieldOrEnumName);
                    return argName ? field === null || field === void 0 ? void 0 : field.argument(argName) : field;
                case 'InputObjectType':
                    if (argName) {
                        throw error_1.ERRORS.INVALID_GRAPHQL.err(`Invalid argument "${coordinate}: it is not a syntactically valid graphQL coordinate."`);
                    }
                    return type.field(fieldOrEnumName);
                case 'EnumType':
                    if (argName) {
                        throw error_1.ERRORS.INVALID_GRAPHQL.err(`Invalid argument "${coordinate}: it is not a syntactically valid graphQL coordinate."`);
                    }
                    return type.value(fieldOrEnumName);
                default:
                    throw error_1.ERRORS.INVALID_GRAPHQL.err(`Invalid argument "${coordinate}: it is not a syntactically valid graphQL coordinate."`);
            }
        }
    }
}
exports.Schema = Schema;
class RootType extends BaseExtensionMember {
    constructor(rootKind, type) {
        super();
        this.rootKind = rootKind;
        this.type = type;
    }
    isDefaultRootName() {
        return defaultRootName(this.rootKind) == this.type.name;
    }
    removeInner() {
        SchemaDefinition.prototype['removeRootType'].call(this._parent, this);
    }
}
exports.RootType = RootType;
class SchemaDefinition extends SchemaElement {
    constructor() {
        super(...arguments);
        this.kind = 'SchemaDefinition';
        this._roots = new utils_1.MapWithCachedArrays();
        this.preserveEmptyDefinition = false;
    }
    roots() {
        return this._roots.values();
    }
    applyDirective(nameOrDef, args, asFirstDirective = false) {
        var _a, _b;
        const applied = super.applyDirective(nameOrDef, args, asFirstDirective);
        const schema = this.schema();
        const coreFeatures = schema.coreFeatures;
        if ((0, coreSpec_1.isCoreSpecDirectiveApplication)(applied)) {
            if (coreFeatures) {
                throw error_1.ERRORS.INVALID_LINK_DIRECTIVE_USAGE.err(`Invalid duplicate application of @core/@link`);
            }
            const schemaDirective = applied;
            const args = schemaDirective.arguments();
            const url = coreSpec_1.FeatureUrl.parse(((_a = args.url) !== null && _a !== void 0 ? _a : args.feature));
            const imports = (0, coreSpec_1.extractCoreFeatureImports)(url, schemaDirective);
            const core = new CoreFeature(url, (_b = args.as) !== null && _b !== void 0 ? _b : url.name, schemaDirective, imports, args.for);
            Schema.prototype['markAsCoreSchema'].call(schema, core);
            this.appliedDirectives
                .filter((a) => a !== applied)
                .forEach((other) => CoreFeatures.prototype['maybeAddFeature'].call(schema.coreFeatures, other));
        }
        else if (coreFeatures) {
            CoreFeatures.prototype['maybeAddFeature'].call(coreFeatures, applied);
        }
        this.onModification();
        return applied;
    }
    root(rootKind) {
        return this._roots.get(rootKind);
    }
    rootType(rootKind) {
        var _a;
        return (_a = this.root(rootKind)) === null || _a === void 0 ? void 0 : _a.type;
    }
    setRoot(rootKind, nameOrType) {
        let toSet;
        if (typeof nameOrType === 'string') {
            this.checkUpdate();
            const obj = this.schema().type(nameOrType);
            if (!obj) {
                throw error_1.ERRORS.INVALID_GRAPHQL.err(`Cannot set schema ${rootKind} root to unknown type ${nameOrType}`);
            }
            else if (obj.kind != 'ObjectType') {
                throw error_1.ERRORS.INVALID_GRAPHQL.err(`${defaultRootName(rootKind)} root type must be an Object type${rootKind === 'query' ? '' : ' if provided'}, it cannot be set to ${nameOrType} (an ${obj.kind}).`);
            }
            toSet = new RootType(rootKind, obj);
        }
        else {
            this.checkUpdate(nameOrType);
            toSet = new RootType(rootKind, nameOrType);
        }
        const prevRoot = this._roots.get(rootKind);
        if (prevRoot) {
            removeReferenceToType(this, prevRoot.type);
        }
        this._roots.set(rootKind, toSet);
        Element.prototype['setParent'].call(toSet, this);
        addReferenceToType(this, toSet.type);
        this.onModification();
        return toSet;
    }
    extensions() {
        var _a;
        return (_a = this._extensions) !== null && _a !== void 0 ? _a : [];
    }
    hasExtension(extension) {
        var _a, _b;
        return (_b = (_a = this._extensions) === null || _a === void 0 ? void 0 : _a.includes(extension)) !== null && _b !== void 0 ? _b : false;
    }
    newExtension() {
        return this.addExtension(new Extension());
    }
    addExtension(extension) {
        this.checkUpdate();
        if (this.hasExtension(extension)) {
            return extension;
        }
        (0, utils_1.assert)(!extension.extendedElement, 'Cannot add extension to this schema: extension is already added to another schema');
        if (this._extensions) {
            this._extensions.push(extension);
        }
        else {
            this._extensions = [extension];
        }
        Extension.prototype['setExtendedElement'].call(extension, this);
        this.onModification();
        return extension;
    }
    hasExtensionElements() {
        return !!this._extensions;
    }
    hasNonExtensionElements() {
        return this.preserveEmptyDefinition
            || this.appliedDirectives.some((d) => d.ofExtension() === undefined)
            || this.roots().some((r) => r.ofExtension() === undefined);
    }
    removeRootType(rootType) {
        this._roots.delete(rootType.rootKind);
        removeReferenceToType(this, rootType.type);
    }
    removeTypeReference(toRemove) {
        for (const rootType of this.roots()) {
            if (rootType.type == toRemove) {
                this._roots.delete(rootType.rootKind);
            }
        }
    }
    toString() {
        return `schema[${this._roots.keys().join(', ')}]`;
    }
}
exports.SchemaDefinition = SchemaDefinition;
class ScalarType extends BaseNamedType {
    constructor() {
        super(...arguments);
        this.kind = 'ScalarType';
        this.astDefinitionKind = graphql_1.Kind.SCALAR_TYPE_DEFINITION;
    }
    removeTypeReference(type) {
        (0, utils_1.assert)(false, `Scalar type ${this} can't reference other types; shouldn't be asked to remove reference to ${type}`);
    }
    hasNonExtensionInnerElements() {
        return false;
    }
    removeInnerElementsExtensions() {
    }
    removeInnerElements() {
    }
    removeReferenceRecursive(ref) {
        ref.remove();
    }
}
exports.ScalarType = ScalarType;
class InterfaceImplementation extends BaseExtensionMember {
    constructor(itf) {
        super();
        this.interface = itf;
    }
    removeInner() {
        FieldBasedType.prototype['removeInterfaceImplementation'].call(this._parent, this.interface);
    }
    toString() {
        return `'implements ${this.interface}'`;
    }
}
exports.InterfaceImplementation = InterfaceImplementation;
class FieldBasedType extends BaseNamedType {
    constructor() {
        super(...arguments);
        this._fields = new utils_1.MapWithCachedArrays();
    }
    onAttached() {
        Schema.prototype['runWithBuiltInModificationAllowed'].call(this.schema(), () => {
            this.addField(new FieldDefinition(exports.typenameFieldName, true), new NonNullType(this.schema().stringType()));
        });
    }
    removeFieldInternal(field) {
        this._fields.delete(field.name);
        this._cachedNonBuiltInFields = undefined;
    }
    interfaceImplementations() {
        var _a, _b;
        return (_b = (_a = this._interfaceImplementations) === null || _a === void 0 ? void 0 : _a.values()) !== null && _b !== void 0 ? _b : [];
    }
    interfaceImplementation(type) {
        return this._interfaceImplementations ? this._interfaceImplementations.get(typeof type === 'string' ? type : type.name) : undefined;
    }
    interfaces() {
        return this.interfaceImplementations().map(impl => impl.interface);
    }
    implementsInterface(type) {
        var _a, _b;
        return (_b = (_a = this._interfaceImplementations) === null || _a === void 0 ? void 0 : _a.has(typeof type === 'string' ? type : type.name)) !== null && _b !== void 0 ? _b : false;
    }
    addImplementedInterface(nameOrItfOrItfImpl) {
        var _a;
        let toAdd;
        if (nameOrItfOrItfImpl instanceof InterfaceImplementation) {
            this.checkUpdate(nameOrItfOrItfImpl);
            toAdd = nameOrItfOrItfImpl;
        }
        else {
            let itf;
            if (typeof nameOrItfOrItfImpl === 'string') {
                this.checkUpdate();
                const maybeItf = this.schema().type(nameOrItfOrItfImpl);
                if (!maybeItf) {
                    throw error_1.ERRORS.INVALID_GRAPHQL.err(`Cannot implement unknown type ${nameOrItfOrItfImpl}`);
                }
                else if (maybeItf.kind != 'InterfaceType') {
                    throw error_1.ERRORS.INVALID_GRAPHQL.err(`Cannot implement non-interface type ${nameOrItfOrItfImpl} (of type ${maybeItf.kind})`);
                }
                itf = maybeItf;
            }
            else {
                itf = nameOrItfOrItfImpl;
            }
            toAdd = new InterfaceImplementation(itf);
        }
        const existing = (_a = this._interfaceImplementations) === null || _a === void 0 ? void 0 : _a.get(toAdd.interface.name);
        if (!existing) {
            if (!this._interfaceImplementations) {
                this._interfaceImplementations = new utils_1.MapWithCachedArrays();
            }
            this._interfaceImplementations.set(toAdd.interface.name, toAdd);
            addReferenceToType(this, toAdd.interface);
            Element.prototype['setParent'].call(toAdd, this);
            this.onModification();
            return toAdd;
        }
        else {
            return existing;
        }
    }
    fields() {
        if (!this._cachedNonBuiltInFields) {
            this._cachedNonBuiltInFields = this._fields.values().filter(f => !f.isBuiltIn);
        }
        return this._cachedNonBuiltInFields;
    }
    hasFields() {
        return this.fields().length > 0;
    }
    builtInFields() {
        return this.allFields().filter(f => f.isBuiltIn);
    }
    allFields() {
        return this._fields.values();
    }
    field(name) {
        return this._fields.get(name);
    }
    typenameField() {
        return this.field(exports.typenameFieldName);
    }
    addField(nameOrField, type) {
        let toAdd;
        if (typeof nameOrField === 'string') {
            this.checkUpdate();
            toAdd = new FieldDefinition(nameOrField);
        }
        else {
            this.checkUpdate(nameOrField);
            toAdd = nameOrField;
        }
        if (this.field(toAdd.name)) {
            throw error_1.ERRORS.INVALID_GRAPHQL.err(`Field ${toAdd.name} already exists on ${this}`);
        }
        if (type && !isOutputType(type)) {
            throw error_1.ERRORS.INVALID_GRAPHQL.err(`Invalid input type ${type} for field ${toAdd.name}: object and interface field types should be output types.`);
        }
        this._fields.set(toAdd.name, toAdd);
        this._cachedNonBuiltInFields = undefined;
        Element.prototype['setParent'].call(toAdd, this);
        if (type) {
            toAdd.type = type;
        }
        this.onModification();
        return toAdd;
    }
    *allChildElements() {
        for (const field of this._fields.values()) {
            yield field;
            yield* field.arguments();
        }
    }
    removeInterfaceImplementation(itf) {
        var _a;
        (_a = this._interfaceImplementations) === null || _a === void 0 ? void 0 : _a.delete(itf.name);
        removeReferenceToType(this, itf);
    }
    removeTypeReference(type) {
        var _a;
        (_a = this._interfaceImplementations) === null || _a === void 0 ? void 0 : _a.delete(type.name);
    }
    removeInnerElements() {
        for (const interfaceImpl of this.interfaceImplementations()) {
            interfaceImpl.remove();
        }
        for (const field of this.allFields()) {
            if (field.isBuiltIn) {
                FieldDefinition.prototype['removeParent'].call(field);
            }
            else {
                field.remove();
            }
        }
    }
    hasNonExtensionInnerElements() {
        return this.interfaceImplementations().some(itf => itf.ofExtension() === undefined)
            || this.fields().some(f => f.ofExtension() === undefined);
    }
    removeInnerElementsExtensions() {
        this.interfaceImplementations().forEach(itf => itf.removeOfExtension());
        this.fields().forEach(f => f.removeOfExtension());
    }
}
class ObjectType extends FieldBasedType {
    constructor() {
        super(...arguments);
        this.kind = 'ObjectType';
        this.astDefinitionKind = graphql_1.Kind.OBJECT_TYPE_DEFINITION;
    }
    isRootType() {
        const schema = this.schema();
        return schema.schemaDefinition.roots().some(rt => rt.type == this);
    }
    isQueryRootType() {
        var _a;
        const schema = this.schema();
        return ((_a = schema.schemaDefinition.root('query')) === null || _a === void 0 ? void 0 : _a.type) === this;
    }
    isSubscriptionRootType() {
        var _a;
        const schema = this.schema();
        return ((_a = schema.schemaDefinition.root('subscription')) === null || _a === void 0 ? void 0 : _a.type) === this;
    }
    removeReferenceRecursive(ref) {
        switch (ref.kind) {
            case 'FieldDefinition':
                ref.removeRecursive();
                break;
            case 'UnionType':
                if (ref.membersCount() === 0) {
                    ref.removeRecursive();
                }
                break;
        }
    }
    unionsWhereMember() {
        var _a;
        const unions = [];
        (_a = this._referencers) === null || _a === void 0 ? void 0 : _a.forEach((r) => {
            if (r instanceof BaseNamedType && isUnionType(r)) {
                unions.push(r);
            }
        });
        return unions;
    }
}
exports.ObjectType = ObjectType;
class InterfaceType extends FieldBasedType {
    constructor() {
        super(...arguments);
        this.kind = 'InterfaceType';
        this.astDefinitionKind = graphql_1.Kind.INTERFACE_TYPE_DEFINITION;
    }
    allImplementations() {
        const implementations = [];
        this.referencers().forEach(ref => {
            if (ref.kind === 'ObjectType' || ref.kind === 'InterfaceType') {
                implementations.push(ref);
            }
        });
        return implementations;
    }
    possibleRuntimeTypes() {
        return this.allImplementations().filter(impl => impl.kind === 'ObjectType');
    }
    isPossibleRuntimeType(type) {
        const typeName = typeof type === 'string' ? type : type.name;
        return this.possibleRuntimeTypes().some(t => t.name == typeName);
    }
    removeReferenceRecursive(ref) {
        if (ref.kind === 'FieldDefinition') {
            ref.removeRecursive();
        }
    }
}
exports.InterfaceType = InterfaceType;
class UnionMember extends BaseExtensionMember {
    constructor(type) {
        super();
        this.type = type;
    }
    removeInner() {
        UnionType.prototype['removeMember'].call(this._parent, this.type);
    }
}
exports.UnionMember = UnionMember;
class UnionType extends BaseNamedType {
    constructor() {
        super(...arguments);
        this.kind = 'UnionType';
        this.astDefinitionKind = graphql_1.Kind.UNION_TYPE_DEFINITION;
        this._members = new utils_1.MapWithCachedArrays();
    }
    onAttached() {
        Schema.prototype['runWithBuiltInModificationAllowed'].call(this.schema(), () => {
            this._typenameField = new FieldDefinition(exports.typenameFieldName, true);
            Element.prototype['setParent'].call(this._typenameField, this);
            this._typenameField.type = new NonNullType(this.schema().stringType());
        });
    }
    types() {
        return this.members().map(m => m.type);
    }
    members() {
        return this._members.values();
    }
    membersCount() {
        return this._members.size;
    }
    hasTypeMember(type) {
        return this._members.has(typeof type === 'string' ? type : type.name);
    }
    addType(nameOrTypeOrMember) {
        let toAdd;
        if (nameOrTypeOrMember instanceof UnionMember) {
            this.checkUpdate(nameOrTypeOrMember);
            toAdd = nameOrTypeOrMember;
        }
        else {
            let obj;
            if (typeof nameOrTypeOrMember === 'string') {
                this.checkUpdate();
                const maybeObj = this.schema().type(nameOrTypeOrMember);
                if (!maybeObj) {
                    throw error_1.ERRORS.INVALID_GRAPHQL.err(`Cannot add unknown type ${nameOrTypeOrMember} as member of union type ${this.name}`);
                }
                else if (maybeObj.kind != 'ObjectType') {
                    throw error_1.ERRORS.INVALID_GRAPHQL.err(`Cannot add non-object type ${nameOrTypeOrMember} (of type ${maybeObj.kind}) as member of union type ${this.name}`);
                }
                obj = maybeObj;
            }
            else {
                this.checkUpdate(nameOrTypeOrMember);
                obj = nameOrTypeOrMember;
            }
            toAdd = new UnionMember(obj);
        }
        const existing = this._members.get(toAdd.type.name);
        if (!existing) {
            this._members.set(toAdd.type.name, toAdd);
            Element.prototype['setParent'].call(toAdd, this);
            addReferenceToType(this, toAdd.type);
            this.onModification();
            return toAdd;
        }
        else {
            return existing;
        }
    }
    clearTypes() {
        for (const type of this.types()) {
            this.removeMember(type);
        }
        this.onModification();
    }
    field(name) {
        if (name === exports.typenameFieldName && this._typenameField) {
            return this._typenameField;
        }
        return undefined;
    }
    typenameField() {
        return this._typenameField;
    }
    removeMember(type) {
        this._members.delete(type.name);
        removeReferenceToType(this, type);
    }
    removeTypeReference(type) {
        this._members.delete(type.name);
    }
    removeInnerElements() {
        for (const member of this.members()) {
            member.remove();
        }
    }
    hasNonExtensionInnerElements() {
        return this.members().some(m => m.ofExtension() === undefined);
    }
    removeReferenceRecursive(ref) {
        ref.removeRecursive();
    }
    removeInnerElementsExtensions() {
        this.members().forEach(m => m.removeOfExtension());
    }
}
exports.UnionType = UnionType;
class EnumType extends BaseNamedType {
    constructor() {
        super(...arguments);
        this.kind = 'EnumType';
        this.astDefinitionKind = graphql_1.Kind.ENUM_TYPE_DEFINITION;
        this._values = new Map();
    }
    get values() {
        return Array.from(this._values.values());
    }
    value(name) {
        return this._values.get(name);
    }
    addValue(nameOrValue) {
        let toAdd;
        if (typeof nameOrValue === 'string') {
            this.checkUpdate();
            toAdd = new EnumValue(nameOrValue);
        }
        else {
            this.checkUpdate(nameOrValue);
            toAdd = nameOrValue;
        }
        const existing = this.value(toAdd.name);
        if (!existing) {
            this._values.set(toAdd.name, toAdd);
            Element.prototype['setParent'].call(toAdd, this);
            this.onModification();
            return toAdd;
        }
        else {
            return existing;
        }
    }
    removeTypeReference(type) {
        (0, utils_1.assert)(false, `Eum type ${this} can't reference other types; shouldn't be asked to remove reference to ${type}`);
    }
    removeValueInternal(value) {
        this._values.delete(value.name);
    }
    removeInnerElements() {
        const values = this.values;
        for (const value of values) {
            value.remove();
        }
    }
    hasNonExtensionInnerElements() {
        return Array.from(this._values.values()).some(v => v.ofExtension() === undefined);
    }
    removeReferenceRecursive(ref) {
        ref.removeRecursive();
    }
    removeInnerElementsExtensions() {
        for (const v of this._values.values()) {
            v.removeOfExtension();
        }
    }
}
exports.EnumType = EnumType;
class InputObjectType extends BaseNamedType {
    constructor() {
        super(...arguments);
        this.kind = 'InputObjectType';
        this.astDefinitionKind = graphql_1.Kind.INPUT_OBJECT_TYPE_DEFINITION;
        this._fields = new Map();
    }
    fields() {
        if (!this._cachedFieldsArray) {
            this._cachedFieldsArray = (0, utils_1.mapValues)(this._fields);
        }
        return this._cachedFieldsArray;
    }
    field(name) {
        return this._fields.get(name);
    }
    addField(nameOrField, type) {
        const toAdd = typeof nameOrField === 'string' ? new InputFieldDefinition(nameOrField) : nameOrField;
        this.checkUpdate(toAdd);
        if (this.field(toAdd.name)) {
            throw error_1.ERRORS.INVALID_GRAPHQL.err(`Field ${toAdd.name} already exists on ${this}`);
        }
        if (type && !isInputType(type)) {
            throw error_1.ERRORS.INVALID_GRAPHQL.err(`Invalid output type ${type} for field ${toAdd.name}: input field types should be input types.`);
        }
        this._fields.set(toAdd.name, toAdd);
        this._cachedFieldsArray = undefined;
        Element.prototype['setParent'].call(toAdd, this);
        if (typeof nameOrField === 'string' && type) {
            toAdd.type = type;
        }
        this.onModification();
        return toAdd;
    }
    hasFields() {
        return this._fields.size > 0;
    }
    *allChildElements() {
        yield* this._fields.values();
    }
    removeTypeReference(type) {
        (0, utils_1.assert)(false, `Input Object type ${this} can't reference other types; shouldn't be asked to remove reference to ${type}`);
    }
    removeInnerElements() {
        for (const field of this.fields()) {
            field.remove();
        }
    }
    removeFieldInternal(field) {
        this._fields.delete(field.name);
        this._cachedFieldsArray = undefined;
    }
    hasNonExtensionInnerElements() {
        return this.fields().some(f => f.ofExtension() === undefined);
    }
    removeReferenceRecursive(ref) {
        if (ref.kind === 'ArgumentDefinition') {
            ref.parent().removeRecursive();
        }
        else {
            ref.removeRecursive();
        }
    }
    removeInnerElementsExtensions() {
        this.fields().forEach(f => f.removeOfExtension());
    }
}
exports.InputObjectType = InputObjectType;
class BaseWrapperType {
    constructor(_type) {
        this._type = _type;
        (0, utils_1.assert)(this._type, 'Cannot wrap an undefined/null type');
    }
    schema() {
        return this.baseType().schema();
    }
    isAttached() {
        return this.baseType().isAttached();
    }
    get ofType() {
        return this._type;
    }
    baseType() {
        return baseType(this._type);
    }
}
class ListType extends BaseWrapperType {
    constructor(type) {
        super(type);
        this.kind = 'ListType';
    }
    toString() {
        return `[${this.ofType}]`;
    }
}
exports.ListType = ListType;
class NonNullType extends BaseWrapperType {
    constructor(type) {
        super(type);
        this.kind = 'NonNullType';
    }
    toString() {
        return `${this.ofType}!`;
    }
}
exports.NonNullType = NonNullType;
class FieldDefinition extends NamedSchemaElementWithType {
    constructor(name, isBuiltIn = false) {
        super(name);
        this.isBuiltIn = isBuiltIn;
        this.kind = 'FieldDefinition';
    }
    isElementBuiltIn() {
        return this.isBuiltIn;
    }
    get coordinate() {
        const parent = this._parent;
        return `${parent == undefined ? '<detached>' : parent.coordinate}.${this.name}`;
    }
    hasArguments() {
        return !!this._args && this._args.size > 0;
    }
    arguments() {
        var _a, _b;
        return (_b = (_a = this._args) === null || _a === void 0 ? void 0 : _a.values()) !== null && _b !== void 0 ? _b : [];
    }
    argument(name) {
        var _a;
        return (_a = this._args) === null || _a === void 0 ? void 0 : _a.get(name);
    }
    addArgument(nameOrArg, type, defaultValue) {
        let toAdd;
        if (typeof nameOrArg === 'string') {
            this.checkUpdate();
            toAdd = new ArgumentDefinition(nameOrArg);
            toAdd.defaultValue = defaultValue;
        }
        else {
            this.checkUpdate(nameOrArg);
            toAdd = nameOrArg;
        }
        const existing = this.argument(toAdd.name);
        if (existing) {
            if (type && existing.type && !(0, types_1.sameType)(type, existing.type)) {
                throw error_1.ERRORS.INVALID_GRAPHQL.err(`Argument ${toAdd.name} already exists on field ${this.name} with a different type (${existing.type})`);
            }
            if (defaultValue && (!existing.defaultValue || !(0, values_1.valueEquals)(defaultValue, existing.defaultValue))) {
                throw error_1.ERRORS.INVALID_GRAPHQL.err(`Argument ${toAdd.name} already exists on field ${this.name} with a different default value (${(0, values_1.valueToString)(existing.defaultValue)})`);
            }
            return existing;
        }
        if (type && !isInputType(type)) {
            throw error_1.ERRORS.INVALID_GRAPHQL.err(`Invalid output type ${type} for argument ${toAdd.name} of ${this}: arguments should be input types.`);
        }
        if (!this._args) {
            this._args = new utils_1.MapWithCachedArrays();
        }
        this._args.set(toAdd.name, toAdd);
        Element.prototype['setParent'].call(toAdd, this);
        if (typeof nameOrArg === 'string') {
            toAdd.type = type;
        }
        this.onModification();
        return toAdd;
    }
    ofExtension() {
        return this._extension;
    }
    removeOfExtension() {
        this._extension = undefined;
    }
    setOfExtension(extension) {
        var _a;
        this.checkUpdate();
        (0, utils_1.assert)(!extension || ((_a = this._parent) === null || _a === void 0 ? void 0 : _a.hasExtension(extension)), () => `Cannot mark field ${this.name} as part of the provided extension: it is not an extension of field parent type ${this.parent}`);
        this._extension = extension;
        this.onModification();
    }
    isIntrospectionField() {
        return (0, introspection_1.isIntrospectionName)(this.name);
    }
    isSchemaIntrospectionField() {
        return introspection_1.introspectionFieldNames.includes(this.name);
    }
    removeArgumentInternal(name) {
        if (this._args) {
            this._args.delete(name);
        }
    }
    removeParent() {
        this._parent = undefined;
    }
    isDeprecated() {
        return this.hasAppliedDirective('deprecated');
    }
    remove() {
        if (!this._parent) {
            return [];
        }
        this.checkRemoval();
        this.onModification();
        this.sourceAST = undefined;
        this.type = undefined;
        this.removeAppliedDirectives();
        for (const arg of this.arguments()) {
            arg.remove();
        }
        FieldBasedType.prototype['removeFieldInternal'].call(this._parent, this);
        this._parent = undefined;
        this._extension = undefined;
        return [];
    }
    removeRecursive() {
        const parent = this._parent;
        this.remove();
        if (parent && !isUnionType(parent) && parent.fields().length === 0) {
            parent.removeRecursive();
        }
    }
    toString() {
        const args = this.hasArguments()
            ? '(' + this.arguments().map(arg => arg.toString()).join(', ') + ')'
            : "";
        return `${this.name}${args}: ${this.type}`;
    }
}
exports.FieldDefinition = FieldDefinition;
class InputFieldDefinition extends NamedSchemaElementWithType {
    constructor() {
        super(...arguments);
        this.kind = 'InputFieldDefinition';
    }
    get coordinate() {
        const parent = this._parent;
        return `${parent == undefined ? '<detached>' : parent.coordinate}.${this.name}`;
    }
    isRequired() {
        return isNonNullType(this.type) && this.defaultValue === undefined;
    }
    ofExtension() {
        return this._extension;
    }
    removeOfExtension() {
        this._extension = undefined;
    }
    setOfExtension(extension) {
        var _a;
        this.checkUpdate();
        (0, utils_1.assert)(!extension || ((_a = this._parent) === null || _a === void 0 ? void 0 : _a.hasExtension(extension)), () => `Cannot mark field ${this.name} as part of the provided extension: it is not an extension of field parent type ${this.parent}`);
        this._extension = extension;
        this.onModification();
    }
    isDeprecated() {
        return this.hasAppliedDirective('deprecated');
    }
    remove() {
        if (!this._parent) {
            return [];
        }
        this.checkRemoval();
        this.onModification();
        this.sourceAST = undefined;
        this.type = undefined;
        this.defaultValue = undefined;
        this.removeAppliedDirectives();
        InputObjectType.prototype['removeFieldInternal'].call(this._parent, this);
        this._parent = undefined;
        this._extension = undefined;
        return [];
    }
    removeRecursive() {
        const parent = this._parent;
        this.remove();
        if (parent && parent.fields().length === 0) {
            parent.removeRecursive();
        }
    }
    toString() {
        const defaultStr = this.defaultValue === undefined ? "" : ` = ${(0, values_1.valueToString)(this.defaultValue, this.type)}`;
        return `${this.name}: ${this.type}${defaultStr}`;
    }
}
exports.InputFieldDefinition = InputFieldDefinition;
class ArgumentDefinition extends NamedSchemaElementWithType {
    constructor(name) {
        super(name);
        this.kind = 'ArgumentDefinition';
    }
    get coordinate() {
        const parent = this._parent;
        return `${parent == undefined ? '<detached>' : parent.coordinate}(${this.name}:)`;
    }
    isRequired() {
        return isNonNullType(this.type) && this.defaultValue === undefined;
    }
    isDeprecated() {
        return this.hasAppliedDirective('deprecated');
    }
    remove() {
        if (!this._parent) {
            return [];
        }
        this.checkRemoval();
        this.onModification();
        this.sourceAST = undefined;
        this.type = undefined;
        this.defaultValue = undefined;
        this.removeAppliedDirectives();
        if (this._parent instanceof FieldDefinition) {
            FieldDefinition.prototype['removeArgumentInternal'].call(this._parent, this.name);
        }
        else {
            DirectiveDefinition.prototype['removeArgumentInternal'].call(this._parent, this.name);
        }
        this._parent = undefined;
        return [];
    }
    toString() {
        const defaultStr = this.defaultValue === undefined ? "" : ` = ${(0, values_1.valueToString)(this.defaultValue, this.type)}`;
        return `${this.name}: ${this.type}${defaultStr}`;
    }
}
exports.ArgumentDefinition = ArgumentDefinition;
class EnumValue extends NamedSchemaElement {
    constructor() {
        super(...arguments);
        this.kind = 'EnumValue';
    }
    get coordinate() {
        const parent = this._parent;
        return `${parent == undefined ? '<detached>' : parent.coordinate}.${this.name}`;
    }
    ofExtension() {
        return this._extension;
    }
    removeOfExtension() {
        this._extension = undefined;
    }
    setOfExtension(extension) {
        var _a;
        this.checkUpdate();
        (0, utils_1.assert)(!extension || ((_a = this._parent) === null || _a === void 0 ? void 0 : _a.hasExtension(extension)), () => `Cannot mark field ${this.name} as part of the provided extension: it is not an extension of enum value parent type ${this.parent}`);
        this._extension = extension;
        this.onModification();
    }
    isDeprecated() {
        return this.hasAppliedDirective('deprecated');
    }
    remove() {
        if (!this._parent) {
            return [];
        }
        this.checkRemoval();
        this.onModification();
        this.sourceAST = undefined;
        this.removeAppliedDirectives();
        EnumType.prototype['removeValueInternal'].call(this._parent, this);
        this._parent = undefined;
        this._extension = undefined;
        return [];
    }
    removeTypeReference(type) {
        (0, utils_1.assert)(false, `Enum value ${this} can't reference other types; shouldn't be asked to remove reference to ${type}`);
    }
    toString() {
        return `${this.name}`;
    }
}
exports.EnumValue = EnumValue;
class DirectiveDefinition extends NamedSchemaElement {
    constructor(name, isBuiltIn = false) {
        super(name);
        this.isBuiltIn = isBuiltIn;
        this.kind = 'DirectiveDefinition';
        this.repeatable = false;
        this._locations = [];
    }
    get coordinate() {
        return `@${this.name}`;
    }
    arguments() {
        var _a, _b;
        return (_b = (_a = this._args) === null || _a === void 0 ? void 0 : _a.values()) !== null && _b !== void 0 ? _b : [];
    }
    argument(name) {
        var _a;
        return (_a = this._args) === null || _a === void 0 ? void 0 : _a.get(name);
    }
    addArgument(nameOrArg, type, defaultValue) {
        let toAdd;
        if (typeof nameOrArg === 'string') {
            this.checkUpdate();
            toAdd = new ArgumentDefinition(nameOrArg);
            toAdd.defaultValue = defaultValue;
        }
        else {
            this.checkUpdate(nameOrArg);
            toAdd = nameOrArg;
        }
        if (this.argument(toAdd.name)) {
            throw error_1.ERRORS.INVALID_GRAPHQL.err(`Argument ${toAdd.name} already exists on field ${this.name}`);
        }
        if (!this._args) {
            this._args = new utils_1.MapWithCachedArrays();
        }
        this._args.set(toAdd.name, toAdd);
        Element.prototype['setParent'].call(toAdd, this);
        if (typeof nameOrArg === 'string') {
            toAdd.type = type;
        }
        this.onModification();
        return toAdd;
    }
    removeArgumentInternal(name) {
        var _a;
        (_a = this._args) === null || _a === void 0 ? void 0 : _a.delete(name);
    }
    get locations() {
        return this._locations;
    }
    addLocations(...locations) {
        let modified = false;
        for (const location of locations) {
            if (!this._locations.includes(location)) {
                this._locations.push(location);
                modified = true;
            }
        }
        if (modified) {
            this.onModification();
        }
        return this;
    }
    addAllLocations() {
        return this.addLocations(...Object.values(graphql_1.DirectiveLocation));
    }
    addAllTypeLocations() {
        return this.addLocations(graphql_1.DirectiveLocation.SCALAR, graphql_1.DirectiveLocation.OBJECT, graphql_1.DirectiveLocation.INTERFACE, graphql_1.DirectiveLocation.UNION, graphql_1.DirectiveLocation.ENUM, graphql_1.DirectiveLocation.INPUT_OBJECT);
    }
    removeLocations(...locations) {
        let modified = false;
        for (const location of locations) {
            modified || (modified = (0, utils_1.removeArrayElement)(location, this._locations));
        }
        if (modified) {
            this.onModification();
        }
        return this;
    }
    hasExecutableLocations() {
        return this.locations.some((loc) => isExecutableDirectiveLocation(loc));
    }
    hasTypeSystemLocations() {
        return this.locations.some((loc) => isTypeSystemDirectiveLocation(loc));
    }
    applications() {
        var _a;
        (_a = this._referencers) !== null && _a !== void 0 ? _a : (this._referencers = new Set());
        return this._referencers;
    }
    addReferencer(referencer) {
        var _a;
        (0, utils_1.assert)(referencer, 'Referencer should exists');
        (_a = this._referencers) !== null && _a !== void 0 ? _a : (this._referencers = new Set());
        this._referencers.add(referencer);
    }
    removeReferencer(referencer) {
        var _a;
        (_a = this._referencers) === null || _a === void 0 ? void 0 : _a.delete(referencer);
    }
    removeTypeReference(type) {
        (0, utils_1.assert)(false, `Directive definition ${this} can't reference other types (it's arguments can); shouldn't be asked to remove reference to ${type}`);
    }
    remove() {
        var _a;
        if (!this._parent) {
            return [];
        }
        this.checkRemoval();
        this.onModification();
        this.sourceAST = undefined;
        (0, utils_1.assert)(!this._appliedDirectives || this._appliedDirectives.length === 0, "Directive definition should not have directive applied to it");
        for (const arg of this.arguments()) {
            arg.remove();
        }
        const toReturn = Array.from((_a = this._referencers) !== null && _a !== void 0 ? _a : []);
        this._referencers = undefined;
        Schema.prototype['removeDirectiveInternal'].call(this._parent, this);
        this._parent = undefined;
        return toReturn;
    }
    removeRecursive() {
        this.remove().forEach(ref => ref.remove());
    }
    toAST() {
        const doc = (0, graphql_1.parse)((0, print_1.printDirectiveDefinition)(this));
        return doc.definitions[0];
    }
    toString() {
        return `@${this.name}`;
    }
}
exports.DirectiveDefinition = DirectiveDefinition;
class Directive extends Element {
    constructor(name, _args = Object.create(null)) {
        super();
        this.name = name;
        this._args = _args;
    }
    schema() {
        return this.parent.schema();
    }
    get definition() {
        if (!this.isAttached()) {
            return undefined;
        }
        const doc = this.schema();
        return doc.directive(this.name);
    }
    arguments(includeDefaultValues = false) {
        if (!includeDefaultValues) {
            return this._args;
        }
        const definition = this.definition;
        (0, utils_1.assert)(definition, () => `Cannot include default values for arguments: cannot find directive definition for ${this.name}`);
        const updated = Object.create(null);
        for (const argDef of definition.arguments()) {
            const argValue = (0, values_1.withDefaultValues)(this._args[argDef.name], argDef);
            if (argValue !== undefined) {
                updated[argDef.name] = argValue;
            }
        }
        return updated;
    }
    onModification() {
        if (this.isAttachedToSchemaElement()) {
            Schema.prototype['onModification'].call(this.schema());
        }
    }
    isAttachedToSchemaElement() {
        return this.isAttached();
    }
    setArguments(args) {
        this._args = args;
        this.onModification();
    }
    argumentType(name) {
        var _a, _b;
        return (_b = (_a = this.definition) === null || _a === void 0 ? void 0 : _a.argument(name)) === null || _b === void 0 ? void 0 : _b.type;
    }
    matchArguments(expectedArgs) {
        const entries = Object.entries(this._args);
        if (entries.length !== Object.keys(expectedArgs).length) {
            return false;
        }
        for (const [key, val] of entries) {
            if (!(key in expectedArgs)) {
                return false;
            }
            const expectedVal = expectedArgs[key];
            if (!(0, values_1.valueEquals)(expectedVal, val)) {
                return false;
            }
        }
        return true;
    }
    ofExtension() {
        return this._extension;
    }
    removeOfExtension() {
        this._extension = undefined;
    }
    setOfExtension(extension) {
        this.checkUpdate();
        if (extension) {
            const parent = this.parent;
            (0, utils_1.assert)(parent instanceof SchemaDefinition || parent instanceof BaseNamedType, 'Can only mark directive parts of extensions when directly apply to type or schema definition.');
            (0, utils_1.assert)(parent.hasExtension(extension), () => `Cannot mark directive ${this.name} as part of the provided extension: it is not an extension of parent ${parent}`);
        }
        this._extension = extension;
        this.onModification();
    }
    argumentsToAST() {
        const entries = Object.entries(this._args);
        if (entries.length === 0) {
            return undefined;
        }
        const definition = this.definition;
        (0, utils_1.assert)(definition, () => `Cannot convert arguments of detached directive ${this}`);
        return entries.map(([n, v]) => {
            return {
                kind: graphql_1.Kind.ARGUMENT,
                name: { kind: graphql_1.Kind.NAME, value: n },
                value: (0, values_1.valueToAST)(v, definition.argument(n).type),
            };
        });
    }
    remove() {
        if (!this._parent) {
            return false;
        }
        this.onModification();
        const coreFeatures = this.schema().coreFeatures;
        if (coreFeatures && this.name === coreFeatures.coreItself.nameInSchema) {
            const url = coreSpec_1.FeatureUrl.parse(this._args[coreFeatures.coreDefinition.urlArgName()]);
            if (url.identity === coreFeatures.coreItself.url.identity) {
                Schema.prototype['unmarkAsCoreSchema'].call(this.schema());
                for (const d of this.schema().schemaDefinition.appliedDirectivesOf(coreFeatures.coreItself.nameInSchema)) {
                    d.removeInternal();
                }
                return true;
            }
            else {
                CoreFeatures.prototype['removeFeature'].call(coreFeatures, url.identity);
            }
        }
        return this.removeInternal();
    }
    removeInternal() {
        if (!this._parent) {
            return false;
        }
        const definition = this.definition;
        if (definition && this.isAttachedToSchemaElement()) {
            DirectiveDefinition.prototype['removeReferencer'].call(definition, this);
        }
        const parentDirectives = this._parent.appliedDirectives;
        const removed = (0, utils_1.removeArrayElement)(this, parentDirectives);
        (0, utils_1.assert)(removed, () => `Directive ${this} lists ${this._parent} as parent, but that parent doesn't list it as applied directive`);
        this._parent = undefined;
        this._extension = undefined;
        return true;
    }
    toString() {
        const entries = Object.entries(this._args).filter(([_, v]) => v !== undefined);
        const args = entries.length == 0 ? '' : '(' + entries.map(([n, v]) => `${n}: ${(0, values_1.valueToString)(v, this.argumentType(n))}`).join(', ') + ')';
        return `@${this.name}${args}`;
    }
}
exports.Directive = Directive;
function directivesToString(directives) {
    return (!directives || directives.length == 0)
        ? ''
        : ' ' + directives.join(' ');
}
exports.directivesToString = directivesToString;
function directivesToDirectiveNodes(directives) {
    return (!directives || directives.length === 0)
        ? undefined
        : directives.map(directive => {
            return {
                kind: graphql_1.Kind.DIRECTIVE,
                name: {
                    kind: graphql_1.Kind.NAME,
                    value: directive.name,
                },
                arguments: directive.argumentsToAST()
            };
        });
}
exports.directivesToDirectiveNodes = directivesToDirectiveNodes;
function sameDirectiveApplication(application1, application2, directivesNeverEqualToThemselves = ['defer']) {
    return application1.name === application2.name
        && !directivesNeverEqualToThemselves.includes(application1.name)
        && !directivesNeverEqualToThemselves.includes(application2.name)
        && (0, values_1.argumentsEquals)(application1.arguments(), application2.arguments());
}
exports.sameDirectiveApplication = sameDirectiveApplication;
function sameDirectiveApplications(applications1, applications2, directivesNeverEqualToThemselves = ['defer']) {
    if (applications1.length !== applications2.length) {
        return false;
    }
    for (const directive1 of applications1) {
        if (!applications2.some(directive2 => sameDirectiveApplication(directive1, directive2, directivesNeverEqualToThemselves))) {
            return false;
        }
    }
    return true;
}
exports.sameDirectiveApplications = sameDirectiveApplications;
function isDirectiveApplicationsSubset(applications, maybeSubset) {
    if (maybeSubset.length > applications.length) {
        return false;
    }
    for (const directive1 of maybeSubset) {
        if (!applications.some(directive2 => sameDirectiveApplication(directive1, directive2))) {
            return false;
        }
    }
    return true;
}
exports.isDirectiveApplicationsSubset = isDirectiveApplicationsSubset;
function directiveApplicationsSubstraction(baseApplications, toRemove) {
    return baseApplications.filter((application) => !toRemove.some((other) => sameDirectiveApplication(application, other)));
}
exports.directiveApplicationsSubstraction = directiveApplicationsSubstraction;
class Variable {
    constructor(name) {
        this.name = name;
    }
    toVariableNode() {
        return {
            kind: graphql_1.Kind.VARIABLE,
            name: { kind: graphql_1.Kind.NAME, value: this.name },
        };
    }
    toString() {
        return '$' + this.name;
    }
}
exports.Variable = Variable;
class VariableCollector {
    constructor() {
        this._variables = new Map();
    }
    add(variable) {
        this._variables.set(variable.name, variable);
    }
    addAll(variables) {
        for (const variable of variables) {
            this.add(variable);
        }
    }
    collectInArguments(args) {
        for (const value of Object.values(args)) {
            (0, values_1.collectVariablesInValue)(value, this);
        }
    }
    variables() {
        return (0, utils_1.mapValues)(this._variables);
    }
    toString() {
        return this.variables().toString();
    }
}
exports.VariableCollector = VariableCollector;
function isVariable(v) {
    return v instanceof Variable;
}
exports.isVariable = isVariable;
class VariableDefinition extends DirectiveTargetElement {
    constructor(schema, variable, type, defaultValue) {
        super(schema);
        this.variable = variable;
        this.type = type;
        this.defaultValue = defaultValue;
    }
    toVariableDefinitionNode() {
        const ast = (0, values_1.valueToAST)(this.defaultValue, this.type);
        return {
            kind: graphql_1.Kind.VARIABLE_DEFINITION,
            variable: this.variable.toVariableNode(),
            type: typeToAST(this.type),
            defaultValue: (ast !== undefined) ? (0, values_1.valueNodeToConstValueNode)(ast) : undefined,
            directives: this.appliedDirectivesToDirectiveNodes(),
        };
    }
    toString() {
        let base = this.variable + ': ' + this.type;
        if (this.defaultValue !== undefined) {
            base = base + ' = ' + (0, values_1.valueToString)(this.defaultValue, this.type);
        }
        return base + this.appliedDirectivesToString();
    }
}
exports.VariableDefinition = VariableDefinition;
class VariableDefinitions {
    constructor() {
        this._definitions = new utils_1.MapWithCachedArrays();
    }
    add(definition) {
        if (this._definitions.has(definition.variable.name)) {
            return false;
        }
        this._definitions.set(definition.variable.name, definition);
        return true;
    }
    addAll(definitions) {
        for (const definition of definitions._definitions.values()) {
            this.add(definition);
        }
    }
    definition(variable) {
        const varName = typeof variable === 'string' ? variable : variable.name;
        return this._definitions.get(varName);
    }
    isEmpty() {
        return this._definitions.size === 0;
    }
    definitions() {
        return this._definitions.values();
    }
    filter(variables) {
        if (variables.length === 0) {
            return new VariableDefinitions();
        }
        const newDefs = new VariableDefinitions();
        for (const variable of variables) {
            const def = this.definition(variable);
            if (!def) {
                throw new Error(`Cannot find variable ${variable} in definitions ${this}`);
            }
            newDefs.add(def);
        }
        return newDefs;
    }
    toVariableDefinitionNodes() {
        if (this._definitions.size === 0) {
            return undefined;
        }
        return this.definitions().map(def => def.toVariableDefinitionNode());
    }
    toString() {
        return '(' + this.definitions().join(', ') + ')';
    }
}
exports.VariableDefinitions = VariableDefinitions;
function variableDefinitionsFromAST(schema, definitionNodes) {
    const definitions = new VariableDefinitions();
    for (const definitionNode of definitionNodes) {
        if (!definitions.add(variableDefinitionFromAST(schema, definitionNode))) {
            const name = definitionNode.variable.name.value;
            throw error_1.ERRORS.INVALID_GRAPHQL.err(`Duplicate definition for variable ${name}`, { nodes: definitionNodes.filter(n => n.variable.name.value === name) });
        }
    }
    return definitions;
}
exports.variableDefinitionsFromAST = variableDefinitionsFromAST;
function variableDefinitionFromAST(schema, definitionNode) {
    const variable = new Variable(definitionNode.variable.name.value);
    const type = typeFromAST(schema, definitionNode.type);
    if (!isInputType(type)) {
        throw error_1.ERRORS.INVALID_GRAPHQL.err(`Invalid type "${type}" for variable $${variable}: not an input type`, { nodes: definitionNode.type });
    }
    const def = new VariableDefinition(schema, variable, type, definitionNode.defaultValue ? (0, values_1.valueFromAST)(definitionNode.defaultValue, type) : undefined);
    return def;
}
exports.variableDefinitionFromAST = variableDefinitionFromAST;
function addReferenceToType(referencer, type) {
    switch (type.kind) {
        case 'ListType':
            addReferenceToType(referencer, type.baseType());
            break;
        case 'NonNullType':
            addReferenceToType(referencer, type.baseType());
            break;
        default:
            BaseNamedType.prototype['addReferencer'].call(type, referencer);
            break;
    }
}
function removeReferenceToType(referencer, type) {
    switch (type.kind) {
        case 'ListType':
            removeReferenceToType(referencer, type.baseType());
            break;
        case 'NonNullType':
            removeReferenceToType(referencer, type.baseType());
            break;
        default:
            BaseNamedType.prototype['removeReferencer'].call(type, referencer);
            break;
    }
}
function newNamedType(kind, name) {
    switch (kind) {
        case 'ScalarType':
            return new ScalarType(name);
        case 'ObjectType':
            return new ObjectType(name);
        case 'InterfaceType':
            return new InterfaceType(name);
        case 'UnionType':
            return new UnionType(name);
        case 'EnumType':
            return new EnumType(name);
        case 'InputObjectType':
            return new InputObjectType(name);
        default:
            (0, utils_1.assert)(false, `Unhandled kind ${kind} for type ${name}`);
    }
}
exports.newNamedType = newNamedType;
function* typesToCopy(source, dest) {
    var _a;
    for (const type of source.builtInTypes()) {
        if (!type.isIntrospectionType() && !((_a = dest.type(type.name)) === null || _a === void 0 ? void 0 : _a.isBuiltIn)) {
            yield type;
        }
    }
    yield* source.types();
}
function* directivesToCopy(source, dest) {
    var _a;
    for (const directive of source.builtInDirectives()) {
        if (!((_a = dest.directive(directive.name)) === null || _a === void 0 ? void 0 : _a.isBuiltIn)) {
            yield directive;
        }
    }
    yield* source.directives();
}
function copyDirectiveDefinitionToSchema({ definition, schema, copyDirectiveApplicationsInArguments = true, locationFilter, }) {
    copyDirectiveDefinitionInner(definition, schema.addDirectiveDefinition(definition.name), copyDirectiveApplicationsInArguments, locationFilter);
}
exports.copyDirectiveDefinitionToSchema = copyDirectiveDefinitionToSchema;
function copy(source, dest, cloneJoinDirectives) {
    for (const type of typesToCopy(source, dest)) {
        dest.addType(newNamedType(type.kind, type.name));
    }
    for (const directive of directivesToCopy(source, dest)) {
        dest.addDirectiveDefinition(directive.name);
    }
    for (const directive of directivesToCopy(source, dest)) {
        copyDirectiveDefinitionInner(directive, dest.directive(directive.name));
    }
    copySchemaDefinitionInner(source.schemaDefinition, dest.schemaDefinition);
    for (const type of typesToCopy(source, dest)) {
        copyNamedTypeInner(type, dest.type(type.name), cloneJoinDirectives);
    }
}
function copyExtensions(source, dest) {
    const extensionMap = new Map();
    for (const sourceExtension of source.extensions()) {
        const destExtension = new Extension();
        dest.addExtension(destExtension);
        extensionMap.set(sourceExtension, destExtension);
    }
    return extensionMap;
}
function copyOfExtension(extensionsMap, source, dest) {
    const toCopy = source.ofExtension();
    if (toCopy) {
        dest.setOfExtension(extensionsMap.get(toCopy));
    }
}
function copySchemaDefinitionInner(source, dest) {
    dest.preserveEmptyDefinition = source.preserveEmptyDefinition;
    const extensionsMap = copyExtensions(source, dest);
    for (const rootType of source.roots()) {
        copyOfExtension(extensionsMap, rootType, dest.setRoot(rootType.rootKind, rootType.type.name));
    }
    for (const directive of source.appliedDirectives) {
        copyOfExtension(extensionsMap, directive, copyAppliedDirective(directive, dest));
    }
    dest.description = source.description;
    dest.sourceAST = source.sourceAST;
}
function copyNamedTypeInner(source, dest, cloneJoinDirectives) {
    dest.preserveEmptyDefinition = source.preserveEmptyDefinition;
    const extensionsMap = copyExtensions(source, dest);
    for (const directive of source.appliedDirectives) {
        copyOfExtension(extensionsMap, directive, copyAppliedDirective(directive, dest));
    }
    dest.description = source.description;
    dest.sourceAST = source.sourceAST;
    switch (source.kind) {
        case 'ObjectType':
        case 'InterfaceType':
            const destFieldBasedType = dest;
            for (const sourceField of source.fields()) {
                const destField = destFieldBasedType.addField(new FieldDefinition(sourceField.name));
                copyOfExtension(extensionsMap, sourceField, destField);
                copyFieldDefinitionInner(sourceField, destField, cloneJoinDirectives);
            }
            for (const sourceImpl of source.interfaceImplementations()) {
                const destImpl = destFieldBasedType.addImplementedInterface(sourceImpl.interface.name);
                copyOfExtension(extensionsMap, sourceImpl, destImpl);
            }
            break;
        case 'UnionType':
            const destUnionType = dest;
            for (const sourceType of source.members()) {
                const destType = destUnionType.addType(sourceType.type.name);
                copyOfExtension(extensionsMap, sourceType, destType);
            }
            break;
        case 'EnumType':
            const destEnumType = dest;
            for (const sourceValue of source.values) {
                const destValue = destEnumType.addValue(sourceValue.name);
                destValue.description = sourceValue.description;
                copyOfExtension(extensionsMap, sourceValue, destValue);
                copyAppliedDirectives(sourceValue, destValue, cloneJoinDirectives);
            }
            break;
        case 'InputObjectType':
            const destInputType = dest;
            for (const sourceField of source.fields()) {
                const destField = destInputType.addField(new InputFieldDefinition(sourceField.name));
                copyOfExtension(extensionsMap, sourceField, destField);
                copyInputFieldDefinitionInner(sourceField, destField, cloneJoinDirectives);
            }
    }
}
function copyAppliedDirectives(source, dest, cloneJoinDirectives) {
    source.appliedDirectives.filter(d => cloneJoinDirectives || !d.name.startsWith('join__')).forEach((d) => copyAppliedDirective(d, dest));
}
function copyAppliedDirective(source, dest) {
    const res = dest.applyDirective(source.name, { ...source.arguments() });
    res.sourceAST = source.sourceAST;
    return res;
}
function copyFieldDefinitionInner(source, dest, cloneJoinDirectives) {
    const type = copyWrapperTypeOrTypeRef(source.type, dest.schema());
    dest.type = type;
    for (const arg of source.arguments()) {
        const argType = copyWrapperTypeOrTypeRef(arg.type, dest.schema());
        copyArgumentDefinitionInner({
            source: arg,
            dest: dest.addArgument(arg.name, argType),
            cloneJoinDirectives,
        });
    }
    copyAppliedDirectives(source, dest, cloneJoinDirectives);
    dest.description = source.description;
    dest.sourceAST = source.sourceAST;
}
function copyInputFieldDefinitionInner(source, dest, cloneJoinDirectives) {
    const type = copyWrapperTypeOrTypeRef(source.type, dest.schema());
    dest.type = type;
    dest.defaultValue = source.defaultValue;
    copyAppliedDirectives(source, dest, cloneJoinDirectives);
    dest.description = source.description;
    dest.sourceAST = source.sourceAST;
}
function copyWrapperTypeOrTypeRef(source, destParent) {
    if (!source) {
        return undefined;
    }
    switch (source.kind) {
        case 'ListType':
            return new ListType(copyWrapperTypeOrTypeRef(source.ofType, destParent));
        case 'NonNullType':
            return new NonNullType(copyWrapperTypeOrTypeRef(source.ofType, destParent));
        default:
            return destParent.type(source.name);
    }
}
function copyArgumentDefinitionInner({ source, dest, copyDirectiveApplications = true, cloneJoinDirectives, }) {
    const type = copyWrapperTypeOrTypeRef(source.type, dest.schema());
    dest.type = type;
    dest.defaultValue = source.defaultValue;
    if (copyDirectiveApplications) {
        copyAppliedDirectives(source, dest, cloneJoinDirectives);
    }
    dest.description = source.description;
    dest.sourceAST = source.sourceAST;
}
function copyDirectiveDefinitionInner(source, dest, copyDirectiveApplicationsInArguments = true, locationFilter) {
    let locations = source.locations;
    if (locationFilter) {
        locations = locations.filter((loc) => locationFilter(loc));
    }
    if (locations.length === 0) {
        return;
    }
    for (const arg of source.arguments()) {
        const type = copyWrapperTypeOrTypeRef(arg.type, dest.schema());
        copyArgumentDefinitionInner({
            source: arg,
            dest: dest.addArgument(arg.name, type),
            copyDirectiveApplications: copyDirectiveApplicationsInArguments,
            cloneJoinDirectives: true,
        });
    }
    dest.repeatable = source.repeatable;
    dest.addLocations(...locations);
    dest.sourceAST = source.sourceAST;
    dest.description = source.description;
}
function isFieldDefinition(elem) {
    return elem instanceof FieldDefinition;
}
exports.isFieldDefinition = isFieldDefinition;
//# sourceMappingURL=definitions.js.map