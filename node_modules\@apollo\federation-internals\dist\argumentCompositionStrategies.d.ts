import { InputType, Schema } from "./definitions";
type TypeSupportValidator = (schema: Schema, type: InputType) => {
    valid: boolean;
    supportedMsg?: string;
};
export type ArgumentCompositionStrategy = {
    name: string;
    isTypeSupported: TypeSupportValidator;
    mergeValues: (values: any[]) => any;
};
declare function unionValues(values: any[]): any;
export declare const ARGUMENT_COMPOSITION_STRATEGIES: {
    MAX: {
        name: string;
        isTypeSupported: TypeSupportValidator;
        mergeValues: (values: number[]) => number;
    };
    MIN: {
        name: string;
        isTypeSupported: TypeSupportValidator;
        mergeValues: (values: number[]) => number;
    };
    INTERSECTION: {
        name: string;
        isTypeSupported: TypeSupportValidator;
        mergeValues: (values: any[]) => any;
    };
    UNION: {
        name: string;
        isTypeSupported: TypeSupportValidator;
        mergeValues: typeof unionValues;
    };
    NULLABLE_AND: {
        name: string;
        isTypeSupported: TypeSupportValidator;
        mergeValues: (values: (boolean | null | undefined)[]) => boolean | undefined;
    };
    NULLABLE_MAX: {
        name: string;
        isTypeSupported: TypeSupportValidator;
        mergeValues: (values: (number | null | undefined)[]) => number | undefined;
    };
    NULLABLE_UNION: {
        name: string;
        isTypeSupported: TypeSupportValidator;
        mergeValues: (values: any[]) => any;
    };
};
export {};
//# sourceMappingURL=argumentCompositionStrategies.d.ts.map