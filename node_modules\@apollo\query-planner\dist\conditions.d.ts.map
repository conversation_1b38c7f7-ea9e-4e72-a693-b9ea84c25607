{"version": 3, "file": "conditions.d.ts", "sourceRoot": "", "sources": ["../src/conditions.ts"], "names": [], "mappings": "AAAA,OAAO,EAKL,aAAa,EAIb,YAAY,EACZ,QAAQ,EACR,mBAAmB,EACpB,MAAM,8BAA8B,CAAC;AAEtC,OAAO,EAAE,aAAa,EAAE,MAAM,GAAG,CAAC;AAElC,MAAM,MAAM,iBAAiB,GAAG;IAC9B,QAAQ,EAAE,QAAQ,CAAC;IACnB,OAAO,EAAE,OAAO,CAAC;CAClB,CAAA;AAED,MAAM,MAAM,SAAS,GAAG,iBAAiB,GAAG,OAAO,CAAC;AAKpD,MAAM,MAAM,UAAU,GAAG,aAAa,CAAC,iBAAiB,CAAC,GAAG,OAAO,CAAC;AAEpE,wBAAgB,mBAAmB,CAAC,IAAI,EAAE,SAAS,GAAG,UAAU,GAAG,IAAI,IAAI,OAAO,CAEjF;AAED,wBAAgB,eAAe,CAAC,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,GAAG,UAAU,CAwB5F;AAcD,wBAAgB,wBAAwB,CAAC,YAAY,EAAE,YAAY,GAAG,UAAU,CAqB/E;AAgED,wBAAgB,iBAAiB,CAAC,aAAa,EAAE,UAAU,EAAE,iBAAiB,EAAE,UAAU,GAAG,UAAU,CAoBtG;AAED,wBAAgB,gCAAgC,CAAC,YAAY,EAAE,YAAY,EAAE,UAAU,EAAE,UAAU,GAAG,YAAY,CA4BjH;AA+BD,wBAAgB,iBAAiB,CAC/B,SAAS,EAAE,aAAa,EACxB,SAAS,EAAE,mBAAmB,EAC9B,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,SAAS,GACtC,OAAO,CAST"}