import { ASTNode, FieldNode, GraphQLNullableType, ListTypeNode, NamedTypeNode } from 'graphql';
export declare function getResponseName(node: FieldNode): string;
export declare function allNodesAreOfSameKind<T extends ASTNode>(firstNode: T, remainingNodes: ASTNode[]): remainingNodes is T[];
export declare function astFromType(type: GraphQLNullableType): NamedTypeNode | ListTypeNode;
//# sourceMappingURL=graphql.d.ts.map