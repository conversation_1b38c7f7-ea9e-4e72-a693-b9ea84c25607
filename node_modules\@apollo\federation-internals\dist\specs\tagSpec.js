"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TAG_VERSIONS = exports.TagSpecDefinition = exports.tagIdentity = void 0;
const graphql_1 = require("graphql");
const coreSpec_1 = require("./coreSpec");
const definitions_1 = require("../definitions");
const directiveAndTypeSpecification_1 = require("../directiveAndTypeSpecification");
const error_1 = require("../error");
const knownCoreFeatures_1 = require("../knownCoreFeatures");
const types_1 = require("../types");
exports.tagIdentity = 'https://specs.apollo.dev/tag';
class TagSpecDefinition extends coreSpec_1.FeatureDefinition {
    constructor(version, minimumFederationVersion) {
        super(new coreSpec_1.FeatureUrl(exports.tagIdentity, 'tag', version), minimumFederationVersion);
        this.tagLocations = [
            graphql_1.DirectiveLocation.FIELD_DEFINITION,
            graphql_1.DirectiveLocation.OBJECT,
            graphql_1.DirectiveLocation.INTERFACE,
            graphql_1.DirectiveLocation.UNION,
        ];
        this.printedTagDefinition = 'directive @tag(name: String!) repeatable on FIELD_DEFINITION | INTERFACE | OBJECT | UNION';
        if (!this.isV01()) {
            this.tagLocations.push(graphql_1.DirectiveLocation.ARGUMENT_DEFINITION, graphql_1.DirectiveLocation.SCALAR, graphql_1.DirectiveLocation.ENUM, graphql_1.DirectiveLocation.ENUM_VALUE, graphql_1.DirectiveLocation.INPUT_OBJECT, graphql_1.DirectiveLocation.INPUT_FIELD_DEFINITION);
            this.printedTagDefinition = 'directive @tag(name: String!) repeatable on FIELD_DEFINITION | INTERFACE | OBJECT | UNION | ARGUMENT_DEFINITION | SCALAR | ENUM | ENUM_VALUE | INPUT_OBJECT | INPUT_FIELD_DEFINITION';
            if (!this.isV02()) {
                this.tagLocations.push(graphql_1.DirectiveLocation.SCHEMA);
                this.printedTagDefinition = 'directive @tag(name: String!) repeatable on FIELD_DEFINITION | INTERFACE | OBJECT | UNION | ARGUMENT_DEFINITION | SCALAR | ENUM | ENUM_VALUE | INPUT_OBJECT | INPUT_FIELD_DEFINITION | SCHEMA';
            }
        }
        this.tagDirectiveSpec = (0, directiveAndTypeSpecification_1.createDirectiveSpecification)({
            name: 'tag',
            locations: this.tagLocations,
            repeatable: true,
            args: [{ name: 'name', type: (schema) => new definitions_1.NonNullType(schema.stringType()) }],
            composes: true,
            supergraphSpecification: (fedVersion) => exports.TAG_VERSIONS.getMinimumRequiredVersion(fedVersion),
        });
        this.registerDirective(this.tagDirectiveSpec);
    }
    isV01() {
        return this.version.equals(new coreSpec_1.FeatureVersion(0, 1));
    }
    isV02() {
        return this.version.equals(new coreSpec_1.FeatureVersion(0, 2));
    }
    checkCompatibleDirective(definition) {
        const hasUnknownArguments = Object.keys(definition.arguments()).length > 1;
        const nameArg = definition.argument('name');
        const hasValidNameArg = nameArg && (0, types_1.sameType)(nameArg.type, new definitions_1.NonNullType(definition.schema().stringType()));
        const hasValidLocations = definition.locations.every(loc => this.tagLocations.includes(loc));
        if (hasUnknownArguments || !hasValidNameArg || !hasValidLocations) {
            return error_1.ERRORS.DIRECTIVE_DEFINITION_INVALID.err(`Found invalid @tag directive definition. Please ensure the directive definition in your schema's definitions matches the following:\n\t${this.printedTagDefinition}`);
        }
        return undefined;
    }
}
exports.TagSpecDefinition = TagSpecDefinition;
exports.TAG_VERSIONS = new coreSpec_1.FeatureDefinitions(exports.tagIdentity)
    .add(new TagSpecDefinition(new coreSpec_1.FeatureVersion(0, 1)))
    .add(new TagSpecDefinition(new coreSpec_1.FeatureVersion(0, 2)))
    .add(new TagSpecDefinition(new coreSpec_1.FeatureVersion(0, 3), new coreSpec_1.FeatureVersion(2, 0)));
(0, knownCoreFeatures_1.registerKnownFeature)(exports.TAG_VERSIONS);
//# sourceMappingURL=tagSpec.js.map