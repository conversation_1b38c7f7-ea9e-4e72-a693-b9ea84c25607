import { Controller, Get, UseGuards, Request } from '@nestjs/common';
import { ApiT<PERSON>s, ApiBearerAuth } from '@nestjs/swagger';

import { JwtAuthGuard, RolesGuard } from '@app/security/guards';
import { Roles } from '@app/security/decorators/roles.decorator';
import { UserRole } from '@app/common';
import { RequestWithUser } from '@app/security';

import { PositionService } from '../services/position.service';

@ApiTags('Positions')
@Controller('positions')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
export class PositionController {
  constructor(private readonly positionService: PositionService) {}

  @Get()
  @Roles(UserRole.ADMIN, UserRole.HR_MANAGER, UserRole.MANAGER, UserRole.EMPLOYEE, UserRole.SUPER_ADMIN)
  async findAll(@Request() req: RequestWithUser) {
    return this.positionService.findAll(req.user.tenantId);
  }
}
