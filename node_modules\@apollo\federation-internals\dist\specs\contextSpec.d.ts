import { CorePurpose, FeatureDefinition, FeatureDefinitions, FeatureVersion } from "./coreSpec";
import { DirectiveDefinition, Schema } from "../definitions";
import { DirectiveSpecification } from "../directiveAndTypeSpecification";
export declare enum ContextDirectiveName {
    CONTEXT = "context",
    FROM_CONTEXT = "fromContext"
}
export declare class ContextSpecDefinition extends FeatureDefinition {
    static readonly directiveName = "context";
    static readonly identity: string;
    readonly contextDirectiveSpec: DirectiveSpecification;
    readonly fromContextDirectiveSpec: DirectiveSpecification;
    constructor(version: FeatureVersion);
    get defaultCorePurpose(): CorePurpose;
    contextDirective(schema: Schema): DirectiveDefinition<{
        name: string;
    }> | undefined;
}
export declare const CONTEXT_VERSIONS: FeatureDefinitions<ContextSpecDefinition>;
//# sourceMappingURL=contextSpec.d.ts.map