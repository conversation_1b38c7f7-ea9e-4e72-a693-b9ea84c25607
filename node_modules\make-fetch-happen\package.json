{"name": "make-fetch-happen", "version": "11.1.1", "description": "Opinionated, caching, retrying fetch client", "main": "lib/index.js", "files": ["bin/", "lib/"], "scripts": {"test": "tap", "posttest": "npm run lint", "eslint": "eslint", "lint": "eslint \"**/*.js\"", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "snap": "tap", "template-oss-apply": "template-oss-apply --force"}, "repository": {"type": "git", "url": "https://github.com/npm/make-fetch-happen.git"}, "keywords": ["http", "request", "fetch", "mean girls", "caching", "cache", "subresource integrity"], "author": "GitHub Inc.", "license": "ISC", "dependencies": {"agentkeepalive": "^4.2.1", "cacache": "^17.0.0", "http-cache-semantics": "^4.1.1", "http-proxy-agent": "^5.0.0", "https-proxy-agent": "^5.0.0", "is-lambda": "^1.0.1", "lru-cache": "^7.7.1", "minipass": "^5.0.0", "minipass-fetch": "^3.0.0", "minipass-flush": "^1.0.5", "minipass-pipeline": "^1.2.4", "negotiator": "^0.6.3", "promise-retry": "^2.0.1", "socks-proxy-agent": "^7.0.0", "ssri": "^10.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.14.1", "nock": "^13.2.4", "safe-buffer": "^5.2.1", "standard-version": "^9.3.2", "tap": "^16.0.0"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "tap": {"color": 1, "files": "test/*.js", "check-coverage": true, "timeout": 60, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.14.1", "publish": "true"}}