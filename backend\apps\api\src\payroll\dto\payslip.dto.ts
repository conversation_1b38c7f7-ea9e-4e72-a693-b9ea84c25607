import { IsString, IsOptional, IsEnum, IsNumber, IsBoolean, IsArray, ValidateNested, IsObject, IsUUID, IsDateString } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PayrollStatus, Currency, PaymentMethod } from '@app/common/enums/status.enum';

export class PayslipItemDto {
  @ApiProperty({
    description: 'Payroll item code',
    example: 'SALARY',
  })
  code!: string;

  @ApiProperty({
    description: 'Payroll item name',
    example: 'Base Salary',
  })
  name!: string;

  @ApiProperty({
    description: 'Item category',
    example: 'earnings',
  })
  category!: string;

  @ApiProperty({
    description: 'Item amount',
    example: 5000.00,
  })
  amount!: number;

  @ApiProperty({
    description: 'Item currency',
    enum: Currency,
    example: Currency.USD,
  })
  currency!: Currency;

  @ApiPropertyOptional({
    description: 'Rate for hourly calculations',
    example: 25.00,
  })
  rate?: number;

  @ApiPropertyOptional({
    description: 'Units/hours for rate-based calculations',
    example: 160,
  })
  units?: number;

  @ApiPropertyOptional({
    description: 'Percentage for percentage-based calculations',
    example: 10.5,
  })
  percentage?: number;

  @ApiProperty({
    description: 'Whether this item is taxable',
    example: true,
  })
  isTaxable!: boolean;

  @ApiProperty({
    description: 'Whether this is a pre-tax deduction',
    example: false,
  })
  isPreTax!: boolean;
}

export class CreatePayslipDto {
  @ApiProperty({
    description: 'Employee ID',
    example: 'uuid-string',
  })
  @IsUUID()
  employeeId!: string;

  @ApiProperty({
    description: 'Payroll period ID',
    example: 'uuid-string',
  })
  @IsUUID()
  payrollPeriodId!: string;

  @ApiProperty({
    description: 'Pay date',
    example: '2024-02-05',
  })
  @IsDateString()
  payDate!: string;

  @ApiProperty({
    description: 'Period start date',
    example: '2024-01-01',
  })
  @IsDateString()
  periodStartDate!: string;

  @ApiProperty({
    description: 'Period end date',
    example: '2024-01-31',
  })
  @IsDateString()
  periodEndDate!: string;

  @ApiProperty({
    description: 'Payslip currency',
    enum: Currency,
    example: Currency.USD,
  })
  @IsEnum(Currency)
  currency!: Currency;

  @ApiProperty({
    description: 'Regular hours worked',
    example: 160,
  })
  @IsNumber()
  regularHours!: number;

  @ApiProperty({
    description: 'Overtime hours worked',
    example: 8,
  })
  @IsNumber()
  overtimeHours!: number;

  @ApiProperty({
    description: 'Payment method',
    enum: PaymentMethod,
    example: PaymentMethod.DIRECT_DEPOSIT,
  })
  @IsEnum(PaymentMethod)
  paymentMethod!: PaymentMethod;

  @ApiProperty({
    description: 'Payroll items',
    type: [PayslipItemDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PayslipItemDto)
  payrollItems!: PayslipItemDto[];

  @ApiPropertyOptional({
    description: 'Payment reference number',
    example: 'PAY-2024-001-001',
  })
  @IsOptional()
  @IsString()
  paymentReference?: string;
}

export class UpdatePayslipDto {
  @ApiPropertyOptional({
    description: 'Payslip status',
    enum: PayrollStatus,
    example: PayrollStatus.APPROVED,
  })
  @IsOptional()
  @IsEnum(PayrollStatus)
  status?: PayrollStatus;

  @ApiPropertyOptional({
    description: 'Pay date',
    example: '2024-02-05',
  })
  @IsOptional()
  @IsDateString()
  payDate?: string;

  @ApiPropertyOptional({
    description: 'Regular hours worked',
    example: 160,
  })
  @IsOptional()
  @IsNumber()
  regularHours?: number;

  @ApiPropertyOptional({
    description: 'Overtime hours worked',
    example: 8,
  })
  @IsOptional()
  @IsNumber()
  overtimeHours?: number;

  @ApiPropertyOptional({
    description: 'Payment method',
    enum: PaymentMethod,
    example: PaymentMethod.DIRECT_DEPOSIT,
  })
  @IsOptional()
  @IsEnum(PaymentMethod)
  paymentMethod?: PaymentMethod;

  @ApiPropertyOptional({
    description: 'Payment reference number',
    example: 'PAY-2024-001-001',
  })
  @IsOptional()
  @IsString()
  paymentReference?: string;

  @ApiPropertyOptional({
    description: 'Correction reason (if this is a correction)',
    example: 'Overtime hours adjustment',
  })
  @IsOptional()
  @IsString()
  correctionReason?: string;
}

export class PayslipQueryDto {
  @ApiPropertyOptional({
    description: 'Filter by employee ID',
    example: 'uuid-string',
  })
  @IsOptional()
  @IsUUID()
  employeeId?: string;

  @ApiPropertyOptional({
    description: 'Filter by payroll period ID',
    example: 'uuid-string',
  })
  @IsOptional()
  @IsUUID()
  payrollPeriodId?: string;

  @ApiPropertyOptional({
    description: 'Filter by status',
    enum: PayrollStatus,
  })
  @IsOptional()
  @IsEnum(PayrollStatus)
  status?: PayrollStatus;

  @ApiPropertyOptional({
    description: 'Filter by pay date (from)',
    example: '2024-01-01',
  })
  @IsOptional()
  @IsDateString()
  payDateFrom?: string;

  @ApiPropertyOptional({
    description: 'Filter by pay date (to)',
    example: '2024-12-31',
  })
  @IsOptional()
  @IsDateString()
  payDateTo?: string;

  @ApiPropertyOptional({
    description: 'Filter by currency',
    enum: Currency,
  })
  @IsOptional()
  @IsEnum(Currency)
  currency?: Currency;

  @ApiPropertyOptional({
    description: 'Filter by payment method',
    enum: PaymentMethod,
  })
  @IsOptional()
  @IsEnum(PaymentMethod)
  paymentMethod?: PaymentMethod;

  @ApiPropertyOptional({
    description: 'Include only correction payslips',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  correctionsOnly?: boolean;

  @ApiPropertyOptional({
    description: 'Page number for pagination',
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  page?: number;

  @ApiPropertyOptional({
    description: 'Number of items per page',
    example: 10,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  limit?: number;

  @ApiPropertyOptional({
    description: 'Sort field',
    example: 'payDate',
  })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @ApiPropertyOptional({
    description: 'Sort order',
    example: 'DESC',
  })
  @IsOptional()
  @IsString()
  sortOrder?: 'ASC' | 'DESC';
}

export class PayslipResponseDto {
  @ApiProperty({
    description: 'Payslip ID',
    example: 'uuid-string',
  })
  id!: string;

  @ApiProperty({
    description: 'Payslip number',
    example: 'PS-2024-001-001',
  })
  payslipNumber!: string;

  @ApiProperty({
    description: 'Employee information',
  })
  employee!: {
    id: string;
    employeeId: string;
    firstName: string;
    lastName: string;
    email: string;
    department: string;
    position: string;
  };

  @ApiProperty({
    description: 'Payroll period information',
  })
  payrollPeriod!: {
    id: string;
    name: string;
    frequency: string;
    startDate: string;
    endDate: string;
  };

  @ApiProperty({
    description: 'Payslip status',
    enum: PayrollStatus,
    example: PayrollStatus.PROCESSED,
  })
  status!: PayrollStatus;

  @ApiProperty({
    description: 'Pay date',
    example: '2024-02-05',
  })
  payDate!: string;

  @ApiProperty({
    description: 'Period start date',
    example: '2024-01-01',
  })
  periodStartDate!: string;

  @ApiProperty({
    description: 'Period end date',
    example: '2024-01-31',
  })
  periodEndDate!: string;

  @ApiProperty({
    description: 'Currency',
    enum: Currency,
    example: Currency.USD,
  })
  currency!: Currency;

  @ApiProperty({
    description: 'Total gross pay',
    example: 5000.00,
  })
  grossPay!: number;

  @ApiProperty({
    description: 'Total net pay',
    example: 3800.00,
  })
  netPay!: number;

  @ApiProperty({
    description: 'Total taxes',
    example: 900.00,
  })
  totalTaxes!: number;

  @ApiProperty({
    description: 'Total deductions',
    example: 300.00,
  })
  totalDeductions!: number;

  @ApiProperty({
    description: 'Total benefits',
    example: 200.00,
  })
  totalBenefits!: number;

  @ApiProperty({
    description: 'Regular hours worked',
    example: 160,
  })
  regularHours!: number;

  @ApiProperty({
    description: 'Overtime hours worked',
    example: 8,
  })
  overtimeHours!: number;

  @ApiProperty({
    description: 'Total hours worked',
    example: 168,
  })
  totalHours!: number;

  @ApiProperty({
    description: 'Payment method',
    enum: PaymentMethod,
    example: PaymentMethod.DIRECT_DEPOSIT,
  })
  paymentMethod!: PaymentMethod;

  @ApiPropertyOptional({
    description: 'Payment reference number',
    example: 'PAY-2024-001-001',
  })
  paymentReference?: string;

  @ApiPropertyOptional({
    description: 'Payment processed timestamp',
    example: '2024-02-05T10:00:00Z',
  })
  paymentProcessedAt?: string;

  @ApiProperty({
    description: 'Payroll items breakdown',
    type: [PayslipItemDto],
  })
  payrollItems!: PayslipItemDto[];

  @ApiPropertyOptional({
    description: 'Year-to-date totals',
  })
  ytdTotals?: {
    grossPay: number;
    netPay: number;
    taxes: number;
    deductions: number;
    benefits: number;
    hours: number;
  };

  @ApiProperty({
    description: 'Whether this is a correction payslip',
    example: false,
  })
  isCorrection!: boolean;

  @ApiPropertyOptional({
    description: 'Correction reason',
    example: 'Overtime hours adjustment',
  })
  correctionReason?: string;

  @ApiProperty({
    description: 'Whether employee has viewed this payslip',
    example: true,
  })
  isViewed!: boolean;

  @ApiPropertyOptional({
    description: 'When employee first viewed this payslip',
    example: '2024-02-05T15:30:00Z',
  })
  viewedAt?: string;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2024-01-15T10:00:00Z',
  })
  createdAt!: string;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2024-01-15T10:30:00Z',
  })
  updatedAt!: string;
}
