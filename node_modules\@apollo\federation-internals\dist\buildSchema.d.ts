import { DocumentNode, Source } from "graphql";
import { SchemaBlueprint, Schema, Type } from "./definitions";
export type BuildSchemaOptions = {
    blueprint?: SchemaBlueprint;
    validate?: boolean;
};
export declare function buildSchema(source: string | Source, options?: BuildSchemaOptions): Schema;
export declare function buildSchemaFromAST(documentNode: DocumentNode, options?: BuildSchemaOptions): Schema;
export declare function builtTypeReference(encodedType: string, schema: Schema): Type;
//# sourceMappingURL=buildSchema.d.ts.map