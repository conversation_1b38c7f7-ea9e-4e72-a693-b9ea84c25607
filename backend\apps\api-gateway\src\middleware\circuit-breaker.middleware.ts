import { Injectable, NestMiddleware, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { ConfigService } from '@nestjs/config';

interface CircuitBreakerState {
  state: 'CLOSED' | 'OPEN' | 'HALF_OPEN';
  failureCount: number;
  lastFailureTime: number;
  successCount: number;
  nextAttempt: number;
}

interface CircuitBreakerConfig {
  failureThreshold: number;
  recoveryTimeout: number;
  monitoringPeriod: number;
  halfOpenMaxCalls: number;
  timeout: number;
}

@Injectable()
export class CircuitBreakerMiddleware implements NestMiddleware {
  private readonly logger = new Logger(CircuitBreakerMiddleware.name);
  private readonly circuits = new Map<string, CircuitBreakerState>();
  private readonly config: CircuitBreakerConfig;

  constructor(private readonly configService: ConfigService) {
    this.config = {
      failureThreshold: this.configService.get<number>('CIRCUIT_BREAKER_FAILURE_THRESHOLD', 5),
      recoveryTimeout: this.configService.get<number>('CIRCUIT_BREAKER_RECOVERY_TIMEOUT', 60000),
      monitoringPeriod: this.configService.get<number>('CIRCUIT_BREAKER_MONITORING_PERIOD', 60000),
      halfOpenMaxCalls: this.configService.get<number>('CIRCUIT_BREAKER_HALF_OPEN_MAX_CALLS', 3),
      timeout: this.configService.get<number>('CIRCUIT_BREAKER_TIMEOUT', 30000),
    };
  }

  use(req: Request, res: Response, next: NextFunction) {
    const serviceKey = this.getServiceKey(req);
    const circuit = this.getOrCreateCircuit(serviceKey);

    // Check circuit state
    if (this.shouldRejectRequest(circuit)) {
      this.logger.warn(`Circuit breaker OPEN for service: ${serviceKey}`);
      throw new HttpException(
        {
          message: 'Service temporarily unavailable',
          error: 'Circuit Breaker Open',
          statusCode: HttpStatus.SERVICE_UNAVAILABLE,
          service: serviceKey,
          retryAfter: Math.ceil((circuit.nextAttempt - Date.now()) / 1000),
        },
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }

    // Set up timeout for the request
    const timeoutId = setTimeout(() => {
      if (!res.headersSent) {
        this.recordFailure(circuit, serviceKey);
        res.status(HttpStatus.REQUEST_TIMEOUT).json({
          message: 'Request timeout',
          error: 'Service Timeout',
          statusCode: HttpStatus.REQUEST_TIMEOUT,
          service: serviceKey,
        });
      }
    }, this.config.timeout);

    // Override res.end to capture response status
    const originalEnd = res.end;
    const self = this;
    res.end = function(chunk?: any, encoding?: any, cb?: () => void) {
      clearTimeout(timeoutId);

      // Record success or failure based on status code
      if (res.statusCode >= 200 && res.statusCode < 300) {
        circuit.successCount++;
        circuit.failureCount = 0; // Reset failure count on success

        // Transition from HALF_OPEN to CLOSED if enough successes
        if (circuit.state === 'HALF_OPEN' && circuit.successCount >= 3) {
          circuit.state = 'CLOSED';
          circuit.successCount = 0;
          self.logger.log(`Circuit breaker CLOSED for service: ${serviceKey}`);
        }
      } else if (res.statusCode >= 500) {
        self.recordFailure(circuit, serviceKey);
      }

      // Call original end method with proper overloads
      if (cb) {
        return originalEnd.call(this, chunk, encoding, cb);
      } else if (encoding) {
        return originalEnd.call(this, chunk, encoding);
      } else {
        return originalEnd.call(this, chunk);
      }
    } as any;

    next();
  }

  private getServiceKey(req: Request): string {
    // Extract service identifier from URL path
    const pathSegments = req.path.split('/').filter(Boolean);
    
    // Map common paths to services
    const serviceMap: Record<string, string> = {
      'auth': 'auth-service',
      'employees': 'employee-service',
      'payroll': 'payroll-service',
      'performance': 'performance-service',
      'ai': 'ai-service',
      'notifications': 'notification-service',
      'reports': 'reporting-service',
    };

    const firstSegment = pathSegments[1]; // Skip 'api/v1'
    return serviceMap[firstSegment] || 'unknown-service';
  }

  private getOrCreateCircuit(serviceKey: string): CircuitBreakerState {
    if (!this.circuits.has(serviceKey)) {
      this.circuits.set(serviceKey, {
        state: 'CLOSED',
        failureCount: 0,
        lastFailureTime: 0,
        successCount: 0,
        nextAttempt: 0,
      });
    }
    return this.circuits.get(serviceKey)!;
  }

  private shouldRejectRequest(circuit: CircuitBreakerState): boolean {
    const now = Date.now();

    switch (circuit.state) {
      case 'CLOSED':
        return false;

      case 'OPEN':
        if (now >= circuit.nextAttempt) {
          // Transition to HALF_OPEN
          circuit.state = 'HALF_OPEN';
          circuit.successCount = 0;
          this.logger.log('Circuit breaker transitioning to HALF_OPEN');
          return false;
        }
        return true;

      case 'HALF_OPEN':
        // Allow limited requests in HALF_OPEN state
        return circuit.successCount >= this.config.halfOpenMaxCalls;

      default:
        return false;
    }
  }

  private recordFailure(circuit: CircuitBreakerState, serviceKey: string): void {
    circuit.failureCount++;
    circuit.lastFailureTime = Date.now();

    if (circuit.state === 'CLOSED' && circuit.failureCount >= this.config.failureThreshold) {
      // Transition to OPEN
      circuit.state = 'OPEN';
      circuit.nextAttempt = Date.now() + this.config.recoveryTimeout;
      this.logger.warn(`Circuit breaker OPEN for service: ${serviceKey} (failures: ${circuit.failureCount})`);
    } else if (circuit.state === 'HALF_OPEN') {
      // Transition back to OPEN
      circuit.state = 'OPEN';
      circuit.nextAttempt = Date.now() + this.config.recoveryTimeout;
      this.logger.warn(`Circuit breaker back to OPEN for service: ${serviceKey}`);
    }
  }

  /**
   * Get circuit breaker status for monitoring
   */
  getCircuitStatus(): Record<string, any> {
    const status: Record<string, any> = {};
    
    for (const [serviceKey, circuit] of this.circuits.entries()) {
      status[serviceKey] = {
        state: circuit.state,
        failureCount: circuit.failureCount,
        successCount: circuit.successCount,
        lastFailureTime: circuit.lastFailureTime ? new Date(circuit.lastFailureTime).toISOString() : null,
        nextAttempt: circuit.nextAttempt ? new Date(circuit.nextAttempt).toISOString() : null,
        healthScore: this.calculateHealthScore(circuit),
      };
    }

    return status;
  }

  private calculateHealthScore(circuit: CircuitBreakerState): number {
    if (circuit.state === 'OPEN') return 0;
    if (circuit.state === 'HALF_OPEN') return 0.5;
    
    // For CLOSED state, calculate based on recent failures
    const recentFailures = circuit.failureCount;
    const maxFailures = this.config.failureThreshold;
    
    return Math.max(0, (maxFailures - recentFailures) / maxFailures);
  }

  /**
   * Reset circuit breaker for a specific service (for admin use)
   */
  resetCircuit(serviceKey: string): void {
    if (this.circuits.has(serviceKey)) {
      const circuit = this.circuits.get(serviceKey)!;
      circuit.state = 'CLOSED';
      circuit.failureCount = 0;
      circuit.successCount = 0;
      circuit.lastFailureTime = 0;
      circuit.nextAttempt = 0;
      
      this.logger.log(`Circuit breaker reset for service: ${serviceKey}`);
    }
  }

  /**
   * Get circuit breaker metrics for Prometheus
   */
  getMetrics(): Array<{ name: string; value: number; labels: Record<string, string> }> {
    const metrics: Array<{ name: string; value: number; labels: Record<string, string> }> = [];

    for (const [serviceKey, circuit] of this.circuits.entries()) {
      const labels = { service: serviceKey };

      metrics.push(
        {
          name: 'circuit_breaker_state',
          value: circuit.state === 'CLOSED' ? 0 : circuit.state === 'HALF_OPEN' ? 1 : 2,
          labels,
        },
        {
          name: 'circuit_breaker_failure_count',
          value: circuit.failureCount,
          labels,
        },
        {
          name: 'circuit_breaker_success_count',
          value: circuit.successCount,
          labels,
        },
      );
    }

    return metrics;
  }

  /**
   * Get circuit breaker status for all services
   */
  getStatus(): Record<string, CircuitBreakerState> {
    const status: Record<string, CircuitBreakerState> = {};
    for (const [serviceKey, circuit] of this.circuits.entries()) {
      status[serviceKey] = { ...circuit };
    }
    return status;
  }
}
