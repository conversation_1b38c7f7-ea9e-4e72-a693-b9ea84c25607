import { SelectionSet } from "@apollo/federation-internals";
import { OpGraphPath, OpTrigger, ContextAtUsageEntry } from "./graphPath";
import { Edge, QueryGraph, RootVertex, Vertex } from "./querygraph";
export declare class PathTree<TTrigger, RV extends Vertex = Vertex, TNullEdge extends null | never = never> {
    readonly graph: QueryGraph;
    readonly vertex: RV;
    readonly localSelections: readonly SelectionSet[] | undefined;
    private readonly triggerEquality;
    private readonly childs;
    private constructor();
    static create<TTrigger, RV extends Vertex = Vertex, TNullEdge extends null | never = never>(graph: QueryGraph, root: RV, triggerEquality: (t1: TTrigger, t2: TTrigger) => boolean): PathTree<TTrigger, RV, TNullEdge>;
    static createOp<RV extends Vertex = Vertex>(graph: QueryGraph, root: RV): OpPathTree<RV>;
    static createFromOpPaths<RV extends Vertex = Vertex>(graph: QueryGraph, root: RV, paths: {
        path: OpGraphPath<RV>;
        selection?: SelectionSet;
    }[]): OpPathTree<RV>;
    private static createFromPaths;
    childCount(): number;
    isLeaf(): boolean;
    childElements(reverseOrder?: boolean): Generator<[Edge | TNullEdge, TTrigger, OpPathTree | null, PathTree<TTrigger, Vertex, TNullEdge>, Set<string> | null, Map<string, ContextAtUsageEntry> | null], void, undefined>;
    private element;
    private mergeChilds;
    mergeIfNotEqual(other: PathTree<TTrigger, RV, TNullEdge>): PathTree<TTrigger, RV, TNullEdge>;
    private mergeLocalSelectionsWith;
    merge(other: PathTree<TTrigger, RV, TNullEdge>): PathTree<TTrigger, RV, TNullEdge>;
    private equalsSameRoot;
    private static parameterToContextEquals;
    concat(other: PathTree<TTrigger, RV, TNullEdge>): PathTree<TTrigger, RV, TNullEdge>;
    private findIndex;
    isAllInSameSubgraph(): boolean;
    private isAllInSameSubgraphInternal;
    toString(indent?: string, includeConditions?: boolean): string;
    private toStringInternal;
}
export type RootPathTree<TTrigger, TNullEdge extends null | never = never> = PathTree<TTrigger, RootVertex, TNullEdge>;
export type OpPathTree<RV extends Vertex = Vertex> = PathTree<OpTrigger, RV, null>;
export type OpRootPathTree = OpPathTree<RootVertex>;
export declare function isRootPathTree(tree: OpPathTree<any>): tree is OpRootPathTree;
export declare function traversePathTree<TTrigger, RV extends Vertex = Vertex, TNullEdge extends null | never = never>(pathTree: PathTree<TTrigger, RV, TNullEdge>, onEdges: (edge: Edge) => void): void;
//# sourceMappingURL=pathTree.d.ts.map