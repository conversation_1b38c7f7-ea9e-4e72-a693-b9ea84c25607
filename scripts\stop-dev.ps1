# =============================================================================
# PeopleNest HRMS - Development Environment Stop Script (PowerShell)
# =============================================================================
# This script stops all running services for the PeopleNest HRMS system
# =============================================================================

param(
    [switch]$InfrastructureOnly,
    [switch]$ApplicationOnly,
    [switch]$Force,
    [switch]$Verbose
)

# Colors for output
$Green = "Green"
$Red = "Red"
$Yellow = "Yellow"
$Blue = "Blue"
$Cyan = "Cyan"

function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

function Write-Header {
    param([string]$Title)
    Write-Host ""
    Write-ColorOutput "=" * 80 $Blue
    Write-ColorOutput "  $Title" $Cyan
    Write-ColorOutput "=" * 80 $Blue
    Write-Host ""
}

function Stop-NodeProcesses {
    param([string]$ProcessName, [string]$ServiceName)
    
    Write-ColorOutput "🛑 Stopping $ServiceName processes..." $Yellow
    
    try {
        $processes = Get-Process -Name $ProcessName -ErrorAction SilentlyContinue
        if ($processes) {
            foreach ($process in $processes) {
                # Check if it's related to our project
                $commandLine = (Get-WmiObject Win32_Process -Filter "ProcessId = $($process.Id)").CommandLine
                if ($commandLine -and ($commandLine -like "*peoplenest*" -or $commandLine -like "*start:dev*" -or $commandLine -like "*npm run dev*")) {
                    Write-ColorOutput "  Stopping process $($process.Id): $($process.ProcessName)" $Yellow
                    if ($Force) {
                        $process | Stop-Process -Force
                    } else {
                        $process | Stop-Process
                    }
                }
            }
            Write-ColorOutput "✅ $ServiceName processes stopped" $Green
        } else {
            Write-ColorOutput "ℹ️  No $ServiceName processes found" $Cyan
        }
    }
    catch {
        Write-ColorOutput "⚠️  Error stopping $ServiceName processes: $($_.Exception.Message)" $Yellow
    }
}

function Stop-PythonProcesses {
    Write-ColorOutput "🛑 Stopping AI service processes..." $Yellow
    
    try {
        $processes = Get-Process -Name "python*" -ErrorAction SilentlyContinue
        if ($processes) {
            foreach ($process in $processes) {
                $commandLine = (Get-WmiObject Win32_Process -Filter "ProcessId = $($process.Id)").CommandLine
                if ($commandLine -and ($commandLine -like "*uvicorn*" -or $commandLine -like "*ai-services*")) {
                    Write-ColorOutput "  Stopping process $($process.Id): $($process.ProcessName)" $Yellow
                    if ($Force) {
                        $process | Stop-Process -Force
                    } else {
                        $process | Stop-Process
                    }
                }
            }
            Write-ColorOutput "✅ AI service processes stopped" $Green
        } else {
            Write-ColorOutput "ℹ️  No AI service processes found" $Cyan
        }
    }
    catch {
        Write-ColorOutput "⚠️  Error stopping AI service processes: $($_.Exception.Message)" $Yellow
    }
}

function Stop-Infrastructure {
    Write-Header "Stopping Infrastructure Services"
    
    Write-ColorOutput "🛑 Stopping Docker infrastructure..." $Yellow
    
    try {
        docker-compose down
        
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✅ Infrastructure services stopped successfully!" $Green
        } else {
            Write-ColorOutput "⚠️  Some issues occurred while stopping infrastructure" $Yellow
        }
    }
    catch {
        Write-ColorOutput "❌ Error stopping infrastructure: $($_.Exception.Message)" $Red
    }
    
    # Optionally remove volumes and networks
    if ($Force) {
        Write-ColorOutput "🧹 Cleaning up volumes and networks..." $Yellow
        try {
            docker-compose down -v --remove-orphans
            Write-ColorOutput "✅ Cleanup completed" $Green
        }
        catch {
            Write-ColorOutput "⚠️  Cleanup had some issues: $($_.Exception.Message)" $Yellow
        }
    }
}

function Stop-ApplicationServices {
    Write-Header "Stopping Application Services"
    
    # Stop Node.js processes (Frontend and Backend)
    Stop-NodeProcesses -ProcessName "node" -ServiceName "Frontend/Backend"
    
    # Stop Python processes (AI Services)
    Stop-PythonProcesses
    
    # Kill any remaining npm processes
    try {
        $npmProcesses = Get-Process -Name "npm" -ErrorAction SilentlyContinue
        if ($npmProcesses) {
            Write-ColorOutput "🛑 Stopping npm processes..." $Yellow
            foreach ($process in $npmProcesses) {
                if ($Force) {
                    $process | Stop-Process -Force
                } else {
                    $process | Stop-Process
                }
            }
            Write-ColorOutput "✅ npm processes stopped" $Green
        }
    }
    catch {
        Write-ColorOutput "⚠️  Error stopping npm processes: $($_.Exception.Message)" $Yellow
    }
}

function Show-RemainingProcesses {
    Write-Header "Checking for Remaining Processes"
    
    $remainingProcesses = @()
    
    # Check for Node.js processes
    $nodeProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue
    foreach ($process in $nodeProcesses) {
        $commandLine = (Get-WmiObject Win32_Process -Filter "ProcessId = $($process.Id)").CommandLine
        if ($commandLine -and ($commandLine -like "*peoplenest*" -or $commandLine -like "*start:dev*" -or $commandLine -like "*npm run dev*")) {
            $remainingProcesses += @{
                Name = "Node.js"
                PID = $process.Id
                Command = $commandLine
            }
        }
    }
    
    # Check for Python processes
    $pythonProcesses = Get-Process -Name "python*" -ErrorAction SilentlyContinue
    foreach ($process in $pythonProcesses) {
        $commandLine = (Get-WmiObject Win32_Process -Filter "ProcessId = $($process.Id)").CommandLine
        if ($commandLine -and ($commandLine -like "*uvicorn*" -or $commandLine -like "*ai-services*")) {
            $remainingProcesses += @{
                Name = "Python"
                PID = $process.Id
                Command = $commandLine
            }
        }
    }
    
    if ($remainingProcesses.Count -gt 0) {
        Write-ColorOutput "⚠️  Found $($remainingProcesses.Count) remaining processes:" $Yellow
        foreach ($proc in $remainingProcesses) {
            Write-ColorOutput "  PID $($proc.PID): $($proc.Name)" $Red
            if ($Verbose) {
                Write-ColorOutput "    Command: $($proc.Command)" $Cyan
            }
        }
        Write-Host ""
        Write-ColorOutput "💡 To force kill remaining processes, run with -Force flag" $Cyan
    } else {
        Write-ColorOutput "✅ No remaining application processes found" $Green
    }
}

function Show-DockerStatus {
    Write-Header "Docker Services Status"
    
    try {
        $containers = docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | Where-Object { $_ -like "*peoplenest*" }
        
        if ($containers) {
            Write-ColorOutput "🐳 Running Docker containers:" $Yellow
            $containers | ForEach-Object { Write-ColorOutput "  $_" $Cyan }
        } else {
            Write-ColorOutput "✅ No PeopleNest Docker containers running" $Green
        }
    }
    catch {
        Write-ColorOutput "⚠️  Could not check Docker status: $($_.Exception.Message)" $Yellow
    }
}

# Main execution
Write-Header "PeopleNest HRMS - Development Environment Stop"

try {
    if ($InfrastructureOnly) {
        Stop-Infrastructure
    } elseif ($ApplicationOnly) {
        Stop-ApplicationServices
    } else {
        # Stop both application and infrastructure
        Stop-ApplicationServices
        Stop-Infrastructure
    }
    
    # Show status
    if (-not $InfrastructureOnly) {
        Show-RemainingProcesses
    }
    
    if (-not $ApplicationOnly) {
        Show-DockerStatus
    }
    
    Write-Header "🛑 Stop Process Completed"
    
    if ($Force) {
        Write-ColorOutput "All services have been forcefully stopped and cleaned up." $Green
    } else {
        Write-ColorOutput "Services have been gracefully stopped." $Green
        Write-ColorOutput "Use -Force flag for forceful shutdown and cleanup." $Cyan
    }
    
} catch {
    Write-ColorOutput "❌ An error occurred during shutdown: $($_.Exception.Message)" $Red
    exit 1
}

Write-Host ""
Write-ColorOutput "📋 Available options:" $Yellow
Write-ColorOutput "  -InfrastructureOnly  : Stop only Docker infrastructure" $Cyan
Write-ColorOutput "  -ApplicationOnly     : Stop only application services" $Cyan
Write-ColorOutput "  -Force              : Force kill processes and cleanup" $Cyan
Write-ColorOutput "  -Verbose            : Show detailed process information" $Cyan
