{"version": 3, "file": "nonTrivialEdgePrecomputing.js", "sourceRoot": "", "sources": ["../src/nonTrivialEdgePrecomputing.ts"], "names": [], "mappings": ";;;AAAA,uEAAsD;AACtD,6CAAkF;AAElF,SAAgB,iCAAiC,CAAC,KAAiB;IACjE,MAAM,KAAK,GAAG,IAAI,4BAAe,EAA8B,CAAC;IAChE,IAAA,4BAAe,EAAC,KAAK,EAAE,GAAG,EAAE,GAAE,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE;QACxC,MAAM,aAAa,GAAG,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChD,KAAK,CAAC,YAAY,CAAC,IAAI,EAAE,0BAA0B,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC,CAAC;QAC1E,OAAO,IAAI,CAAC;IACd,CAAC,CAAC,CAAC;IACH,OAAO,CAAC,YAAY,EAAE,EAAE;QACtB,MAAM,mBAAmB,GAAG,KAAK,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;QAC7D,IAAA,6BAAM,EAAC,mBAAmB,EAAE,GAAG,EAAE,CAAC,iCAAiC,YAAY,4BAA4B,CAAC,CAAC;QAC7G,OAAO,mBAAmB,CAAC;IAC7B,CAAC,CAAA;AACH,CAAC;AAZD,8EAYC;AAED,SAAS,0BAA0B,CAAC,IAAU,EAAE,YAA6B;IAC3E,QAAQ,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;QAC7B,KAAK,eAAe;YAMlB,OAAO,YAAY,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,KAAK,eAAe,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;QAC5H,KAAK,oBAAoB;YAKvB,OAAO,YAAY,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,KAAK,oBAAoB,CAAC,CAAC;QAC9F,KAAK,4BAA4B;YAI/B,OAAO,YAAY,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,KAAK,oBAAoB,CAAC,CAAC;QAC9F;YACE,OAAO,YAAY,CAAC;IACxB,CAAC;AACH,CAAC;AAED,SAAS,cAAc,CAAC,EAAQ,EAAE,EAAQ;IACxC,IAAI,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC;QACnB,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC;IACxB,CAAC;IACD,OAAO,CAAC,CAAC,EAAE,CAAC,UAAU,IAAI,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC;AAChE,CAAC"}