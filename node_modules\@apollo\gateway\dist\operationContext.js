"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.buildOperationContext = void 0;
const federation_internals_1 = require("@apollo/federation-internals");
const graphql_1 = require("graphql");
function buildOperationContext({ schema, operationDocument, operationName, }) {
    let operation;
    let operationCount = 0;
    const fragments = Object.create(null);
    operationDocument.definitions.forEach(definition => {
        switch (definition.kind) {
            case graphql_1.Kind.OPERATION_DEFINITION:
                operationCount++;
                if (!operationName && operationCount > 1) {
                    throw federation_internals_1.ERRORS.INVALID_GRAPHQL.err('Must provide operation name if query contains ' +
                        'multiple operations.');
                }
                if (!operationName ||
                    (definition.name && definition.name.value === operationName)) {
                    operation = definition;
                }
                break;
            case graphql_1.Kind.FRAGMENT_DEFINITION:
                fragments[definition.name.value] = definition;
                break;
        }
    });
    if (!operation) {
        throw federation_internals_1.ERRORS.INVALID_GRAPHQL.err(operationName ? `Unknown operation named "${operationName}".` : 'Must provide an operation.');
    }
    return {
        schema,
        operation,
        fragments,
    };
}
exports.buildOperationContext = buildOperationContext;
//# sourceMappingURL=operationContext.js.map