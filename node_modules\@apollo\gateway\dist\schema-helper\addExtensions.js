"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.addExtensions = void 0;
const { version } = require('../../package.json');
function addExtensions(schema) {
    var _a, _b, _c;
    const schemaExtension = (_a = schema.extensions) !== null && _a !== void 0 ? _a : {};
    const apolloExtension = (_b = schemaExtension === null || schemaExtension === void 0 ? void 0 : schemaExtension.apollo) !== null && _b !== void 0 ? _b : {};
    const gatewayExtension = (_c = apolloExtension === null || apolloExtension === void 0 ? void 0 : apolloExtension.gateway) !== null && _c !== void 0 ? _c : {};
    schema.extensions = {
        ...schema.extensions,
        apollo: {
            ...apolloExtension,
            gateway: {
                ...gatewayExtension,
                version,
            }
        },
    };
    return schema;
}
exports.addExtensions = addExtensions;
//# sourceMappingURL=addExtensions.js.map