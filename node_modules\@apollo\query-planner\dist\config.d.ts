import { Concrete } from "@apollo/federation-internals";
import { QueryPlan } from ".";
import { KeyValueCache } from '@apollo/utils.keyvaluecache';
export type QueryPlanCache = KeyValueCache<QueryPlan> & {
    clear: () => void;
};
export type QueryPlannerConfig = {
    exposeDocumentNodeInFetchNode?: boolean;
    reuseQueryFragments?: boolean;
    generateQueryFragments?: boolean;
    incrementalDelivery?: {
        enableDefer?: boolean;
    };
    cache?: QueryPlanCache;
    debug?: {
        bypassPlannerForSingleSubgraph?: boolean;
        maxEvaluatedPlans?: number;
        pathsLimit?: number | null;
    };
    typeConditionedFetching?: boolean;
};
export declare function enforceQueryPlannerConfigDefaults(config?: QueryPlannerConfig): Concrete<QueryPlannerConfig>;
export declare function validateQueryPlannerConfig(config: Concrete<QueryPlannerConfig>): void;
//# sourceMappingURL=config.d.ts.map