import { FieldSelection, Operation } from "@apollo/federation-internals";
import { GraphQLError } from "graphql";
export declare function computeResponse({ operation, variables, input, introspectionHandling, }: {
    operation: Operation;
    variables?: Record<string, any>;
    input: Record<string, any> | null | undefined;
    introspectionHandling: (introspectionSelection: FieldSelection) => any;
}): {
    data: Record<string, any> | null | undefined;
    errors: GraphQLError[];
};
//# sourceMappingURL=resultShaping.d.ts.map