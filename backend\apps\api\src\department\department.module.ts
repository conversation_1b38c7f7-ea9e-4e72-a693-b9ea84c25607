import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// Entities
import {
  Department,
  Employee,
  Position,
  User,
  Tenant,
  AuditLog,
} from '@app/database';

// Controllers
import { DepartmentController } from './department.controller';

// Services
import { DepartmentService } from './services/department.service';
import { DepartmentValidationService } from './services/department-validation.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Department,
      Employee,
      Position,
      User,
      Tenant,
      AuditLog,
    ]),
  ],
  controllers: [DepartmentController],
  providers: [
    DepartmentService,
    DepartmentValidationService,
  ],
  exports: [
    DepartmentService,
    DepartmentValidationService,
  ],
})
export class DepartmentModule {}
