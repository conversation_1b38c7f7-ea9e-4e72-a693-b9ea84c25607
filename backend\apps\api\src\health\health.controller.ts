import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { HealthService } from './health.service';
import { Public } from '@app/common';

@ApiTags('Health')
@Controller('health')
export class HealthController {
  constructor(private readonly healthService: HealthService) {}

  @Public()
  @Get()
  @ApiOperation({ 
    summary: 'Health check',
    description: 'Returns the health status of the API and its dependencies'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Health check completed successfully',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', enum: ['healthy', 'degraded', 'unhealthy'] },
        timestamp: { type: 'string' },
        uptime: { type: 'number' },
        version: { type: 'string' },
        environment: { type: 'string' },
        checks: {
          type: 'object',
          properties: {
            database: { type: 'object' },
            redis: { type: 'object' },
            memory: { type: 'object' },
            disk: { type: 'object' },
          }
        }
      }
    }
  })
  @ApiResponse({ 
    status: 503, 
    description: 'Service unavailable - health check failed'
  })
  async getHealth() {
    return this.healthService.getHealthStatus();
  }

  @Public()
  @Get('ready')
  @ApiOperation({ 
    summary: 'Readiness check',
    description: 'Returns whether the API is ready to serve requests'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Service is ready'
  })
  @ApiResponse({ 
    status: 503, 
    description: 'Service is not ready'
  })
  async getReadiness() {
    return this.healthService.getReadinessStatus();
  }

  @Public()
  @Get('live')
  @ApiOperation({ 
    summary: 'Liveness check',
    description: 'Returns whether the API is alive and responding'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Service is alive'
  })
  async getLiveness() {
    return this.healthService.getLivenessStatus();
  }
}
