"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.recordExceptions = exports.operationContextSpanAttributes = exports.requestContextSpanAttributes = exports.tracer = exports.OpenTelemetryAttributeNames = exports.OpenTelemetrySpanNames = void 0;
const api_1 = __importDefault(require("@opentelemetry/api"));
var OpenTelemetrySpanNames;
(function (OpenTelemetrySpanNames) {
    OpenTelemetrySpanNames["REQUEST"] = "gateway.request";
    OpenTelemetrySpanNames["PLAN"] = "gateway.plan";
    OpenTelemetrySpanNames["FETCH"] = "gateway.fetch";
    OpenTelemetrySpanNames["POST_PROCESSING"] = "gateway.postprocessing";
    OpenTelemetrySpanNames["EXECUTE"] = "gateway.execute";
    OpenTelemetrySpanNames["VALIDATE"] = "gateway.validate";
})(OpenTelemetrySpanNames || (exports.OpenTelemetrySpanNames = OpenTelemetrySpanNames = {}));
var OpenTelemetryAttributeNames;
(function (OpenTelemetryAttributeNames) {
    OpenTelemetryAttributeNames["GRAPHQL_DOCUMENT"] = "graphql.document";
    OpenTelemetryAttributeNames["GRAPHQL_OPERATION_NAME"] = "graphql.operation.name";
    OpenTelemetryAttributeNames["GRAPHQL_OPERATION_NAME_DEPRECATED"] = "operationName";
    OpenTelemetryAttributeNames["GRAPHQL_OPERATION_TYPE"] = "graphql.operation.type";
})(OpenTelemetryAttributeNames || (exports.OpenTelemetryAttributeNames = OpenTelemetryAttributeNames = {}));
const { name, version } = require('../../package.json');
exports.tracer = api_1.default.trace.getTracer(`${name}/${version}`);
function requestContextSpanAttributes(requestContext, config) {
    const spanAttributes = {};
    if (requestContext.operationName) {
        spanAttributes[OpenTelemetryAttributeNames.GRAPHQL_OPERATION_NAME_DEPRECATED] = requestContext.operationName;
        spanAttributes[OpenTelemetryAttributeNames.GRAPHQL_OPERATION_NAME] =
            requestContext.operationName;
    }
    if ((config === null || config === void 0 ? void 0 : config.includeDocument) && requestContext.source) {
        spanAttributes[OpenTelemetryAttributeNames.GRAPHQL_DOCUMENT] =
            requestContext.source;
    }
    return spanAttributes;
}
exports.requestContextSpanAttributes = requestContextSpanAttributes;
function operationContextSpanAttributes(operationContext) {
    const spanAttributes = {};
    if (operationContext.operation.operation) {
        spanAttributes[OpenTelemetryAttributeNames.GRAPHQL_OPERATION_TYPE] =
            operationContext.operation.operation;
    }
    return spanAttributes;
}
exports.operationContextSpanAttributes = operationContextSpanAttributes;
function recordExceptions(span, exceptions, config) {
    const recordExceptions = config === null || config === void 0 ? void 0 : config.recordExceptions;
    if (recordExceptions === undefined || recordExceptions === false) {
        return;
    }
    let exceptionsToRecord;
    if (recordExceptions === true) {
        exceptionsToRecord = exceptions;
    }
    else if (recordExceptions <= 0) {
        return;
    }
    else {
        exceptionsToRecord = exceptions.slice(0, recordExceptions);
    }
    for (const exception of exceptionsToRecord) {
        span.recordException(exception);
    }
}
exports.recordExceptions = recordExceptions;
//# sourceMappingURL=opentelemetry.js.map