"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.preComputeNonTrivialFollowupEdges = void 0;
const federation_internals_1 = require("@apollo/federation-internals");
const querygraph_1 = require("./querygraph");
function preComputeNonTrivialFollowupEdges(graph) {
    const state = new querygraph_1.QueryGraphState();
    (0, querygraph_1.simpleTraversal)(graph, () => { }, (edge) => {
        const followupEdges = graph.outEdges(edge.tail);
        state.setEdgeState(edge, computeNonTrivialFollowups(edge, followupEdges));
        return true;
    });
    return (previousEdge) => {
        const nonTrivialFollowups = state.getEdgeState(previousEdge);
        (0, federation_internals_1.assert)(nonTrivialFollowups, () => `Non-trivial followup edges of ${previousEdge} should have been computed`);
        return nonTrivialFollowups;
    };
}
exports.preComputeNonTrivialFollowupEdges = preComputeNonTrivialFollowupEdges;
function computeNonTrivialFollowups(edge, allFollowups) {
    switch (edge.transition.kind) {
        case 'KeyResolution':
            return allFollowups.filter((followup) => followup.transition.kind !== 'KeyResolution' || !sameConditions(edge, followup));
        case 'RootTypeResolution':
            return allFollowups.filter((followup) => followup.transition.kind !== 'RootTypeResolution');
        case 'SubgraphEnteringTransition':
            return allFollowups.filter((followup) => followup.transition.kind !== 'RootTypeResolution');
        default:
            return allFollowups;
    }
}
function sameConditions(e1, e2) {
    if (!e1.conditions) {
        return !e2.conditions;
    }
    return !!e2.conditions && e1.conditions.equals(e2.conditions);
}
//# sourceMappingURL=nonTrivialEdgePrecomputing.js.map