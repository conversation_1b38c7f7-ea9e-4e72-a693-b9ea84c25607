{"version": 3, "file": "config.js", "sourceRoot": "", "sources": ["../src/config.ts"], "names": [], "mappings": ";;;AAUA,6DAA+D;AA0E/D,SAAgB,qBAAqB,CACnC,MAAyB;IAEzB,OAAO,eAAe,IAAI,MAAM,CAAC;AACnC,CAAC;AAJD,sDAIC;AAED,SAAgB,yBAAyB,CACvC,MAAyB;IAEzB,OAAO,aAAa,IAAI,MAAM,CAAC;AACjC,CAAC;AAJD,8DAIC;AAsHD,SAAgB,2CAA2C,CACzD,MAAqB;IAErB,OAAO,yBAAyB,CAAC,MAAM,CAAC,IAAI,yBAAyB,CAAC,MAAM,CAAC,CAAC;AAChF,CAAC;AAJD,kGAIC;AAyED,SAAgB,aAAa,CAC3B,MAAqB;IAErB,OAAO,kBAAkB,IAAI,MAAM,CAAC;AACtC,CAAC;AAJD,sCAIC;AAGD,SAAgB,mBAAmB,CACjC,MAAqB;IAErB,OAAO,aAAa,IAAI,MAAM,CAAC;AACjC,CAAC;AAJD,kDAIC;AAED,SAAgB,2BAA2B,CACzC,MAAqB;IAErB,OAAO,eAAe,IAAI,MAAM,IAAI,OAAO,MAAM,CAAC,aAAa,KAAK,QAAQ,CAAC;AAC/E,CAAC;AAJD,kEAIC;AAED,SAAgB,yBAAyB,CACvC,MAAqB;IAErB,OAAO,CACL,eAAe,IAAI,MAAM,IAAI,OAAO,MAAM,CAAC,aAAa,KAAK,UAAU,CACxE,CAAC;AACJ,CAAC;AAND,8DAMC;AAED,SAAgB,yBAAyB,CACvC,MAAqB;IAErB,OAAO,CACL,eAAe,IAAI,MAAM;QACzB,OAAO,MAAM,CAAC,aAAa,KAAK,QAAQ;QACxC,YAAY,IAAI,MAAM,CAAC,aAAa,CACrC,CAAC;AACJ,CAAC;AARD,8DAQC;AAID,SAAgB,uBAAuB,CACrC,MAAqB;IAErB,OAAO,CACL,2CAA2C,CAAC,MAAM,CAAC;QACnD,uCAAuC,IAAI,MAAM;QACjD,kCAAkC,IAAI,MAAM;QAE5C,mBAAmB,CAAC,MAAM,CAAC,CAC5B,CAAC;AACJ,CAAC;AAVD,0DAUC;AAGD,SAAgB,eAAe,CAC7B,MAAqB;IAErB,OAAO,CACL,8BAA8B,IAAI,MAAM;QACxC,iBAAiB,IAAI,MAAM;QAC3B,0BAA0B,IAAI,MAAM;QACpC,CAAC,yBAAyB,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,aAAa,YAAY,4CAAuB,CAAC;QAC9F,CAAC,CAAC,aAAa,CAAC,MAAM,CAAC;YACrB,CAAC,2BAA2B,CAAC,MAAM,CAAC;YACpC,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC,CACpC,CAAC;AACJ,CAAC;AAZD,0CAYC;AAGD,SAAgB,cAAc,CAC5B,MAAqB;IAErB,OAAO,aAAa,CAAC,MAAM,CAAC,IAAI,2BAA2B,CAAC,MAAM,CAAC,CAAC;AACtE,CAAC;AAJD,wCAIC"}