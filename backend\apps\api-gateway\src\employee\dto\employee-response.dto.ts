import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Exclude, Expose, Transform, Type } from 'class-transformer';
import {
  EmployeeStatus,
  EmploymentType,
  Gender,
  MaritalStatus,
} from '@app/common/enums/status.enum';

export class EmployeeContactResponseDto {
  @ApiProperty()
  id!: string;

  @ApiProperty()
  type!: string;

  @ApiProperty()
  value!: string;

  @ApiPropertyOptional()
  label?: string;

  @ApiProperty()
  isPrimary!: boolean;

  @ApiProperty()
  createdAt!: Date;

  @ApiProperty()
  updatedAt!: Date;
}

export class EmployeeAddressResponseDto {
  @ApiProperty()
  id!: string;

  @ApiProperty()
  type!: string;

  @ApiProperty()
  addressLine1!: string;

  @ApiPropertyOptional()
  addressLine2?: string;

  @ApiProperty()
  city!: string;

  @ApiPropertyOptional()
  state?: string;

  @ApiProperty()
  postalCode!: string;

  @ApiProperty()
  country!: string;

  @ApiProperty()
  isPrimary!: boolean;

  @ApiProperty()
  createdAt!: Date;

  @ApiProperty()
  updatedAt!: Date;
}

export class EmployeeEmergencyContactResponseDto {
  @ApiProperty()
  id!: string;

  @ApiProperty()
  name!: string;

  @ApiProperty()
  relationship!: string;

  @ApiProperty()
  phoneNumber!: string;

  @ApiPropertyOptional()
  email?: string;

  @ApiProperty()
  isPrimary!: boolean;

  @ApiProperty()
  createdAt!: Date;

  @ApiProperty()
  updatedAt!: Date;
}

export class DepartmentResponseDto {
  @ApiProperty()
  id!: string;

  @ApiProperty()
  name!: string;

  @ApiProperty()
  code!: string;

  @ApiPropertyOptional()
  description?: string;

  @ApiProperty()
  isActive!: boolean;

  @ApiPropertyOptional()
  parentId?: string;

  @ApiPropertyOptional()
  headId?: string;

  @ApiProperty()
  employeeCount!: number;

  @ApiProperty()
  createdAt!: Date;

  @ApiProperty()
  updatedAt!: Date;
}

export class PositionResponseDto {
  @ApiProperty()
  id!: string;

  @ApiProperty()
  title!: string;

  @ApiProperty()
  code!: string;

  @ApiPropertyOptional()
  description?: string;

  @ApiProperty()
  level!: string;

  @ApiProperty()
  isActive!: boolean;

  @ApiPropertyOptional()
  minSalary?: number;

  @ApiPropertyOptional()
  maxSalary?: number;

  @ApiProperty()
  salaryCurrency!: string;

  @ApiProperty()
  employmentType!: EmploymentType;

  @ApiProperty()
  createdAt!: Date;

  @ApiProperty()
  updatedAt!: Date;
}

export class EmployeeResponseDto {
  @ApiProperty()
  id!: string;

  @ApiProperty()
  employeeId!: string;

  @ApiProperty()
  email!: string;

  @ApiProperty()
  firstName!: string;

  @ApiPropertyOptional()
  middleName?: string;

  @ApiProperty()
  lastName!: string;

  @ApiPropertyOptional()
  preferredName?: string;

  @ApiProperty()
  @Transform(({ obj }) => obj ? `${obj.firstName} ${obj.middleName || ''} ${obj.lastName}`.trim() : '')
  fullName!: string;

  @ApiProperty()
  @Transform(({ obj }) => obj.preferredName || obj.firstName)
  displayName!: string;

  @ApiPropertyOptional()
  dateOfBirth?: Date;

  @ApiPropertyOptional({ enum: Gender })
  gender?: Gender;

  @ApiPropertyOptional({ enum: MaritalStatus })
  maritalStatus?: MaritalStatus;

  @ApiPropertyOptional()
  nationality?: string;

  @ApiPropertyOptional()
  phoneNumber?: string;

  @ApiPropertyOptional()
  personalPhoneNumber?: string;

  @ApiPropertyOptional()
  profilePicture?: string;

  @ApiProperty({ enum: EmployeeStatus })
  status!: EmployeeStatus;

  @ApiProperty({ enum: EmploymentType })
  employmentType!: EmploymentType;

  @ApiProperty()
  dateOfJoining!: Date;

  @ApiPropertyOptional()
  dateOfLeaving?: Date;

  @ApiPropertyOptional()
  probationEndDate?: Date;

  @ApiProperty()
  @Transform(({ obj }) => obj.probationEndDate ? new Date() < obj.probationEndDate : false)
  isProbationPeriod!: boolean;

  @ApiPropertyOptional()
  baseSalary?: number;

  @ApiProperty()
  salaryCurrency!: string;

  @ApiProperty()
  salaryFrequency!: string;

  @ApiPropertyOptional()
  workLocation?: string;

  @ApiPropertyOptional()
  timeZone?: string;

  @ApiPropertyOptional()
  workSchedule?: {
    hoursPerWeek?: number;
    workDays?: string[];
    startTime?: string;
    endTime?: string;
    isFlexible?: boolean;
  };

  @ApiProperty()
  @Transform(({ obj }) => {
    const today = new Date();
    const joinDate = new Date(obj.dateOfJoining);
    return Math.floor((today.getTime() - joinDate.getTime()) / (1000 * 60 * 60 * 24 * 365.25));
  })
  yearsOfService!: number;

  @ApiProperty()
  @Transform(({ obj }) => {
    if (!obj.dateOfBirth) return null;
    const today = new Date();
    const birthDate = new Date(obj.dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  })
  age!: number | null;

  @ApiProperty()
  @Transform(({ obj }) => obj.status === EmployeeStatus.ACTIVE)
  isActive!: boolean;

  @ApiProperty()
  departmentId!: string;

  @ApiProperty()
  positionId!: string;

  @ApiPropertyOptional()
  managerId?: string;

  @ApiPropertyOptional()
  userId?: string;

  @ApiPropertyOptional({ type: DepartmentResponseDto })
  @Type(() => DepartmentResponseDto)
  department?: DepartmentResponseDto;

  @ApiPropertyOptional({ type: PositionResponseDto })
  @Type(() => PositionResponseDto)
  position?: PositionResponseDto;

  @ApiPropertyOptional({ type: EmployeeResponseDto })
  @Type(() => EmployeeResponseDto)
  manager?: EmployeeResponseDto;

  @ApiPropertyOptional({ type: [EmployeeContactResponseDto] })
  @Type(() => EmployeeContactResponseDto)
  contacts?: EmployeeContactResponseDto[];

  @ApiPropertyOptional({ type: [EmployeeAddressResponseDto] })
  @Type(() => EmployeeAddressResponseDto)
  addresses?: EmployeeAddressResponseDto[];

  @ApiPropertyOptional({ type: [EmployeeEmergencyContactResponseDto] })
  @Type(() => EmployeeEmergencyContactResponseDto)
  emergencyContacts?: EmployeeEmergencyContactResponseDto[];

  @ApiPropertyOptional()
  notes?: string;

  @ApiProperty()
  createdAt!: Date;

  @ApiProperty()
  updatedAt!: Date;

  @ApiPropertyOptional()
  deletedAt?: Date;

  // Exclude sensitive fields
  @Exclude()
  tenantId!: string;

  @Exclude()
  socialSecurityNumber?: string;

  @Exclude()
  taxId?: string;

  @Exclude()
  bankAccountNumber?: string;

  @Exclude()
  passportNumber?: string;

  @Exclude()
  visaStatus?: string;

  @Exclude()
  visaExpiryDate?: Date;
}

export class EmployeeListResponseDto {
  @ApiProperty({ type: [EmployeeResponseDto] })
  @Type(() => EmployeeResponseDto)
  data!: EmployeeResponseDto[];

  @ApiProperty()
  total!: number;

  @ApiProperty()
  page!: number;

  @ApiProperty()
  limit!: number;

  @ApiProperty()
  totalPages!: number;

  @ApiProperty()
  hasNext!: boolean;

  @ApiProperty()
  hasPrev!: boolean;
}

export class EmployeeStatsResponseDto {
  @ApiProperty()
  totalEmployees!: number;

  @ApiProperty()
  activeEmployees!: number;

  @ApiProperty()
  inactiveEmployees!: number;

  @ApiProperty()
  employeesOnProbation!: number;

  @ApiProperty()
  newHiresThisMonth!: number;

  @ApiProperty()
  newHiresThisYear!: number;

  @ApiProperty()
  terminationsThisMonth!: number;

  @ApiProperty()
  terminationsThisYear!: number;

  @ApiProperty()
  averageYearsOfService!: number;

  @ApiProperty()
  employmentTypeBreakdown!: Record<string, number>;

  @ApiProperty()
  departmentBreakdown!: Record<string, number>;

  @ApiProperty()
  genderBreakdown!: Record<string, number>;

  @ApiProperty()
  ageGroupBreakdown!: Record<string, number>;

  @ApiProperty()
  generatedAt!: Date;
}
