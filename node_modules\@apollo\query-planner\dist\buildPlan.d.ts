import { Operation, Supergraph } from "@apollo/federation-internals";
import { Vertex, SimultaneousPaths } from "@apollo/query-graphs";
import { QueryPlannerConfig } from "./config";
import { QueryPlan } from "./QueryPlan";
export declare function compareOptionsComplexityOutOfContext<RV extends Vertex>(opt1: SimultaneousPaths<RV>, opt2: SimultaneousPaths<RV>): number;
export type PlanningStatistics = {
    evaluatedPlanCount: number;
};
interface BuildQueryPlanOptions {
    overrideConditions?: Map<string, boolean>;
    recursiveSelectionsLimitDisabled?: boolean;
    nonLocalSelectionsLimitDisabled?: boolean;
}
export declare class QueryPlanner {
    readonly supergraph: Supergraph;
    private readonly config;
    private readonly federatedQueryGraph;
    private _lastGeneratedPlanStatistics;
    private _defaultOverrideConditions;
    private readonly interfaceTypesWithInterfaceObjects;
    private readonly inconsistentAbstractTypesRuntimes;
    constructor(supergraph: Supergraph, config?: QueryPlannerConfig);
    private collectInterfaceTypesWithInterfaceObjects;
    private collectInconsistentAbstractTypesRuntimes;
    private collectAllOverrideLabels;
    buildQueryPlan(operation: Operation, options?: BuildQueryPlanOptions): QueryPlan;
    private optimizeSiblingTypenames;
    private withSiblingTypenameOptimizedAway;
    lastGeneratedPlanStatistics(): PlanningStatistics | undefined;
}
export {};
//# sourceMappingURL=buildPlan.d.ts.map