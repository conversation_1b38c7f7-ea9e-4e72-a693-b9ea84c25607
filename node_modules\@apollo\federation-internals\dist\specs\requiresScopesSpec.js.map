{"version": 3, "file": "requiresScopesSpec.js", "sourceRoot": "", "sources": ["../../src/specs/requiresScopesSpec.ts"], "names": [], "mappings": ";;;AAAA,qCAA4C;AAC5C,yCAMoB;AACpB,gDAAuD;AACvD,oFAA+G;AAC/G,4DAA4D;AAC5D,oFAAmF;AACnF,oCAAkC;AAElC,IAAY,sBAEX;AAFD,WAAY,sBAAsB;IAChC,yCAAe,CAAA;AACjB,CAAC,EAFW,sBAAsB,sCAAtB,sBAAsB,QAEjC;AAED,MAAa,4BAA6B,SAAQ,4BAAiB;IAKjE,YAAY,OAAuB;QACjC,KAAK,CACH,IAAI,qBAAU,CACZ,4BAA4B,CAAC,QAAQ,EACrC,4BAA4B,CAAC,aAAa,EAC1C,OAAO,CACR,CACF,CAAC;QAEF,IAAI,CAAC,YAAY,CAAC,IAAA,6DAA6B,EAAC,EAAE,IAAI,EAAE,sBAAsB,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAEzF,IAAI,CAAC,iBAAiB,CAAC,IAAA,4DAA4B,EAAC;YAClD,IAAI,EAAE,4BAA4B,CAAC,aAAa;YAChD,IAAI,EAAE,CAAC;oBACL,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE;wBACxB,IAAA,cAAM,EAAC,OAAO,EAAE,2DAA2D,CAAC,CAAC;wBAC7E,MAAM,SAAS,GAAG,OAAO,CAAC,gBAAgB,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;wBACzE,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;wBACzC,IAAA,cAAM,EAAC,SAAS,EAAE,GAAG,EAAE,CAAC,aAAa,SAAS,iBAAiB,CAAC,CAAC;wBACjE,OAAO,IAAI,yBAAW,CAAC,IAAI,sBAAQ,CAAC,IAAI,yBAAW,CAAC,IAAI,sBAAQ,CAAC,IAAI,yBAAW,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAClG,CAAC;oBACD,mBAAmB,EAAE,+DAA+B,CAAC,KAAK;iBAC3D,CAAC;YACF,SAAS,EAAE;gBACT,2BAAiB,CAAC,gBAAgB;gBAClC,2BAAiB,CAAC,MAAM;gBACxB,2BAAiB,CAAC,SAAS;gBAC3B,2BAAiB,CAAC,MAAM;gBACxB,2BAAiB,CAAC,IAAI;aACvB;YACD,QAAQ,EAAE,IAAI;YACd,uBAAuB,EAAE,GAAG,EAAE,CAAC,gCAAwB,CAAC,MAAM,EAAE;SACjE,CAAC,CAAC,CAAC;IACN,CAAC;IAED,IAAI,kBAAkB;QACpB,OAAO,UAAU,CAAC;IACpB,CAAC;;AA3CH,oEA4CC;AA3CwB,0CAAa,GAAG,gBAAgB,CAAC;AACjC,qCAAQ,GAC7B,4BAA4B,4BAA4B,CAAC,aAAa,EAAE,CAAC;AA2ChE,QAAA,wBAAwB,GACnC,IAAI,6BAAkB,CACpB,4BAA4B,CAAC,QAAQ,CACtC,CAAC,GAAG,CAAC,IAAI,4BAA4B,CAAC,IAAI,yBAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAEpE,IAAA,wCAAoB,EAAC,gCAAwB,CAAC,CAAC"}