import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards, ParseUUIDPipe } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard, RolesGuard, TenantGuard } from '@app/security';
import { Roles, CurrentUser, CurrentTenant } from '@app/common';
import { ReportTemplateService } from '../services/report-template.service';

@ApiTags('Report Templates')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard, TenantGuard, RolesGuard)
@Controller('report-templates')
export class ReportTemplateController {
  constructor(private readonly reportTemplateService: ReportTemplateService) {}

  @Post()
  @Roles('admin', 'hr_manager')
  @ApiOperation({ summary: 'Create report template' })
  async create(@Body() createTemplateDto: any, @CurrentUser() user: any, @CurrentTenant() tenantId: string) {
    return this.reportTemplateService.create(createTemplateDto, user.id, tenantId);
  }

  @Get()
  @Roles('admin', 'hr_manager', 'hr_user', 'manager')
  @ApiOperation({ summary: 'Get report templates' })
  async findAll(@Query() query: any, @CurrentTenant() tenantId: string) {
    return this.reportTemplateService.findAll(query, tenantId);
  }

  @Get(':id')
  @Roles('admin', 'hr_manager', 'hr_user', 'manager')
  @ApiOperation({ summary: 'Get report template by ID' })
  async findOne(@Param('id', ParseUUIDPipe) id: string, @CurrentTenant() tenantId: string) {
    return this.reportTemplateService.findOne(id, tenantId);
  }

  @Put(':id')
  @Roles('admin', 'hr_manager')
  @ApiOperation({ summary: 'Update report template' })
  async update(@Param('id', ParseUUIDPipe) id: string, @Body() updateTemplateDto: any, @CurrentUser() user: any, @CurrentTenant() tenantId: string) {
    return this.reportTemplateService.update(id, updateTemplateDto, user.id, tenantId);
  }

  @Delete(':id')
  @Roles('admin', 'hr_manager')
  @ApiOperation({ summary: 'Delete report template' })
  async remove(@Param('id', ParseUUIDPipe) id: string, @CurrentUser() user: any, @CurrentTenant() tenantId: string) {
    return this.reportTemplateService.remove(id, user.id, tenantId);
  }
}
