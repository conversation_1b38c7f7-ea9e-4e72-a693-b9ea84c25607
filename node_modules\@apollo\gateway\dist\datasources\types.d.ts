import { ResponsePath } from '@apollo/query-planner';
import { GatewayGraphQLResponse, GatewayGraphQLRequestContext } from '@apollo/server-gateway-interface';
export interface GraphQLDataSource<TContext extends Record<string, any> = Record<string, any>> {
    process(options: GraphQLDataSourceProcessOptions<TContext>): Promise<GatewayGraphQLResponse>;
}
export declare enum GraphQLDataSourceRequestKind {
    INCOMING_OPERATION = "incoming operation",
    HEALTH_CHECK = "health check",
    LOADING_SCHEMA = "loading schema"
}
export type GraphQLDataSourceProcessOptions<TContext extends Record<string, any> = Record<string, any>> = {
    request: GatewayGraphQLRequestContext<TContext>['request'];
} & ({
    kind: GraphQLDataSourceRequestKind.INCOMING_OPERATION;
    incomingRequestContext: GatewayGraphQLRequestContext<TContext>;
    context: GatewayGraphQLRequestContext<TContext>['context'];
    document?: GatewayGraphQLRequestContext<TContext>['document'];
    pathInIncomingRequest?: ResponsePath;
} | {
    kind: GraphQLDataSourceRequestKind.HEALTH_CHECK | GraphQLDataSourceRequestKind.LOADING_SCHEMA;
    context: Record<string, any>;
});
//# sourceMappingURL=types.d.ts.map