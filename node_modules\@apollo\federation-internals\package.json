{"name": "@apollo/federation-internals", "version": "2.11.2", "description": "Apollo Federation internal utilities", "main": "dist/index.js", "types": "dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/apollographql/federation.git", "directory": "internals-js/"}, "scripts": {"test": "jest"}, "keywords": ["graphql", "federation", "apollo"], "author": "Apollo <<EMAIL>>", "license": "Elastic-2.0", "engines": {"node": ">=14.15.0"}, "dependencies": {"chalk": "^4.1.0", "js-levenshtein": "^1.1.6", "@types/uuid": "^9.0.0", "uuid": "^9.0.0"}, "publishConfig": {"access": "public"}, "peerDependencies": {"graphql": "^16.5.0"}}