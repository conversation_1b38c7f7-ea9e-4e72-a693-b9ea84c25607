import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Position, Employee } from '@app/database';

@Injectable()
export class PositionValidationService {
  constructor(
    @InjectRepository(Position)
    private readonly positionRepository: Repository<Position>,
    @InjectRepository(Employee)
    private readonly employeeRepository: Repository<Employee>,
  ) {}

  async validatePositionCode(code: string, tenantId: string, excludeId?: string): Promise<boolean> {
    const query = this.positionRepository
      .createQueryBuilder('position')
      .where('position.code = :code', { code })
      .andWhere('position.tenantId = :tenantId', { tenantId });

    if (excludeId) {
      query.andWhere('position.id != :excludeId', { excludeId });
    }

    const existing = await query.getOne();
    return !existing;
  }

  async validateReportsToPosition(reportsToId: string, positionId: string, tenantId: string): Promise<boolean> {
    // Prevent circular reference
    if (reportsToId === positionId) {
      return false;
    }

    // Check if reports to position exists
    const reportsTo = await this.positionRepository.findOne({
      where: { id: reportsToId, tenantId },
    });

    if (!reportsTo) {
      return false;
    }

    // Check for circular dependency by traversing up the hierarchy
    return this.checkCircularDependency(reportsToId, positionId, tenantId);
  }

  async validateSalaryRange(minSalary?: number, maxSalary?: number): Promise<boolean> {
    if (!minSalary || !maxSalary) {
      return true; // Valid if either is not provided
    }

    return minSalary <= maxSalary;
  }

  async canDeletePosition(positionId: string, tenantId: string): Promise<{ canDelete: boolean; reason?: string }> {
    // Check for active employees
    const employeeCount = await this.employeeRepository.count({
      where: { positionId, tenantId, isActive: true },
    });

    if (employeeCount > 0) {
      return {
        canDelete: false,
        reason: `Position has ${employeeCount} active employees`,
      };
    }

    // Check for positions reporting to this position
    const subordinateCount = await this.positionRepository.count({
      where: { reportsToId: positionId, tenantId, isActive: true },
    });

    if (subordinateCount > 0) {
      return {
        canDelete: false,
        reason: `Position has ${subordinateCount} positions reporting to it`,
      };
    }

    return { canDelete: true };
  }

  async validatePositionHierarchy(positionId: string, departmentId: string, tenantId: string): Promise<boolean> {
    // Check if the position's reporting structure is within the same department
    const position = await this.positionRepository.findOne({
      where: { id: positionId, tenantId },
      relations: ['reportsTo'],
    });

    if (!position) {
      return false;
    }

    // If position has a reports to, validate it's in the same department or a parent department
    if (position.reportsTo) {
      const reportsToPosition = await this.positionRepository.findOne({
        where: { id: position.reportsTo.id, tenantId },
        relations: ['department'],
      });

      if (!reportsToPosition) {
        return false;
      }

      // For now, we'll allow cross-department reporting
      // In a more strict implementation, you might want to validate department hierarchy
      return true;
    }

    return true;
  }

  private async checkCircularDependency(reportsToId: string, positionId: string, tenantId: string): Promise<boolean> {
    const visited = new Set<string>();
    let currentId = reportsToId;

    while (currentId && !visited.has(currentId)) {
      if (currentId === positionId) {
        return false; // Circular dependency found
      }

      visited.add(currentId);

      const position = await this.positionRepository.findOne({
        where: { id: currentId, tenantId },
        select: ['reportsToId'],
      });

      currentId = position?.reportsToId || null;
    }

    return true; // No circular dependency
  }
}
