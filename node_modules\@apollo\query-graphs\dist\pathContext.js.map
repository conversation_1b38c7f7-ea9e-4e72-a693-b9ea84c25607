{"version": 3, "file": "pathContext.js", "sourceRoot": "", "sources": ["../src/pathContext.ts"], "names": [], "mappings": ";;;;;;AAAA,uEAKsC;AACtC,4DAAmC;AAEnC,SAAgB,aAAa,CAAC,CAAM;IAClC,OAAO,CAAC,YAAY,WAAW,CAAC;AAClC,CAAC;AAFD,sCAEC;AAOD,SAAgB,4BAA4B,CAAC,SAA2B;IACtE,MAAM,YAAY,GAA2B,EAAE,CAAC;IAChD,uBAAuB,CAAC,SAAS,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;IACzD,uBAAuB,CAAC,SAAS,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC;IAC5D,OAAO,YAAY,CAAC;AACtB,CAAC;AALD,oEAKC;AAED,SAAS,uBAAuB,CAAC,SAA2B,EAAE,IAAwB,EAAE,KAA6B;IACnH,MAAM,OAAO,GAAG,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;IACpD,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACvB,IAAA,6BAAM,EAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,IAAI,6BAA6B,SAAS,EAAE,CAAC,CAAA;QACnF,MAAM,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC;QAC3C,IAAA,6BAAM,EAAC,OAAO,KAAK,KAAK,SAAS,IAAI,IAAA,iCAAU,EAAC,KAAK,CAAC,EAAE,GAAG,EAAE,CAAC,iBAAiB,KAAK,2BAA2B,IAAI,EAAE,CAAC,CAAC;QACvH,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;IAC9B,CAAC;AACH,CAAC;AAKD,MAAa,WAAW;IACtB,YAGW,YAAoC;QAApC,iBAAY,GAAZ,YAAY,CAAwB;IAE/C,CAAC;IAED,OAAO;QACL,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC,CAAC;IACxC,CAAC;IAED,aAAa,CAAC,SAA2B;QACvC,IAAI,SAAS,CAAC,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC7C,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,eAAe,GAAG,4BAA4B,CAAC,SAAS,CAAC,CAAC;QAChE,OAAO,eAAe,CAAC,MAAM,KAAK,CAAC;YACjC,CAAC,CAAC,IAAI;YACN,CAAC,CAAC,IAAI,WAAW,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;IACjE,CAAC;IAED,MAAM,CAAC,IAAiB;QACtB,OAAO,IAAA,oBAAS,EAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;IACzD,CAAC;IAED,QAAQ;QACN,OAAO,GAAG;cACN,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,EAAC,IAAI,EAAE,KAAK,EAAC,EAAE,EAAE,CAAC,IAAI,IAAI,QAAQ,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;cAC7E,GAAG,CAAC;IACV,CAAC;CACF;AAhCD,kCAgCC;AAEY,QAAA,YAAY,GAAG,IAAI,WAAW,CAAC,EAAE,CAAC,CAAC"}