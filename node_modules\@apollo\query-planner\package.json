{"name": "@apollo/query-planner", "version": "2.11.2", "description": "Apollo Query Planner", "author": "Apollo <<EMAIL>>", "main": "dist/index.js", "types": "dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/apollographql/federation.git", "directory": "query-planner-js/"}, "keywords": ["graphql", "federation", "gateway", "server", "apollo"], "engines": {"node": ">=14.15.0"}, "license": "Elastic-2.0", "publishConfig": {"access": "public"}, "dependencies": {"@apollo/federation-internals": "2.11.2", "@apollo/query-graphs": "2.11.2", "@apollo/utils.keyvaluecache": "^2.1.0", "chalk": "^4.1.0", "deep-equal": "^2.0.5", "pretty-format": "^29.0.0"}, "peerDependencies": {"graphql": "^16.5.0"}}