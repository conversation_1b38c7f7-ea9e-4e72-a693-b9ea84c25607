import { CompositeType, CoreFeature, Directive, DirectiveDefinition, FieldDefinition, InputFieldDefinition, InterfaceType, NamedType, ObjectType, ScalarType, Schema, SchemaBlueprint, SchemaConfig, SchemaElement, UnionType } from "./definitions";
import { SDLValidationRule } from "graphql/validation/ValidationContext";
import { ASTNode, DocumentNode, GraphQLError } from "graphql";
import { SelectionSet } from './operations';
import { ErrorCodeDefinition } from "./error";
import { PrintOptions as PrintOptions } from "./print";
import { CostDirectiveArguments, ListSizeDirectiveArguments } from "./specs/costSpec";
export declare const FEDERATION_RESERVED_SUBGRAPH_NAME = "_";
export declare const FEDERATION_UNNAMED_SUBGRAPH_NAME = "<unnamed>";
export declare function parseContext(input: string): {
    context: undefined;
    selection: undefined;
} | {
    context: string;
    selection: string;
};
export declare function collectUsedFields(metadata: FederationMetadata): Set<FieldDefinition<CompositeType>>;
export declare class FederationMetadata {
    readonly schema: Schema;
    private _externalTester?;
    private _sharingPredicate?;
    private _fieldUsedPredicate?;
    private _isFed2Schema?;
    constructor(schema: Schema);
    private onInvalidate;
    isFed2Schema(): boolean;
    federationFeature(): CoreFeature | undefined;
    private externalTester;
    private sharingPredicate;
    private fieldUsedPredicate;
    isFieldUsed(field: FieldDefinition<CompositeType>): boolean;
    isFieldExternal(field: FieldDefinition<any> | InputFieldDefinition): boolean;
    isFieldPartiallyExternal(field: FieldDefinition<any> | InputFieldDefinition): boolean;
    isFieldFullyExternal(field: FieldDefinition<any> | InputFieldDefinition): boolean;
    isFieldFakeExternal(field: FieldDefinition<any> | InputFieldDefinition): boolean;
    selectionSelectsAnyExternalField(selectionSet: SelectionSet): boolean;
    isFieldShareable(field: FieldDefinition<any>): boolean;
    isInterfaceObjectType(type: NamedType): type is ObjectType;
    federationDirectiveNameInSchema(name: string): string;
    federationTypeNameInSchema(name: string): string;
    private getLegacyFederationDirective;
    private getFederationDirective;
    private getPost20FederationDirective;
    keyDirective(): DirectiveDefinition<{
        fields: any;
        resolvable?: boolean;
    }>;
    overrideDirective(): DirectiveDefinition<{
        from: string;
        label?: string;
    }>;
    extendsDirective(): DirectiveDefinition<Record<string, never>>;
    externalDirective(): DirectiveDefinition<{
        reason: string;
    }>;
    requiresDirective(): DirectiveDefinition<{
        fields: any;
    }>;
    providesDirective(): DirectiveDefinition<{
        fields: any;
    }>;
    shareableDirective(): DirectiveDefinition<{}>;
    tagDirective(): DirectiveDefinition<{
        name: string;
    }>;
    composeDirective(): Post20FederationDirectiveDefinition<{
        name: string;
    }>;
    inaccessibleDirective(): DirectiveDefinition<{}>;
    interfaceObjectDirective(): Post20FederationDirectiveDefinition<{}>;
    authenticatedDirective(): Post20FederationDirectiveDefinition<{}>;
    requiresScopesDirective(): Post20FederationDirectiveDefinition<{
        scopes: string[];
    }>;
    policyDirective(): Post20FederationDirectiveDefinition<{
        scopes: string[];
    }>;
    fromContextDirective(): Post20FederationDirectiveDefinition<{
        field: string;
    }>;
    contextDirective(): Post20FederationDirectiveDefinition<{
        name: string;
    }>;
    costDirective(): Post20FederationDirectiveDefinition<CostDirectiveArguments>;
    listSizeDirective(): Post20FederationDirectiveDefinition<ListSizeDirectiveArguments>;
    allFederationDirectives(): DirectiveDefinition[];
    entityType(): UnionType | undefined;
    anyType(): ScalarType;
    serviceType(): ObjectType;
    fieldSetType(): ScalarType;
    allFederationTypes(): NamedType[];
}
export type FederationDirectiveNotDefinedInSchema<TApplicationArgs extends {
    [key: string]: any;
}> = {
    name: string;
    applications: () => ReadonlySet<Directive<any, TApplicationArgs>>;
};
export type Post20FederationDirectiveDefinition<TApplicationArgs extends {
    [key: string]: any;
}> = DirectiveDefinition<TApplicationArgs> | FederationDirectiveNotDefinedInSchema<TApplicationArgs>;
export declare function isFederationDirectiveDefinedInSchema<TApplicationArgs extends {
    [key: string]: any;
}>(definition: Post20FederationDirectiveDefinition<TApplicationArgs>): definition is DirectiveDefinition<TApplicationArgs>;
export declare function hasAppliedDirective(type: NamedType, definition: Post20FederationDirectiveDefinition<any>): boolean;
export declare class FederationBlueprint extends SchemaBlueprint {
    private readonly withRootTypeRenaming;
    constructor(withRootTypeRenaming: boolean);
    onAddedCoreFeature(schema: Schema, feature: CoreFeature): void;
    onMissingDirectiveDefinition(schema: Schema, directive: Directive): DirectiveDefinition | GraphQLError[] | undefined;
    ignoreParsedField(type: NamedType, fieldName: string): boolean;
    onConstructed(schema: Schema): void;
    onDirectiveDefinitionAndSchemaParsed(schema: Schema): GraphQLError[];
    onInvalidation(schema: Schema): void;
    onValidation(schema: Schema): GraphQLError[];
    validationRules(): readonly SDLValidationRule[];
    onUnknownDirectiveValidationError(schema: Schema, unknownDirectiveName: string, error: GraphQLError): GraphQLError;
    applyDirectivesAfterParsing(): boolean;
}
export declare function setSchemaAsFed2Subgraph(schema: Schema, useLatest?: boolean): void;
export declare const FEDERATION2_LINK_WITH_FULL_IMPORTS = "@link(url: \"https://specs.apollo.dev/federation/v2.11\", import: [\"@key\", \"@requires\", \"@provides\", \"@external\", \"@tag\", \"@extends\", \"@shareable\", \"@inaccessible\", \"@override\", \"@composeDirective\", \"@interfaceObject\", \"@authenticated\", \"@requiresScopes\", \"@policy\", \"@context\", \"@fromContext\", \"@cost\", \"@listSize\"])";
export declare const FEDERATION2_LINK_WITH_AUTO_EXPANDED_IMPORTS = "@link(url: \"https://specs.apollo.dev/federation/v2.11\", import: [\"@key\", \"@requires\", \"@provides\", \"@external\", \"@tag\", \"@extends\", \"@shareable\", \"@inaccessible\", \"@override\", \"@composeDirective\", \"@interfaceObject\"])";
export declare const FEDERATION2_LINK_WITH_AUTO_EXPANDED_IMPORTS_UPGRADED = "@link(url: \"https://specs.apollo.dev/federation/v2.4\", import: [\"@key\", \"@requires\", \"@provides\", \"@external\", \"@tag\", \"@extends\", \"@shareable\", \"@inaccessible\", \"@override\", \"@composeDirective\", \"@interfaceObject\"])";
export declare function asFed2SubgraphDocument(document: DocumentNode, options?: {
    addAsSchemaExtension?: boolean;
    includeAllImports?: boolean;
}): DocumentNode;
export declare function printSubgraphNames(names: string[]): string;
export declare function federationMetadata(schema: Schema): FederationMetadata | undefined;
export declare function isFederationSubgraphSchema(schema: Schema): boolean;
export declare function isFederationField(field: FieldDefinition<CompositeType>): boolean;
export declare function isEntityType(type: NamedType): boolean;
export declare function isInterfaceObjectType(type: NamedType): boolean;
export declare function buildSubgraph(name: string, url: string, source: DocumentNode | string, withRootTypeRenaming?: boolean): Subgraph;
export declare function newEmptyFederation2Schema(config?: SchemaConfig): Schema;
export declare function parseFieldSetArgument({ parentType, directive, fieldAccessor, validate, decorateValidationErrors, normalize, }: {
    parentType: CompositeType;
    directive: Directive<SchemaElement<any, any>, {
        fields: any;
    }>;
    fieldAccessor?: (type: CompositeType, fieldName: string) => FieldDefinition<any> | undefined;
    validate?: boolean;
    decorateValidationErrors?: boolean;
    normalize?: boolean;
}): SelectionSet;
export declare function collectTargetFields({ parentType, directive, includeInterfaceFieldsImplementations, validate, }: {
    parentType: CompositeType;
    directive: Directive<NamedType | FieldDefinition<CompositeType>, {
        fields: any;
    }>;
    includeInterfaceFieldsImplementations: boolean;
    validate?: boolean;
}): FieldDefinition<CompositeType>[];
export interface ServiceDefinition {
    typeDefs: DocumentNode;
    name: string;
    url?: string;
}
export declare function subgraphsFromServiceList(serviceList: ServiceDefinition[]): Subgraphs | GraphQLError[];
export declare class Subgraphs {
    private readonly subgraphs;
    add(subgraph: Subgraph): Subgraph;
    get(name: string): Subgraph | undefined;
    size(): number;
    names(): readonly string[];
    values(): readonly Subgraph[];
    [Symbol.iterator](): Generator<Subgraph, void, unknown>;
    validate(): GraphQLError[] | undefined;
    toString(): string;
}
export declare const anyTypeSpec: import("./directiveAndTypeSpecification").TypeSpecification;
export declare const serviceTypeSpec: import("./directiveAndTypeSpecification").TypeSpecification;
export declare const entityTypeSpec: import("./directiveAndTypeSpecification").TypeSpecification;
export declare const FEDERATION_OPERATION_TYPES: import("./directiveAndTypeSpecification").TypeSpecification[];
export declare const serviceFieldName = "_service";
export declare const entitiesFieldName = "_entities";
export declare const FEDERATION_OPERATION_FIELDS: readonly string[];
export declare class Subgraph {
    readonly name: string;
    readonly url: string;
    readonly schema: Schema;
    constructor(name: string, url: string, schema: Schema);
    metadata(): FederationMetadata;
    isFed2Subgraph(): boolean;
    private addFederationOperations;
    assumeValid(): Subgraph;
    validate(): Subgraph;
    private isPrintedDirective;
    private isPrintedType;
    private isPrintedDirectiveApplication;
    toString(basePrintOptions?: PrintOptions): string;
}
export type SubgraphASTNode = ASTNode & {
    subgraph: string;
};
export declare function addSubgraphToASTNode(node: ASTNode, subgraph: string): SubgraphASTNode;
export declare function addSubgraphToError(e: GraphQLError, subgraphName: string, errorCode?: ErrorCodeDefinition): GraphQLError;
export type ProvidesOrRequiresApplication = Directive<FieldDefinition<ObjectType | InterfaceType>, {
    fields: any;
}>;
export declare function removeInactiveProvidesAndRequires(schema: Schema, onModified?: (field: FieldDefinition<any>, original: ProvidesOrRequiresApplication, updated?: ProvidesOrRequiresApplication) => void): void;
//# sourceMappingURL=federation.d.ts.map