"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.prettyFormatQueryPlan = void 0;
const pretty_format_1 = __importDefault(require("pretty-format"));
const snapshotSerializers_1 = require("./snapshotSerializers");
function prettyFormatQueryPlan(queryPlan) {
    return (0, pretty_format_1.default)(queryPlan, {
        plugins: [snapshotSerializers_1.queryPlanSerializer, snapshotSerializers_1.astSerializer],
    });
}
exports.prettyFormatQueryPlan = prettyFormatQueryPlan;
//# sourceMappingURL=prettyFormatQueryPlan.js.map