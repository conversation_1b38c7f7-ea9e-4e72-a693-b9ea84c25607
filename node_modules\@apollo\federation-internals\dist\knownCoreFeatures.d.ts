import { FeatureDefinition, FeatureDefinitions, FeatureUrl } from "./specs/coreSpec";
export declare function registerKnownFeature(definitions: FeatureDefinitions): void;
export declare function coreFeatureDefinitionIfKnown(url: FeatureUrl): FeatureDefinition | undefined;
export declare function unregisterKnownFeatures(definitions: FeatureDefinitions): void;
//# sourceMappingURL=knownCoreFeatures.d.ts.map