{"version": 3, "file": "error.d.ts", "sourceRoot": "", "sources": ["../src/error.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,mBAAmB,EAAE,qBAAqB,EAAE,MAAM,SAAS,CAAC;AAO5F,KAAK,cAAc,GAAG,OAAO,GAAG,UAAU,GAAG,cAAc,CAAC;AAQ5D,MAAM,MAAM,iBAAiB,GAAG;IAC9B,OAAO,EAAE,MAAM,CAAC;IAChB,QAAQ,CAAC,EAAE,MAAM,EAAE,CAAC;CACrB,CAAA;AAED,MAAM,MAAM,mBAAmB,GAAG;IAChC,IAAI,EAAE,MAAM,CAAC;IACb,WAAW,EAAE,MAAM,CAAC;IACpB,QAAQ,EAAE,iBAAiB,CAAC;IAC5B,GAAG,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,mBAAmB,KAAK,YAAY,CAAC;CACvE,CAAA;AAsBD,wBAAgB,0BAA0B,CAAC,CAAC,EAAE,YAAY,GAAG,mBAAmB,CAS/E;AA6BD,wBAAgB,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG,YAAY,CAElG;AAMD,wBAAgB,WAAW,CAAC,CAAC,EAAE,KAAK,GAAG,YAAY,EAAE,GAAG,SAAS,CAQhE;AAED,wBAAgB,2BAA2B,CAAC,CAAC,EAAE,KAAK,GAAG,MAAM,CAM5D;AAED,wBAAgB,WAAW,CAAC,MAAM,EAAE,YAAY,EAAE,GAAG,MAAM,CAE1D;AAOD,MAAM,MAAM,iBAAiB,CAAC,QAAQ,GAAG,MAAM,IAAI;IACjD,GAAG,CAAC,OAAO,EAAE,QAAQ,GAAG,mBAAmB,CAAC;CAC7C,CAAA;AAwBD,wBAAgB,SAAS,CAAC,CAAC,EAAE,YAAY,GAAG,qBAAqB,GAAG,MAAM,GAAG,SAAS,CAKrF;AAED,wBAAgB,YAAY,CAAC,CAAC,EAAE,YAAY,GAAG,qBAAqB,GAAG,MAAM,GAAG,mBAAmB,GAAG,SAAS,CAG9G;AAED,wBAAgB,wBAAwB,CAAC,CAAC,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,GAAG,YAAY,CAY1F;AAED,wBAAgB,sBAAsB,CAAC,CAAC,EAAE,YAAY,EAAE,QAAQ,EAAE,SAAS,OAAO,EAAE,GAAG,OAAO,GAAG,SAAS,GAAG,YAAY,CAYxH;AAmcD,eAAO,MAAM,gBAAgB;;qCA3fqC,mBAAmB;;;qCAAnB,mBAAmB;;;qCAAnB,mBAAmB;;;qCAAnB,mBAAmB;;;qCAAnB,mBAAmB;;;6CAAnB,mBAAmB;;;qCAAnB,mBAAmB;;CAmgBpF,CAAA;AAED,eAAO,MAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA2FlB,CAAC;AAYF,eAAO,MAAM,cAAc,YA0B1B,CAAC"}