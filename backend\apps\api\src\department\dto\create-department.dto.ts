import {
  IsString,
  IsOptional,
  IsUUID,
  IsObject,
  Length,
  Matches,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateDepartmentDto {
  @ApiProperty({ description: 'Department name', example: 'Human Resources' })
  @IsString()
  @Length(2, 100)
  name: string;

  @ApiProperty({ description: 'Department code', example: 'HR' })
  @IsString()
  @Length(2, 10)
  @Matches(/^[A-Z0-9]+$/, { message: 'Department code must contain only uppercase letters and numbers' })
  code: string;

  @ApiPropertyOptional({ description: 'Department description' })
  @IsOptional()
  @IsString()
  @Length(0, 500)
  description?: string;

  @ApiPropertyOptional({ description: 'Parent department ID' })
  @IsOptional()
  @IsUUID()
  parentId?: string;

  @ApiPropertyOptional({ description: 'Department head employee ID' })
  @IsOptional()
  @IsUUID()
  headId?: string;

  @ApiPropertyOptional({ description: 'Cost center code' })
  @IsOptional()
  @IsString()
  @Length(0, 20)
  costCenter?: string;

  @ApiPropertyOptional({ description: 'Budget amount' })
  @IsOptional()
  budget?: number;

  @ApiPropertyOptional({ description: 'Location information' })
  @IsOptional()
  @IsString()
  location?: string;

  @ApiPropertyOptional({ description: 'Additional metadata' })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}
