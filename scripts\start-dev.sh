#!/bin/bash
# =============================================================================
# PeopleNest HRMS - Development Environment Startup Script (Bash)
# =============================================================================
# This script starts all services for development including infrastructure,
# backend, frontend, and AI services
# =============================================================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Default options
SKIP_INFRASTRUCTURE=false
BACKEND_ONLY=false
FRONTEND_ONLY=false
AI_ONLY=false
VERBOSE=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-infrastructure)
            SKIP_INFRASTRUCTURE=true
            shift
            ;;
        --backend-only)
            BACKEND_ONLY=true
            shift
            ;;
        --frontend-only)
            FRONTEND_ONLY=true
            shift
            ;;
        --ai-only)
            AI_ONLY=true
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --skip-infrastructure  Skip starting Docker infrastructure"
            echo "  --backend-only         Start only backend service"
            echo "  --frontend-only        Start only frontend service"
            echo "  --ai-only              Start only AI services"
            echo "  --verbose              Enable verbose output"
            echo "  --help                 Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option $1"
            exit 1
            ;;
    esac
done

function print_color() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

function print_header() {
    local title=$1
    echo ""
    print_color $BLUE "================================================================================"
    print_color $CYAN "  $title"
    print_color $BLUE "================================================================================"
    echo ""
}

function test_service_health() {
    local service_name=$1
    local url=$2
    local timeout=${3:-30}
    
    print_color $YELLOW "🔍 Checking $service_name health..."
    
    local start_time=$(date +%s)
    while true; do
        if curl -s --max-time 5 "$url" > /dev/null 2>&1; then
            print_color $GREEN "✅ $service_name is healthy!"
            return 0
        fi
        
        local current_time=$(date +%s)
        local elapsed=$((current_time - start_time))
        
        if [ $elapsed -ge $timeout ]; then
            print_color $RED "❌ $service_name health check failed after $timeout seconds"
            return 1
        fi
        
        sleep 2
    done
}

function start_infrastructure() {
    print_header "Starting Infrastructure Services"
    
    # Check if Docker is running
    if ! docker version > /dev/null 2>&1; then
        print_color $RED "❌ Docker is not running. Please start Docker first."
        exit 1
    fi
    
    print_color $GREEN "✅ Docker is running"
    
    # Start infrastructure services
    print_color $YELLOW "🚀 Starting infrastructure services..."
    docker-compose up -d
    
    if [ $? -eq 0 ]; then
        print_color $GREEN "✅ Infrastructure services started successfully!"
    else
        print_color $RED "❌ Failed to start infrastructure services"
        exit 1
    fi
    
    # Wait for critical services to be healthy
    print_color $YELLOW "⏳ Waiting for services to be ready..."
    sleep 10
    
    # Check service health
    test_service_health "Elasticsearch" "http://localhost:9200" 60
    test_service_health "Kibana" "http://localhost:5601" 60
    test_service_health "MinIO" "http://localhost:9000" 60
    test_service_health "Grafana" "http://localhost:3001" 60
    test_service_health "Prometheus" "http://localhost:9090" 60
    
    print_color $GREEN "🎉 Infrastructure services are ready!"
}

function start_backend() {
    print_header "Starting Backend Service"
    
    # Check if backend dependencies are installed
    if [ ! -d "backend/node_modules" ]; then
        print_color $YELLOW "📦 Installing backend dependencies..."
        cd backend
        npm install
        cd ..
    fi
    
    print_color $YELLOW "🚀 Starting NestJS backend..."
    
    # Start backend in a new terminal/tmux session if available
    if command -v tmux > /dev/null 2>&1; then
        tmux new-session -d -s peoplenest-backend -c backend 'npm run start:dev'
        print_color $GREEN "✅ Backend started in tmux session 'peoplenest-backend'"
    elif command -v gnome-terminal > /dev/null 2>&1; then
        gnome-terminal --working-directory=backend --title="PeopleNest Backend" -- npm run start:dev &
        print_color $GREEN "✅ Backend started in new terminal"
    else
        # Fallback: start in background
        cd backend
        npm run start:dev > ../logs/backend.log 2>&1 &
        echo $! > ../logs/backend.pid
        cd ..
        print_color $GREEN "✅ Backend started in background (PID: $(cat logs/backend.pid))"
    fi
    
    # Wait for backend to start
    sleep 5
    
    # Test backend health
    if test_service_health "Backend API" "http://localhost:4000/health" 60; then
        print_color $GREEN "✅ Backend service started successfully!"
        print_color $CYAN "🌐 Backend API: http://localhost:4000"
        print_color $CYAN "📚 GraphQL Playground: http://localhost:4000/graphql"
    fi
}

function start_frontend() {
    print_header "Starting Frontend Service"
    
    # Check if frontend dependencies are installed
    if [ ! -d "frontend/node_modules" ]; then
        print_color $YELLOW "📦 Installing frontend dependencies..."
        cd frontend
        npm install
        cd ..
    fi
    
    print_color $YELLOW "🚀 Starting React frontend..."
    
    # Start frontend in a new terminal/tmux session if available
    if command -v tmux > /dev/null 2>&1; then
        tmux new-session -d -s peoplenest-frontend -c frontend 'npm run dev'
        print_color $GREEN "✅ Frontend started in tmux session 'peoplenest-frontend'"
    elif command -v gnome-terminal > /dev/null 2>&1; then
        gnome-terminal --working-directory=frontend --title="PeopleNest Frontend" -- npm run dev &
        print_color $GREEN "✅ Frontend started in new terminal"
    else
        # Fallback: start in background
        cd frontend
        npm run dev > ../logs/frontend.log 2>&1 &
        echo $! > ../logs/frontend.pid
        cd ..
        print_color $GREEN "✅ Frontend started in background (PID: $(cat logs/frontend.pid))"
    fi
    
    # Wait for frontend to start
    sleep 5
    
    # Test frontend health
    if test_service_health "Frontend" "http://localhost:3000" 60; then
        print_color $GREEN "✅ Frontend service started successfully!"
        print_color $CYAN "🌐 Frontend: http://localhost:3000"
    fi
}

function start_ai_services() {
    print_header "Starting AI Services"
    
    # Check if Python is available
    if ! command -v python3 > /dev/null 2>&1; then
        print_color $RED "❌ Python3 is not available. Please install Python first."
        return 1
    fi
    
    print_color $GREEN "✅ Python3 is available"
    
    # Check if AI service dependencies are installed
    if [ ! -d "ai-services/venv" ]; then
        print_color $YELLOW "📦 Setting up AI services virtual environment..."
        cd ai-services
        python3 -m venv venv
        source venv/bin/activate
        pip install -r requirements.txt
        cd ..
    fi
    
    print_color $YELLOW "🚀 Starting AI services..."
    
    # Start AI services in a new terminal/tmux session if available
    if command -v tmux > /dev/null 2>&1; then
        tmux new-session -d -s peoplenest-ai -c ai-services 'source venv/bin/activate && python -m uvicorn main:app --reload --port 8000'
        print_color $GREEN "✅ AI services started in tmux session 'peoplenest-ai'"
    elif command -v gnome-terminal > /dev/null 2>&1; then
        gnome-terminal --working-directory=ai-services --title="PeopleNest AI Services" -- bash -c 'source venv/bin/activate && python -m uvicorn main:app --reload --port 8000' &
        print_color $GREEN "✅ AI services started in new terminal"
    else
        # Fallback: start in background
        cd ai-services
        source venv/bin/activate
        python -m uvicorn main:app --reload --port 8000 > ../logs/ai-services.log 2>&1 &
        echo $! > ../logs/ai-services.pid
        cd ..
        print_color $GREEN "✅ AI services started in background (PID: $(cat logs/ai-services.pid))"
    fi
    
    # Wait for AI services to start
    sleep 5
    
    # Test AI services health
    if test_service_health "AI Services" "http://localhost:8000/health" 60; then
        print_color $GREEN "✅ AI services started successfully!"
        print_color $CYAN "🌐 AI Services: http://localhost:8000"
        print_color $CYAN "📚 AI API Docs: http://localhost:8000/docs"
    fi
}

function show_service_status() {
    print_header "Service Status Summary"
    
    # Create logs directory if it doesn't exist
    mkdir -p logs
    
    local services=(
        "Frontend (React)|http://localhost:3000"
        "Backend (NestJS)|http://localhost:4000"
        "AI Services (FastAPI)|http://localhost:8000"
        "Elasticsearch|http://localhost:9200"
        "Kibana|http://localhost:5601"
        "MinIO|http://localhost:9000"
        "Grafana|http://localhost:3001"
        "Prometheus|http://localhost:9090"
    )
    
    for service_info in "${services[@]}"; do
        IFS='|' read -r name url <<< "$service_info"
        
        if curl -s --max-time 3 "$url" > /dev/null 2>&1; then
            printf "  %-25s %s %s\n" "$name" "$(print_color $GREEN "✅ Running")" "$url"
        else
            printf "  %-25s %s %s\n" "$name" "$(print_color $RED "❌ Down")" "$url"
        fi
    done
    
    echo ""
    print_color $YELLOW "🔑 Default Admin Credentials:"
    print_color $CYAN "   Username: awadhesh"
    print_color $CYAN "   Password: awadhesh123"
    print_color $CYAN "   Email: <EMAIL>"
    
    echo ""
    print_color $YELLOW "📋 Useful Commands:"
    print_color $CYAN "   Stop all services: ./scripts/stop-dev.sh"
    print_color $CYAN "   View logs: tail -f logs/*.log"
    print_color $CYAN "   Tmux sessions: tmux list-sessions"
}

# Main execution
print_header "PeopleNest HRMS - Development Environment Startup"

# Create logs directory
mkdir -p logs

# Start services based on parameters
if [ "$SKIP_INFRASTRUCTURE" = false ] && [ "$BACKEND_ONLY" = false ] && [ "$FRONTEND_ONLY" = false ] && [ "$AI_ONLY" = false ]; then
    start_infrastructure
fi

if [ "$FRONTEND_ONLY" = false ] && [ "$AI_ONLY" = false ]; then
    start_backend
fi

if [ "$BACKEND_ONLY" = false ] && [ "$AI_ONLY" = false ]; then
    start_frontend
fi

if [ "$BACKEND_ONLY" = false ] && [ "$FRONTEND_ONLY" = false ]; then
    start_ai_services
fi

# Show final status
sleep 3
show_service_status

print_header "🎉 PeopleNest HRMS Development Environment Ready!"
print_color $GREEN "All services are running. You can now access the application."
print_color $YELLOW "Use tmux attach-session -t <session-name> to view service logs."
