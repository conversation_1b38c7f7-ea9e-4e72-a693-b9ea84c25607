const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.APP_PORT || 4000;

// Middleware
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:3002'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// Add error handling for JSON parsing
app.use(express.json({
  verify: (req, res, buf, encoding) => {
    try {
      JSON.parse(buf);
    } catch (e) {
      console.error('JSON Parse Error:', e.message);
      console.error('Raw body:', buf.toString());
      throw new Error('Invalid JSON');
    }
  }
}));
app.use(express.urlencoded({ extended: true }));

// Basic routes
app.get('/', (req, res) => {
  res.json({
    message: 'Hello from PeopleNest HRMS API Gateway!',
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: 'PeopleNest HRMS API Gateway',
    version: '1.0.0'
  });
});

app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: 'PeopleNest HRMS API Gateway',
    version: '1.0.0',
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// API routes
app.get('/api/v1/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    services: {
      database: 'connected',
      redis: 'connected',
      mongodb: 'connected'
    },
    version: '1.0.0'
  });
});

// Auth routes
app.post('/api/v1/auth/login', (req, res) => {
  console.log('Login request received');
  console.log('Headers:', req.headers);
  console.log('Body:', req.body);
  console.log('Raw body type:', typeof req.body);

  const { email, password } = req.body;
  console.log('Extracted credentials:', { email, password });

  // Simple mock authentication
  if (email === '<EMAIL>' && password === 'Password1234') {
    console.log('✅ Admin login successful, sending response...');
    const response = {
      success: true,
      message: 'Login successful',
      user: {
        id: '1',
        email: '<EMAIL>',
        firstName: 'Admin',
        lastName: 'User',
        role: 'SUPER_ADMIN',
        permissions: ['*']
      },
      token: 'mock-jwt-token-' + Date.now(),
      refreshToken: 'mock-refresh-token-' + Date.now()
    };
    console.log('Sending response:', response);
    res.json(response);
  } else if (email === 'awadhesh' && password === 'awadhesh123') {
    console.log('✅ Awadhesh login successful, sending response...');
    const response = {
      success: true,
      message: 'Login successful',
      user: {
        id: '1',
        email: '<EMAIL>',
        firstName: 'Admin',
        lastName: 'User',
        role: 'SUPER_ADMIN',
        permissions: ['*']
      },
      token: 'mock-jwt-token-' + Date.now(),
      refreshToken: 'mock-refresh-token-' + Date.now()
    };
    console.log('Sending response:', response);
    res.json(response);
  } else {
    console.log('❌ Login failed for:', { email, password });
    res.status(401).json({
      success: false,
      message: 'Invalid credentials'
    });
  }
});

// Profile endpoint
app.get('/api/v1/auth/profile', (req, res) => {
  console.log('Profile request received');
  console.log('Headers:', req.headers);

  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      success: false,
      message: 'No token provided'
    });
  }

  const token = authHeader.substring(7); // Remove 'Bearer ' prefix
  console.log('Token received:', token);

  // Simple token validation (in real app, verify JWT)
  if (token.startsWith('mock-jwt-token-')) {
    console.log('✅ Valid token, returning user profile');
    const userProfile = {
      id: '1',
      email: '<EMAIL>',
      firstName: 'Admin',
      lastName: 'User',
      role: 'SUPER_ADMIN',
      permissions: ['*']
    };

    console.log('Sending profile response:', userProfile);
    res.json(userProfile);
  } else {
    console.log('❌ Invalid token');
    res.status(401).json({
      success: false,
      message: 'Invalid token'
    });
  }
});

// Dashboard routes
app.get('/api/v1/dashboard/metrics', (req, res) => {
  console.log('Dashboard metrics request received');
  const { timeRange = '30d' } = req.query;

  // Mock dashboard metrics data
  const metrics = {
    employees: {
      total: 156,
      active: 142,
      newHires: 8,
      turnover: 3.2
    },
    performance: {
      averageRating: 4.2,
      completedReviews: 89,
      pendingReviews: 12,
      goalAchievementRate: 78.5
    },
    payroll: {
      totalPayroll: 1250000,
      averageSalary: 85000,
      pendingPayments: 3,
      payrollCosts: 1350000
    },
    attendance: {
      presentToday: 134,
      onLeave: 8,
      lateArrivals: 5,
      averageHours: 8.2
    }
  };

  console.log('Sending dashboard metrics:', metrics);
  res.json(metrics);
});

app.get('/api/v1/dashboard/activities', (req, res) => {
  console.log('Dashboard activities request received');
  const { limit = 10 } = req.query;

  // Mock recent activities data
  const activities = [
    {
      id: '1',
      type: 'employee',
      title: 'New Employee Onboarded',
      description: 'Sarah Johnson joined the Engineering team',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      status: 'success'
    },
    {
      id: '2',
      type: 'performance',
      title: 'Performance Review Completed',
      description: 'Q4 review completed for Marketing team',
      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
      status: 'success'
    },
    {
      id: '3',
      type: 'payroll',
      title: 'Payroll Processing',
      description: 'Monthly payroll processed successfully',
      timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
      status: 'success'
    },
    {
      id: '4',
      type: 'leave',
      title: 'Leave Request Approved',
      description: 'Vacation request approved for John Doe',
      timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),
      status: 'info'
    },
    {
      id: '5',
      type: 'performance',
      title: 'Goal Update Required',
      description: '3 employees need to update their quarterly goals',
      timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),
      status: 'warning'
    }
  ];

  console.log('Sending dashboard activities:', activities.slice(0, parseInt(limit)));
  res.json(activities.slice(0, parseInt(limit)));
});

app.get('/api/v1/dashboard/charts', (req, res) => {
  console.log('Dashboard charts request received');
  const { timeRange = '30d' } = req.query;

  // Mock chart data
  const chartData = {
    employeeGrowth: [
      { month: 'Jan', employees: 145, newHires: 5, departures: 2 },
      { month: 'Feb', employees: 148, newHires: 6, departures: 3 },
      { month: 'Mar', employees: 151, newHires: 4, departures: 1 },
      { month: 'Apr', employees: 154, newHires: 7, departures: 4 },
      { month: 'May', employees: 156, newHires: 8, departures: 6 }
    ],
    departmentDistribution: [
      { department: 'Engineering', count: 45, percentage: 28.8 },
      { department: 'Sales', count: 32, percentage: 20.5 },
      { department: 'Marketing', count: 28, percentage: 17.9 },
      { department: 'HR', count: 15, percentage: 9.6 },
      { department: 'Finance', count: 12, percentage: 7.7 },
      { department: 'Operations', count: 24, percentage: 15.4 }
    ],
    performanceTrends: [
      { month: 'Jan', averageRating: 4.1, completedReviews: 78 },
      { month: 'Feb', averageRating: 4.0, completedReviews: 82 },
      { month: 'Mar', averageRating: 4.2, completedReviews: 85 },
      { month: 'Apr', averageRating: 4.3, completedReviews: 87 },
      { month: 'May', averageRating: 4.2, completedReviews: 89 }
    ],
    attendanceTrends: [
      { date: '2024-05-20', presentCount: 132, absentCount: 8, lateCount: 4 },
      { date: '2024-05-21', presentCount: 135, absentCount: 6, lateCount: 3 },
      { date: '2024-05-22', presentCount: 138, absentCount: 5, lateCount: 2 },
      { date: '2024-05-23', presentCount: 134, absentCount: 7, lateCount: 5 },
      { date: '2024-05-24', presentCount: 136, absentCount: 6, lateCount: 4 }
    ]
  };

  console.log('Sending dashboard charts data');
  res.json(chartData);
});

// Employee routes
app.get('/api/v1/employees', (req, res) => {
  res.json({
    success: true,
    data: [
      {
        id: '1',
        employeeId: 'EMP001',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        department: 'Engineering',
        position: 'Software Engineer',
        status: 'ACTIVE'
      },
      {
        id: '2',
        employeeId: 'EMP002',
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        department: 'HR',
        position: 'HR Manager',
        status: 'ACTIVE'
      }
    ],
    total: 2,
    page: 1,
    limit: 10
  });
});

app.post('/api/v1/employees', (req, res) => {
  const employee = req.body;
  res.json({
    success: true,
    message: 'Employee created successfully',
    data: {
      id: Date.now().toString(),
      employeeId: 'EMP' + Date.now(),
      ...employee,
      status: 'ACTIVE',
      createdAt: new Date().toISOString()
    }
  });
});

// Department routes
app.get('/api/v1/departments', (req, res) => {
  res.json({
    success: true,
    data: [
      {
        id: '1',
        name: 'Engineering',
        description: 'Software development and engineering',
        employeeCount: 25
      },
      {
        id: '2',
        name: 'Human Resources',
        description: 'HR and people operations',
        employeeCount: 5
      },
      {
        id: '3',
        name: 'Finance',
        description: 'Financial operations and accounting',
        employeeCount: 8
      }
    ]
  });
});

// Payroll routes
app.get('/api/v1/payroll', (req, res) => {
  res.json({
    success: true,
    data: [
      {
        id: '1',
        employeeId: 'EMP001',
        employeeName: 'John Doe',
        period: '2024-01',
        grossSalary: 5000,
        deductions: 500,
        netSalary: 4500,
        status: 'PROCESSED'
      }
    ]
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found',
    path: req.originalUrl
  });
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 PeopleNest HRMS API Gateway started successfully`);
  console.log(`📱 Application is running on: http://localhost:${PORT}`);
  console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`⏰ Started at: ${new Date().toISOString()}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  process.exit(0);
});
