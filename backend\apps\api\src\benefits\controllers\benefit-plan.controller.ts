import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards, ParseUUIDPipe } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard, RolesGuard, TenantGuard } from '@app/security';
import { Roles, CurrentUser, CurrentTenant } from '@app/common';
import { BenefitPlanService } from '../services/benefit-plan.service';

@ApiTags('Benefit Plans')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard, TenantGuard, RolesGuard)
@Controller('benefit-plans')
export class BenefitPlanController {
  constructor(private readonly benefitPlanService: BenefitPlanService) {}

  @Post()
  @Roles('admin', 'hr_manager')
  @ApiOperation({ summary: 'Create benefit plan' })
  async create(@Body() createBenefitPlanDto: any, @CurrentUser() user: any, @CurrentTenant() tenantId: string) {
    return this.benefitPlanService.create(createBenefitPlanDto, user.id, tenantId);
  }

  @Get()
  @Roles('admin', 'hr_manager', 'hr_user', 'manager', 'employee')
  @ApiOperation({ summary: 'Get benefit plans' })
  async findAll(@Query() query: any, @CurrentTenant() tenantId: string) {
    return this.benefitPlanService.findAll(query, tenantId);
  }

  @Get(':id')
  @Roles('admin', 'hr_manager', 'hr_user', 'manager', 'employee')
  @ApiOperation({ summary: 'Get benefit plan by ID' })
  async findOne(@Param('id', ParseUUIDPipe) id: string, @CurrentTenant() tenantId: string) {
    return this.benefitPlanService.findOne(id, tenantId);
  }

  @Put(':id')
  @Roles('admin', 'hr_manager')
  @ApiOperation({ summary: 'Update benefit plan' })
  async update(@Param('id', ParseUUIDPipe) id: string, @Body() updateBenefitPlanDto: any, @CurrentUser() user: any, @CurrentTenant() tenantId: string) {
    return this.benefitPlanService.update(id, updateBenefitPlanDto, user.id, tenantId);
  }

  @Delete(':id')
  @Roles('admin', 'hr_manager')
  @ApiOperation({ summary: 'Delete benefit plan' })
  async remove(@Param('id', ParseUUIDPipe) id: string, @CurrentUser() user: any, @CurrentTenant() tenantId: string) {
    return this.benefitPlanService.remove(id, user.id, tenantId);
  }
}
