import { ErrorCodeDefinition, NamedSchemaElement, SubgraphASTNode } from '@apollo/federation-internals';
import { ASTNode, GraphQLError } from 'graphql';
import { CompositionHint, HintCodeDefinition } from '../hints';
import { Sources } from './merge';
export declare class MismatchReporter {
    readonly names: readonly string[];
    pushError: (error: GraphQLError) => void;
    pushHint: (hint: CompositionHint) => void;
    constructor(names: readonly string[], pushError: (error: GraphQLError) => void, pushHint: (hint: CompositionHint) => void);
    reportMismatchError<TMismatched extends {
        sourceAST?: ASTNode;
    }>(code: ErrorCodeDefinition, message: string, mismatchedElement: TMismatched, subgraphElements: Sources<TMismatched>, mismatchAccessor: (elt: TMismatched, isSupergraph: boolean) => string | undefined): void;
    reportMismatchErrorWithoutSupergraph<TMismatched extends {
        sourceAST?: ASTNode;
    }>(code: ErrorCodeDefinition, message: string, subgraphElements: Sources<TMismatched>, mismatchAccessor: (elt: TMismatched, isSupergraph: boolean) => string | undefined): void;
    reportMismatchErrorWithSpecifics<TMismatched extends {
        sourceAST?: ASTNode;
    }>({ code, message, mismatchedElement, subgraphElements, mismatchAccessor, supergraphElementPrinter, otherElementsPrinter, ignorePredicate, includeMissingSources, extraNodes, }: {
        code: ErrorCodeDefinition;
        message: string;
        mismatchedElement: TMismatched;
        subgraphElements: Sources<TMismatched>;
        mismatchAccessor: (elt: TMismatched | undefined, isSupergraph: boolean) => string | undefined;
        supergraphElementPrinter: (elt: string, subgraphs: string | undefined) => string;
        otherElementsPrinter: (elt: string, subgraphs: string) => string;
        ignorePredicate?: (elt: TMismatched | undefined) => boolean;
        includeMissingSources?: boolean;
        extraNodes?: SubgraphASTNode[];
    }): void;
    reportMismatchHint<TMismatched extends {
        sourceAST?: ASTNode;
    }>({ code, message, supergraphElement, subgraphElements, targetedElement, elementToString, supergraphElementPrinter, otherElementsPrinter, ignorePredicate, includeMissingSources, noEndOfMessageDot, }: {
        code: HintCodeDefinition;
        message: string;
        supergraphElement: TMismatched;
        subgraphElements: Sources<TMismatched>;
        targetedElement?: NamedSchemaElement<any, any, any>;
        elementToString: (elt: TMismatched, isSupergraph: boolean) => string | undefined;
        supergraphElementPrinter: (elt: string, subgraphs: string | undefined) => string;
        otherElementsPrinter: (elt: string, subgraphs: string) => string;
        ignorePredicate?: (elt: TMismatched | undefined) => boolean;
        includeMissingSources?: boolean;
        noEndOfMessageDot?: boolean;
    }): void;
    private reportMismatch;
}
//# sourceMappingURL=reporter.d.ts.map