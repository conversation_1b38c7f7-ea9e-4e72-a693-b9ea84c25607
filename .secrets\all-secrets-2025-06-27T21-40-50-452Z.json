{"jwt_secrets": {"JWT_SECRET": "H6F68dKvM7z9r90rJ6U1SthDNcwJn3ANhyvEtIcSGHK85s8gMgb7JHAwf_qzsyCKpD6DaQ2Xi3LtbLNhXt5r4A", "JWT_REFRESH_SECRET": "pynSOesafEPWseJYpt82pd8G2uQrWeLyxe8QWoD_m6P80HmplhqMAt-9g2l-KjkpFj4fxyM7Tg74CxI6ZRmJVg"}, "encryption_keys": {"ENCRYPTION_KEY": "507520c36f1532b7f4f49cc87c4ec572fd1d1e127447c835204e174a686736fd", "ENCRYPTION_MASTER_KEY": "65b1ea76554cdf7af11cd14807669f172b6f202e2e2929cb651830a3bf6e4a196df076c3255d9a3db39f49888cefec0eadbd7ebda5faa756ec9fc49e901d9e38", "INDEX_HASH_SALT": "5aad018b82c8a0d99f530db7b2dfbb7276de4579bc968cf4ccd8cfa641f14c47"}, "metadata": {"generated_at": "2025-06-27T21-40-50-452Z", "entropy_bits": {"JWT_SECRET": 512, "JWT_REFRESH_SECRET": 512, "ENCRYPTION_KEY": 256, "ENCRYPTION_MASTER_KEY": 512, "INDEX_HASH_SALT": 256, "total": 2048}, "algorithms": {"jwt": "HS256/HS512", "encryption": "aes-256-gcm", "hashing": "sha256"}, "key_specifications": {"JWT_SECRET": "64 bytes, base64url encoded, for access token signing", "JWT_REFRESH_SECRET": "64 bytes, base64url encoded, for refresh token signing", "ENCRYPTION_KEY": "32 bytes, hex encoded, for AES-256-GCM data encryption", "ENCRYPTION_MASTER_KEY": "64 bytes, hex encoded, for tenant key derivation", "INDEX_HASH_SALT": "32 bytes, hex encoded, for searchable encryption"}}, "security_notes": ["All secrets generated using cryptographically secure random number generation", "JWT secrets will invalidate existing tokens when changed", "Encryption keys will make existing encrypted data unreadable when changed", "Store these secrets securely and never commit to version control", "Use proper key rotation procedures in production environments"], "usage_instructions": {"development": "Secrets are automatically loaded from .env file", "production": "Update environment variables in your deployment system", "testing": "Use separate secrets for test environments", "rotation": "Generate new secrets periodically (recommended: every 90 days)"}}