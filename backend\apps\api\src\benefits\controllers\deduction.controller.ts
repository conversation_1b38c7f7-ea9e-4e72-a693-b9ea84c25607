import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards, ParseUUIDPipe } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard, RolesGuard, TenantGuard } from '@app/security';
import { Roles, CurrentUser, CurrentTenant } from '@app/common';
import { UserRole } from '@app/common/enums/user-role.enum';
import { DeductionService } from '../services/deduction.service';

@ApiTags('Deductions')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard, TenantGuard, RolesGuard)
@Controller('deductions')
export class DeductionController {
  constructor(private readonly deductionService: DeductionService) {}

  @Post()
  @Roles(UserRole.ADMIN, UserRole.HR)
  @ApiOperation({ summary: 'Create deduction' })
  async create(@Body() createDeductionDto: any, @CurrentUser() user: any, @CurrentTenant() tenantId: string) {
    return this.deductionService.create(createDeductionDto, user.id, tenantId);
  }

  @Get()
  @Roles(UserRole.ADMIN, UserRole.HR, UserRole.MANAGER, UserRole.EMPLOYEE)
  @ApiOperation({ summary: 'Get deductions' })
  async findAll(@Query() query: any, @CurrentTenant() tenantId: string) {
    return this.deductionService.findAll(query, tenantId);
  }

  @Get(':id')
  @Roles(UserRole.ADMIN, UserRole.HR, UserRole.MANAGER, UserRole.EMPLOYEE)
  @ApiOperation({ summary: 'Get deduction by ID' })
  async findOne(@Param('id', ParseUUIDPipe) id: string, @CurrentTenant() tenantId: string) {
    return this.deductionService.findOne(id, tenantId);
  }

  @Put(':id')
  @Roles(UserRole.ADMIN, UserRole.HR)
  @ApiOperation({ summary: 'Update deduction' })
  async update(@Param('id', ParseUUIDPipe) id: string, @Body() updateDeductionDto: any, @CurrentUser() user: any, @CurrentTenant() tenantId: string) {
    return this.deductionService.update(id, updateDeductionDto, user.id, tenantId);
  }

  @Delete(':id')
  @Roles(UserRole.ADMIN, UserRole.HR)
  @ApiOperation({ summary: 'Delete deduction' })
  async remove(@Param('id', ParseUUIDPipe) id: string, @CurrentUser() user: any, @CurrentTenant() tenantId: string) {
    return this.deductionService.remove(id, user.id, tenantId);
  }
}
