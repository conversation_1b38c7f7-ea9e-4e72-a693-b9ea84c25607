import type { Attributes, Exception, Span } from '@opentelemetry/api';
import type { GatewayGraphQLRequestContext } from '@apollo/server-gateway-interface';
import { OperationContext } from '../operationContext';
export type OpenTelemetryConfig = {
    includeDocument?: boolean;
    recordExceptions?: boolean | number;
};
export declare enum OpenTelemetrySpanNames {
    REQUEST = "gateway.request",
    PLAN = "gateway.plan",
    FETCH = "gateway.fetch",
    POST_PROCESSING = "gateway.postprocessing",
    EXECUTE = "gateway.execute",
    VALIDATE = "gateway.validate"
}
export declare enum OpenTelemetryAttributeNames {
    GRAPHQL_DOCUMENT = "graphql.document",
    GRAPHQL_OPERATION_NAME = "graphql.operation.name",
    GRAPHQL_OPERATION_NAME_DEPRECATED = "operationName",
    GRAPHQL_OPERATION_TYPE = "graphql.operation.type"
}
export declare const tracer: import("@opentelemetry/api").Tracer;
export interface SpanAttributes extends Attributes {
    [OpenTelemetryAttributeNames.GRAPHQL_OPERATION_NAME_DEPRECATED]?: string;
    [OpenTelemetryAttributeNames.GRAPHQL_OPERATION_NAME]?: string;
    [OpenTelemetryAttributeNames.GRAPHQL_OPERATION_TYPE]?: string;
    [OpenTelemetryAttributeNames.GRAPHQL_DOCUMENT]?: string;
}
export declare function requestContextSpanAttributes(requestContext: GatewayGraphQLRequestContext, config: OpenTelemetryConfig | undefined): SpanAttributes;
export declare function operationContextSpanAttributes(operationContext: OperationContext): SpanAttributes;
export declare function recordExceptions(span: Span, exceptions: readonly Exception[], config: OpenTelemetryConfig | undefined): void;
//# sourceMappingURL=opentelemetry.d.ts.map