import {
  Entity,
  Column,
  PrimaryGeneratedC<PERSON>umn,
  <PERSON>To<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';
import { Employee } from './employee.entity';
import { User } from './user.entity';
import { Tenant } from './tenant.entity';
import { PerformanceReview } from './performance-review.entity';

export enum FeedbackType {
  PEER = 'peer',
  UPWARD = 'upward',
  DOWNWARD = 'downward',
  SELF = 'self',
  CUSTOMER = 'customer',
  CONTINUOUS = 'continuous',
  FORMAL = 'formal',
  INFORMAL = 'informal',
}

export enum FeedbackStatus {
  DRAFT = 'draft',
  PENDING = 'pending',
  SUBMITTED = 'submitted',
  REVIEWED = 'reviewed',
  ACKNOWLEDGED = 'acknowledged',
  CANCELLED = 'cancelled',
  ARCHIVED = 'archived',
}

export enum FeedbackCategory {
  PERFORMANCE = 'performance',
  BEHAVIOR = 'behavior',
  SKILLS = 'skills',
  COMMUNICATION = 'communication',
  LEADERSHIP = 'leadership',
  TEAMWORK = 'teamwork',
  INNOVATION = 'innovation',
  CUSTOMER_SERVICE = 'customer_service',
  TECHNICAL = 'technical',
  GENERAL = 'general',
}

@Entity('feedback')
@Index(['tenantId', 'recipientId'])
@Index(['tenantId', 'giverId'])
@Index(['tenantId', 'feedbackType'])
@Index(['tenantId', 'status'])
@Index(['tenantId', 'createdAt'])
export class Feedback {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'tenant_id' })
  @Index()
  tenantId: string;

  @ManyToOne(() => Tenant)
  @JoinColumn({ name: 'tenant_id' })
  tenant: Tenant;

  @Column({ name: 'recipient_id' })
  recipientId: string;

  @ManyToOne(() => Employee, { eager: true })
  @JoinColumn({ name: 'recipient_id' })
  recipient: Employee;

  @Column({ name: 'giver_id' })
  giverId: string;

  @ManyToOne(() => Employee, { eager: true })
  @JoinColumn({ name: 'giver_id' })
  giver: Employee;

  @Column({ name: 'performance_review_id', nullable: true })
  performanceReviewId: string;

  @ManyToOne(() => PerformanceReview, review => review.feedback, { nullable: true })
  @JoinColumn({ name: 'performance_review_id' })
  performanceReview: PerformanceReview;

  @Column({
    type: 'enum',
    enum: FeedbackType,
    name: 'feedback_type',
  })
  feedbackType: FeedbackType;

  @Column({
    type: 'enum',
    enum: FeedbackStatus,
    default: FeedbackStatus.DRAFT,
  })
  status: FeedbackStatus;

  @Column({
    type: 'enum',
    enum: FeedbackCategory,
    name: 'feedback_category',
  })
  feedbackCategory: FeedbackCategory;

  @Column({ length: 255, nullable: true })
  title: string;

  @Column({ type: 'text' })
  content: string;

  // Structured Feedback
  @Column({ type: 'json', nullable: true, name: 'structured_feedback' })
  structuredFeedback: {
    strengths?: Array<{
      area: string;
      description: string;
      examples?: string[];
    }>;
    improvementAreas?: Array<{
      area: string;
      description: string;
      suggestions?: string[];
    }>;
    ratings?: {
      [competencyId: string]: {
        rating: number;
        comments?: string;
      };
    };
  };

  // 360 Feedback Specific
  @Column({ type: 'json', nullable: true, name: 'competency_ratings' })
  competencyRatings: Array<{
    competencyId: string;
    competencyName: string;
    rating: number;
    comments?: string;
    examples?: string[];
  }>;

  @Column({ type: 'json', nullable: true })
  questions: Array<{
    questionId: string;
    question: string;
    answer: string;
    rating?: number;
  }>;

  // Visibility and Privacy
  @Column({ type: 'boolean', default: false, name: 'is_anonymous' })
  isAnonymous: boolean;

  @Column({ type: 'boolean', default: true, name: 'is_confidential' })
  isConfidential: boolean;

  @Column({ type: 'json', nullable: true, name: 'visibility_settings' })
  visibilitySettings: {
    visibleToRecipient: boolean;
    visibleToManager: boolean;
    visibleToHR: boolean;
    visibleToSeniorLeadership: boolean;
  };

  // Timing and Context
  @Column({ name: 'feedback_period_start', type: 'date', nullable: true })
  feedbackPeriodStart: Date;

  @Column({ name: 'feedback_period_end', type: 'date', nullable: true })
  feedbackPeriodEnd: Date;

  @Column({ name: 'submitted_at', type: 'timestamp', nullable: true })
  submittedAt: Date;

  @Column({ name: 'acknowledged_at', type: 'timestamp', nullable: true })
  acknowledgedAt: Date;

  @Column({ name: 'due_date', type: 'date', nullable: true })
  dueDate: Date;

  // AI Analysis
  @Column({ type: 'json', nullable: true, name: 'ai_analysis' })
  aiAnalysis: {
    sentimentScore?: number;
    sentiment?: 'positive' | 'neutral' | 'negative';
    emotionalTone?: string[];
    keyThemes?: string[];
    actionableItems?: string[];
    confidenceScore?: number;
    languageQuality?: {
      clarity: number;
      constructiveness: number;
      specificity: number;
    };
  };

  // Response and Follow-up
  @Column({ type: 'text', nullable: true, name: 'recipient_response' })
  recipientResponse: string;

  @Column({ name: 'response_date', type: 'timestamp', nullable: true })
  responseDate: Date;

  @Column({ type: 'json', nullable: true, name: 'action_items' })
  actionItems: Array<{
    id: string;
    description: string;
    dueDate?: string;
    completed: boolean;
    completedDate?: string;
  }>;

  @Column({ type: 'json', nullable: true, name: 'follow_up_notes' })
  followUpNotes: Array<{
    id: string;
    date: string;
    note: string;
    author: string;
  }>;

  // Metadata
  @Column({ type: 'json', nullable: true })
  metadata: {
    source?: string; // 'manual', 'survey', 'system_generated'
    tags?: string[];
    priority?: 'low' | 'medium' | 'high';
    customFields?: Record<string, any>;
    requestId?: string; // For tracking feedback requests
  };

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'created_by' })
  createdBy: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @Column({ name: 'updated_by', nullable: true })
  updatedBy: string;

  @ManyToOne(() => User, { nullable: true })
  @JoinColumn({ name: 'updated_by' })
  updater: User;

  // Computed properties
  get isOverdue(): boolean {
    return this.dueDate && this.dueDate < new Date() && this.status !== FeedbackStatus.SUBMITTED;
  }

  get averageRating(): number {
    if (!this.competencyRatings || this.competencyRatings.length === 0) {
      return 0;
    }
    const totalRating = this.competencyRatings.reduce((sum, rating) => sum + rating.rating, 0);
    return totalRating / this.competencyRatings.length;
  }

  get hasActionItems(): boolean {
    return this.actionItems && this.actionItems.length > 0;
  }

  get completedActionItems(): number {
    if (!this.actionItems) return 0;
    return this.actionItems.filter(item => item.completed).length;
  }
}
