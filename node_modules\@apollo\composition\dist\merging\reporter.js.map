{"version": 3, "file": "reporter.js", "sourceRoot": "", "sources": ["../../src/merging/reporter.ts"], "names": [], "mappings": ";;;AAAA,uEAAiL;AAEjL,oCAA+D;AAG/D,MAAa,gBAAgB;IAI3B,YAAqB,KAAwB,EAAE,SAAwC,EAAE,QAAyC;QAA7G,UAAK,GAAL,KAAK,CAAmB;QAC3C,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC3B,CAAC;IAED,mBAAmB,CACjB,IAAyB,EACzB,OAAe,EACf,iBAA6B,EAC7B,gBAAsC,EACtC,gBAAiF;QAEjF,IAAI,CAAC,cAAc,CACjB,iBAAiB,EACjB,gBAAgB,EAChB,gBAAgB,EAChB,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,EACpC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,EACpC,CAAC,YAAY,EAAE,KAAK,EAAE,EAAE;YACtB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CACrB,OAAO,GAAG,IAAA,kCAAW,EAAC,YAAY,EAAE,OAAO,EAAE,OAAO,CAAC,EACrD,EAAE,KAAK,EAAE,CACV,CAAC,CAAC;QACL,CAAC,EACD,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CACZ,CAAC;IACJ,CAAC;IAED,oCAAoC,CAClC,IAAyB,EACzB,OAAe,EACf,gBAAsC,EACtC,gBAAiF;QAEjF,IAAI,CAAC,cAAc,CACjB,SAAS,EACT,gBAAgB,EAChB,gBAAgB,EAChB,GAAG,EAAE,CAAC,EAAE,EACR,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,EACpC,CAAC,YAAY,EAAE,KAAK,EAAE,EAAE;YACtB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CACrB,OAAO,GAAG,IAAA,kCAAW,EAAC,YAAY,EAAE,OAAO,EAAE,OAAO,CAAC,EACrD,EAAE,KAAK,EAAE,CACV,CAAC,CAAC;QACL,CAAC,EACD,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CACZ,CAAC;IACJ,CAAC;IAED,gCAAgC,CAA8C,EAC5E,IAAI,EACJ,OAAO,EACP,iBAAiB,EACjB,gBAAgB,EAChB,gBAAgB,EAChB,wBAAwB,EACxB,oBAAoB,EACpB,eAAe,EACf,qBAAqB,GAAG,KAAK,EAC7B,UAAU,GAYX;QACC,IAAI,CAAC,cAAc,CACjB,iBAAiB,EACjB,gBAAgB,EAChB,gBAAgB,EAChB,wBAAwB,EACxB,oBAAoB,EACpB,CAAC,YAAY,EAAE,KAAK,EAAE,EAAE;YACtB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CACrB,OAAO,GAAG,YAAY,CAAC,CAAC,CAAC,GAAG,IAAA,kCAAW,EAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,EACvE,EAAE,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,UAAU,aAAV,UAAU,cAAV,UAAU,GAAI,EAAE,CAAC,EAAE,CAC1C,CAAC,CAAC;QACL,CAAC,EACD,eAAe,EACf,qBAAqB,CACtB,CAAC;IACJ,CAAC;IAED,kBAAkB,CAA8C,EAC9D,IAAI,EACJ,OAAO,EACP,iBAAiB,EACjB,gBAAgB,EAChB,eAAe,EACf,eAAe,EACf,wBAAwB,EACxB,oBAAoB,EACpB,eAAe,EACf,qBAAqB,GAAG,KAAK,EAC7B,iBAAiB,GAAG,KAAK,GAa1B;QACC,IAAI,CAAC,cAAc,CACjB,iBAAiB,EACjB,gBAAgB,EAChB,eAAe,EACf,wBAAwB,EACxB,oBAAoB,EACpB,CAAC,YAAY,EAAE,QAAQ,EAAE,EAAE;YACzB,IAAI,CAAC,QAAQ,CAAC,IAAI,uBAAe,CAC/B,IAAI,EACJ,OAAO,GAAG,YAAY,CAAC,CAAC,CAAC,GAAG,IAAA,kCAAW,EAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EACxG,eAAe,aAAf,eAAe,cAAf,eAAe,GAAI,CAAC,CAAC,iBAAiB,YAAY,yCAAkB,CAAC,CAAC,CAAC,CAAC,iBAAsD,CAAC,CAAC,CAAC,SAAS,CAAC,EAC3I,QAAQ,CACT,CAAC,CAAC;QACL,CAAC,EACD,eAAe,EACf,qBAAqB,CACtB,CAAC;IACJ,CAAC;IAGO,cAAc,CACpB,iBAAyC,EACzC,gBAAsC,EACtC,gBAAqF,EACrF,wBAAgF,EAChF,oBAAgE,EAChE,QAAsE,EACtE,eAA2D,EAC3D,wBAAiC,KAAK;;QAEtC,MAAM,eAAe,GAAG,IAAI,+BAAQ,EAAkB,CAAC;QACvD,MAAM,QAAQ,GAAsB,EAAE,CAAC;QACvC,MAAM,kBAAkB,GAAG,CAAC,IAAY,EAAE,WAAwB,EAAE,EAAE;YACpE,IAAI,eAAe,IAAI,eAAe,CAAC,WAAW,CAAC,EAAE,CAAC;gBACpD,OAAO;YACT,CAAC;YACD,MAAM,GAAG,GAAG,gBAAgB,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;YACjD,eAAe,CAAC,GAAG,CAAC,GAAG,aAAH,GAAG,cAAH,GAAG,GAAI,EAAE,EAAE,IAAI,CAAC,CAAC;YACrC,IAAI,WAAW,CAAC,SAAS,EAAE,CAAC;gBAC1B,QAAQ,CAAC,IAAI,CAAC,IAAA,2CAAoB,EAAC,WAAW,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC;YACnE,CAAC;QACH,CAAC,CAAA;QACD,IAAI,qBAAqB,EAAE,CAAC;YAC1B,KAAK,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC;gBAC7C,MAAM,WAAW,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC5C,IAAI,CAAC,WAAW,EAAE,CAAC;oBACjB,eAAe,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;oBAC9B,SAAS;gBACX,CAAC;gBACD,kBAAkB,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;YACxC,CAAC;QACH,CAAC;aAAM,CAAC;YACN,KAAK,MAAM,CAAC,CAAC,EAAE,WAAW,CAAC,IAAI,gBAAgB,CAAC,OAAO,EAAE,EAAE,CAAC;gBAC1D,IAAI,CAAC,WAAW,EAAE,CAAC;oBACjB,SAAS;gBACX,CAAC;gBACD,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;YACjD,CAAC;QACH,CAAC;QACD,MAAM,kBAAkB,GAAG,MAAA,CAAC,iBAAiB,IAAI,gBAAgB,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC,mCAAI,EAAE,CAAC;QAClG,IAAA,6BAAM,EAAC,eAAe,CAAC,IAAI,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,mCAAmC,iBAAiB,EAAE,CAAC,CAAC;QAC/F,MAAM,YAAY,GAAG,EAAE,CAAC;QAExB,MAAM,uBAAuB,GAAG,eAAe,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;QACxE,YAAY,CAAC,IAAI,CAAC,wBAAwB,CAAC,kBAAkB,EAAE,uBAAuB,CAAC,CAAC,CAAC,IAAA,yCAAkB,EAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;QACnJ,KAAK,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,eAAe,CAAC,OAAO,EAAE,EAAE,CAAC;YACnD,IAAI,CAAC,KAAK,kBAAkB,EAAE,CAAC;gBAC7B,SAAS;YACX,CAAC;YACD,YAAY,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,IAAA,yCAAkB,EAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACxE,CAAC;QACD,QAAQ,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;IACnC,CAAC;CACF;AAhMD,4CAgMC"}