import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

// Types
export interface DashboardMetrics {
  totalEmployees: number;
  activeEmployees: number;
  newHiresThisMonth: number;
  terminationsThisMonth: number;
  averageSalary: number;
  departmentDistribution: { department: string; count: number }[];
  recentActivities: Activity[];
  upcomingBirthdays: Birthday[];
  pendingRequests: PendingRequest[];
  performanceOverview: PerformanceOverview;
}

export interface Activity {
  id: string;
  type: 'hire' | 'termination' | 'promotion' | 'transfer' | 'leave';
  employee: {
    id: string;
    name: string;
    avatar?: string;
  };
  description: string;
  timestamp: string;
}

export interface Birthday {
  id: string;
  employeeId: string;
  name: string;
  avatar?: string;
  department: string;
  date: string;
  daysUntil: number;
}

export interface PendingRequest {
  id: string;
  type: 'leave' | 'expense' | 'timeoff' | 'equipment';
  employee: {
    id: string;
    name: string;
    avatar?: string;
  };
  title: string;
  submittedAt: string;
  priority: 'low' | 'medium' | 'high';
}

export interface PerformanceOverview {
  averageRating: number;
  totalReviews: number;
  pendingReviews: number;
  topPerformers: {
    id: string;
    name: string;
    department: string;
    rating: number;
  }[];
}

export interface DashboardState {
  metrics: DashboardMetrics | null;
  isLoading: boolean;
  error: string | null;
  lastUpdated: string | null;
  selectedDateRange: {
    start: string;
    end: string;
  };
}

const initialState: DashboardState = {
  metrics: null,
  isLoading: false,
  error: null,
  lastUpdated: null,
  selectedDateRange: {
    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days ago
    end: new Date().toISOString().split('T')[0], // today
  },
};

// Async thunks
export const fetchDashboardMetrics = createAsyncThunk(
  'dashboard/fetchMetrics',
  async (dateRange?: { start: string; end: string }, { rejectWithValue }) => {
    try {
      const queryParams = new URLSearchParams();
      
      if (dateRange) {
        queryParams.append('start', dateRange.start);
        queryParams.append('end', dateRange.end);
      }

      const response = await fetch(`/api/dashboard/metrics?${queryParams.toString()}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (!response.ok) {
        const error = await response.json();
        return rejectWithValue(error.message || 'Failed to fetch dashboard metrics');
      }

      return await response.json();
    } catch (error) {
      return rejectWithValue('Network error occurred');
    }
  }
);

export const fetchRecentActivities = createAsyncThunk(
  'dashboard/fetchRecentActivities',
  async (limit: number = 10, { rejectWithValue }) => {
    try {
      const response = await fetch(`/api/dashboard/activities?limit=${limit}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (!response.ok) {
        const error = await response.json();
        return rejectWithValue(error.message || 'Failed to fetch recent activities');
      }

      return await response.json();
    } catch (error) {
      return rejectWithValue('Network error occurred');
    }
  }
);

export const fetchUpcomingBirthdays = createAsyncThunk(
  'dashboard/fetchUpcomingBirthdays',
  async (days: number = 30, { rejectWithValue }) => {
    try {
      const response = await fetch(`/api/dashboard/birthdays?days=${days}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (!response.ok) {
        const error = await response.json();
        return rejectWithValue(error.message || 'Failed to fetch upcoming birthdays');
      }

      return await response.json();
    } catch (error) {
      return rejectWithValue('Network error occurred');
    }
  }
);

export const fetchPendingRequests = createAsyncThunk(
  'dashboard/fetchPendingRequests',
  async (_, { rejectWithValue }) => {
    try {
      const response = await fetch('/api/dashboard/pending-requests', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (!response.ok) {
        const error = await response.json();
        return rejectWithValue(error.message || 'Failed to fetch pending requests');
      }

      return await response.json();
    } catch (error) {
      return rejectWithValue('Network error occurred');
    }
  }
);

// Dashboard slice
const dashboardSlice = createSlice({
  name: 'dashboard',
  initialState,
  reducers: {
    setDateRange: (state, action: PayloadAction<{ start: string; end: string }>) => {
      state.selectedDateRange = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    updateActivity: (state, action: PayloadAction<Activity>) => {
      if (state.metrics?.recentActivities) {
        state.metrics.recentActivities.unshift(action.payload);
        // Keep only the latest 10 activities
        state.metrics.recentActivities = state.metrics.recentActivities.slice(0, 10);
      }
    },
    markRequestAsProcessed: (state, action: PayloadAction<string>) => {
      if (state.metrics?.pendingRequests) {
        state.metrics.pendingRequests = state.metrics.pendingRequests.filter(
          request => request.id !== action.payload
        );
      }
    },
  },
  extraReducers: (builder) => {
    // Fetch dashboard metrics
    builder
      .addCase(fetchDashboardMetrics.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchDashboardMetrics.fulfilled, (state, action) => {
        state.isLoading = false;
        state.metrics = action.payload;
        state.lastUpdated = new Date().toISOString();
      })
      .addCase(fetchDashboardMetrics.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch recent activities
    builder
      .addCase(fetchRecentActivities.fulfilled, (state, action) => {
        if (state.metrics) {
          state.metrics.recentActivities = action.payload;
        }
      });

    // Fetch upcoming birthdays
    builder
      .addCase(fetchUpcomingBirthdays.fulfilled, (state, action) => {
        if (state.metrics) {
          state.metrics.upcomingBirthdays = action.payload;
        }
      });

    // Fetch pending requests
    builder
      .addCase(fetchPendingRequests.fulfilled, (state, action) => {
        if (state.metrics) {
          state.metrics.pendingRequests = action.payload;
        }
      });
  },
});

export const {
  setDateRange,
  clearError,
  updateActivity,
  markRequestAsProcessed,
} = dashboardSlice.actions;

export default dashboardSlice.reducer;
