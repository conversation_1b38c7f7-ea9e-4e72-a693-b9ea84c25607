import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  ManyTo<PERSON>ne,
  Join<PERSON><PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  Unique,
} from 'typeorm';
import { Employee } from './employee.entity';
import { User } from './user.entity';
import { Tenant } from './tenant.entity';

export enum NotificationChannel {
  EMAIL = 'email',
  SMS = 'sms',
  PUSH = 'push',
  IN_APP = 'in_app',
  SLACK = 'slack',
  TEAMS = 'teams',
}

export enum NotificationFrequency {
  IMMEDIATE = 'immediate',
  HOURLY = 'hourly',
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  NEVER = 'never',
}

@Entity('notification_preferences')
@Index(['tenantId', 'employeeId'])
@Index(['tenantId', 'isActive'])
@Unique(['tenantId', 'employeeId', 'notificationCategory'])
export class NotificationPreference {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'tenant_id' })
  @Index()
  tenantId: string;

  @ManyToOne(() => Tenant)
  @JoinColumn({ name: 'tenant_id' })
  tenant: Tenant;

  @Column({ name: 'employee_id' })
  employeeId: string;

  @ManyToOne(() => Employee, { eager: true })
  @JoinColumn({ name: 'employee_id' })
  employee: Employee;

  @Column({
    type: 'varchar',
    length: 50,
    name: 'notification_category',
    comment: 'Category of notifications (payroll, leave, performance, etc.)',
  })
  notificationCategory: string;

  @Column({
    type: 'json',
    comment: 'Enabled notification channels',
  })
  enabledChannels: NotificationChannel[];

  @Column({
    type: 'enum',
    enum: NotificationFrequency,
    default: NotificationFrequency.IMMEDIATE,
    comment: 'How frequently to receive notifications',
  })
  frequency: NotificationFrequency;

  @Column({
    type: 'boolean',
    default: true,
    name: 'is_active',
    comment: 'Whether this preference is active',
  })
  isActive: boolean;

  @Column({
    type: 'json',
    nullable: true,
    name: 'quiet_hours',
    comment: 'Time periods when notifications should not be sent',
  })
  quietHours?: {
    enabled: boolean;
    startTime: string; // HH:MM format
    endTime: string; // HH:MM format
    timezone: string;
    weekdays?: number[]; // 0-6, Sunday = 0
  };

  @Column({
    type: 'json',
    nullable: true,
    name: 'channel_settings',
    comment: 'Channel-specific settings',
  })
  channelSettings?: {
    email?: {
      address?: string;
      format?: 'html' | 'text';
    };
    sms?: {
      phoneNumber?: string;
    };
    slack?: {
      userId?: string;
      channelId?: string;
    };
    teams?: {
      userId?: string;
      channelId?: string;
    };
  };

  @Column({
    type: 'json',
    nullable: true,
    name: 'filter_rules',
    comment: 'Rules for filtering notifications',
  })
  filterRules?: Array<{
    field: string;
    operator: 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than';
    value: any;
    action: 'include' | 'exclude';
  }>;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Additional metadata',
  })
  metadata?: Record<string, any>;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'created_by' })
  createdBy: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @Column({ name: 'updated_by', nullable: true })
  updatedBy?: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'updated_by' })
  updater?: User;

  // Computed properties
  get hasQuietHours(): boolean {
    return this.quietHours?.enabled || false;
  }

  get isInQuietHours(): boolean {
    if (!this.hasQuietHours) return false;
    
    const now = new Date();
    const currentTime = now.toTimeString().slice(0, 5); // HH:MM format
    
    return currentTime >= this.quietHours!.startTime && 
           currentTime <= this.quietHours!.endTime;
  }

  get enabledChannelCount(): number {
    return this.enabledChannels?.length || 0;
  }
}
