#!/usr/bin/env node

/**
 * Complete Secrets Generator Script
 * Generates all cryptographic secrets for PeopleNest HRMS
 * 
 * Features:
 * - JWT secrets for authentication
 * - Encryption keys for data protection
 * - Index hash salts for searchable encryption
 * - Automatic environment file updates
 * - Secure backup creation
 */

const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

// Configuration
const ENV_FILES = ['.env', '.env.local', '.env.development', '.env.production', '.env.staging', '.env.test'];

/**
 * Generate cryptographically secure key
 * @param {number} bytes - Number of bytes for the key
 * @param {string} encoding - Encoding format (hex, base64url)
 * @returns {string} - Generated key
 */
function generateSecureKey(bytes, encoding = 'hex') {
  const buffer = crypto.randomBytes(bytes);
  
  switch (encoding) {
    case 'base64url':
      return buffer.toString('base64').replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
    case 'hex':
    default:
      return buffer.toString('hex');
  }
}

/**
 * Generate all secrets
 * @returns {Object} - Object containing all generated secrets
 */
function generateAllSecrets() {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  
  const secrets = {
    // JWT Secrets (base64url encoded for URL safety)
    JWT_SECRET: generateSecureKey(64, 'base64url'),
    JWT_REFRESH_SECRET: generateSecureKey(64, 'base64url'),
    
    // Encryption Keys (hex encoded for AES compatibility)
    ENCRYPTION_KEY: generateSecureKey(32, 'hex'),
    ENCRYPTION_MASTER_KEY: generateSecureKey(64, 'hex'),
    INDEX_HASH_SALT: generateSecureKey(32, 'hex'),
    
    // Metadata
    generated_at: timestamp,
    entropy_bits: {
      JWT_SECRET: 512,
      JWT_REFRESH_SECRET: 512,
      ENCRYPTION_KEY: 256,
      ENCRYPTION_MASTER_KEY: 512,
      INDEX_HASH_SALT: 256,
      total: 2048
    },
    algorithms: {
      jwt: 'HS256/HS512',
      encryption: 'aes-256-gcm',
      hashing: 'sha256'
    }
  };
  
  console.log('🔐 Generated complete cryptographic suite:');
  console.log('');
  console.log('📝 JWT Secrets:');
  console.log(`   JWT_SECRET: ${secrets.JWT_SECRET.substring(0, 20)}... (64 bytes, base64url)`);
  console.log(`   JWT_REFRESH_SECRET: ${secrets.JWT_REFRESH_SECRET.substring(0, 20)}... (64 bytes, base64url)`);
  console.log('');
  console.log('🔒 Encryption Keys:');
  console.log(`   ENCRYPTION_KEY: ${secrets.ENCRYPTION_KEY.substring(0, 16)}... (32 bytes, hex)`);
  console.log(`   ENCRYPTION_MASTER_KEY: ${secrets.ENCRYPTION_MASTER_KEY.substring(0, 16)}... (64 bytes, hex)`);
  console.log(`   INDEX_HASH_SALT: ${secrets.INDEX_HASH_SALT.substring(0, 16)}... (32 bytes, hex)`);
  console.log('');
  console.log(`📊 Total entropy: ${secrets.entropy_bits.total} bits`);
  console.log(`⏰ Generated at: ${secrets.generated_at}`);
  
  return secrets;
}

/**
 * Update environment file with all secrets
 * @param {string} filePath - Path to environment file
 * @param {Object} secrets - Generated secrets
 * @returns {boolean} - Success status
 */
function updateEnvFile(filePath, secrets) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  File ${filePath} does not exist, skipping...`);
      return false;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    let updated = false;

    // Create backup
    const timestamp = Date.now();
    const backupPath = `${filePath}.backup.${timestamp}`;
    fs.copyFileSync(filePath, backupPath);
    
    // Update JWT secrets
    const jwtUpdates = [
      ['JWT_SECRET', secrets.JWT_SECRET],
      ['JWT_REFRESH_SECRET', secrets.JWT_REFRESH_SECRET]
    ];
    
    for (const [key, value] of jwtUpdates) {
      if (content.match(new RegExp(`^${key}=.*$`, 'm'))) {
        content = content.replace(new RegExp(`^${key}=.*$`, 'm'), `${key}=${value}`);
        updated = true;
      } else {
        // Add to JWT section if it exists
        if (content.match(/^# JWT Configuration$/m)) {
          content = content.replace(
            /^# JWT Configuration$/m,
            `# JWT Configuration\n${key}=${value}`
          );
        } else {
          content += `\n# JWT Configuration\n${key}=${value}`;
        }
        updated = true;
      }
    }

    // Update encryption keys
    const encryptionUpdates = [
      ['ENCRYPTION_KEY', secrets.ENCRYPTION_KEY],
      ['ENCRYPTION_MASTER_KEY', secrets.ENCRYPTION_MASTER_KEY],
      ['INDEX_HASH_SALT', secrets.INDEX_HASH_SALT]
    ];
    
    for (const [key, value] of encryptionUpdates) {
      if (content.match(new RegExp(`^${key}=.*$`, 'm'))) {
        content = content.replace(new RegExp(`^${key}=.*$`, 'm'), `${key}=${value}`);
        updated = true;
      } else {
        // Add to encryption section if it exists
        if (content.match(/^# Encryption Configuration$/m)) {
          const encryptionSection = content.match(/^# Encryption Configuration$/m);
          if (encryptionSection) {
            content = content.replace(
              /^# Encryption Configuration$/m,
              `# Encryption Configuration\n${key}=${value}`
            );
          }
        } else {
          content += `\n# Encryption Configuration\n${key}=${value}`;
        }
        updated = true;
      }
    }

    if (updated) {
      fs.writeFileSync(filePath, content);
      console.log(`✅ Updated ${filePath}`);
      console.log(`📁 Backup created: ${backupPath}`);
      return true;
    }

    return false;
  } catch (error) {
    console.error(`❌ Error updating ${filePath}:`, error.message);
    return false;
  }
}

/**
 * Save complete secrets reference
 * @param {Object} secrets - Generated secrets
 */
function saveSecretsReference(secrets) {
  const secretsDir = path.join(process.cwd(), '.secrets');
  const secretsFile = path.join(secretsDir, `all-secrets-${secrets.generated_at}.json`);
  
  try {
    // Create .secrets directory if it doesn't exist
    if (!fs.existsSync(secretsDir)) {
      fs.mkdirSync(secretsDir, { recursive: true });
    }

    // Save secrets with comprehensive metadata
    const secretsData = {
      jwt_secrets: {
        JWT_SECRET: secrets.JWT_SECRET,
        JWT_REFRESH_SECRET: secrets.JWT_REFRESH_SECRET
      },
      encryption_keys: {
        ENCRYPTION_KEY: secrets.ENCRYPTION_KEY,
        ENCRYPTION_MASTER_KEY: secrets.ENCRYPTION_MASTER_KEY,
        INDEX_HASH_SALT: secrets.INDEX_HASH_SALT
      },
      metadata: {
        generated_at: secrets.generated_at,
        entropy_bits: secrets.entropy_bits,
        algorithms: secrets.algorithms,
        key_specifications: {
          JWT_SECRET: "64 bytes, base64url encoded, for access token signing",
          JWT_REFRESH_SECRET: "64 bytes, base64url encoded, for refresh token signing",
          ENCRYPTION_KEY: "32 bytes, hex encoded, for AES-256-GCM data encryption",
          ENCRYPTION_MASTER_KEY: "64 bytes, hex encoded, for tenant key derivation",
          INDEX_HASH_SALT: "32 bytes, hex encoded, for searchable encryption"
        }
      },
      security_notes: [
        "All secrets generated using cryptographically secure random number generation",
        "JWT secrets will invalidate existing tokens when changed",
        "Encryption keys will make existing encrypted data unreadable when changed",
        "Store these secrets securely and never commit to version control",
        "Use proper key rotation procedures in production environments"
      ],
      usage_instructions: {
        development: "Secrets are automatically loaded from .env file",
        production: "Update environment variables in your deployment system",
        testing: "Use separate secrets for test environments",
        rotation: "Generate new secrets periodically (recommended: every 90 days)"
      }
    };

    fs.writeFileSync(secretsFile, JSON.stringify(secretsData, null, 2));
    console.log(`💾 Complete secrets reference saved: ${secretsFile}`);
    
    // Add .secrets to .gitignore if not already present
    const gitignorePath = path.join(process.cwd(), '.gitignore');
    if (fs.existsSync(gitignorePath)) {
      const gitignoreContent = fs.readFileSync(gitignorePath, 'utf8');
      if (!gitignoreContent.includes('.secrets/')) {
        fs.appendFileSync(gitignorePath, '\n# Cryptographic Secrets\n.secrets/\n');
        console.log('📝 Added .secrets/ to .gitignore');
      }
    }
  } catch (error) {
    console.error('❌ Error saving secrets reference:', error.message);
  }
}

/**
 * Main execution function
 */
function main() {
  console.log('🚀 PeopleNest HRMS - Complete Secrets Generator');
  console.log('================================================');
  console.log('');

  // Generate all secrets
  const secrets = generateAllSecrets();
  console.log('');

  // Update environment files
  let updatedFiles = 0;
  for (const envFile of ENV_FILES) {
    const filePath = path.join(process.cwd(), envFile);
    if (updateEnvFile(filePath, secrets)) {
      updatedFiles++;
    }
  }

  console.log('');
  
  if (updatedFiles > 0) {
    console.log(`✅ Successfully updated ${updatedFiles} environment file(s)`);
    
    // Save secrets reference
    saveSecretsReference(secrets);
    
    console.log('');
    console.log('🔒 Security Summary:');
    console.log('   • JWT secrets: 1024 bits entropy (invalidates existing tokens)');
    console.log('   • Encryption keys: 1024 bits entropy (makes existing encrypted data unreadable)');
    console.log('   • Total entropy: 2048 bits');
    console.log('   • Backup files created for rollback');
    console.log('   • Secrets reference saved in .secrets/ directory (git-ignored)');
    console.log('');
    console.log('⚠️  Critical Actions Required:');
    console.log('   • Restart your application to use the new secrets');
    console.log('   • Update production environment variables separately');
    console.log('   • Migrate existing encrypted data if necessary');
    console.log('   • Never commit the new secrets to version control');
  } else {
    console.log('❌ No environment files were updated');
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  generateAllSecrets,
  generateSecureKey,
  updateEnvFile,
  saveSecretsReference
};
