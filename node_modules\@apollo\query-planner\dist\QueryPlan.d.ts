import { SelectionNode as GraphQLJSSelectionNode, OperationTypeNode, DocumentNode } from 'graphql';
export type ResponsePath = (string | number)[];
export interface QueryPlan {
    kind: 'QueryPlan';
    node?: PlanNode | SubscriptionNode;
}
export type PlanNode = SequenceNode | ParallelNode | FetchNode | FlattenNode | DeferNode | ConditionNode;
export interface SequenceNode {
    kind: 'Sequence';
    nodes: PlanNode[];
}
export interface ParallelNode {
    kind: 'Parallel';
    nodes: PlanNode[];
}
export interface SubscriptionNode {
    kind: 'Subscription';
    primary: FetchNode;
    rest?: PlanNode;
}
export interface FetchNode {
    kind: 'Fetch';
    serviceName: string;
    id?: string;
    hasDefers?: boolean;
    variableUsages?: string[];
    requires?: QueryPlanSelectionNode[];
    operation: string;
    operationName: string | undefined;
    operationKind: OperationTypeNode;
    operationDocumentNode?: DocumentNode;
    inputRewrites?: FetchDataRewrite[];
    outputRewrites?: FetchDataRewrite[];
    contextRewrites?: FetchDataKeyRenamer[];
}
export type FetchDataRewrite = FetchDataValueSetter | FetchDataKeyRenamer;
export interface FetchDataValueSetter {
    kind: 'ValueSetter';
    path: string[];
    setValueTo: any;
}
export interface FetchDataKeyRenamer {
    kind: 'KeyRenamer';
    path: string[];
    renameKeyTo: string;
}
export interface FlattenNode {
    kind: 'Flatten';
    path: ResponsePath;
    node: PlanNode;
}
export interface DeferNode {
    kind: 'Defer';
    primary: {
        subselection?: string;
        node?: PlanNode;
    };
    deferred: DeferredNode[];
}
export interface DeferredNode {
    depends: {
        id: string;
        deferLabel?: string;
    }[];
    label?: string;
    queryPath: string[];
    subselection?: string;
    node?: PlanNode;
}
export interface ConditionNode {
    kind: 'Condition';
    condition: string;
    ifClause?: PlanNode;
    elseClause?: PlanNode;
}
export type QueryPlanSelectionNode = QueryPlanFieldNode | QueryPlanInlineFragmentNode;
export interface QueryPlanFieldNode {
    readonly kind: 'Field';
    readonly alias?: string;
    readonly name: string;
    readonly selections?: QueryPlanSelectionNode[];
}
export interface QueryPlanInlineFragmentNode {
    readonly kind: 'InlineFragment';
    readonly typeCondition?: string;
    readonly selections: QueryPlanSelectionNode[];
}
export declare function serializeQueryPlan(queryPlan: QueryPlan): string;
export declare function getResponseName(node: QueryPlanFieldNode): string;
export declare const trimSelectionNodes: (selections: readonly GraphQLJSSelectionNode[]) => QueryPlanSelectionNode[];
export declare const isPlanNode: (node: PlanNode | SubscriptionNode | undefined) => node is PlanNode;
//# sourceMappingURL=QueryPlan.d.ts.map