"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.loadSupergraphSdlFromStorage = exports.loadSupergraphSdlFromUplinks = exports.UplinkFetcherError = exports.SUPERGRAPH_SDL_QUERY = void 0;
const async_retry_1 = __importDefault(require("async-retry"));
const node_abort_controller_1 = require("node-abort-controller");
exports.SUPERGRAPH_SDL_QUERY = `#graphql
  query SupergraphSdl($apiKey: String!, $ref: String!, $ifAfterId: ID) {
    routerConfig(ref: $ref, apiKey: $apiKey, ifAfterId: $ifAfterId) {
      __typename
      ... on RouterConfigResult {
        id
        supergraphSdl: supergraphSDL
        minDelaySeconds
      }
      ... on FetchError {
        code
        message
      }
    }
  }
`;
const { name, version } = require('../../../package.json');
const fetchErrorMsg = "An error occurred while fetching your schema from Apollo: ";
class UplinkFetcherError extends Error {
    constructor(message) {
        super(message);
        this.name = 'UplinkFetcherError';
    }
}
exports.UplinkFetcherError = UplinkFetcherError;
async function loadSupergraphSdlFromUplinks({ graphRef, apiKey, endpoints, fetcher, compositionId, maxRetries, requestTimeoutMs, roundRobinSeed, logger, }) {
    return (0, async_retry_1.default)(() => loadSupergraphSdlFromStorage({
        graphRef,
        apiKey,
        endpoint: endpoints[roundRobinSeed++ % endpoints.length],
        fetcher,
        requestTimeoutMs,
        compositionId,
        logger,
    }), {
        retries: maxRetries,
        maxTimeout: 60000,
        onRetry(e, attempt) {
            logger.debug(`Unable to fetch supergraph SDL (attempt ${attempt}), waiting before retry: ${e}`);
        },
    });
}
exports.loadSupergraphSdlFromUplinks = loadSupergraphSdlFromUplinks;
async function loadSupergraphSdlFromStorage({ graphRef, apiKey, endpoint, fetcher, requestTimeoutMs, compositionId, logger, }) {
    var _a, _b;
    const requestBody = JSON.stringify({
        query: exports.SUPERGRAPH_SDL_QUERY,
        variables: {
            ref: graphRef,
            apiKey,
            ifAfterId: compositionId,
        },
    });
    const controller = new node_abort_controller_1.AbortController();
    const signal = setTimeout(() => {
        logger.debug(`Aborting request due to timeout`);
        controller.abort();
    }, requestTimeoutMs);
    const requestDetails = {
        method: 'POST',
        body: requestBody,
        headers: {
            'apollographql-client-name': name,
            'apollographql-client-version': version,
            'user-agent': `${name}/${version}`,
            'content-type': 'application/json',
        },
        signal: controller.signal,
    };
    logger.debug(`🔧 Fetching ${graphRef} supergraph schema from ${endpoint} ifAfterId ${compositionId}`);
    let result;
    try {
        result = await fetcher(endpoint, requestDetails);
    }
    catch (e) {
        throw new UplinkFetcherError(fetchErrorMsg + ((_a = e.message) !== null && _a !== void 0 ? _a : e));
    }
    finally {
        clearTimeout(signal);
    }
    let response;
    if (result.ok || result.status === 400) {
        try {
            response = await result.json();
        }
        catch (e) {
            throw new UplinkFetcherError((_b = fetchErrorMsg + result.status + ' ' + e.message) !== null && _b !== void 0 ? _b : e);
        }
        if ('errors' in response) {
            throw new UplinkFetcherError([fetchErrorMsg, ...response.errors.map((error) => error.message)].join('\n'));
        }
    }
    else {
        throw new UplinkFetcherError(fetchErrorMsg + result.status + ' ' + result.statusText);
    }
    const { routerConfig } = response.data;
    if (routerConfig.__typename === 'RouterConfigResult') {
        const { id, supergraphSdl, minDelaySeconds, } = routerConfig;
        return { id, supergraphSdl, minDelaySeconds };
    }
    else if (routerConfig.__typename === 'FetchError') {
        const { code, message } = routerConfig;
        throw new UplinkFetcherError(`${code}: ${message}`);
    }
    else if (routerConfig.__typename === 'Unchanged') {
        return null;
    }
    else {
        throw new UplinkFetcherError('Programming error: unhandled response failure');
    }
}
exports.loadSupergraphSdlFromStorage = loadSupergraphSdlFromStorage;
//# sourceMappingURL=loadSupergraphSdlFromStorage.js.map