{"name": "@apollo/query-graphs", "version": "2.11.2", "description": "Apollo Federation library to work with 'query graphs'", "main": "dist/index.js", "types": "dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/apollographql/federation.git", "directory": "query-graphs-js/"}, "scripts": {"test": "jest"}, "keywords": ["graphql", "federation", "apollo"], "author": "Apollo <<EMAIL>>", "license": "Elastic-2.0", "engines": {"node": ">=14.15.0"}, "dependencies": {"@apollo/federation-internals": "2.11.2", "deep-equal": "^2.0.5", "ts-graphviz": "^1.5.4", "uuid": "^9.0.0"}, "publishConfig": {"access": "public"}, "peerDependencies": {"graphql": "^16.5.0"}}