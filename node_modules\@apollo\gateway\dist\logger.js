"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getDefaultLogger = void 0;
const loglevel_1 = __importDefault(require("loglevel"));
function getDefaultLogger(debug = false) {
    const logger = loglevel_1.default.getLogger('apollo-gateway');
    const level = debug === true ? loglevel_1.default.levels.DEBUG : loglevel_1.default.levels.WARN;
    logger.setLevel(level);
    return logger;
}
exports.getDefaultLogger = getDefaultLogger;
//# sourceMappingURL=logger.js.map