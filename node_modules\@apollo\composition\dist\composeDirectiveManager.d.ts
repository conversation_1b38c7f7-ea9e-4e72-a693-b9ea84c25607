import { CoreFeature, DirectiveDefinition, Subgraphs } from '@apollo/federation-internals';
import { GraphQLError } from 'graphql';
import { CompositionHint } from './hints';
import { MismatchReporter } from './merging/reporter';
export declare class ComposeDirectiveManager {
    readonly subgraphs: Subgraphs;
    readonly pushError: (error: GraphQLError) => void;
    readonly pushHint: (hint: CompositionHint) => void;
    mergeDirectiveMap: Map<string, Set<string>>;
    latestFeatureMap: Map<string, [CoreFeature, string]>;
    directiveIdentityMap: Map<string, [string, string]>;
    mismatchReporter: MismatchReporter;
    constructor(subgraphs: Subgraphs, pushError: (error: GraphQLError) => void, pushHint: (hint: CompositionHint) => void);
    private coreFeatureASTs;
    private getLatestIfCompatible;
    private forFederationDirective;
    private allCoreFeaturesUsedBySubgraphs;
    validate(): {
        errors: GraphQLError[];
        hints: CompositionHint[];
    };
    shouldComposeDirective({ subgraphName, directiveName }: {
        subgraphName: string;
        directiveName: string;
    }): boolean;
    directiveExistsInSupergraph(directiveName: string): boolean;
    getLatestDirectiveDefinition(directiveName: string): DirectiveDefinition | undefined;
    private directivesForFeature;
    allComposedCoreFeatures(): [CoreFeature, [string, string][]][];
}
//# sourceMappingURL=composeDirectiveManager.d.ts.map