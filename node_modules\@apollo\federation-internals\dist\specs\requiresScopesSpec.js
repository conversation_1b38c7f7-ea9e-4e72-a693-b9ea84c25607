"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.REQUIRES_SCOPES_VERSIONS = exports.RequiresScopesSpecDefinition = exports.RequiresScopesTypeName = void 0;
const graphql_1 = require("graphql");
const coreSpec_1 = require("./coreSpec");
const definitions_1 = require("../definitions");
const directiveAndTypeSpecification_1 = require("../directiveAndTypeSpecification");
const knownCoreFeatures_1 = require("../knownCoreFeatures");
const argumentCompositionStrategies_1 = require("../argumentCompositionStrategies");
const utils_1 = require("../utils");
var RequiresScopesTypeName;
(function (RequiresScopesTypeName) {
    RequiresScopesTypeName["SCOPE"] = "Scope";
})(RequiresScopesTypeName || (exports.RequiresScopesTypeName = RequiresScopesTypeName = {}));
class RequiresScopesSpecDefinition extends coreSpec_1.FeatureDefinition {
    constructor(version) {
        super(new coreSpec_1.FeatureUrl(RequiresScopesSpecDefinition.identity, RequiresScopesSpecDefinition.directiveName, version));
        this.registerType((0, directiveAndTypeSpecification_1.createScalarTypeSpecification)({ name: RequiresScopesTypeName.SCOPE }));
        this.registerDirective((0, directiveAndTypeSpecification_1.createDirectiveSpecification)({
            name: RequiresScopesSpecDefinition.directiveName,
            args: [{
                    name: 'scopes',
                    type: (schema, feature) => {
                        (0, utils_1.assert)(feature, "Shouldn't be added without being attached to a @link spec");
                        const scopeName = feature.typeNameInSchema(RequiresScopesTypeName.SCOPE);
                        const scopeType = schema.type(scopeName);
                        (0, utils_1.assert)(scopeType, () => `Expected "${scopeName}" to be defined`);
                        return new definitions_1.NonNullType(new definitions_1.ListType(new definitions_1.NonNullType(new definitions_1.ListType(new definitions_1.NonNullType(scopeType)))));
                    },
                    compositionStrategy: argumentCompositionStrategies_1.ARGUMENT_COMPOSITION_STRATEGIES.UNION,
                }],
            locations: [
                graphql_1.DirectiveLocation.FIELD_DEFINITION,
                graphql_1.DirectiveLocation.OBJECT,
                graphql_1.DirectiveLocation.INTERFACE,
                graphql_1.DirectiveLocation.SCALAR,
                graphql_1.DirectiveLocation.ENUM,
            ],
            composes: true,
            supergraphSpecification: () => exports.REQUIRES_SCOPES_VERSIONS.latest(),
        }));
    }
    get defaultCorePurpose() {
        return 'SECURITY';
    }
}
exports.RequiresScopesSpecDefinition = RequiresScopesSpecDefinition;
RequiresScopesSpecDefinition.directiveName = "requiresScopes";
RequiresScopesSpecDefinition.identity = `https://specs.apollo.dev/${RequiresScopesSpecDefinition.directiveName}`;
exports.REQUIRES_SCOPES_VERSIONS = new coreSpec_1.FeatureDefinitions(RequiresScopesSpecDefinition.identity).add(new RequiresScopesSpecDefinition(new coreSpec_1.FeatureVersion(0, 1)));
(0, knownCoreFeatures_1.registerKnownFeature)(exports.REQUIRES_SCOPES_VERSIONS);
//# sourceMappingURL=requiresScopesSpec.js.map