{"version": 3, "file": "conditions.js", "sourceRoot": "", "sources": ["../src/conditions.ts"], "names": [], "mappings": ";;;AAAA,uEAYsC;AACtC,uDAAoE;AAepE,SAAgB,mBAAmB,CAAC,IAA4B;IAC9D,OAAO,OAAO,IAAI,KAAK,SAAS,CAAC;AACnC,CAAC;AAFD,kDAEC;AAED,SAAgB,eAAe,CAAC,WAAuB,EAAE,WAAuB;IAC9E,IAAI,mBAAmB,CAAC,WAAW,CAAC,EAAE,CAAC;QACrC,OAAO,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC;IAC3C,CAAC;IACD,IAAI,mBAAmB,CAAC,WAAW,CAAC,EAAE,CAAC;QACrC,OAAO,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC;IAC3C,CAAC;IAMD,MAAM,MAAM,GAAqC,CAAC,GAAG,WAAW,CAAC,CAAC;IAClE,KAAK,MAAM,KAAK,IAAI,WAAW,EAAE,CAAC;QAChC,MAAM,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,KAAK,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACjF,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,KAAK,CAAC,OAAO,KAAK,KAAK,CAAC,OAAO,EAAE,CAAC;gBACpC,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrB,CAAC;IACH,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAxBD,0CAwBC;AAED,SAAS,cAAc,CAAC,WAAuB,EAAE,WAAuB;IACtE,IAAI,mBAAmB,CAAC,WAAW,CAAC,EAAE,CAAC;QACrC,OAAO,mBAAmB,CAAC,WAAW,CAAC,IAAI,WAAW,KAAK,WAAW,CAAC;IACzE,CAAC;IACD,IAAI,mBAAmB,CAAC,WAAW,CAAC,EAAE,CAAC;QACrC,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO,WAAW,CAAC,MAAM,KAAK,WAAW,CAAC,MAAM;WAC3C,WAAW,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,KAAK,KAAK,CAAC,QAAQ,CAAC,IAAI,IAAI,KAAK,CAAC,OAAO,KAAK,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;AACjJ,CAAC;AAED,SAAgB,wBAAwB,CAAC,YAA0B;IAOjE,MAAM,UAAU,GAAG,YAAY,CAAC,UAAU,EAAE,CAAC;IAC7C,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAG5B,OAAO,KAAK,CAAC;IACf,CAAC;IACD,MAAM,UAAU,GAAG,qBAAqB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;IACxD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QAC3C,MAAM,eAAe,GAAG,qBAAqB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7D,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,eAAe,CAAC,EAAE,CAAC;YACjD,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IACD,OAAO,UAAU,CAAC;AACpB,CAAC;AArBD,4DAqBC;AAED,SAAS,qBAAqB,CAAC,SAAoB;IACjD,MAAM,iBAAiB,GAAG,mBAAmB,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;IACjE,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC;QAC5B,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAED,IAAI,mBAAmB,CAAC,iBAAiB,CAAC,EAAE,CAAC;QAQ3C,IAAI,CAAC,iBAAiB,IAAI,SAAS,CAAC,IAAI,KAAK,gBAAgB,EAAE,CAAC;YAC9D,OAAO,iBAAiB,CAAC;QAC3B,CAAC;IACH,CAAC;IAED,MAAM,mBAAmB,GAAG,wBAAwB,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;IAC7E,OAAO,eAAe,CAAC,iBAAiB,EAAE,mBAAmB,CAAC,CAAC;AACjE,CAAC;AAED,SAAS,mBAAmB,CAAC,OAAyB;IACpD,MAAM,YAAY,GAAG,IAAA,2CAA4B,EAAC,OAAO,CAAC,CAAC;IAC3D,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC9B,OAAO,IAAI,CAAC;IACd,CAAC;IACD,MAAM,UAAU,GAAwB,EAAE,CAAC;IAC3C,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE,CAAC;QACvC,MAAM,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC;QAChC,IAAI,OAAO,KAAK,KAAK,SAAS,EAAE,CAAC;YAI/B,IAAI,KAAK,KAAK,CAAC,WAAW,CAAC,IAAI,KAAK,MAAM,CAAC,EAAE,CAAC;gBAC5C,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;aAAM,CAAC;YACN,UAAU,CAAC,IAAI,CAAC;gBACd,QAAQ,EAAE,KAAK;gBACf,OAAO,EAAE,WAAW,CAAC,IAAI,KAAK,MAAM;aACrC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED,IAAI,IAAA,sCAAe,EAAC,UAAU,CAAC,EAAE,CAAC;QAKhC,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,KAAK,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;YAG3F,OAAO,KAAK,CAAC;QACf,CAAC;QACD,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAgB,iBAAiB,CAAC,aAAyB,EAAE,iBAA6B;IACxF,IAAI,mBAAmB,CAAC,aAAa,CAAC,IAAI,mBAAmB,CAAC,iBAAiB,CAAC,EAAE,CAAC;QACjF,OAAO,aAAa,CAAC;IACvB,CAAC;IAED,MAAM,QAAQ,GAAwB,EAAE,CAAC;IACzC,KAAK,MAAM,IAAI,IAAI,aAAa,EAAE,CAAC;QACjC,MAAM,WAAW,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC1F,IAAI,WAAW,EAAE,CAAC;YAIhB,IAAI,IAAI,CAAC,OAAO,KAAK,WAAW,CAAC,OAAO,EAAE,CAAC;gBACzC,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;aAAM,CAAC;YACN,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtB,CAAC;IACH,CAAC;IACD,OAAO,IAAA,sCAAe,EAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC;AACrD,CAAC;AApBD,8CAoBC;AAED,SAAgB,gCAAgC,CAAC,YAA0B,EAAE,UAAsB;IACjG,IAAI,mBAAmB,CAAC,UAAU,CAAC,EAAE,CAAC;QAMpC,OAAO,YAAY,CAAC;IACtB,CAAC;IAED,OAAO,YAAY,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE;QAExC,MAAM,cAAc,GAAG,yBAAyB,CAAC,SAAS,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QAChF,IAAI,SAAS,CAAC,YAAY,EAAE,CAAC;YAC3B,MAAM,mBAAmB,GAAG,gCAAgC,CAAC,SAAS,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;YACjG,IAAI,cAAc,KAAK,SAAS,CAAC,OAAO,EAAE,CAAC;gBACzC,IAAI,mBAAmB,KAAK,SAAS,CAAC,YAAY,EAAE,CAAC;oBACnD,OAAO,SAAS,CAAC;gBACnB,CAAC;qBAAM,CAAC;oBACN,OAAO,SAAS,CAAC,uBAAuB,CAAC,mBAAmB,CAAC,CAAC;gBAChE,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,OAAO,IAAA,yCAAkB,EAAC,cAAc,EAAE,mBAAmB,CAAC,CAAC;YACjE,CAAC;QACH,CAAC;aAAM,CAAC;YACN,OAAO,cAAc,KAAK,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAA,yCAAkB,EAAC,cAAc,CAAC,CAAC;QAC/F,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AA5BD,4EA4BC;AAED,SAAS,yBAAyB,CAAC,OAAyB,EAAE,UAA+B;IAC3F,MAAM,iBAAiB,GAAI,OAAO,CAAC,iBAAmD,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,uBAAuB,CAAC,CAAC,EAAE,UAAU,EAAE,SAAS,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC;IAC5M,IAAI,iBAAiB,CAAC,MAAM,KAAK,OAAO,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC;QAClE,OAAO,OAAO,CAAC;IACjB,CAAC;IACD,OAAO,OAAO,CAAC,qBAAqB,CAAC,iBAAiB,CAAC,CAAC;AAC1D,CAAC;AAED,SAAS,uBAAuB,CAC9B,SAAsC,EACtC,UAA+B,EAC/B,IAAwB;IAExB,IAAI,SAAS,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;QAC5B,OAAO,KAAK,CAAC;IACf,CAAC;IAED,MAAM,KAAK,GAAG,SAAS,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,CAAC;IAC1C,OAAO,CAAC,IAAA,iCAAU,EAAC,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC;AAClI,CAAC;AAUD,SAAgB,iBAAiB,CAC/B,SAAwB,EACxB,SAA8B,EAC9B,MAAuC;;IAEvC,MAAM,QAAQ,GAAG,SAAS,CAAC,SAAS,CAAC;IACrC,IAAI,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IAChD,IAAI,GAAG,KAAK,SAAS,EAAE,CAAC;QACtB,GAAG,GAAG,MAAA,SAAS,CAAC,UAAU,CAAC,QAAQ,CAAC,0CAAE,YAAY,CAAC;IACrD,CAAC;IACD,IAAA,6BAAM,EAAC,GAAG,KAAK,SAAS,EAAE,GAAG,EAAE,CAAC,gCAAgC,QAAQ,yBAAyB,CAAC,CAAC;IACnG,IAAA,6BAAM,EAAC,OAAO,GAAG,KAAK,SAAS,EAAE,GAAG,EAAE,CAAC,6BAA6B,GAAG,4BAA4B,QAAQ,EAAE,CAAC,CAAA;IAC9G,OAAO,GAAG,CAAC;AACb,CAAC;AAbD,8CAaC"}