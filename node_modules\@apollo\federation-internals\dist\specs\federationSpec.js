"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FEDERATION_VERSIONS = exports.FederationSpecDefinition = exports.FEDERATION1_DIRECTIVES = exports.FEDERATION1_TYPES = exports.FederationDirectiveName = exports.FederationTypeName = exports.federationIdentity = void 0;
const definitions_1 = require("../definitions");
const coreSpec_1 = require("./coreSpec");
const directiveAndTypeSpecification_1 = require("../directiveAndTypeSpecification");
const graphql_1 = require("graphql");
const utils_1 = require("../utils");
const tagSpec_1 = require("./tagSpec");
const federation_1 = require("../federation");
const knownCoreFeatures_1 = require("../knownCoreFeatures");
const inaccessibleSpec_1 = require("./inaccessibleSpec");
const authenticatedSpec_1 = require("./authenticatedSpec");
const requiresScopesSpec_1 = require("./requiresScopesSpec");
const policySpec_1 = require("./policySpec");
const contextSpec_1 = require("./contextSpec");
const costSpec_1 = require("./costSpec");
exports.federationIdentity = 'https://specs.apollo.dev/federation';
var FederationTypeName;
(function (FederationTypeName) {
    FederationTypeName["FIELD_SET"] = "FieldSet";
    FederationTypeName["CONTEXT_FIELD_VALUE"] = "ContextFieldValue";
})(FederationTypeName || (exports.FederationTypeName = FederationTypeName = {}));
var FederationDirectiveName;
(function (FederationDirectiveName) {
    FederationDirectiveName["KEY"] = "key";
    FederationDirectiveName["EXTERNAL"] = "external";
    FederationDirectiveName["REQUIRES"] = "requires";
    FederationDirectiveName["PROVIDES"] = "provides";
    FederationDirectiveName["EXTENDS"] = "extends";
    FederationDirectiveName["SHAREABLE"] = "shareable";
    FederationDirectiveName["OVERRIDE"] = "override";
    FederationDirectiveName["TAG"] = "tag";
    FederationDirectiveName["INACCESSIBLE"] = "inaccessible";
    FederationDirectiveName["COMPOSE_DIRECTIVE"] = "composeDirective";
    FederationDirectiveName["INTERFACE_OBJECT"] = "interfaceObject";
    FederationDirectiveName["AUTHENTICATED"] = "authenticated";
    FederationDirectiveName["REQUIRES_SCOPES"] = "requiresScopes";
    FederationDirectiveName["POLICY"] = "policy";
    FederationDirectiveName["CONTEXT"] = "context";
    FederationDirectiveName["FROM_CONTEXT"] = "fromContext";
    FederationDirectiveName["COST"] = "cost";
    FederationDirectiveName["LIST_SIZE"] = "listSize";
})(FederationDirectiveName || (exports.FederationDirectiveName = FederationDirectiveName = {}));
const fieldSetTypeSpec = (0, directiveAndTypeSpecification_1.createScalarTypeSpecification)({ name: FederationTypeName.FIELD_SET });
const fieldsArgument = { name: 'fields', type: (schema) => fieldSetType(schema) };
const keyDirectiveSpec = (0, directiveAndTypeSpecification_1.createDirectiveSpecification)({
    name: FederationDirectiveName.KEY,
    locations: [graphql_1.DirectiveLocation.OBJECT, graphql_1.DirectiveLocation.INTERFACE],
    repeatable: true,
    args: [
        fieldsArgument,
        { name: 'resolvable', type: (schema) => schema.booleanType(), defaultValue: true },
    ]
});
const extendsDirectiveSpec = (0, directiveAndTypeSpecification_1.createDirectiveSpecification)({
    name: FederationDirectiveName.EXTENDS,
    locations: [graphql_1.DirectiveLocation.OBJECT, graphql_1.DirectiveLocation.INTERFACE],
});
const externalDirectiveSpec = (0, directiveAndTypeSpecification_1.createDirectiveSpecification)({
    name: FederationDirectiveName.EXTERNAL,
    locations: [graphql_1.DirectiveLocation.OBJECT, graphql_1.DirectiveLocation.FIELD_DEFINITION],
    args: [{ name: 'reason', type: (schema) => schema.stringType() }],
});
const requiresDirectiveSpec = (0, directiveAndTypeSpecification_1.createDirectiveSpecification)({
    name: FederationDirectiveName.REQUIRES,
    locations: [graphql_1.DirectiveLocation.FIELD_DEFINITION],
    args: [fieldsArgument],
});
const providesDirectiveSpec = (0, directiveAndTypeSpecification_1.createDirectiveSpecification)({
    name: FederationDirectiveName.PROVIDES,
    locations: [graphql_1.DirectiveLocation.FIELD_DEFINITION],
    args: [fieldsArgument],
});
const legacyFederationTypes = [
    fieldSetTypeSpec,
];
const legacyFederationDirectives = [
    keyDirectiveSpec,
    requiresDirectiveSpec,
    providesDirectiveSpec,
    externalDirectiveSpec,
    tagSpec_1.TAG_VERSIONS.find(new coreSpec_1.FeatureVersion(0, 2)).tagDirectiveSpec,
    extendsDirectiveSpec,
];
exports.FEDERATION1_TYPES = legacyFederationTypes;
exports.FEDERATION1_DIRECTIVES = legacyFederationDirectives;
function fieldSetType(schema) {
    const metadata = (0, federation_1.federationMetadata)(schema);
    (0, utils_1.assert)(metadata, `The schema is not a federation subgraph`);
    return new definitions_1.NonNullType(metadata.fieldSetType());
}
class FederationSpecDefinition extends coreSpec_1.FeatureDefinition {
    constructor(version) {
        super(new coreSpec_1.FeatureUrl(exports.federationIdentity, 'federation', version));
        for (const type of legacyFederationTypes) {
            this.registerType(type);
        }
        for (const directive of legacyFederationDirectives) {
            this.registerDirective(directive);
        }
        this.registerDirective((0, directiveAndTypeSpecification_1.createDirectiveSpecification)({
            name: FederationDirectiveName.SHAREABLE,
            locations: [graphql_1.DirectiveLocation.OBJECT, graphql_1.DirectiveLocation.FIELD_DEFINITION],
            repeatable: version.gte(new coreSpec_1.FeatureVersion(2, 2)),
        }));
        this.registerSubFeature(inaccessibleSpec_1.INACCESSIBLE_VERSIONS.getMinimumRequiredVersion(version));
        if (version.gte(new coreSpec_1.FeatureVersion(2, 7))) {
            this.registerDirective((0, directiveAndTypeSpecification_1.createDirectiveSpecification)({
                name: FederationDirectiveName.OVERRIDE,
                locations: [graphql_1.DirectiveLocation.FIELD_DEFINITION],
                args: [
                    { name: 'from', type: (schema) => new definitions_1.NonNullType(schema.stringType()) },
                    { name: 'label', type: (schema) => schema.stringType() },
                ],
            }));
        }
        else {
            this.registerDirective((0, directiveAndTypeSpecification_1.createDirectiveSpecification)({
                name: FederationDirectiveName.OVERRIDE,
                locations: [graphql_1.DirectiveLocation.FIELD_DEFINITION],
                args: [{ name: 'from', type: (schema) => new definitions_1.NonNullType(schema.stringType()) }],
            }));
        }
        if (version.gte(new coreSpec_1.FeatureVersion(2, 1))) {
            this.registerDirective((0, directiveAndTypeSpecification_1.createDirectiveSpecification)({
                name: FederationDirectiveName.COMPOSE_DIRECTIVE,
                locations: [graphql_1.DirectiveLocation.SCHEMA],
                repeatable: true,
                args: [{ name: 'name', type: (schema) => schema.stringType() }],
            }));
        }
        if (version.gte(new coreSpec_1.FeatureVersion(2, 3))) {
            this.registerDirective((0, directiveAndTypeSpecification_1.createDirectiveSpecification)({
                name: FederationDirectiveName.INTERFACE_OBJECT,
                locations: [graphql_1.DirectiveLocation.OBJECT],
            }));
            this.registerSubFeature(tagSpec_1.TAG_VERSIONS.find(new coreSpec_1.FeatureVersion(0, 3)));
        }
        if (version.gte(new coreSpec_1.FeatureVersion(2, 5))) {
            this.registerSubFeature(authenticatedSpec_1.AUTHENTICATED_VERSIONS.find(new coreSpec_1.FeatureVersion(0, 1)));
            this.registerSubFeature(requiresScopesSpec_1.REQUIRES_SCOPES_VERSIONS.find(new coreSpec_1.FeatureVersion(0, 1)));
        }
        if (version.gte(new coreSpec_1.FeatureVersion(2, 6))) {
            this.registerSubFeature(policySpec_1.POLICY_VERSIONS.find(new coreSpec_1.FeatureVersion(0, 1)));
        }
        if (version.gte(new coreSpec_1.FeatureVersion(2, 8))) {
            this.registerSubFeature(contextSpec_1.CONTEXT_VERSIONS.find(new coreSpec_1.FeatureVersion(0, 1)));
        }
        if (version.gte(new coreSpec_1.FeatureVersion(2, 9))) {
            this.registerSubFeature(costSpec_1.COST_VERSIONS.find(new coreSpec_1.FeatureVersion(0, 1)));
        }
    }
}
exports.FederationSpecDefinition = FederationSpecDefinition;
exports.FEDERATION_VERSIONS = new coreSpec_1.FeatureDefinitions(exports.federationIdentity)
    .add(new FederationSpecDefinition(new coreSpec_1.FeatureVersion(2, 0)))
    .add(new FederationSpecDefinition(new coreSpec_1.FeatureVersion(2, 1)))
    .add(new FederationSpecDefinition(new coreSpec_1.FeatureVersion(2, 2)))
    .add(new FederationSpecDefinition(new coreSpec_1.FeatureVersion(2, 3)))
    .add(new FederationSpecDefinition(new coreSpec_1.FeatureVersion(2, 4)))
    .add(new FederationSpecDefinition(new coreSpec_1.FeatureVersion(2, 5)))
    .add(new FederationSpecDefinition(new coreSpec_1.FeatureVersion(2, 6)))
    .add(new FederationSpecDefinition(new coreSpec_1.FeatureVersion(2, 7)))
    .add(new FederationSpecDefinition(new coreSpec_1.FeatureVersion(2, 8)))
    .add(new FederationSpecDefinition(new coreSpec_1.FeatureVersion(2, 9)))
    .add(new FederationSpecDefinition(new coreSpec_1.FeatureVersion(2, 10)))
    .add(new FederationSpecDefinition(new coreSpec_1.FeatureVersion(2, 11)));
(0, knownCoreFeatures_1.registerKnownFeature)(exports.FEDERATION_VERSIONS);
//# sourceMappingURL=federationSpec.js.map