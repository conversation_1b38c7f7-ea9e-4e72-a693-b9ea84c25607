{"name": "@apollo/gateway", "version": "2.11.2", "description": "Apollo Gateway", "author": "Apollo <<EMAIL>>", "main": "dist/index.js", "types": "dist/index.d.ts", "repository": {"type": "git", "url": "git+https://github.com/apollographql/federation.git", "directory": "gateway-js/"}, "keywords": ["graphql", "federation", "gateway", "server", "apollo"], "engines": {"node": ">=14.15.0"}, "license": "Elastic-2.0", "publishConfig": {"access": "public"}, "dependencies": {"@apollo/composition": "2.11.2", "@apollo/federation-internals": "2.11.2", "@apollo/query-planner": "2.11.2", "@apollo/server-gateway-interface": "^1.1.0", "@apollo/usage-reporting-protobuf": "^4.1.0", "@apollo/utils.createhash": "^2.0.0", "@apollo/utils.fetcher": "^2.0.0", "@apollo/utils.isnodelike": "^2.0.0", "@apollo/utils.keyvaluecache": "^2.1.0", "@apollo/utils.logger": "^2.0.0", "@josephg/resolvable": "^1.0.1", "@opentelemetry/api": "^1.0.1", "@types/node-fetch": "^2.6.2", "async-retry": "^1.3.3", "loglevel": "^1.6.1", "make-fetch-happen": "^11.0.0", "node-abort-controller": "^3.0.1", "node-fetch": "^2.6.7"}, "peerDependencies": {"graphql": "^16.5.0"}}