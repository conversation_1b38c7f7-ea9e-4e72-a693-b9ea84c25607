@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-gray-50/50 text-gray-900;
    font-feature-settings: "rlig" 1, "calt" 1;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    line-height: 1.6;
    letter-spacing: -0.01em;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100/50;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }

  /* Focus styles */
  .focus-visible {
    @apply outline-none ring-2 ring-blue-500 ring-offset-2 ring-offset-white;
  }

  /* Loading spinner */
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-200 border-t-blue-600;
  }

  /* Enhanced gradient backgrounds */
  .gradient-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  }

  .gradient-secondary {
    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  }

  .gradient-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  }

  .gradient-warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  }

  .gradient-danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  }

  /* Enhanced glass morphism effect */
  .glass {
    @apply backdrop-blur-lg bg-white/80 border border-white/30 shadow-lg;
  }

  .glass-dark {
    @apply backdrop-blur-lg bg-gray-900/80 border border-gray-700/30 shadow-lg;
  }

  /* Enhanced card hover effects */
  .card-hover {
    @apply transition-all duration-300 hover:shadow-xl hover:-translate-y-2 hover:scale-[1.02];
  }

  .card-hover-subtle {
    @apply transition-all duration-200 hover:shadow-lg hover:-translate-y-1;
  }

  /* Enhanced text gradients */
  .text-gradient {
    @apply bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent;
  }

  .text-gradient-success {
    @apply bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent;
  }

  .text-gradient-warning {
    @apply bg-gradient-to-r from-yellow-600 to-orange-600 bg-clip-text text-transparent;
  }

  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-slide-down {
    animation: slideDown 0.3s ease-out;
  }

  /* Custom animations */
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slideUp {
    from {
      transform: translateY(20px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes slideDown {
    from {
      transform: translateY(-20px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  /* Print styles */
  @media print {
    .no-print {
      display: none !important;
    }
    
    .print-break {
      page-break-before: always;
    }
    
    .print-avoid-break {
      page-break-inside: avoid;
    }
  }

  /* Enhanced mobile optimizations */
  @media (max-width: 640px) {
    .mobile-hidden {
      display: none;
    }

    .mobile-full {
      width: 100% !important;
    }

    .mobile-stack {
      flex-direction: column !important;
    }

    .mobile-center {
      text-align: center !important;
    }

    .mobile-p-4 {
      padding: 1rem !important;
    }

    .mobile-text-sm {
      font-size: 0.875rem !important;
    }
  }

  /* Tablet optimizations */
  @media (min-width: 641px) and (max-width: 1024px) {
    .tablet-hidden {
      display: none;
    }

    .tablet-full {
      width: 100% !important;
    }

    .tablet-stack {
      flex-direction: column !important;
    }
  }

  /* Desktop optimizations */
  @media (min-width: 1025px) {
    .desktop-hidden {
      display: none;
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .high-contrast {
      @apply border-2 border-gray-900;
    }
  }

  /* Responsive typography utilities */
  .text-heading-1 {
    @apply text-2xl sm:text-3xl lg:text-4xl font-bold tracking-tight text-gray-900 leading-tight;
  }

  .text-heading-2 {
    @apply text-xl sm:text-2xl lg:text-3xl font-bold tracking-tight text-gray-900 leading-tight;
  }

  .text-heading-3 {
    @apply text-lg sm:text-xl lg:text-2xl font-semibold tracking-tight text-gray-900 leading-snug;
  }

  .text-heading-4 {
    @apply text-base sm:text-lg lg:text-xl font-semibold tracking-tight text-gray-900 leading-snug;
  }

  .text-body-large {
    @apply text-lg font-medium text-gray-700 leading-relaxed;
  }

  .text-body {
    @apply text-base font-medium text-gray-700 leading-relaxed;
  }

  .text-body-small {
    @apply text-sm font-medium text-gray-600 leading-relaxed;
  }

  .text-caption {
    @apply text-xs font-medium text-gray-500 leading-normal;
  }

  /* Responsive spacing utilities */
  .section-spacing {
    @apply py-8 sm:py-12 lg:py-16 px-4 sm:px-6 lg:px-8;
  }

  .container-spacing {
    @apply px-4 sm:px-6 lg:px-8 xl:px-12;
  }

  .card-spacing {
    @apply p-4 sm:p-6 lg:p-8;
  }

  .form-spacing {
    @apply space-y-4 sm:space-y-6;
  }

  .grid-spacing {
    @apply gap-4 sm:gap-6 lg:gap-8;
  }

  /* Interactive states */
  .interactive {
    @apply transition-all duration-200 cursor-pointer;
  }

  .interactive:hover {
    @apply transform scale-105;
  }

  .interactive:active {
    @apply transform scale-95;
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}
