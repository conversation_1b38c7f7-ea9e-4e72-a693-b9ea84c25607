import { CorePurpose, FeatureDefinition, FeatureDefinitions, FeatureVersion } from "./coreSpec";
export declare class AuthenticatedSpecDefinition extends FeatureDefinition {
    static readonly directiveName = "authenticated";
    static readonly identity: string;
    constructor(version: FeatureVersion, minimumFederationVersion: FeatureVersion);
    get defaultCorePurpose(): CorePurpose;
}
export declare const AUTHENTICATED_VERSIONS: FeatureDefinitions<AuthenticatedSpecDefinition>;
//# sourceMappingURL=authenticatedSpec.d.ts.map