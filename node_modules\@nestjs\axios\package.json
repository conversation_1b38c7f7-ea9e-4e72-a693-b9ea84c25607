{"name": "@nestjs/axios", "version": "3.1.3", "description": "Nest - modern, fast, powerful node.js web framework (@axios)", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "url": "https://github.com/nestjs/axios#readme", "scripts": {"build": "rimraf -rf dist && tsc -p tsconfig.json", "format": "prettier --write \"{lib,test}/**/*.ts\"", "lint": "eslint 'lib/**/*.ts' --fix", "prepublish:npm": "npm run build", "publish:npm": "npm publish --access public", "prepublish:next": "npm run build", "publish:next": "npm publish --access public --tag next", "test:e2e": "jest --config ./tests/jest-e2e.json --runInBand", "prerelease": "npm run build", "release": "release-it", "prepare": "husky install"}, "devDependencies": {"@commitlint/cli": "19.6.0", "@commitlint/config-angular": "19.6.0", "@nestjs/common": "10.4.13", "@nestjs/core": "10.4.13", "@nestjs/platform-express": "10.4.13", "@nestjs/testing": "10.4.13", "@types/jest": "29.5.14", "@types/node": "22.10.1", "@typescript-eslint/eslint-plugin": "8.17.0", "@typescript-eslint/parser": "8.17.0", "axios": "1.7.9", "eslint": "9.16.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "2.31.0", "husky": "9.1.7", "jest": "29.7.0", "lint-staged": "15.2.10", "prettier": "3.4.2", "reflect-metadata": "0.2.2", "release-it": "17.10.0", "rimraf": "6.0.1", "rxjs": "7.8.1", "ts-jest": "29.2.5", "typescript": "5.7.2"}, "peerDependencies": {"@nestjs/common": "^7.0.0 || ^8.0.0 || ^9.0.0 || ^10.0.0", "axios": "^1.3.1", "rxjs": "^6.0.0 || ^7.0.0"}, "lint-staged": {"**/*.{ts,json}": []}, "repository": {"type": "git", "url": "https://github.com/nestjs/axios"}}