import { NamedSchemaElement, SubgraphASTNode } from "@apollo/federation-internals";
export declare enum HintLevel {
    WARN = 60,
    INFO = 40,
    DEBUG = 20
}
export type HintCodeDefinition = {
    code: string;
    level: {
        value: HintLevel;
        name: string;
    };
    description: string;
};
export declare const HINTS: {
    INCONSISTENT_BUT_COMPATIBLE_FIELD_TYPE: HintCodeDefinition;
    INCONSISTENT_BUT_COMPATIBLE_ARGUMENT_TYPE: HintCodeDefinition;
    INCONSISTENT_DEFAULT_VALUE_PRESENCE: HintCodeDefinition;
    INCONSISTENT_ENTITY: HintCodeDefinition;
    INCONSISTENT_OBJECT_VALUE_TYPE_FIELD: HintCodeDefinition;
    INCONSISTENT_INTERFACE_VALUE_TYPE_FIELD: HintCodeDefinition;
    INCONSISTENT_INPUT_OBJECT_FIELD: HintCodeDefinition;
    INCONSISTENT_UNION_MEMBER: HintCodeDefinition;
    INCONSISTENT_ENUM_VALUE_FOR_INPUT_ENUM: HintCodeDefinition;
    INCONSISTENT_ENUM_VALUE_FOR_OUTPUT_ENUM: HintCodeDefinition;
    INCONSISTENT_TYPE_SYSTEM_DIRECTIVE_REPEATABLE: HintCodeDefinition;
    INCONSISTENT_TYPE_SYSTEM_DIRECTIVE_LOCATIONS: HintCodeDefinition;
    INCONSISTENT_EXECUTABLE_DIRECTIVE_PRESENCE: HintCodeDefinition;
    NO_EXECUTABLE_DIRECTIVE_LOCATIONS_INTERSECTION: HintCodeDefinition;
    INCONSISTENT_EXECUTABLE_DIRECTIVE_REPEATABLE: HintCodeDefinition;
    INCONSISTENT_EXECUTABLE_DIRECTIVE_LOCATIONS: HintCodeDefinition;
    INCONSISTENT_DESCRIPTION: HintCodeDefinition;
    INCONSISTENT_ARGUMENT_PRESENCE: HintCodeDefinition;
    FROM_SUBGRAPH_DOES_NOT_EXIST: HintCodeDefinition;
    OVERRIDDEN_FIELD_CAN_BE_REMOVED: HintCodeDefinition;
    OVERRIDE_DIRECTIVE_CAN_BE_REMOVED: HintCodeDefinition;
    OVERRIDE_MIGRATION_IN_PROGRESS: HintCodeDefinition;
    UNUSED_ENUM_TYPE: HintCodeDefinition;
    INCONSISTENT_NON_REPEATABLE_DIRECTIVE_ARGUMENTS: HintCodeDefinition;
    MERGED_NON_REPEATABLE_DIRECTIVE_ARGUMENTS: HintCodeDefinition;
    DIRECTIVE_COMPOSITION_INFO: HintCodeDefinition;
    DIRECTIVE_COMPOSITION_WARN: HintCodeDefinition;
    INCONSISTENT_RUNTIME_TYPES_FOR_SHAREABLE_RETURN: HintCodeDefinition;
    IMPLICITLY_UPGRADED_FEDERATION_VERSION: HintCodeDefinition;
    CONTEXTUAL_ARGUMENT_NOT_CONTEXTUAL_IN_ALL_SUBGRAPHS: HintCodeDefinition;
};
export declare class CompositionHint {
    readonly definition: HintCodeDefinition;
    readonly message: string;
    readonly element: NamedSchemaElement<any, any, any> | undefined;
    readonly nodes?: readonly SubgraphASTNode[];
    readonly coordinate?: string;
    constructor(definition: HintCodeDefinition, message: string, element: NamedSchemaElement<any, any, any> | undefined, nodes?: readonly SubgraphASTNode[] | SubgraphASTNode);
    toString(): string;
}
export declare function printHint(hint: CompositionHint): string;
//# sourceMappingURL=hints.d.ts.map