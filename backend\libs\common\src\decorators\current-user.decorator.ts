import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';

/**
 * Decorator to extract the current user from the request
 * 
 * @example
 * ```typescript
 * @Get('profile')
 * getProfile(@CurrentUser() user: User) {
 *   return user;
 * }
 * 
 * // Extract specific property
 * @Get('my-id')
 * getMyId(@CurrentUser('id') userId: string) {
 *   return { id: userId };
 * }
 * ```
 */
export const CurrentUser = createParamDecorator(
  (data: unknown, context: ExecutionContext) => {
    // Handle HTTP requests
    if (context.getType() === 'http') {
      const request = context.switchToHttp().getRequest();
      return request.user;
    }

    // Handle GraphQL requests
    if (context.getType<any>() === 'graphql') {
      const ctx = GqlExecutionContext.create(context);
      const request = ctx.getContext().req;
      return request.user;
    }

    // Handle other request types
    const request = context.switchToHttp().getRequest();
    return request.user;
  },
);
