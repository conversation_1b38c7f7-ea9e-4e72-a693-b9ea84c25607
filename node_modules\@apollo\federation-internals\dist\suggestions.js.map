{"version": 3, "file": "suggestions.js", "sourceRoot": "", "sources": ["../src/suggestions.ts"], "names": [], "mappings": ";;;;;;AAAA,oEAAyC;AACzC,mCAAkC;AAMlC,SAAgB,cAAc,CAAC,KAAa,EAAE,OAA0B;IACtE,MAAM,iBAAiB,GAAG,IAAI,GAAG,EAAkB,CAAC;IAEpD,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IACrD,MAAM,cAAc,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;IAC3C,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;QAG7B,MAAM,QAAQ,GAAG,cAAc,KAAK,MAAM,CAAC,WAAW,EAAE;YACtD,CAAC,CAAC,CAAC;YACH,CAAC,CAAC,IAAA,wBAAW,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QAC/B,IAAI,QAAQ,IAAI,SAAS,EAAE,CAAC;YAC1B,iBAAiB,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;IAED,OAAO,IAAA,eAAO,EAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QAC9C,MAAM,YAAY,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAE,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC,CAAE,CAAC;QAC3E,OAAO,YAAY,KAAK,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;IAChE,CAAC,CAAC,CAAC;AACL,CAAC;AApBD,wCAoBC;AAED,MAAM,eAAe,GAAG,CAAC,CAAC;AAK1B,SAAgB,UAAU,CAAC,WAA8B;IACvD,MAAM,OAAO,GAAG,gBAAgB,CAAC;IAEjC,MAAM,iBAAiB,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC3D,QAAQ,WAAW,CAAC,MAAM,EAAE,CAAC;QAC3B,KAAK,CAAC;YACJ,OAAO,EAAE,CAAC;QACZ,KAAK,CAAC;YACJ,OAAO,OAAO,GAAG,iBAAiB,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;QAC9C,KAAK,CAAC;YACJ,OAAO,OAAO,GAAG,iBAAiB,CAAC,CAAC,CAAC,GAAG,MAAM,GAAG,iBAAiB,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;IAChF,CAAC;IAED,MAAM,QAAQ,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC,EAAE,eAAe,CAAC,CAAC;IAC7D,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAC;IAChC,OAAO,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,OAAO,GAAG,QAAQ,GAAG,GAAG,CAAC;AAClE,CAAC;AAhBD,gCAgBC"}