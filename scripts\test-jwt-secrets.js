#!/usr/bin/env node

/**
 * JWT Secrets Test Script
 * Tests that the JWT secrets are properly configured and working
 */

const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

/**
 * Test JWT secret configuration
 */
function testJWTSecrets() {
  console.log('🔐 Testing JWT Secret Configuration');
  console.log('===================================\n');

  const jwtSecret = process.env.JWT_SECRET;
  const jwtRefreshSecret = process.env.JWT_REFRESH_SECRET;

  // Test 1: Check if secrets exist
  console.log('📋 Test 1: Secret Existence');
  if (!jwtSecret) {
    console.log('❌ JWT_SECRET is not set in environment');
    return false;
  }
  if (!jwtRefreshSecret) {
    console.log('❌ JWT_REFRESH_SECRET is not set in environment');
    return false;
  }
  console.log('✅ Both JWT secrets are present in environment\n');

  // Test 2: Check secret length and format
  console.log('📋 Test 2: Secret Format and Length');
  console.log(`   JWT_SECRET length: ${jwtSecret.length} characters`);
  console.log(`   JWT_REFRESH_SECRET length: ${jwtRefreshSecret.length} characters`);
  
  // Base64url encoded 64-byte secrets should be ~86 characters
  if (jwtSecret.length < 80) {
    console.log('⚠️  JWT_SECRET seems too short for secure usage');
  } else {
    console.log('✅ JWT_SECRET has adequate length');
  }
  
  if (jwtRefreshSecret.length < 80) {
    console.log('⚠️  JWT_REFRESH_SECRET seems too short for secure usage');
  } else {
    console.log('✅ JWT_REFRESH_SECRET has adequate length');
  }
  console.log('');

  // Test 3: Check if secrets are different
  console.log('📋 Test 3: Secret Uniqueness');
  if (jwtSecret === jwtRefreshSecret) {
    console.log('❌ JWT_SECRET and JWT_REFRESH_SECRET are identical (security risk)');
    return false;
  }
  console.log('✅ JWT secrets are unique\n');

  // Test 4: Check if secrets are not default values
  console.log('📋 Test 4: Default Value Check');
  const defaultSecrets = [
    'peoplenest-jwt-secret-2024-enterprise-hrms-super-secure-key',
    'peoplenest-refresh-secret-2024-enterprise-hrms-super-secure-key',
    'your-jwt-secret',
    'your-refresh-secret',
    'jwt-secret',
    'refresh-secret'
  ];
  
  if (defaultSecrets.includes(jwtSecret)) {
    console.log('❌ JWT_SECRET appears to be a default value (security risk)');
    return false;
  }
  if (defaultSecrets.includes(jwtRefreshSecret)) {
    console.log('❌ JWT_REFRESH_SECRET appears to be a default value (security risk)');
    return false;
  }
  console.log('✅ JWT secrets are not default values\n');

  // Test 5: Entropy estimation
  console.log('📋 Test 5: Entropy Estimation');
  const jwtEntropy = estimateEntropy(jwtSecret);
  const refreshEntropy = estimateEntropy(jwtRefreshSecret);
  
  console.log(`   JWT_SECRET estimated entropy: ${jwtEntropy.toFixed(2)} bits`);
  console.log(`   JWT_REFRESH_SECRET estimated entropy: ${refreshEntropy.toFixed(2)} bits`);
  
  if (jwtEntropy < 128) {
    console.log('⚠️  JWT_SECRET entropy is lower than recommended (128+ bits)');
  } else {
    console.log('✅ JWT_SECRET has good entropy');
  }
  
  if (refreshEntropy < 128) {
    console.log('⚠️  JWT_REFRESH_SECRET entropy is lower than recommended (128+ bits)');
  } else {
    console.log('✅ JWT_REFRESH_SECRET has good entropy');
  }
  console.log('');

  // Test 6: Character set analysis
  console.log('📋 Test 6: Character Set Analysis');
  const jwtCharSet = analyzeCharacterSet(jwtSecret);
  const refreshCharSet = analyzeCharacterSet(jwtRefreshSecret);
  
  console.log(`   JWT_SECRET character diversity: ${jwtCharSet.uniqueChars}/${jwtSecret.length} (${(jwtCharSet.uniqueChars/jwtSecret.length*100).toFixed(1)}%)`);
  console.log(`   JWT_REFRESH_SECRET character diversity: ${refreshCharSet.uniqueChars}/${jwtRefreshSecret.length} (${(refreshCharSet.uniqueChars/jwtRefreshSecret.length*100).toFixed(1)}%)`);
  
  if (jwtCharSet.isBase64url) {
    console.log('✅ JWT_SECRET uses base64url character set');
  } else {
    console.log('⚠️  JWT_SECRET does not appear to be base64url encoded');
  }
  
  if (refreshCharSet.isBase64url) {
    console.log('✅ JWT_REFRESH_SECRET uses base64url character set');
  } else {
    console.log('⚠️  JWT_REFRESH_SECRET does not appear to be base64url encoded');
  }

  console.log('\n🎉 JWT Secret Configuration Test Complete!');
  console.log('✅ All tests passed - JWT secrets are properly configured');
  
  return true;
}

/**
 * Estimate entropy of a string using Shannon entropy
 * @param {string} str - String to analyze
 * @returns {number} - Estimated entropy in bits
 */
function estimateEntropy(str) {
  const freq = {};
  for (const char of str) {
    freq[char] = (freq[char] || 0) + 1;
  }
  
  let entropy = 0;
  const len = str.length;
  
  for (const count of Object.values(freq)) {
    const p = count / len;
    entropy -= p * Math.log2(p);
  }
  
  return entropy * len;
}

/**
 * Analyze character set of a string
 * @param {string} str - String to analyze
 * @returns {Object} - Analysis results
 */
function analyzeCharacterSet(str) {
  const uniqueChars = new Set(str).size;
  const base64urlChars = /^[A-Za-z0-9_-]+$/;
  const isBase64url = base64urlChars.test(str);
  
  return {
    uniqueChars,
    isBase64url,
    length: str.length
  };
}

/**
 * Main function
 */
function main() {
  try {
    const success = testJWTSecrets();
    process.exit(success ? 0 : 1);
  } catch (error) {
    console.error('❌ Error testing JWT secrets:', error.message);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  testJWTSecrets,
  estimateEntropy,
  analyzeCharacterSet
};
