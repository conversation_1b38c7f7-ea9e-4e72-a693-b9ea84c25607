import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MulterModule } from '@nestjs/platform-express';
import { ConfigModule, ConfigService } from '@nestjs/config';

// Entities
import {
  Employee,
  Department,
  Position,
  EmployeeDocument,
  EmployeeContact,
  EmployeeAddress,
  EmployeeEmergencyContact,
  EmployeeEducation,
  EmployeeExperience,
  EmployeeSkill,
  EmployeeCertification,
  User,
  Tenant,
  AuditLog,
} from '@app/database';

// Controllers
import { EmployeeController } from './employee.controller';
import { EmployeeDocumentController } from './employee-document.controller';
import { EmployeeProfileController } from './employee-profile.controller';

// Services
import { EmployeeService } from './services/employee.service';
import { EmployeeDocumentService } from './services/employee-document.service';
import { EmployeeProfileService } from './services/employee-profile.service';
import { EmployeeValidationService } from './services/employee-validation.service';
import { EmployeeSearchService } from './services/employee-search.service';

@Module({
  imports: [
    // TypeORM entities
    TypeOrmModule.forFeature([
      Employee,
      Department,
      Position,
      EmployeeDocument,
      EmployeeContact,
      EmployeeAddress,
      EmployeeEmergencyContact,
      EmployeeEducation,
      EmployeeExperience,
      EmployeeSkill,
      EmployeeCertification,
      User,
      Tenant,
      AuditLog,
    ]),

    // File upload configuration
    MulterModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        dest: configService.get('UPLOAD_DESTINATION', './uploads'),
        limits: {
          fileSize: configService.get('MAX_FILE_SIZE', 10 * 1024 * 1024), // 10MB
          files: configService.get('MAX_FILES_PER_REQUEST', 10),
        },
        fileFilter: (req, file, callback) => {
          // Allow common document types
          const allowedMimes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'image/jpeg',
            'image/png',
            'image/gif',
            'text/plain',
          ];
          
          if (allowedMimes.includes(file.mimetype)) {
            callback(null, true);
          } else {
            callback(new Error('Invalid file type'), false);
          }
        },
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [
    EmployeeController,
    EmployeeDocumentController,
    EmployeeProfileController,
  ],
  providers: [
    EmployeeService,
    EmployeeDocumentService,
    EmployeeProfileService,
    EmployeeValidationService,
    EmployeeSearchService,
  ],
  exports: [
    EmployeeService,
    EmployeeDocumentService,
    EmployeeProfileService,
    EmployeeValidationService,
    EmployeeSearchService,
  ],
})
export class EmployeeModule {}
