import { ArgumentDefinition, EnumType, InputFieldDefinition, InputType, ScalarType, Schema, VariableCollector, VariableDefinitions } from './definitions';
import { ArgumentNode, ValueNode, ConstValueNode } from 'graphql';
export declare function valueToString(v: any, expectedType?: InputType): string;
export declare function valueEquals(a: any, b: any): boolean;
export declare function argumentsEquals(args1: {
    [key: string]: any;
}, args2: {
    [key: string]: any;
}): boolean;
export declare function withDefaultValues(value: any, argument: ArgumentDefinition<any>): any;
export declare function valueNodeToConstValueNode(value: ValueNode): ConstValueNode;
export declare function valueToAST(value: any, type: InputType): ValueNode | undefined;
export declare function isValidValue(value: any, argument: ArgumentDefinition<any> | InputFieldDefinition, variableDefinitions: VariableDefinitions): boolean;
export declare function isValidValueApplication(value: any, locationType: InputType, locationDefault: any, variableDefinitions: VariableDefinitions): boolean;
export declare function valueFromAST(node: ValueNode, expectedType: InputType): any;
export declare function valueFromASTUntyped(node: ValueNode): any;
export declare function isValidLeafValue(schema: Schema, value: any, type: ScalarType | EnumType): boolean;
export declare function argumentsFromAST(context: string, args: readonly ArgumentNode[] | undefined, argsDefiner: {
    argument(name: string): ArgumentDefinition<any> | undefined;
}): {
    [key: string]: any;
} | undefined;
export declare function collectVariablesInValue(value: any, collector: VariableCollector): void;
//# sourceMappingURL=values.d.ts.map