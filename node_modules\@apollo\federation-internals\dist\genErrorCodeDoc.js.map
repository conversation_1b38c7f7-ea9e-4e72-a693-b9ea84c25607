{"version": 3, "file": "genErrorCodeDoc.js", "sourceRoot": "", "sources": ["../src/genErrorCodeDoc.ts"], "names": [], "mappings": ";;AAAA,mCAAiC;AACjC,mCAAiD;AAEjD,MAAM,MAAM,GAAG;;;;;;;;;;;;CAYd,CAAC;AAEF,SAAS,iBAAiB,CACxB,OAAiB,EACjB,IAAgB;IAEhB,MAAM,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;IAC/B,IAAI,GAAG,GAAG,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC;IAC9C,GAAG,IAAI,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;IACvD,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,IAAA,cAAM,EAAC,GAAG,CAAC,MAAM,IAAI,OAAO,EAAE,QAAQ,GAAG,6BAA6B,OAAO,YAAY,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;QACxG,MAAM,IAAI,GAAG,GAAG,CAAC,MAAM,KAAK,OAAO;YACjC,CAAC,CAAC,GAAG;YACL,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,KAAK,CAAS,OAAO,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;QACjE,GAAG,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,MAAM,CAAA;IACzC,CAAC;IACD,OAAO,GAAG,CAAC;AACb,CAAC;AAED,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,cAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;IAC5C,GAAG,GAAG,GAAG,CAAC,IAAI,GAAG,GAAG;IACpB,GAAG,CAAC,WAAW;IACf,GAAG,CAAC,QAAQ,CAAC,OAAO;IACpB,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,aAAa,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;CACrG,CAAC,CAAC;AAEH,MAAM,cAAc,GAAG,CAAC,EAAY,EAAE,EAAY,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAElF,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;AAE1B,MAAM,aAAa,GAAG;;;;;;EAMpB,iBAAiB,CACjB,CAAE,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,SAAS,CAAE,EAC7C,IAAI,CACL;OACM,CAAC;AAER,MAAM,aAAa,GAAG,sBAAc;KACjC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,GAAG,EAAE,OAAO,CAAC,CAAC;KACrD,IAAI,CAAC,cAAc,CAAC,CAAC;AAExB,MAAM,cAAc,GAAG;;;;;;EAMrB,iBAAiB,CAAC,CAAC,cAAc,EAAE,SAAS,CAAC,EAAE,aAAa,CAAC;OACxD,CAAC;AAER,OAAO,CAAC,GAAG,CACT,MAAM,GAAG,MAAM;MACb,aAAa,GAAG,MAAM;MACtB,cAAc,CACjB,CAAC"}