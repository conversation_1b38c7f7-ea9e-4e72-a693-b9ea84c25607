import { GatewayGraphQLResponse } from '@apollo/server-gateway-interface';
import { GraphQLSchema, DocumentNode } from 'graphql';
import { GraphQLDataSource, GraphQLDataSourceProcessOptions } from './types';
export declare class LocalGraphQLDataSource<TContext extends Record<string, any> = Record<string, any>> implements GraphQLDataSource<TContext> {
    readonly schema: GraphQLSchema;
    constructor(schema: GraphQLSchema);
    process({ request, context, }: GraphQLDataSourceProcessOptions<TContext>): Promise<GatewayGraphQLResponse>;
    sdl(): DocumentNode;
}
//# sourceMappingURL=LocalGraphQLDataSource.d.ts.map