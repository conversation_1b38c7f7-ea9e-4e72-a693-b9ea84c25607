{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2022", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strict": true, "strictNullChecks": true, "noImplicitAny": true, "strictBindCallApply": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "paths": {"@app/common": ["libs/common/src"], "@app/common/*": ["libs/common/src/*"], "@app/database": ["libs/database/src"], "@app/database/*": ["libs/database/src/*"], "@app/security": ["libs/security/src"], "@app/security/*": ["libs/security/src/*"]}}}