#!/bin/bash
# =============================================================================
# PeopleNest HRMS - Development Environment Stop Script (Bash)
# =============================================================================
# This script stops all running services for the PeopleNest HRMS system
# =============================================================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Default options
INFRASTRUCTURE_ONLY=false
APPLICATION_ONLY=false
FORCE=false
VERBOSE=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --infrastructure-only)
            INFRASTRUCTURE_ONLY=true
            shift
            ;;
        --application-only)
            APPLICATION_ONLY=true
            shift
            ;;
        --force)
            FORCE=true
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --infrastructure-only  Stop only Docker infrastructure"
            echo "  --application-only     Stop only application services"
            echo "  --force               Force kill processes and cleanup"
            echo "  --verbose             Show detailed process information"
            echo "  --help                Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option $1"
            exit 1
            ;;
    esac
done

function print_color() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

function print_header() {
    local title=$1
    echo ""
    print_color $BLUE "================================================================================"
    print_color $CYAN "  $title"
    print_color $BLUE "================================================================================"
    echo ""
}

function stop_tmux_sessions() {
    print_color $YELLOW "🛑 Stopping tmux sessions..."
    
    local sessions=("peoplenest-backend" "peoplenest-frontend" "peoplenest-ai")
    
    for session in "${sessions[@]}"; do
        if tmux has-session -t "$session" 2>/dev/null; then
            print_color $YELLOW "  Stopping tmux session: $session"
            tmux kill-session -t "$session"
            print_color $GREEN "  ✅ Session $session stopped"
        else
            print_color $CYAN "  ℹ️  Session $session not found"
        fi
    done
}

function stop_background_processes() {
    print_color $YELLOW "🛑 Stopping background processes..."
    
    local pid_files=("logs/backend.pid" "logs/frontend.pid" "logs/ai-services.pid")
    
    for pid_file in "${pid_files[@]}"; do
        if [ -f "$pid_file" ]; then
            local pid=$(cat "$pid_file")
            local service_name=$(basename "$pid_file" .pid)
            
            if kill -0 "$pid" 2>/dev/null; then
                print_color $YELLOW "  Stopping $service_name (PID: $pid)"
                if [ "$FORCE" = true ]; then
                    kill -9 "$pid" 2>/dev/null || true
                else
                    kill "$pid" 2>/dev/null || true
                fi
                print_color $GREEN "  ✅ $service_name stopped"
            else
                print_color $CYAN "  ℹ️  $service_name process not running"
            fi
            
            rm -f "$pid_file"
        fi
    done
}

function stop_node_processes() {
    print_color $YELLOW "🛑 Stopping Node.js processes..."
    
    local node_pids=$(pgrep -f "node.*peoplenest\|npm.*dev\|npm.*start:dev" 2>/dev/null || true)
    
    if [ -n "$node_pids" ]; then
        for pid in $node_pids; do
            local cmd=$(ps -p "$pid" -o cmd= 2>/dev/null || echo "unknown")
            print_color $YELLOW "  Stopping Node.js process $pid: $cmd"
            
            if [ "$FORCE" = true ]; then
                kill -9 "$pid" 2>/dev/null || true
            else
                kill "$pid" 2>/dev/null || true
            fi
        done
        print_color $GREEN "✅ Node.js processes stopped"
    else
        print_color $CYAN "ℹ️  No Node.js processes found"
    fi
}

function stop_python_processes() {
    print_color $YELLOW "🛑 Stopping Python processes..."
    
    local python_pids=$(pgrep -f "python.*uvicorn\|python.*ai-services" 2>/dev/null || true)
    
    if [ -n "$python_pids" ]; then
        for pid in $python_pids; do
            local cmd=$(ps -p "$pid" -o cmd= 2>/dev/null || echo "unknown")
            print_color $YELLOW "  Stopping Python process $pid: $cmd"
            
            if [ "$FORCE" = true ]; then
                kill -9 "$pid" 2>/dev/null || true
            else
                kill "$pid" 2>/dev/null || true
            fi
        done
        print_color $GREEN "✅ Python processes stopped"
    else
        print_color $CYAN "ℹ️  No Python processes found"
    fi
}

function stop_infrastructure() {
    print_header "Stopping Infrastructure Services"
    
    print_color $YELLOW "🛑 Stopping Docker infrastructure..."
    
    if docker-compose down; then
        print_color $GREEN "✅ Infrastructure services stopped successfully!"
    else
        print_color $YELLOW "⚠️  Some issues occurred while stopping infrastructure"
    fi
    
    # Optionally remove volumes and networks
    if [ "$FORCE" = true ]; then
        print_color $YELLOW "🧹 Cleaning up volumes and networks..."
        if docker-compose down -v --remove-orphans; then
            print_color $GREEN "✅ Cleanup completed"
        else
            print_color $YELLOW "⚠️  Cleanup had some issues"
        fi
    fi
}

function stop_application_services() {
    print_header "Stopping Application Services"
    
    # Stop tmux sessions if tmux is available
    if command -v tmux > /dev/null 2>&1; then
        stop_tmux_sessions
    fi
    
    # Stop background processes
    stop_background_processes
    
    # Stop any remaining Node.js and Python processes
    stop_node_processes
    stop_python_processes
    
    # Clean up log files if force is enabled
    if [ "$FORCE" = true ] && [ -d "logs" ]; then
        print_color $YELLOW "🧹 Cleaning up log files..."
        rm -f logs/*.log logs/*.pid
        print_color $GREEN "✅ Log files cleaned up"
    fi
}

function show_remaining_processes() {
    print_header "Checking for Remaining Processes"
    
    local remaining_processes=()
    
    # Check for Node.js processes
    local node_pids=$(pgrep -f "node.*peoplenest\|npm.*dev\|npm.*start:dev" 2>/dev/null || true)
    if [ -n "$node_pids" ]; then
        for pid in $node_pids; do
            remaining_processes+=("Node.js:$pid")
        done
    fi
    
    # Check for Python processes
    local python_pids=$(pgrep -f "python.*uvicorn\|python.*ai-services" 2>/dev/null || true)
    if [ -n "$python_pids" ]; then
        for pid in $python_pids; do
            remaining_processes+=("Python:$pid")
        done
    fi
    
    if [ ${#remaining_processes[@]} -gt 0 ]; then
        print_color $YELLOW "⚠️  Found ${#remaining_processes[@]} remaining processes:"
        for proc in "${remaining_processes[@]}"; do
            IFS=':' read -r name pid <<< "$proc"
            print_color $RED "  PID $pid: $name"
            if [ "$VERBOSE" = true ]; then
                local cmd=$(ps -p "$pid" -o cmd= 2>/dev/null || echo "unknown")
                print_color $CYAN "    Command: $cmd"
            fi
        done
        echo ""
        print_color $CYAN "💡 To force kill remaining processes, run with --force flag"
    else
        print_color $GREEN "✅ No remaining application processes found"
    fi
}

function show_docker_status() {
    print_header "Docker Services Status"
    
    local containers=$(docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" 2>/dev/null | grep peoplenest || true)
    
    if [ -n "$containers" ]; then
        print_color $YELLOW "🐳 Running Docker containers:"
        echo "$containers" | while read -r line; do
            print_color $CYAN "  $line"
        done
    else
        print_color $GREEN "✅ No PeopleNest Docker containers running"
    fi
}

function show_tmux_status() {
    if command -v tmux > /dev/null 2>&1; then
        print_header "Tmux Sessions Status"
        
        local sessions=$(tmux list-sessions 2>/dev/null | grep peoplenest || true)
        
        if [ -n "$sessions" ]; then
            print_color $YELLOW "📺 Running tmux sessions:"
            echo "$sessions" | while read -r line; do
                print_color $CYAN "  $line"
            done
        else
            print_color $GREEN "✅ No PeopleNest tmux sessions running"
        fi
    fi
}

# Main execution
print_header "PeopleNest HRMS - Development Environment Stop"

if [ "$INFRASTRUCTURE_ONLY" = true ]; then
    stop_infrastructure
elif [ "$APPLICATION_ONLY" = true ]; then
    stop_application_services
else
    # Stop both application and infrastructure
    stop_application_services
    stop_infrastructure
fi

# Show status
if [ "$INFRASTRUCTURE_ONLY" = false ]; then
    show_remaining_processes
    show_tmux_status
fi

if [ "$APPLICATION_ONLY" = false ]; then
    show_docker_status
fi

print_header "🛑 Stop Process Completed"

if [ "$FORCE" = true ]; then
    print_color $GREEN "All services have been forcefully stopped and cleaned up."
else
    print_color $GREEN "Services have been gracefully stopped."
    print_color $CYAN "Use --force flag for forceful shutdown and cleanup."
fi

echo ""
print_color $YELLOW "📋 Available options:"
print_color $CYAN "  --infrastructure-only  : Stop only Docker infrastructure"
print_color $CYAN "  --application-only     : Stop only application services"
print_color $CYAN "  --force               : Force kill processes and cleanup"
print_color $CYAN "  --verbose             : Show detailed process information"
