import { DirectiveLocation, GraphQLError } from "graphql";
import { FeatureDefinition, FeatureDefinitions, FeatureVersion } from "./coreSpec";
import { DirectiveDefinition } from "../definitions";
import { DirectiveSpecification } from "../directiveAndTypeSpecification";
export declare const tagIdentity = "https://specs.apollo.dev/tag";
export declare class TagSpecDefinition extends FeatureDefinition {
    readonly tagLocations: DirectiveLocation[];
    readonly tagDirectiveSpec: DirectiveSpecification;
    private readonly printedTagDefinition;
    constructor(version: FeatureVersion, minimumFederationVersion?: FeatureVersion);
    private isV01;
    private isV02;
    checkCompatibleDirective(definition: DirectiveDefinition): GraphQLError | undefined;
}
export declare const TAG_VERSIONS: FeatureDefinitions<TagSpecDefinition>;
//# sourceMappingURL=tagSpec.d.ts.map