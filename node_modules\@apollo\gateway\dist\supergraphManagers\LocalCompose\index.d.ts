import type { Logger } from '@apollo/utils.logger';
import { SupergraphSdlHookOptions, SupergraphManager } from '../../config';
import { ServiceDefinition } from '@apollo/federation-internals';
export interface LocalComposeOptions {
    logger?: Logger;
    localServiceList: ServiceDefinition[];
}
export declare class LocalCompose implements SupergraphManager {
    private config;
    private getDataSource?;
    constructor(options: LocalComposeOptions);
    private issueDeprecationWarnings;
    initialize({ getDataSource }: SupergraphSdlHookOptions): Promise<{
        supergraphSdl: string;
    }>;
    private createSupergraphFromServiceList;
    private logUpdateFailure;
}
//# sourceMappingURL=index.d.ts.map