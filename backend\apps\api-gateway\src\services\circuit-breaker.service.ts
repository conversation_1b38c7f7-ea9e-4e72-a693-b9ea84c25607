import { Injectable, Logger } from '@nestjs/common';

export enum CircuitState {
  CLOSED = 'closed',
  OPEN = 'open',
  HALF_OPEN = 'half-open',
}

interface CircuitBreakerConfig {
  failureThreshold: number;
  recoveryTimeout: number;
  monitoringPeriod: number;
  expectedErrorRate: number;
}

interface CircuitStats {
  state: CircuitState;
  failureCount: number;
  successCount: number;
  lastFailureTime: Date | null;
  lastSuccessTime: Date | null;
  nextAttemptTime: Date | null;
  totalRequests: number;
  totalFailures: number;
}

@Injectable()
export class CircuitBreakerService {
  private readonly logger = new Logger(CircuitBreakerService.name);
  private readonly circuits = new Map<string, CircuitStats>();
  private readonly configs = new Map<string, CircuitBreakerConfig>();

  constructor() {
    // Default configuration
    this.setDefaultConfigs();
  }

  private setDefaultConfigs(): void {
    const defaultConfig: CircuitBreakerConfig = {
      failureThreshold: 5,
      recoveryTimeout: 60000, // 1 minute
      monitoringPeriod: 10000, // 10 seconds
      expectedErrorRate: 0.5, // 50%
    };

    // Set default configs for known services
    const services = [
      'auth-service',
      'employee-service',
      'payroll-service',
      'performance-service',
      'notification-service',
      'ai-service',
      'api-service',
    ];

    services.forEach(service => {
      this.configs.set(service, { ...defaultConfig });
    });
  }

  async executeWithCircuitBreaker<T>(
    serviceName: string,
    operation: () => Promise<T>,
    fallback?: () => Promise<T>
  ): Promise<T> {
    const circuit = this.getOrCreateCircuit(serviceName);
    const config = this.getConfig(serviceName);

    // Check if circuit is open
    if (circuit.state === CircuitState.OPEN) {
      if (this.shouldAttemptReset(circuit, config)) {
        circuit.state = CircuitState.HALF_OPEN;
        this.logger.log(`Circuit breaker for ${serviceName} moved to HALF_OPEN state`);
      } else {
        this.logger.warn(`Circuit breaker for ${serviceName} is OPEN, using fallback`);
        if (fallback) {
          return await fallback();
        }
        throw new Error(`Service ${serviceName} is currently unavailable (circuit breaker open)`);
      }
    }

    try {
      const result = await operation();
      this.recordSuccess(serviceName);
      return result;
    } catch (error) {
      this.recordFailure(serviceName);
      
      if (fallback) {
        this.logger.warn(`Operation failed for ${serviceName}, using fallback: ${(error as Error).message}`);
        return await fallback();
      }
      
      throw error;
    }
  }

  recordSuccess(serviceName: string): void {
    const circuit = this.getOrCreateCircuit(serviceName);
    
    circuit.successCount++;
    circuit.totalRequests++;
    circuit.lastSuccessTime = new Date();

    if (circuit.state === CircuitState.HALF_OPEN) {
      // Reset circuit if we get a success in half-open state
      circuit.state = CircuitState.CLOSED;
      circuit.failureCount = 0;
      this.logger.log(`Circuit breaker for ${serviceName} reset to CLOSED state`);
    }
  }

  recordFailure(serviceName: string): void {
    const circuit = this.getOrCreateCircuit(serviceName);
    const config = this.getConfig(serviceName);
    
    circuit.failureCount++;
    circuit.totalFailures++;
    circuit.totalRequests++;
    circuit.lastFailureTime = new Date();

    if (circuit.state === CircuitState.HALF_OPEN) {
      // Go back to open if we fail in half-open state
      circuit.state = CircuitState.OPEN;
      circuit.nextAttemptTime = new Date(Date.now() + config.recoveryTimeout);
      this.logger.warn(`Circuit breaker for ${serviceName} moved back to OPEN state`);
    } else if (circuit.state === CircuitState.CLOSED && circuit.failureCount >= config.failureThreshold) {
      // Open circuit if failure threshold is reached
      circuit.state = CircuitState.OPEN;
      circuit.nextAttemptTime = new Date(Date.now() + config.recoveryTimeout);
      this.logger.warn(`Circuit breaker for ${serviceName} opened due to ${circuit.failureCount} failures`);
    }
  }

  getCircuitState(serviceName: string): CircuitState {
    const circuit = this.getOrCreateCircuit(serviceName);
    return circuit.state;
  }

  getCircuitStats(serviceName: string): CircuitStats {
    return { ...this.getOrCreateCircuit(serviceName) };
  }

  getAllCircuitStats(): Map<string, CircuitStats> {
    const stats = new Map<string, CircuitStats>();
    for (const [serviceName, circuit] of this.circuits.entries()) {
      stats.set(serviceName, { ...circuit });
    }
    return stats;
  }

  resetCircuit(serviceName: string): void {
    const circuit = this.getOrCreateCircuit(serviceName);
    circuit.state = CircuitState.CLOSED;
    circuit.failureCount = 0;
    circuit.successCount = 0;
    circuit.lastFailureTime = null;
    circuit.lastSuccessTime = null;
    circuit.nextAttemptTime = null;
    
    this.logger.log(`Circuit breaker for ${serviceName} manually reset`);
  }

  configureCircuit(serviceName: string, config: Partial<CircuitBreakerConfig>): void {
    const currentConfig = this.getConfig(serviceName);
    const newConfig = { ...currentConfig, ...config };
    this.configs.set(serviceName, newConfig);
    
    this.logger.log(`Circuit breaker configuration updated for ${serviceName}`);
  }

  private getOrCreateCircuit(serviceName: string): CircuitStats {
    if (!this.circuits.has(serviceName)) {
      this.circuits.set(serviceName, {
        state: CircuitState.CLOSED,
        failureCount: 0,
        successCount: 0,
        lastFailureTime: null,
        lastSuccessTime: null,
        nextAttemptTime: null,
        totalRequests: 0,
        totalFailures: 0,
      });
    }
    return this.circuits.get(serviceName)!;
  }

  private getConfig(serviceName: string): CircuitBreakerConfig {
    if (!this.configs.has(serviceName)) {
      const defaultConfig: CircuitBreakerConfig = {
        failureThreshold: 5,
        recoveryTimeout: 60000,
        monitoringPeriod: 10000,
        expectedErrorRate: 0.5,
      };
      this.configs.set(serviceName, defaultConfig);
    }
    return this.configs.get(serviceName)!;
  }

  private shouldAttemptReset(circuit: CircuitStats, config: CircuitBreakerConfig): boolean {
    if (!circuit.nextAttemptTime) {
      return true;
    }
    return Date.now() >= circuit.nextAttemptTime.getTime();
  }
}
