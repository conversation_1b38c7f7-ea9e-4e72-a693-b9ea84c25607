import { <PERSON>, Post, Param, UseGuards, Request, ParseUUIDPipe } from '@nestjs/common';
import { ApiTags, ApiBearerAuth } from '@nestjs/swagger';

import { JwtAuthGuard, RolesGuard } from '@app/security/guards';
import { Roles } from '@app/security/decorators/roles.decorator';
import { UserRole } from '@app/common';
import { RequestWithUser } from '@app/security';

import { EmployeeOnboardingService } from '../services/employee-onboarding.service';

@ApiTags('Employee Onboarding')
@Controller('employee-onboarding')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
export class EmployeeOnboardingController {
  constructor(private readonly onboardingService: EmployeeOnboardingService) {}

  @Post(':employeeId/start')
  @Roles(UserRole.ADMIN, UserRole.HR, UserRole.SUPER_ADMIN)
  async startOnboarding(
    @Param('employeeId', ParseUUIDPipe) employeeId: string,
    @Request() req: RequestWithUser,
  ) {
    return this.onboardingService.startOnboardingProcess(employeeId, req.user.tenantId);
  }
}
