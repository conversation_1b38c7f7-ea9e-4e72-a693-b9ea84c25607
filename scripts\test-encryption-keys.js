#!/usr/bin/env node

/**
 * Encryption Keys Test Script
 * Tests that the encryption keys are properly configured and working
 */

const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

/**
 * Test encryption key configuration
 */
function testEncryptionKeys() {
  console.log('🔐 Testing Encryption Key Configuration');
  console.log('======================================\n');

  const encryptionKey = process.env.ENCRYPTION_KEY;
  const masterKey = process.env.ENCRYPTION_MASTER_KEY;
  const indexSalt = process.env.INDEX_HASH_SALT;
  const algorithm = process.env.ENCRYPTION_ALGORITHM;
  const saltRounds = process.env.HASH_SALT_ROUNDS;

  // Test 1: Check if keys exist
  console.log('📋 Test 1: Key Existence');
  if (!encryptionKey) {
    console.log('❌ ENCRYPTION_KEY is not set in environment');
    return false;
  }
  if (!masterKey) {
    console.log('❌ ENCRYPTION_MASTER_KEY is not set in environment');
    return false;
  }
  if (!indexSalt) {
    console.log('❌ INDEX_HASH_SALT is not set in environment');
    return false;
  }
  console.log('✅ All encryption keys are present in environment\n');

  // Test 2: Check key lengths
  console.log('📋 Test 2: Key Length Validation');
  console.log(`   ENCRYPTION_KEY length: ${encryptionKey.length} characters (${encryptionKey.length/2} bytes)`);
  console.log(`   ENCRYPTION_MASTER_KEY length: ${masterKey.length} characters (${masterKey.length/2} bytes)`);
  console.log(`   INDEX_HASH_SALT length: ${indexSalt.length} characters (${indexSalt.length/2} bytes)`);
  
  // AES-256-GCM requires 32-byte key (64 hex characters)
  if (encryptionKey.length !== 64) {
    console.log('❌ ENCRYPTION_KEY should be 64 hex characters (32 bytes) for AES-256-GCM');
    return false;
  } else {
    console.log('✅ ENCRYPTION_KEY has correct length for AES-256-GCM');
  }
  
  // Master key should be 64 bytes (128 hex characters)
  if (masterKey.length !== 128) {
    console.log('❌ ENCRYPTION_MASTER_KEY should be 128 hex characters (64 bytes)');
    return false;
  } else {
    console.log('✅ ENCRYPTION_MASTER_KEY has correct length');
  }
  
  // Index salt should be 32 bytes (64 hex characters)
  if (indexSalt.length !== 64) {
    console.log('❌ INDEX_HASH_SALT should be 64 hex characters (32 bytes)');
    return false;
  } else {
    console.log('✅ INDEX_HASH_SALT has correct length');
  }
  console.log('');

  // Test 3: Check if keys are valid hex
  console.log('📋 Test 3: Hex Format Validation');
  const hexPattern = /^[0-9a-fA-F]+$/;
  
  if (!hexPattern.test(encryptionKey)) {
    console.log('❌ ENCRYPTION_KEY is not valid hexadecimal');
    return false;
  } else {
    console.log('✅ ENCRYPTION_KEY is valid hexadecimal');
  }
  
  if (!hexPattern.test(masterKey)) {
    console.log('❌ ENCRYPTION_MASTER_KEY is not valid hexadecimal');
    return false;
  } else {
    console.log('✅ ENCRYPTION_MASTER_KEY is valid hexadecimal');
  }
  
  if (!hexPattern.test(indexSalt)) {
    console.log('❌ INDEX_HASH_SALT is not valid hexadecimal');
    return false;
  } else {
    console.log('✅ INDEX_HASH_SALT is valid hexadecimal');
  }
  console.log('');

  // Test 4: Check if keys are unique
  console.log('📋 Test 4: Key Uniqueness');
  if (encryptionKey === masterKey || encryptionKey === indexSalt || masterKey === indexSalt) {
    console.log('❌ Encryption keys are not unique (security risk)');
    return false;
  }
  console.log('✅ All encryption keys are unique\n');

  // Test 5: Test actual encryption/decryption
  console.log('📋 Test 5: Encryption/Decryption Functionality');
  try {
    const testData = 'PeopleNest HRMS - Test encryption data';
    const encrypted = encryptData(testData, encryptionKey);
    const decrypted = decryptData(encrypted, encryptionKey);
    
    if (decrypted === testData) {
      console.log('✅ Encryption/decryption test successful');
      console.log(`   Original: "${testData}"`);
      console.log(`   Encrypted: ${encrypted.substring(0, 32)}...`);
      console.log(`   Decrypted: "${decrypted}"`);
    } else {
      console.log('❌ Encryption/decryption test failed');
      return false;
    }
  } catch (error) {
    console.log('❌ Encryption/decryption test failed:', error.message);
    return false;
  }
  console.log('');

  // Test 6: Test hash salt functionality
  console.log('📋 Test 6: Hash Salt Functionality');
  try {
    const testValue = '<EMAIL>';
    const hash1 = hashWithSalt(testValue, indexSalt);
    const hash2 = hashWithSalt(testValue, indexSalt);
    
    if (hash1 === hash2) {
      console.log('✅ Hash salt produces consistent results');
      console.log(`   Input: "${testValue}"`);
      console.log(`   Hash: ${hash1.substring(0, 32)}...`);
    } else {
      console.log('❌ Hash salt produces inconsistent results');
      return false;
    }
  } catch (error) {
    console.log('❌ Hash salt test failed:', error.message);
    return false;
  }
  console.log('');

  // Test 7: Algorithm configuration
  console.log('📋 Test 7: Algorithm Configuration');
  if (algorithm !== 'aes-256-gcm') {
    console.log('⚠️  ENCRYPTION_ALGORITHM is not set to recommended aes-256-gcm');
  } else {
    console.log('✅ ENCRYPTION_ALGORITHM is correctly set to aes-256-gcm');
  }
  
  const rounds = parseInt(saltRounds);
  if (isNaN(rounds) || rounds < 10) {
    console.log('⚠️  HASH_SALT_ROUNDS should be at least 10 for security');
  } else {
    console.log(`✅ HASH_SALT_ROUNDS is set to ${rounds} (adequate for security)`);
  }

  console.log('\n🎉 Encryption Key Configuration Test Complete!');
  console.log('✅ All tests passed - Encryption keys are properly configured');
  
  return true;
}

/**
 * Encrypt data using AES-256-GCM
 * @param {string} text - Text to encrypt
 * @param {string} keyHex - Encryption key in hex format
 * @returns {string} - Encrypted data in format: iv:authTag:encryptedData
 */
function encryptData(text, keyHex) {
  const key = Buffer.from(keyHex, 'hex');
  const iv = crypto.randomBytes(16); // 128-bit IV for GCM
  const cipher = crypto.createCipheriv('aes-256-gcm', key, iv);
  cipher.setAAD(Buffer.from('PeopleNest-HRMS', 'utf8')); // Additional authenticated data

  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');

  const authTag = cipher.getAuthTag();

  return `${iv.toString('hex')}:${authTag.toString('hex')}:${encrypted}`;
}

/**
 * Decrypt data using AES-256-GCM
 * @param {string} encryptedData - Encrypted data in format: iv:authTag:encryptedData
 * @param {string} keyHex - Encryption key in hex format
 * @returns {string} - Decrypted text
 */
function decryptData(encryptedData, keyHex) {
  const key = Buffer.from(keyHex, 'hex');
  const [ivHex, authTagHex, encrypted] = encryptedData.split(':');

  const iv = Buffer.from(ivHex, 'hex');
  const authTag = Buffer.from(authTagHex, 'hex');

  const decipher = crypto.createDecipheriv('aes-256-gcm', key, iv);
  decipher.setAuthTag(authTag);
  decipher.setAAD(Buffer.from('PeopleNest-HRMS', 'utf8'));

  let decrypted = decipher.update(encrypted, 'hex', 'utf8');
  decrypted += decipher.final('utf8');

  return decrypted;
}

/**
 * Hash value with salt
 * @param {string} value - Value to hash
 * @param {string} saltHex - Salt in hex format
 * @returns {string} - Hashed value
 */
function hashWithSalt(value, saltHex) {
  const salt = Buffer.from(saltHex, 'hex');
  const hash = crypto.createHash('sha256');
  hash.update(value);
  hash.update(salt);
  return hash.digest('hex');
}

/**
 * Main function
 */
function main() {
  try {
    const success = testEncryptionKeys();
    process.exit(success ? 0 : 1);
  } catch (error) {
    console.error('❌ Error testing encryption keys:', error.message);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  testEncryptionKeys,
  encryptData,
  decryptData,
  hashWithSalt
};
