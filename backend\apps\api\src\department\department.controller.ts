import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  ParseUUIDPipe,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
} from '@nestjs/swagger';

import { JwtAuthGuard, RolesGuard, TenantGuard } from '@app/security';
import { Roles, CurrentUser, CurrentTenant } from '@app/common';
import { UserRole } from '@app/common/enums/user-role.enum';
import { DepartmentService } from './services/department.service';
import { CreateDepartmentDto, UpdateDepartmentDto, DepartmentQueryDto } from './dto';

@ApiTags('Departments')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard, TenantGuard, RolesGuard)
@Controller('departments')
export class DepartmentController {
  constructor(private readonly departmentService: DepartmentService) {}

  @Post()
  @Roles(UserRole.ADMIN, UserRole.HR)
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ 
    summary: 'Create new department',
    description: 'Create a new department in the organization'
  })
  @ApiResponse({ 
    status: 201, 
    description: 'Department created successfully'
  })
  @ApiResponse({ 
    status: 400, 
    description: 'Invalid input data'
  })
  @ApiResponse({ 
    status: 403, 
    description: 'Insufficient permissions'
  })
  async create(
    @Body() createDepartmentDto: CreateDepartmentDto,
    @CurrentUser() user: any,
    @CurrentTenant() tenantId: string,
  ) {
    return this.departmentService.create(createDepartmentDto, user.id, tenantId);
  }

  @Get()
  @Roles(UserRole.ADMIN, UserRole.HR, UserRole.MANAGER, UserRole.EMPLOYEE)
  @ApiOperation({ 
    summary: 'Get all departments',
    description: 'Retrieve a list of all departments with optional filtering'
  })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'search', required: false, type: String })
  @ApiQuery({ name: 'includeInactive', required: false, type: Boolean })
  @ApiResponse({ 
    status: 200, 
    description: 'Departments retrieved successfully'
  })
  async findAll(
    @Query() query: DepartmentQueryDto,
    @CurrentTenant() tenantId: string,
  ) {
    return this.departmentService.findAll(query, tenantId);
  }

  @Get(':id')
  @Roles(UserRole.ADMIN, UserRole.HR, UserRole.MANAGER, UserRole.EMPLOYEE)
  @ApiOperation({ 
    summary: 'Get department by ID',
    description: 'Retrieve detailed information about a specific department'
  })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiResponse({ 
    status: 200, 
    description: 'Department found successfully'
  })
  @ApiResponse({ 
    status: 404, 
    description: 'Department not found'
  })
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentTenant() tenantId: string,
  ) {
    return this.departmentService.findOne(id, tenantId);
  }

  @Put(':id')
  @Roles(UserRole.ADMIN, UserRole.HR)
  @ApiOperation({ 
    summary: 'Update department',
    description: 'Update department information'
  })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiResponse({ 
    status: 200, 
    description: 'Department updated successfully'
  })
  @ApiResponse({ 
    status: 404, 
    description: 'Department not found'
  })
  @ApiResponse({ 
    status: 403, 
    description: 'Insufficient permissions'
  })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDepartmentDto: UpdateDepartmentDto,
    @CurrentUser() user: any,
    @CurrentTenant() tenantId: string,
  ) {
    return this.departmentService.update(id, updateDepartmentDto, user.id, tenantId);
  }

  @Delete(':id')
  @Roles(UserRole.ADMIN, UserRole.HR)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ 
    summary: 'Delete department',
    description: 'Soft delete a department'
  })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiResponse({ 
    status: 204, 
    description: 'Department deleted successfully'
  })
  @ApiResponse({ 
    status: 404, 
    description: 'Department not found'
  })
  @ApiResponse({ 
    status: 403, 
    description: 'Insufficient permissions'
  })
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() user: any,
    @CurrentTenant() tenantId: string,
  ) {
    return this.departmentService.remove(id, user.id, tenantId);
  }

  @Get(':id/employees')
  @Roles(UserRole.ADMIN, UserRole.HR, UserRole.MANAGER)
  @ApiOperation({ 
    summary: 'Get department employees',
    description: 'Retrieve all employees in a specific department'
  })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiResponse({ 
    status: 200, 
    description: 'Department employees retrieved successfully'
  })
  async getDepartmentEmployees(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentTenant() tenantId: string,
  ) {
    return this.departmentService.getDepartmentEmployees(id, tenantId);
  }

  @Get(':id/positions')
  @Roles(UserRole.ADMIN, UserRole.HR, UserRole.MANAGER)
  @ApiOperation({ 
    summary: 'Get department positions',
    description: 'Retrieve all positions in a specific department'
  })
  @ApiParam({ name: 'id', type: 'string', format: 'uuid' })
  @ApiResponse({ 
    status: 200, 
    description: 'Department positions retrieved successfully'
  })
  async getDepartmentPositions(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentTenant() tenantId: string,
  ) {
    return this.departmentService.getDepartmentPositions(id, tenantId);
  }
}
