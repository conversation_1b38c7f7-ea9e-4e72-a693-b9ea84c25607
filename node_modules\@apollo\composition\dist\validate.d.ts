import { CompositeType, FieldDefinition, Operation, Schema, SchemaRootKind } from "@apollo/federation-internals";
import { Edge, QueryGraph, RootPath, Transition, TransitionPathWithLazyIndirectPaths, RootVertex, ConditionResolver } from "@apollo/query-graphs";
import { CompositionHint } from "./hints";
import { GraphQLError } from "graphql";
import { CompositionOptions } from './compose';
export declare class ValidationError extends Error {
    readonly supergraphUnsatisfiablePath: RootPath<Transition>;
    readonly subgraphsPaths: RootPath<Transition>[];
    readonly witness: Operation;
    constructor(message: string, supergraphUnsatisfiablePath: RootPath<Transition>, subgraphsPaths: RootPath<Transition>[], witness: Operation);
}
export declare function validateGraphComposition(supergraphSchema: Schema, subgraphNameToGraphEnumValue: Map<string, string>, supergraphAPI: QueryGraph, federatedQueryGraph: QueryGraph, compositionOptions?: CompositionOptions): {
    errors?: GraphQLError[];
    hints?: CompositionHint[];
};
export declare function extractValidationError(error: any): ValidationError | undefined;
export declare class ValidationContext {
    readonly supergraphSchema: Schema;
    readonly subgraphNameToGraphEnumValue: Map<string, string>;
    private readonly joinTypeDirective;
    private readonly joinFieldDirective;
    private readonly typesToContexts;
    constructor(supergraphSchema: Schema, subgraphNameToGraphEnumValue: Map<string, string>);
    isShareable(field: FieldDefinition<CompositeType>): boolean;
    matchingContexts(typeName: string): string[];
}
type SubgraphPathInfo = {
    path: TransitionPathWithLazyIndirectPaths<RootVertex>;
    contexts: Map<string, {
        subgraphName: string;
        typeName: string;
    }>;
};
export declare class ValidationState {
    readonly supergraphPath: RootPath<Transition>;
    readonly subgraphPathInfos: SubgraphPathInfo[];
    selectedOverrideConditions: Map<string, boolean>;
    constructor(supergraphPath: RootPath<Transition>, subgraphPathInfos: SubgraphPathInfo[], selectedOverrideConditions?: Map<string, boolean>);
    static initial({ supergraphAPI, kind, federatedQueryGraph, conditionResolver, overrideConditions, }: {
        supergraphAPI: QueryGraph;
        kind: SchemaRootKind;
        federatedQueryGraph: QueryGraph;
        conditionResolver: ConditionResolver;
        overrideConditions: Map<string, boolean>;
    }): ValidationState;
    validateTransition(context: ValidationContext, supergraphEdge: Edge, matchingContexts: string[]): {
        state?: ValidationState;
        error?: GraphQLError;
        hint?: CompositionHint;
    };
    currentSubgraphNames(): string[];
    currentSubgraphContextKeys(subgraphNameToGraphEnumValue: Map<string, string>): Set<string>;
    currentSubgraphs(): {
        name: string;
        schema: Schema;
    }[];
    toString(): string;
}
export {};
//# sourceMappingURL=validate.d.ts.map