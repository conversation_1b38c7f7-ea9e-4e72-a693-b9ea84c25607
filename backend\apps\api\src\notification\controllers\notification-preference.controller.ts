import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards, ParseUUIDPipe } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard, RolesGuard, TenantGuard } from '@app/security';
import { Roles, CurrentUser, CurrentTenant } from '@app/common';
import { NotificationPreferenceService } from '../services/notification-preference.service';

@ApiTags('Notification Preferences')
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard, TenantGuard, RolesGuard)
@Controller('notification-preferences')
export class NotificationPreferenceController {
  constructor(private readonly notificationPreferenceService: NotificationPreferenceService) {}

  @Post()
  @Roles('admin', 'hr_manager', 'hr_user', 'manager', 'employee')
  @ApiOperation({ summary: 'Create notification preference' })
  async create(@Body() createPreferenceDto: any, @CurrentUser() user: any, @CurrentTenant() tenantId: string) {
    return this.notificationPreferenceService.create(createPreferenceDto, user.id, tenantId);
  }

  @Get()
  @Roles('admin', 'hr_manager', 'hr_user', 'manager', 'employee')
  @ApiOperation({ summary: 'Get notification preferences' })
  async findAll(@Query() query: any, @CurrentUser() user: any, @CurrentTenant() tenantId: string) {
    return this.notificationPreferenceService.findAll(query, user, tenantId);
  }

  @Get('my-preferences')
  @Roles('admin', 'hr_manager', 'hr_user', 'manager', 'employee')
  @ApiOperation({ summary: 'Get my notification preferences' })
  async getMyPreferences(@CurrentUser() user: any, @CurrentTenant() tenantId: string) {
    return this.notificationPreferenceService.getUserPreferences(user.id, tenantId);
  }

  @Get(':id')
  @Roles('admin', 'hr_manager', 'hr_user', 'manager', 'employee')
  @ApiOperation({ summary: 'Get notification preference by ID' })
  async findOne(@Param('id', ParseUUIDPipe) id: string, @CurrentUser() user: any, @CurrentTenant() tenantId: string) {
    return this.notificationPreferenceService.findOne(id, user, tenantId);
  }

  @Put(':id')
  @Roles('admin', 'hr_manager', 'hr_user', 'manager', 'employee')
  @ApiOperation({ summary: 'Update notification preference' })
  async update(@Param('id', ParseUUIDPipe) id: string, @Body() updatePreferenceDto: any, @CurrentUser() user: any, @CurrentTenant() tenantId: string) {
    return this.notificationPreferenceService.update(id, updatePreferenceDto, user.id, tenantId);
  }

  @Delete(':id')
  @Roles('admin', 'hr_manager', 'hr_user', 'manager', 'employee')
  @ApiOperation({ summary: 'Delete notification preference' })
  async remove(@Param('id', ParseUUIDPipe) id: string, @CurrentUser() user: any, @CurrentTenant() tenantId: string) {
    return this.notificationPreferenceService.remove(id, user.id, tenantId);
  }
}
