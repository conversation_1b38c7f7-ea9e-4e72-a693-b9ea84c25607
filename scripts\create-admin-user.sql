-- =============================================================================
-- PeopleNest HRMS - Create Default Admin User Script
-- =============================================================================
-- This script creates the default admin user 'awadhesh' with superadmin privileges
-- =============================================================================

-- Set search path
SET search_path TO public, tenant_default;

-- Variables for the admin user
DO $$
DECLARE
    tenant_uuid UUID := '00000000-0000-0000-0000-000000000001';
    admin_user_id UUID := uuid_generate_v4();
    admin_employee_id UUID := uuid_generate_v4();
    super_admin_role_id UUID;
    default_dept_id UUID := uuid_generate_v4();
    password_hash TEXT;
BEGIN
    -- Generate password hash for 'awadhesh123'
    password_hash := crypt('awadhesh123', gen_salt('bf', 12));
    
    -- Get Super Admin role ID
    SELECT id INTO super_admin_role_id 
    FROM roles 
    WHERE tenant_id = tenant_uuid AND name = 'Super Admin';
    
    -- Create default department if it doesn't exist
    INSERT INTO departments (
        id,
        tenant_id,
        name,
        description,
        status
    ) VALUES (
        default_dept_id,
        tenant_uuid,
        'Administration',
        'System Administration Department',
        'active'
    ) ON CONFLICT DO NOTHING;
    
    -- Create the admin user
    INSERT INTO users (
        id,
        tenant_id,
        username,
        email,
        password_hash,
        first_name,
        last_name,
        role,
        status,
        email_verified,
        created_at,
        updated_at
    ) VALUES (
        admin_user_id,
        tenant_uuid,
        'awadhesh',
        '<EMAIL>',
        password_hash,
        'Awadhesh',
        'Admin',
        'super_admin',
        'active',
        true,
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP
    ) ON CONFLICT (username) DO UPDATE SET
        password_hash = EXCLUDED.password_hash,
        updated_at = CURRENT_TIMESTAMP;
    
    -- Create employee record for the admin user
    INSERT INTO employees (
        id,
        user_id,
        tenant_id,
        employee_id,
        department_id,
        job_title,
        employment_type,
        employment_status,
        hire_date,
        salary,
        currency,
        pay_frequency,
        work_location,
        created_at,
        updated_at
    ) VALUES (
        admin_employee_id,
        admin_user_id,
        tenant_uuid,
        'EMP-000001',
        default_dept_id,
        'System Administrator',
        'full-time',
        'active',
        CURRENT_DATE,
        100000.00,
        'USD',
        'monthly',
        'Head Office',
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP
    ) ON CONFLICT (user_id) DO UPDATE SET
        updated_at = CURRENT_TIMESTAMP;
    
    -- Assign Super Admin role to the user
    INSERT INTO user_roles (
        user_id,
        role_id,
        assigned_at
    ) VALUES (
        admin_user_id,
        super_admin_role_id,
        CURRENT_TIMESTAMP
    ) ON CONFLICT (user_id, role_id) DO NOTHING;
    
    -- Log the admin user creation
    INSERT INTO audit.security_events (
        tenant_id,
        user_id,
        event_type,
        event_description,
        severity,
        additional_data,
        created_at
    ) VALUES (
        tenant_uuid,
        admin_user_id,
        'ADMIN_USER_CREATED',
        'Default admin user created during system initialization',
        'info',
        jsonb_build_object(
            'username', 'awadhesh',
            'email', '<EMAIL>',
            'role', 'super_admin'
        ),
        CURRENT_TIMESTAMP
    );
    
    RAISE NOTICE 'Admin user "awadhesh" created successfully with Super Admin privileges';
    RAISE NOTICE 'Login credentials: username=awadhesh, password=awadhesh123';
    RAISE NOTICE 'Email: <EMAIL>';
    
END $$;

-- Create additional sample data for development
DO $$
DECLARE
    tenant_uuid UUID := 'default-tenant-uuid-0000-0000-000000000001';
    hr_dept_id UUID := uuid_generate_v4();
    it_dept_id UUID := uuid_generate_v4();
    finance_dept_id UUID := uuid_generate_v4();
BEGIN
    -- Create sample departments
    INSERT INTO departments (id, tenant_id, name, description, status) VALUES
    (hr_dept_id, tenant_uuid, 'Human Resources', 'HR Department managing employee lifecycle', 'active'),
    (it_dept_id, tenant_uuid, 'Information Technology', 'IT Department managing technology infrastructure', 'active'),
    (finance_dept_id, tenant_uuid, 'Finance', 'Finance Department managing financial operations', 'active')
    ON CONFLICT DO NOTHING;
    
    RAISE NOTICE 'Sample departments created successfully';
END $$;

-- Grant necessary permissions
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO postgres;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA tenant_default TO postgres;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA audit TO postgres;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA analytics TO postgres;

GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO postgres;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA tenant_default TO postgres;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA audit TO postgres;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA analytics TO postgres;

-- Create database views for common queries
CREATE OR REPLACE VIEW v_employee_details AS
SELECT 
    e.id,
    e.employee_id,
    u.username,
    u.email,
    u.first_name,
    u.last_name,
    e.job_title,
    d.name as department_name,
    e.employment_type,
    e.employment_status,
    e.hire_date,
    e.salary,
    e.currency,
    m.first_name || ' ' || m.last_name as manager_name,
    e.created_at,
    e.updated_at
FROM employees e
JOIN users u ON e.user_id = u.id
LEFT JOIN departments d ON e.department_id = d.id
LEFT JOIN employees me ON e.manager_id = me.id
LEFT JOIN users m ON me.user_id = m.id;

CREATE OR REPLACE VIEW v_user_roles AS
SELECT 
    u.id as user_id,
    u.username,
    u.email,
    u.first_name,
    u.last_name,
    r.name as role_name,
    r.description as role_description,
    r.permissions,
    ur.assigned_at,
    ur.expires_at
FROM users u
JOIN user_roles ur ON u.id = ur.user_id
JOIN roles r ON ur.role_id = r.id;

-- Create indexes on views
CREATE INDEX IF NOT EXISTS idx_v_employee_details_employee_id ON employees(employee_id);
CREATE INDEX IF NOT EXISTS idx_v_employee_details_department ON employees(department_id);

COMMIT;

-- Display success message
SELECT 
    'PeopleNest HRMS Database Initialized Successfully!' as status,
    'Admin User: awadhesh / awadhesh123' as credentials,
    'Email: <EMAIL>' as email,
    CURRENT_TIMESTAMP as initialized_at;
