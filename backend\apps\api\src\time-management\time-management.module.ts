import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// Entities
import {
  TimeEntry,
  LeaveType,
  LeaveRequest,
  Holiday,
  WorkSchedule,
  Attendance,
  Employee,
  User,
  Tenant,
  AuditLog,
} from '@app/database';

// Controllers
import { TimeEntryController } from './controllers/time-entry.controller';
import { LeaveController } from './controllers/leave.controller';
import { AttendanceController } from './controllers/attendance.controller';
import { WorkScheduleController } from './controllers/work-schedule.controller';
import { HolidayController } from './controllers/holiday.controller';

// Services
import { TimeEntryService } from './services/time-entry.service';
import { LeaveService } from './services/leave.service';
import { AttendanceService } from './services/attendance.service';
import { WorkScheduleService } from './services/work-schedule.service';
import { HolidayService } from './services/holiday.service';
import { TimeTrackingService } from './services/time-tracking.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      TimeEntry,
      LeaveType,
      LeaveRequest,
      Holiday,
      WorkSchedule,
      Attendance,
      Employee,
      User,
      Tenant,
      AuditLog,
    ]),
  ],
  controllers: [
    TimeEntryController,
    LeaveController,
    AttendanceController,
    WorkScheduleController,
    HolidayController,
  ],
  providers: [
    TimeEntryService,
    LeaveService,
    AttendanceService,
    WorkScheduleService,
    HolidayService,
    TimeTrackingService,
  ],
  exports: [
    TimeEntryService,
    LeaveService,
    AttendanceService,
    WorkScheduleService,
    HolidayService,
    TimeTrackingService,
  ],
})
export class TimeManagementModule {}
