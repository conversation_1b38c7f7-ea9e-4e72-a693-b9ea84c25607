import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';

import { Feedback, FeedbackType, FeedbackStatus, FeedbackCategory } from '@app/database/entities/feedback.entity';
import { Employee } from '@app/database/entities/employee.entity';
import { PerformanceReview } from '@app/database/entities/performance-review.entity';
// TenantService removed - tenantId passed as parameter

export interface CreateFeedbackDto {
  recipientId: string;
  giverId: string;
  performanceReviewId?: string;
  feedbackType: FeedbackType;
  category: FeedbackCategory;
  subject: string;
  content: string;
  competencyRatings?: Record<string, { rating: number; comments?: string }>;
  isAnonymous?: boolean;
  isConfidential?: boolean;
  requestedCompetencies?: string[];
  metadata?: any;
}

export interface UpdateFeedbackDto {
  subject?: string;
  content?: string;
  competencyRatings?: Record<string, { rating: number; comments?: string }>;
  status?: FeedbackStatus;
  response?: string;
  submittedAt?: Date;
  acknowledgedAt?: Date;
  actionItems?: Array<{
    description: string;
    dueDate?: string;
    assignedTo?: string;
    completed?: boolean;
  }>;
  followUpNotes?: string;
  aiSentimentAnalysis?: any;
  metadata?: any;
}

export interface FeedbackFilters {
  recipientId?: string;
  giverId?: string;
  performanceReviewId?: string;
  feedbackType?: FeedbackType;
  category?: FeedbackCategory;
  status?: FeedbackStatus;
  isAnonymous?: boolean;
  isConfidential?: boolean;
}

@Injectable()
export class FeedbackService {
  constructor(
    @InjectRepository(Feedback)
    private feedbackRepository: Repository<Feedback>,
    @InjectRepository(Employee)
    private employeeRepository: Repository<Employee>,
    @InjectRepository(PerformanceReview)
    private performanceReviewRepository: Repository<PerformanceReview>,
    private eventEmitter: EventEmitter2,
  ) {}

  async create(createDto: CreateFeedbackDto, userId: string, tenantId: string): Promise<Feedback> {

    // Validate recipient exists
    const recipient = await this.employeeRepository.findOne({
      where: { id: createDto.recipientId, tenantId },
    });

    if (!recipient) {
      throw new NotFoundException('Recipient not found');
    }

    // Validate giver exists
    const giver = await this.employeeRepository.findOne({
      where: { id: createDto.giverId, tenantId },
    });

    if (!giver) {
      throw new NotFoundException('Feedback giver not found');
    }

    // Validate performance review if provided
    if (createDto.performanceReviewId) {
      const performanceReview = await this.performanceReviewRepository.findOne({
        where: { id: createDto.performanceReviewId, tenantId },
      });
      if (!performanceReview) {
        throw new NotFoundException('Performance review not found');
      }
    }

    // Check if giver can provide feedback to recipient
    await this.validateFeedbackPermission(createDto.giverId, createDto.recipientId, createDto.feedbackType, tenantId);

    // Transform competencyRatings from Record to Array format
    let competencyRatings: Array<{
      competencyId: string;
      competencyName: string;
      rating: number;
      comments?: string;
      examples?: string[];
    }> | undefined;

    if (createDto.competencyRatings) {
      competencyRatings = Object.entries(createDto.competencyRatings).map(([competencyId, data]) => ({
        competencyId,
        competencyName: competencyId, // Use ID as name for now
        rating: data.rating,
        comments: data.comments,
        examples: [],
      }));
    }

    const feedback = this.feedbackRepository.create({
      ...createDto,
      competencyRatings,
      tenantId,
      createdBy: userId,
      status: FeedbackStatus.DRAFT,
      submittedAt: new Date(),
    });

    const savedFeedback = await this.feedbackRepository.save(feedback);

    // Emit event
    this.eventEmitter.emit('feedback.created', {
      feedback: savedFeedback,
      tenantId,
      userId,
    });

    return savedFeedback;
  }

  async findAll(
    tenantId: string,
    filters: FeedbackFilters = {},
    page: number = 1,
    limit: number = 10,
  ): Promise<{ data: Feedback[]; total: number; page: number; limit: number }> {

    const where: FindOptionsWhere<Feedback> = {
      tenantId,
      ...filters,
    };

    const [data, total] = await this.feedbackRepository.findAndCount({
      where,
      relations: ['recipient', 'giver', 'performanceReview'],
      order: { submittedAt: 'DESC' },
      skip: (page - 1) * limit,
      take: limit,
    });

    return { data, total, page, limit };
  }

  async findOne(id: string, tenantId: string): Promise<Feedback> {

    const feedback = await this.feedbackRepository.findOne({
      where: { id, tenantId },
      relations: ['recipient', 'giver', 'performanceReview'],
    });

    if (!feedback) {
      throw new NotFoundException('Feedback not found');
    }

    return feedback;
  }

  async update(id: string, updateDto: UpdateFeedbackDto, userId: string, tenantId: string): Promise<Feedback> {
    const feedback = await this.findOne(id, tenantId);

    // Validate status transitions
    if (updateDto.status) {
      this.validateStatusTransition(feedback.status, updateDto.status);
    }

    // Update timestamps based on status changes
    if (updateDto.status === FeedbackStatus.SUBMITTED) {
      updateDto.submittedAt = new Date();
    }

    if (updateDto.status === FeedbackStatus.ACKNOWLEDGED && updateDto.response) {
      updateDto.acknowledgedAt = new Date();
    }

    Object.assign(feedback, updateDto);
    feedback.updatedBy = userId;

    const savedFeedback = await this.feedbackRepository.save(feedback);

    // Emit event
    this.eventEmitter.emit('feedback.updated', {
      feedback: savedFeedback,
      tenantId,
      userId,
      changes: updateDto,
    });

    return savedFeedback;
  }

  async delete(id: string, userId: string, tenantId: string): Promise<void> {
    const feedback = await this.findOne(id, tenantId);

    if (feedback.status === FeedbackStatus.SUBMITTED) {
      throw new BadRequestException('Cannot delete submitted feedback');
    }

    await this.feedbackRepository.remove(feedback);

    // Emit event
    this.eventEmitter.emit('feedback.deleted', {
      feedbackId: id,
      tenantId,
      userId,
    });
  }

  async getReceivedFeedback(
    employeeId: string,
    tenantId: string,
    page: number = 1,
    limit: number = 10,
  ): Promise<{ data: Feedback[]; total: number; page: number; limit: number }> {
    return this.findAll(tenantId, { recipientId: employeeId }, page, limit);
  }

  async getGivenFeedback(
    employeeId: string,
    tenantId: string,
    page: number = 1,
    limit: number = 10,
  ): Promise<{ data: Feedback[]; total: number; page: number; limit: number }> {
    return this.findAll(tenantId, { giverId: employeeId }, page, limit);
  }

  async getPerformanceReviewFeedback(
    performanceReviewId: string,
    tenantId: string,
    page: number = 1,
    limit: number = 10,
  ): Promise<{ data: Feedback[]; total: number; page: number; limit: number }> {
    return this.findAll(tenantId, { performanceReviewId }, page, limit);
  }

  async requestFeedback(
    recipientId: string,
    giverIds: string[],
    tenantId: string,
    performanceReviewId?: string,
    requestedCompetencies?: string[],
    userId?: string,
  ): Promise<Feedback[]> {
    const feedbacks: Feedback[] = [];

    for (const giverId of giverIds) {
      // Validate giver exists
      const giver = await this.employeeRepository.findOne({
        where: { id: giverId, tenantId },
      });

      if (!giver) {
        continue; // Skip invalid givers
      }

      // Determine feedback type based on relationship
      const feedbackType = await this.determineFeedbackType(giverId, recipientId, tenantId);

      const createDto: CreateFeedbackDto = {
        recipientId,
        giverId,
        performanceReviewId,
        feedbackType,
        category: FeedbackCategory.PERFORMANCE,
        subject: `Feedback Request for ${recipientId}`,
        content: 'Please provide your feedback on the requested competencies.',
        requestedCompetencies,
        isAnonymous: false,
        isConfidential: false,
      };

      const feedback = await this.create(createDto, userId || recipientId, tenantId);
      feedbacks.push(feedback);
    }

    return feedbacks;
  }

  async submitFeedback(id: string, userId: string, tenantId: string): Promise<Feedback> {
    const feedback = await this.findOne(id, tenantId);

    if (feedback.status !== FeedbackStatus.DRAFT) {
      throw new BadRequestException('Only draft feedback can be submitted');
    }

    return this.update(id, { status: FeedbackStatus.SUBMITTED }, userId, tenantId);
  }

  async acknowledgeFeedback(
    id: string,
    response: string,
    userId: string,
    tenantId: string,
  ): Promise<Feedback> {
    const feedback = await this.findOne(id, tenantId);

    if (feedback.status !== FeedbackStatus.SUBMITTED) {
      throw new BadRequestException('Only submitted feedback can be acknowledged');
    }

    return this.update(id, {
      status: FeedbackStatus.ACKNOWLEDGED,
      response,
    }, userId, tenantId);
  }

  private async validateFeedbackPermission(
    giverId: string,
    recipientId: string,
    feedbackType: FeedbackType,
    tenantId: string,
  ): Promise<void> {

    const giver = await this.employeeRepository.findOne({
      where: { id: giverId, tenantId },
      relations: ['manager', 'directReports'],
    });

    const recipient = await this.employeeRepository.findOne({
      where: { id: recipientId, tenantId },
      relations: ['manager', 'directReports'],
    });

    if (!giver || !recipient) {
      throw new NotFoundException('Employee not found');
    }

    // Validate feedback type permissions
    switch (feedbackType) {
      case FeedbackType.UPWARD:
        if (recipient.managerId !== giverId) {
          throw new ForbiddenException('Upward feedback can only be given to direct manager');
        }
        break;
      case FeedbackType.DOWNWARD:
        if (giver.managerId !== recipientId) {
          throw new ForbiddenException('Downward feedback can only be given to direct reports');
        }
        break;
      case FeedbackType.PEER:
        // Peers can give feedback to anyone in the same department or level
        break;
      case FeedbackType.SELF:
        if (giverId !== recipientId) {
          throw new ForbiddenException('Self feedback can only be given to oneself');
        }
        break;
      case FeedbackType.CUSTOMER:
        // Customer feedback validation would depend on business rules
        break;
    }
  }

  private async determineFeedbackType(giverId: string, recipientId: string, tenantId: string): Promise<FeedbackType> {

    if (giverId === recipientId) {
      return FeedbackType.SELF;
    }

    const giver = await this.employeeRepository.findOne({
      where: { id: giverId, tenantId },
    });

    const recipient = await this.employeeRepository.findOne({
      where: { id: recipientId, tenantId },
    });

    if (giver?.managerId === recipientId) {
      return FeedbackType.UPWARD;
    }

    if (recipient?.managerId === giverId) {
      return FeedbackType.DOWNWARD;
    }

    return FeedbackType.PEER;
  }

  private validateStatusTransition(currentStatus: FeedbackStatus, newStatus: FeedbackStatus): void {
    const validTransitions: Record<FeedbackStatus, FeedbackStatus[]> = {
      [FeedbackStatus.DRAFT]: [FeedbackStatus.PENDING, FeedbackStatus.SUBMITTED, FeedbackStatus.CANCELLED],
      [FeedbackStatus.PENDING]: [FeedbackStatus.SUBMITTED, FeedbackStatus.CANCELLED],
      [FeedbackStatus.SUBMITTED]: [FeedbackStatus.REVIEWED, FeedbackStatus.ACKNOWLEDGED, FeedbackStatus.CANCELLED],
      [FeedbackStatus.REVIEWED]: [FeedbackStatus.ACKNOWLEDGED, FeedbackStatus.ARCHIVED],
      [FeedbackStatus.ACKNOWLEDGED]: [FeedbackStatus.ARCHIVED],
      [FeedbackStatus.CANCELLED]: [FeedbackStatus.DRAFT],
      [FeedbackStatus.ARCHIVED]: [],
    };

    if (!validTransitions[currentStatus]?.includes(newStatus)) {
      throw new BadRequestException(`Invalid status transition from ${currentStatus} to ${newStatus}`);
    }
  }
}
