import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';

import { Feedback, FeedbackType, FeedbackStatus, FeedbackCategory } from '@app/database/entities/feedback.entity';
import { Employee } from '@app/database/entities/employee.entity';
import { PerformanceReview } from '@app/database/entities/performance-review.entity';
import { TenantService } from '../../tenant/tenant.service';

export interface CreateFeedbackDto {
  recipientId: string;
  giverId: string;
  performanceReviewId?: string;
  feedbackType: FeedbackType;
  category: FeedbackCategory;
  subject: string;
  content: string;
  competencyRatings?: Record<string, { rating: number; comments?: string }>;
  isAnonymous?: boolean;
  isConfidential?: boolean;
  requestedCompetencies?: string[];
  metadata?: any;
}

export interface UpdateFeedbackDto {
  subject?: string;
  content?: string;
  competencyRatings?: Record<string, { rating: number; comments?: string }>;
  status?: FeedbackStatus;
  response?: string;
  actionItems?: Array<{
    description: string;
    dueDate?: string;
    assignedTo?: string;
    completed?: boolean;
  }>;
  followUpNotes?: string;
  aiSentimentAnalysis?: any;
  metadata?: any;
}

export interface FeedbackFilters {
  recipientId?: string;
  giverId?: string;
  performanceReviewId?: string;
  feedbackType?: FeedbackType;
  category?: FeedbackCategory;
  status?: FeedbackStatus;
  isAnonymous?: boolean;
  isConfidential?: boolean;
}

@Injectable()
export class FeedbackService {
  constructor(
    @InjectRepository(Feedback)
    private feedbackRepository: Repository<Feedback>,
    @InjectRepository(Employee)
    private employeeRepository: Repository<Employee>,
    @InjectRepository(PerformanceReview)
    private performanceReviewRepository: Repository<PerformanceReview>,
    private tenantService: TenantService,
    private eventEmitter: EventEmitter2,
  ) {}

  async create(createDto: CreateFeedbackDto, userId: string): Promise<Feedback> {
    const tenantId = this.tenantService.getCurrentTenantId();

    // Validate recipient exists
    const recipient = await this.employeeRepository.findOne({
      where: { id: createDto.recipientId, tenantId },
    });

    if (!recipient) {
      throw new NotFoundException('Recipient not found');
    }

    // Validate giver exists
    const giver = await this.employeeRepository.findOne({
      where: { id: createDto.giverId, tenantId },
    });

    if (!giver) {
      throw new NotFoundException('Feedback giver not found');
    }

    // Validate performance review if provided
    if (createDto.performanceReviewId) {
      const performanceReview = await this.performanceReviewRepository.findOne({
        where: { id: createDto.performanceReviewId, tenantId },
      });
      if (!performanceReview) {
        throw new NotFoundException('Performance review not found');
      }
    }

    // Check if giver can provide feedback to recipient
    await this.validateFeedbackPermission(createDto.giverId, createDto.recipientId, createDto.feedbackType);

    const feedback = this.feedbackRepository.create({
      ...createDto,
      tenantId,
      createdBy: userId,
      status: FeedbackStatus.DRAFT,
      submittedDate: new Date(),
    });

    const savedFeedback = await this.feedbackRepository.save(feedback);

    // Emit event
    this.eventEmitter.emit('feedback.created', {
      feedback: savedFeedback,
      tenantId,
      userId,
    });

    return savedFeedback;
  }

  async findAll(
    filters: FeedbackFilters = {},
    page: number = 1,
    limit: number = 10,
  ): Promise<{ data: Feedback[]; total: number; page: number; limit: number }> {
    const tenantId = this.tenantService.getCurrentTenantId();

    const where: FindOptionsWhere<Feedback> = {
      tenantId,
      ...filters,
    };

    const [data, total] = await this.feedbackRepository.findAndCount({
      where,
      relations: ['recipient', 'giver', 'performanceReview'],
      order: { submittedDate: 'DESC' },
      skip: (page - 1) * limit,
      take: limit,
    });

    return { data, total, page, limit };
  }

  async findOne(id: string): Promise<Feedback> {
    const tenantId = this.tenantService.getCurrentTenantId();

    const feedback = await this.feedbackRepository.findOne({
      where: { id, tenantId },
      relations: ['recipient', 'giver', 'performanceReview'],
    });

    if (!feedback) {
      throw new NotFoundException('Feedback not found');
    }

    return feedback;
  }

  async update(id: string, updateDto: UpdateFeedbackDto, userId: string): Promise<Feedback> {
    const feedback = await this.findOne(id);

    // Validate status transitions
    if (updateDto.status) {
      this.validateStatusTransition(feedback.status, updateDto.status);
    }

    // Update timestamps based on status changes
    if (updateDto.status === FeedbackStatus.SUBMITTED) {
      updateDto['submittedDate'] = new Date();
    }

    if (updateDto.status === FeedbackStatus.ACKNOWLEDGED && updateDto.response) {
      updateDto['acknowledgedDate'] = new Date();
    }

    Object.assign(feedback, updateDto);
    feedback.updatedBy = userId;

    const savedFeedback = await this.feedbackRepository.save(feedback);

    // Emit event
    this.eventEmitter.emit('feedback.updated', {
      feedback: savedFeedback,
      tenantId: this.tenantService.getCurrentTenantId(),
      userId,
      changes: updateDto,
    });

    return savedFeedback;
  }

  async delete(id: string, userId: string): Promise<void> {
    const feedback = await this.findOne(id);

    if (feedback.status === FeedbackStatus.SUBMITTED) {
      throw new BadRequestException('Cannot delete submitted feedback');
    }

    await this.feedbackRepository.remove(feedback);

    // Emit event
    this.eventEmitter.emit('feedback.deleted', {
      feedbackId: id,
      tenantId: this.tenantService.getCurrentTenantId(),
      userId,
    });
  }

  async getReceivedFeedback(
    employeeId: string,
    page: number = 1,
    limit: number = 10,
  ): Promise<{ data: Feedback[]; total: number; page: number; limit: number }> {
    return this.findAll({ recipientId: employeeId }, page, limit);
  }

  async getGivenFeedback(
    employeeId: string,
    page: number = 1,
    limit: number = 10,
  ): Promise<{ data: Feedback[]; total: number; page: number; limit: number }> {
    return this.findAll({ giverId: employeeId }, page, limit);
  }

  async getPerformanceReviewFeedback(
    performanceReviewId: string,
    page: number = 1,
    limit: number = 10,
  ): Promise<{ data: Feedback[]; total: number; page: number; limit: number }> {
    return this.findAll({ performanceReviewId }, page, limit);
  }

  async requestFeedback(
    recipientId: string,
    giverIds: string[],
    performanceReviewId?: string,
    requestedCompetencies?: string[],
    userId?: string,
  ): Promise<Feedback[]> {
    const tenantId = this.tenantService.getCurrentTenantId();
    const feedbacks: Feedback[] = [];

    for (const giverId of giverIds) {
      // Validate giver exists
      const giver = await this.employeeRepository.findOne({
        where: { id: giverId, tenantId },
      });

      if (!giver) {
        continue; // Skip invalid givers
      }

      // Determine feedback type based on relationship
      const feedbackType = await this.determineFeedbackType(giverId, recipientId);

      const createDto: CreateFeedbackDto = {
        recipientId,
        giverId,
        performanceReviewId,
        feedbackType,
        category: FeedbackCategory.PERFORMANCE,
        subject: `Feedback Request for ${recipientId}`,
        content: 'Please provide your feedback on the requested competencies.',
        requestedCompetencies,
        isAnonymous: false,
        isConfidential: false,
      };

      const feedback = await this.create(createDto, userId || recipientId);
      feedbacks.push(feedback);
    }

    return feedbacks;
  }

  async submitFeedback(id: string, userId: string): Promise<Feedback> {
    const feedback = await this.findOne(id);

    if (feedback.status !== FeedbackStatus.DRAFT) {
      throw new BadRequestException('Only draft feedback can be submitted');
    }

    return this.update(id, { status: FeedbackStatus.SUBMITTED }, userId);
  }

  async acknowledgeFeedback(
    id: string,
    response: string,
    userId: string,
  ): Promise<Feedback> {
    const feedback = await this.findOne(id);

    if (feedback.status !== FeedbackStatus.SUBMITTED) {
      throw new BadRequestException('Only submitted feedback can be acknowledged');
    }

    return this.update(id, {
      status: FeedbackStatus.ACKNOWLEDGED,
      response,
    }, userId);
  }

  private async validateFeedbackPermission(
    giverId: string,
    recipientId: string,
    feedbackType: FeedbackType,
  ): Promise<void> {
    const tenantId = this.tenantService.getCurrentTenantId();

    const giver = await this.employeeRepository.findOne({
      where: { id: giverId, tenantId },
      relations: ['manager', 'directReports'],
    });

    const recipient = await this.employeeRepository.findOne({
      where: { id: recipientId, tenantId },
      relations: ['manager', 'directReports'],
    });

    if (!giver || !recipient) {
      throw new NotFoundException('Employee not found');
    }

    // Validate feedback type permissions
    switch (feedbackType) {
      case FeedbackType.UPWARD:
        if (recipient.managerId !== giverId) {
          throw new ForbiddenException('Upward feedback can only be given to direct manager');
        }
        break;
      case FeedbackType.DOWNWARD:
        if (giver.managerId !== recipientId) {
          throw new ForbiddenException('Downward feedback can only be given to direct reports');
        }
        break;
      case FeedbackType.PEER:
        // Peers can give feedback to anyone in the same department or level
        break;
      case FeedbackType.SELF:
        if (giverId !== recipientId) {
          throw new ForbiddenException('Self feedback can only be given to oneself');
        }
        break;
      case FeedbackType.CUSTOMER:
        // Customer feedback validation would depend on business rules
        break;
    }
  }

  private async determineFeedbackType(giverId: string, recipientId: string): Promise<FeedbackType> {
    const tenantId = this.tenantService.getCurrentTenantId();

    if (giverId === recipientId) {
      return FeedbackType.SELF;
    }

    const giver = await this.employeeRepository.findOne({
      where: { id: giverId, tenantId },
    });

    const recipient = await this.employeeRepository.findOne({
      where: { id: recipientId, tenantId },
    });

    if (giver?.managerId === recipientId) {
      return FeedbackType.UPWARD;
    }

    if (recipient?.managerId === giverId) {
      return FeedbackType.DOWNWARD;
    }

    return FeedbackType.PEER;
  }

  private validateStatusTransition(currentStatus: FeedbackStatus, newStatus: FeedbackStatus): void {
    const validTransitions: Record<FeedbackStatus, FeedbackStatus[]> = {
      [FeedbackStatus.DRAFT]: [FeedbackStatus.SUBMITTED, FeedbackStatus.CANCELLED],
      [FeedbackStatus.SUBMITTED]: [FeedbackStatus.ACKNOWLEDGED, FeedbackStatus.CANCELLED],
      [FeedbackStatus.ACKNOWLEDGED]: [],
      [FeedbackStatus.CANCELLED]: [FeedbackStatus.DRAFT],
    };

    if (!validTransitions[currentStatus]?.includes(newStatus)) {
      throw new BadRequestException(`Invalid status transition from ${currentStatus} to ${newStatus}`);
    }
  }
}
