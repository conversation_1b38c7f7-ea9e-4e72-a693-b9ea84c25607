"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.unregisterKnownFeatures = exports.coreFeatureDefinitionIfKnown = exports.registerKnownFeature = void 0;
const registeredFeatures = new Map();
function registerKnownFeature(definitions) {
    if (!registeredFeatures.has(definitions.identity)) {
        registeredFeatures.set(definitions.identity, definitions);
    }
}
exports.registerKnownFeature = registerKnownFeature;
function coreFeatureDefinitionIfKnown(url) {
    var _a;
    return (_a = registeredFeatures.get(url.identity)) === null || _a === void 0 ? void 0 : _a.find(url.version);
}
exports.coreFeatureDefinitionIfKnown = coreFeatureDefinitionIfKnown;
function unregisterKnownFeatures(definitions) {
    registeredFeatures.delete(definitions.identity);
}
exports.unregisterKnownFeatures = unregisterKnownFeatures;
//# sourceMappingURL=knownCoreFeatures.js.map