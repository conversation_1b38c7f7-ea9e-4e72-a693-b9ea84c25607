"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.POLICY_VERSIONS = exports.PolicySpecDefinition = exports.PolicyTypeName = void 0;
const graphql_1 = require("graphql");
const coreSpec_1 = require("./coreSpec");
const definitions_1 = require("../definitions");
const directiveAndTypeSpecification_1 = require("../directiveAndTypeSpecification");
const knownCoreFeatures_1 = require("../knownCoreFeatures");
const argumentCompositionStrategies_1 = require("../argumentCompositionStrategies");
const utils_1 = require("../utils");
var PolicyTypeName;
(function (PolicyTypeName) {
    PolicyTypeName["POLICY"] = "Policy";
})(PolicyTypeName || (exports.PolicyTypeName = PolicyTypeName = {}));
class PolicySpecDefinition extends coreSpec_1.FeatureDefinition {
    constructor(version) {
        super(new coreSpec_1.FeatureUrl(PolicySpecDefinition.identity, PolicySpecDefinition.directiveName, version));
        this.registerType((0, directiveAndTypeSpecification_1.createScalarTypeSpecification)({ name: PolicyTypeName.POLICY }));
        this.registerDirective((0, directiveAndTypeSpecification_1.createDirectiveSpecification)({
            name: PolicySpecDefinition.directiveName,
            args: [{
                    name: 'policies',
                    type: (schema, feature) => {
                        (0, utils_1.assert)(feature, "Shouldn't be added without being attached to a @link spec");
                        const policyName = feature.typeNameInSchema(PolicyTypeName.POLICY);
                        const PolicyType = schema.type(policyName);
                        (0, utils_1.assert)(PolicyType, () => `Expected "${policyName}" to be defined`);
                        return new definitions_1.NonNullType(new definitions_1.ListType(new definitions_1.NonNullType(new definitions_1.ListType(new definitions_1.NonNullType(PolicyType)))));
                    },
                    compositionStrategy: argumentCompositionStrategies_1.ARGUMENT_COMPOSITION_STRATEGIES.UNION,
                }],
            locations: [
                graphql_1.DirectiveLocation.FIELD_DEFINITION,
                graphql_1.DirectiveLocation.OBJECT,
                graphql_1.DirectiveLocation.INTERFACE,
                graphql_1.DirectiveLocation.SCALAR,
                graphql_1.DirectiveLocation.ENUM,
            ],
            composes: true,
            supergraphSpecification: () => exports.POLICY_VERSIONS.latest(),
        }));
    }
    get defaultCorePurpose() {
        return 'SECURITY';
    }
}
exports.PolicySpecDefinition = PolicySpecDefinition;
PolicySpecDefinition.directiveName = "policy";
PolicySpecDefinition.identity = `https://specs.apollo.dev/${PolicySpecDefinition.directiveName}`;
exports.POLICY_VERSIONS = new coreSpec_1.FeatureDefinitions(PolicySpecDefinition.identity).add(new PolicySpecDefinition(new coreSpec_1.FeatureVersion(0, 1)));
(0, knownCoreFeatures_1.registerKnownFeature)(exports.POLICY_VERSIONS);
//# sourceMappingURL=policySpec.js.map