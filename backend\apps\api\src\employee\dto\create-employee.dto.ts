import {
  IsString,
  IsE<PERSON>,
  IsOptional,
  IsDateString,
  IsEnum,
  IsUUID,
  IsPhoneNumber,
  IsArray,
  ValidateNested,
  IsObject,
  Length,
  Matches,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { EmploymentStatus, EmploymentType, Gender } from '@app/common/enums/status.enum';

export class CreateEmployeeContactDto {
  @ApiProperty({ description: 'Primary email address' })
  @IsEmail()
  email!: string;

  @ApiPropertyOptional({ description: 'Secondary email address' })
  @IsOptional()
  @IsEmail()
  secondaryEmail?: string;

  @ApiProperty({ description: 'Primary phone number' })
  @IsPhoneNumber()
  phone!: string;

  @ApiPropertyOptional({ description: 'Secondary phone number' })
  @IsOptional()
  @IsPhoneNumber()
  secondaryPhone?: string;

  @ApiPropertyOptional({ description: 'Emergency contact name' })
  @IsOptional()
  @IsString()
  emergencyContactName?: string;

  @ApiPropertyOptional({ description: 'Emergency contact phone' })
  @IsOptional()
  @IsPhoneNumber()
  emergencyContactPhone?: string;
}

export class CreateEmployeeAddressDto {
  @ApiProperty({ description: 'Street address' })
  @IsString()
  street!: string;

  @ApiProperty({ description: 'City' })
  @IsString()
  city!: string;

  @ApiProperty({ description: 'State or province' })
  @IsString()
  state!: string;

  @ApiProperty({ description: 'Postal code' })
  @IsString()
  postalCode!: string;

  @ApiProperty({ description: 'Country' })
  @IsString()
  country!: string;

  @ApiPropertyOptional({ description: 'Address type (home, work, etc.)' })
  @IsOptional()
  @IsString()
  type?: string;
}

export class CreateEmployeeDto {
  @ApiProperty({ description: 'Employee ID/Number', example: 'EMP001' })
  @IsString()
  @Length(3, 20)
  @Matches(/^[A-Z0-9]+$/, { message: 'Employee ID must contain only uppercase letters and numbers' })
  employeeId!: string;

  @ApiProperty({ description: 'First name' })
  @IsString()
  @Length(1, 50)
  firstName!: string;

  @ApiProperty({ description: 'Last name' })
  @IsString()
  @Length(1, 50)
  lastName!: string;

  @ApiPropertyOptional({ description: 'Middle name' })
  @IsOptional()
  @IsString()
  @Length(1, 50)
  middleName?: string;

  @ApiProperty({ description: 'Email address' })
  @IsEmail()
  email!: string;

  @ApiPropertyOptional({ description: 'Phone number' })
  @IsOptional()
  @IsPhoneNumber()
  phone?: string;

  @ApiProperty({ description: 'Date of birth' })
  @IsDateString()
  dateOfBirth!: string;

  @ApiProperty({ description: 'Gender', enum: Gender })
  @IsEnum(Gender)
  gender!: Gender;

  @ApiProperty({ description: 'Hire date' })
  @IsDateString()
  hireDate!: string;

  @ApiProperty({ description: 'Department ID' })
  @IsUUID()
  departmentId!: string;

  @ApiProperty({ description: 'Position ID' })
  @IsUUID()
  positionId!: string;

  @ApiPropertyOptional({ description: 'Manager ID' })
  @IsOptional()
  @IsUUID()
  managerId?: string;

  @ApiProperty({ description: 'Employment status', enum: EmploymentStatus })
  @IsEnum(EmploymentStatus)
  employmentStatus!: EmploymentStatus;

  @ApiProperty({ description: 'Employment type', enum: EmploymentType })
  @IsEnum(EmploymentType)
  employmentType!: EmploymentType;

  @ApiPropertyOptional({ description: 'Salary amount' })
  @IsOptional()
  salary?: number;

  @ApiPropertyOptional({ description: 'National ID number' })
  @IsOptional()
  @IsString()
  nationalId?: string;

  @ApiPropertyOptional({ description: 'Passport number' })
  @IsOptional()
  @IsString()
  passportNumber?: string;

  @ApiPropertyOptional({ description: 'Tax ID number' })
  @IsOptional()
  @IsString()
  taxId?: string;

  @ApiPropertyOptional({ description: 'Contact information' })
  @IsOptional()
  @ValidateNested()
  @Type(() => CreateEmployeeContactDto)
  contact?: CreateEmployeeContactDto;

  @ApiPropertyOptional({ description: 'Address information', type: [CreateEmployeeAddressDto] })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateEmployeeAddressDto)
  addresses?: CreateEmployeeAddressDto[];

  @ApiPropertyOptional({ description: 'Additional metadata' })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}
