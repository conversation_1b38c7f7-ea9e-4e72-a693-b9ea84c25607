import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  Users,
  TrendingUp,
  DollarSign,
  Target,
  Calendar,
  AlertTriangle,
  CheckCircle,
  Clock,
  BarChart3,
  <PERSON><PERSON>hart,
  Activity,
  Award,
} from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { useAuth } from '@/hooks/use-auth';
import { PageContainer, PageHeader, PageSection, GridContainer } from '@/components/layout/page-container';
import { dashboardService } from '@/services/dashboard-service';

interface DashboardMetrics {
  employees: {
    total: number;
    active: number;
    newHires: number;
    turnover: number;
  };
  performance: {
    averageRating: number;
    completedReviews: number;
    pendingReviews: number;
    goalAchievementRate: number;
  };
  payroll: {
    totalPayroll: number;
    averageSalary: number;
    pendingPayments: number;
    payrollCosts: number;
  };
  attendance: {
    presentToday: number;
    onLeave: number;
    lateArrivals: number;
    averageHours: number;
  };
}

interface RecentActivity {
  id: string;
  type: 'employee' | 'performance' | 'payroll' | 'leave';
  title: string;
  description: string;
  timestamp: string;
  status: 'success' | 'warning' | 'error' | 'info';
}

export default function DashboardPage() {
  const { user } = useAuth();
  const [timeRange, setTimeRange] = useState('30d');
  const [selectedTab, setSelectedTab] = useState('overview');

  const {
    data: metrics,
    isLoading: metricsLoading,
    error: metricsError,
  } = useQuery({
    queryKey: ['dashboard-metrics', timeRange],
    queryFn: () => dashboardService.getMetrics(timeRange),
    refetchInterval: 5 * 60 * 1000, // Refresh every 5 minutes
  });

  const {
    data: activities,
    isLoading: activitiesLoading,
  } = useQuery({
    queryKey: ['dashboard-activities'],
    queryFn: () => dashboardService.getRecentActivities(),
    refetchInterval: 2 * 60 * 1000, // Refresh every 2 minutes
  });

  const {
    data: chartData,
    isLoading: chartLoading,
  } = useQuery({
    queryKey: ['dashboard-charts', timeRange],
    queryFn: () => dashboardService.getChartData(timeRange),
  });

  if (metricsLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  if (metricsError) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-destructive mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Failed to load dashboard</h3>
          <p className="text-muted-foreground">Please try refreshing the page</p>
          <Button
            onClick={() => window.location.reload()}
            className="mt-4"
            variant="outline"
          >
            Refresh Page
          </Button>
        </div>
      </div>
    );
  }

  return (
    <PageContainer>
      <PageHeader
        title={`Welcome back, ${user?.firstName}!`}
        description="Here's what's happening with your organization today."
      >
        <Select value={timeRange} onValueChange={setTimeRange}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select time range" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7d">Last 7 days</SelectItem>
            <SelectItem value="30d">Last 30 days</SelectItem>
            <SelectItem value="90d">Last 90 days</SelectItem>
            <SelectItem value="1y">Last year</SelectItem>
          </SelectContent>
        </Select>
        <Button variant="outline" size="sm">
          <Activity className="h-4 w-4 mr-2" />
          Live View
        </Button>
      </PageHeader>

      <PageSection title="Key Metrics">
        <GridContainer cols={4} gap="lg">
        <Card className="group hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br from-blue-50 to-blue-100/50 hover:from-blue-100 hover:to-blue-200/50 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-transparent" />
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 relative">
            <CardTitle className="text-sm font-semibold text-blue-900/80">Total Employees</CardTitle>
            <div className="p-3 bg-blue-500/10 rounded-xl group-hover:bg-blue-500/20 transition-colors">
              <Users className="h-6 w-6 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent className="relative">
            <div className="text-4xl font-bold text-blue-900 mb-3">{metrics?.employees.total || 0}</div>
            <div className="flex items-center gap-2 mb-2">
              <Badge variant="secondary" className="bg-emerald-100 text-emerald-800 border-emerald-200 font-medium">
                +{metrics?.employees.newHires || 0} new hires
              </Badge>
            </div>
            <p className="text-sm text-blue-700/70 font-medium">
              {metrics?.employees.active || 0} active employees
            </p>
          </CardContent>
        </Card>

        <Card className="group hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br from-emerald-50 to-emerald-100/50 hover:from-emerald-100 hover:to-emerald-200/50 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-transparent" />
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 relative">
            <CardTitle className="text-sm font-semibold text-emerald-900/80">Performance Score</CardTitle>
            <div className="p-3 bg-emerald-500/10 rounded-xl group-hover:bg-emerald-500/20 transition-colors">
              <TrendingUp className="h-6 w-6 text-emerald-600" />
            </div>
          </CardHeader>
          <CardContent className="relative">
            <div className="text-4xl font-bold text-emerald-900 mb-3">
              {metrics?.performance.averageRating?.toFixed(1) || '0.0'}
            </div>
            <div className="flex items-center gap-2 mb-2">
              <Badge variant="secondary" className="bg-blue-100 text-blue-800 border-blue-200 font-medium">
                {metrics?.performance.completedReviews || 0} completed
              </Badge>
            </div>
            <p className="text-sm text-emerald-700/70 font-medium">
              {metrics?.performance.pendingReviews || 0} pending reviews
            </p>
          </CardContent>
        </Card>

        <Card className="group hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br from-purple-50 to-purple-100/50 hover:from-purple-100 hover:to-purple-200/50 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-transparent" />
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 relative">
            <CardTitle className="text-sm font-semibold text-purple-900/80">Monthly Payroll</CardTitle>
            <div className="p-3 bg-purple-500/10 rounded-xl group-hover:bg-purple-500/20 transition-colors">
              <DollarSign className="h-6 w-6 text-purple-600" />
            </div>
          </CardHeader>
          <CardContent className="relative">
            <div className="text-4xl font-bold text-purple-900 mb-3">
              ${(metrics?.payroll.totalPayroll || 0).toLocaleString()}
            </div>
            <div className="flex items-center gap-2 mb-2">
              <Badge variant="secondary" className="bg-orange-100 text-orange-800 border-orange-200 font-medium">
                {metrics?.payroll.pendingPayments || 0} pending
              </Badge>
            </div>
            <p className="text-sm text-purple-700/70 font-medium">
              Avg: ${(metrics?.payroll.averageSalary || 0).toLocaleString()}
            </p>
          </CardContent>
        </Card>

        <Card className="group hover:shadow-xl transition-all duration-300 border-0 bg-gradient-to-br from-orange-50 to-orange-100/50 hover:from-orange-100 hover:to-orange-200/50 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-orange-500/5 to-transparent" />
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3 relative">
            <CardTitle className="text-sm font-semibold text-orange-900/80">Goal Achievement</CardTitle>
            <div className="p-3 bg-orange-500/10 rounded-xl group-hover:bg-orange-500/20 transition-colors">
              <Target className="h-6 w-6 text-orange-600" />
            </div>
          </CardHeader>
          <CardContent className="relative">
            <div className="text-4xl font-bold text-orange-900 mb-3">
              {metrics?.performance.goalAchievementRate?.toFixed(0) || 0}%
            </div>
            <Progress
              value={metrics?.performance.goalAchievementRate || 0}
              className="mt-3 h-3 bg-orange-100"
            />
            <p className="text-sm text-orange-700/70 font-medium mt-3">
              Target: 85% achievement rate
            </p>
          </CardContent>
        </Card>
        </GridContainer>
      </PageSection>

      <PageSection title="Dashboard Overview">
        <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4 bg-gray-100/50 p-1 rounded-xl">
          <TabsTrigger value="overview" className="rounded-lg data-[state=active]:bg-white data-[state=active]:shadow-sm">Overview</TabsTrigger>
          <TabsTrigger value="performance" className="rounded-lg data-[state=active]:bg-white data-[state=active]:shadow-sm">Performance</TabsTrigger>
          <TabsTrigger value="attendance" className="rounded-lg data-[state=active]:bg-white data-[state=active]:shadow-sm">Attendance</TabsTrigger>
          <TabsTrigger value="analytics" className="rounded-lg data-[state=active]:bg-white data-[state=active]:shadow-sm">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6 mt-8">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-7">
            {/* Recent Activities */}
            <Card className="col-span-4 shadow-lg border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <CardTitle className="text-xl font-semibold text-gray-900 flex items-center gap-2">
                  <Activity className="h-5 w-5 text-primary" />
                  Recent Activities
                </CardTitle>
              </CardHeader>
              <CardContent>
                {activitiesLoading ? (
                  <div className="flex items-center justify-center h-[200px]">
                    <LoadingSpinner />
                  </div>
                ) : (
                  <div className="space-y-4">
                    {activities?.slice(0, 5).map((activity: RecentActivity) => (
                      <div key={activity.id} className="flex items-center space-x-4 p-3 rounded-lg hover:bg-gray-50/80 transition-colors">
                        <div className={`w-3 h-3 rounded-full flex-shrink-0 ${
                          activity.status === 'success' ? 'bg-emerald-500' :
                          activity.status === 'warning' ? 'bg-amber-500' :
                          activity.status === 'error' ? 'bg-red-500' : 'bg-blue-500'
                        }`} />
                        <div className="flex-1 space-y-1 min-w-0">
                          <p className="text-sm font-semibold leading-none text-gray-900">
                            {activity.title}
                          </p>
                          <p className="text-sm text-gray-600 truncate">
                            {activity.description}
                          </p>
                        </div>
                        <div className="text-xs text-gray-500 font-medium flex-shrink-0">
                          {new Date(activity.timestamp).toLocaleDateString()}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Quick Stats */}
            <Card className="col-span-3">
              <CardHeader>
                <CardTitle>Today's Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-sm">Present Today</span>
                  </div>
                  <Badge variant="secondary">
                    {metrics?.attendance.presentToday || 0}
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4 text-blue-500" />
                    <span className="text-sm">On Leave</span>
                  </div>
                  <Badge variant="secondary">
                    {metrics?.attendance.onLeave || 0}
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4 text-yellow-500" />
                    <span className="text-sm">Late Arrivals</span>
                  </div>
                  <Badge variant="secondary">
                    {metrics?.attendance.lateArrivals || 0}
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Activity className="h-4 w-4 text-purple-500" />
                    <span className="text-sm">Avg. Hours</span>
                  </div>
                  <Badge variant="secondary">
                    {metrics?.attendance.averageHours?.toFixed(1) || '0.0'}h
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Performance Reviews</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span>Completed Reviews</span>
                    <Badge variant="secondary">
                      {metrics?.performance.completedReviews || 0}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Pending Reviews</span>
                    <Badge variant="destructive">
                      {metrics?.performance.pendingReviews || 0}
                    </Badge>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span>Completion Rate</span>
                      <span>
                        {((metrics?.performance.completedReviews || 0) / 
                          ((metrics?.performance.completedReviews || 0) + (metrics?.performance.pendingReviews || 0)) * 100
                        ).toFixed(0)}%
                      </span>
                    </div>
                    <Progress 
                      value={
                        ((metrics?.performance.completedReviews || 0) / 
                          ((metrics?.performance.completedReviews || 0) + (metrics?.performance.pendingReviews || 0)) * 100
                        ) || 0
                      }
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Goal Tracking</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-primary">
                      {metrics?.performance.goalAchievementRate?.toFixed(0) || 0}%
                    </div>
                    <p className="text-sm text-muted-foreground">Goal Achievement Rate</p>
                  </div>
                  <Progress value={metrics?.performance.goalAchievementRate || 0} />
                  <div className="flex items-center justify-center space-x-2">
                    <Award className="h-4 w-4 text-yellow-500" />
                    <span className="text-sm">Top performing department</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="attendance" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>Attendance Overview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Present</span>
                    <span className="font-medium">{metrics?.attendance.presentToday || 0}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">On Leave</span>
                    <span className="font-medium">{metrics?.attendance.onLeave || 0}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Late</span>
                    <span className="font-medium">{metrics?.attendance.lateArrivals || 0}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Working Hours</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <div className="text-2xl font-bold">
                    {metrics?.attendance.averageHours?.toFixed(1) || '0.0'}
                  </div>
                  <p className="text-sm text-muted-foreground">Average hours today</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Attendance Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {(((metrics?.attendance.presentToday || 0) / (metrics?.employees.active || 1)) * 100).toFixed(0)}%
                  </div>
                  <p className="text-sm text-muted-foreground">Present today</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <BarChart3 className="h-5 w-5" />
                  <span>Employee Growth</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {chartLoading ? (
                  <div className="flex items-center justify-center h-[200px]">
                    <LoadingSpinner />
                  </div>
                ) : (
                  <div className="h-[200px] flex items-center justify-center text-muted-foreground">
                    Chart component would go here
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <PieChart className="h-5 w-5" />
                  <span>Department Distribution</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {chartLoading ? (
                  <div className="flex items-center justify-center h-[200px]">
                    <LoadingSpinner />
                  </div>
                ) : (
                  <div className="h-[200px] flex items-center justify-center text-muted-foreground">
                    Chart component would go here
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
      </PageSection>
    </PageContainer>
  );
}
