import { Injectable, CanActivate, ExecutionContext, ForbiddenException, Logger } from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';
import { Reflector } from '@nestjs/core';
import { RequestWithUser } from '../interfaces/request-with-user.interface';

@Injectable()
export class TenantGuard implements CanActivate {
  private readonly logger = new Logger(TenantGuard.name);

  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const request = this.getRequest(context);
    const user = request.user;

    if (!user) {
      this.logger.warn('TenantGuard: No user found in request');
      throw new ForbiddenException('User not authenticated');
    }

    if (!user.tenantId) {
      this.logger.warn(`TenantGuard: User ${user.id} has no tenant ID`);
      throw new ForbiddenException('User has no tenant access');
    }

    // Check if the requested resource belongs to the user's tenant
    const resourceTenantId = this.extractTenantIdFromRequest(request);
    
    if (resourceTenantId && resourceTenantId !== user.tenantId) {
      this.logger.warn(
        `TenantGuard: User ${user.id} from tenant ${user.tenantId} attempted to access resource from tenant ${resourceTenantId}`
      );
      throw new ForbiddenException('Access denied: Resource belongs to different tenant');
    }

    return true;
  }

  private getRequest(context: ExecutionContext): RequestWithUser {
    if (context.getType() === 'http') {
      return context.switchToHttp().getRequest<RequestWithUser>();
    }

    if (context.getType<any>() === 'graphql') {
      const ctx = GqlExecutionContext.create(context);
      return ctx.getContext().req;
    }

    return context.switchToHttp().getRequest<RequestWithUser>();
  }

  private extractTenantIdFromRequest(request: RequestWithUser): string | null {
    // Try to extract tenant ID from various sources
    
    // 1. From query parameters
    if (request.query?.tenantId) {
      return request.query.tenantId as string;
    }

    // 2. From request body
    if (request.body?.tenantId) {
      return request.body.tenantId;
    }

    // 3. From route parameters
    if (request.params?.tenantId) {
      return request.params.tenantId;
    }

    // 4. From headers
    if (request.headers['x-tenant-id']) {
      return request.headers['x-tenant-id'] as string;
    }

    // If no explicit tenant ID is found, use the user's tenant ID
    return request.user?.tenantId || null;
  }
}
