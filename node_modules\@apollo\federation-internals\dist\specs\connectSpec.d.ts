import { GraphQLError } from 'graphql';
import { CorePurpose, FeatureDefinition, FeatureDefinitions, FeatureVersion } from './coreSpec';
import { Schema } from '../definitions';
export declare const connectIdentity = "https://specs.apollo.dev/connect";
export declare class ConnectSpecDefinition extends FeatureDefinition {
    readonly minimumFederationVersion: FeatureVersion;
    constructor(version: FeatureVersion, minimumFederationVersion: FeatureVersion);
    addElementsToSchema(schema: Schema): GraphQLError[];
    get defaultCorePurpose(): CorePurpose;
}
export declare const CONNECT_VERSIONS: FeatureDefinitions<ConnectSpecDefinition>;
//# sourceMappingURL=connectSpec.d.ts.map