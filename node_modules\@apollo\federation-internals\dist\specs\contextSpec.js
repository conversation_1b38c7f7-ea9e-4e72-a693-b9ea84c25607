"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CONTEXT_VERSIONS = exports.ContextSpecDefinition = exports.ContextDirectiveName = void 0;
const graphql_1 = require("graphql");
const coreSpec_1 = require("./coreSpec");
const definitions_1 = require("../definitions");
const directiveAndTypeSpecification_1 = require("../directiveAndTypeSpecification");
const knownCoreFeatures_1 = require("../knownCoreFeatures");
const utils_1 = require("../utils");
var ContextDirectiveName;
(function (ContextDirectiveName) {
    ContextDirectiveName["CONTEXT"] = "context";
    ContextDirectiveName["FROM_CONTEXT"] = "fromContext";
})(ContextDirectiveName || (exports.ContextDirectiveName = ContextDirectiveName = {}));
const fieldValueScalar = 'ContextFieldValue';
class ContextSpecDefinition extends coreSpec_1.FeatureDefinition {
    constructor(version) {
        super(new coreSpec_1.FeatureUrl(ContextSpecDefinition.identity, ContextSpecDefinition.directiveName, version));
        this.registerType((0, directiveAndTypeSpecification_1.createScalarTypeSpecification)({ name: fieldValueScalar }));
        this.contextDirectiveSpec = (0, directiveAndTypeSpecification_1.createDirectiveSpecification)({
            name: ContextDirectiveName.CONTEXT,
            locations: [graphql_1.DirectiveLocation.INTERFACE, graphql_1.DirectiveLocation.OBJECT, graphql_1.DirectiveLocation.UNION],
            args: [{ name: 'name', type: (schema) => new definitions_1.NonNullType(schema.stringType()) }],
            composes: true,
            repeatable: true,
            supergraphSpecification: (fedVersion) => exports.CONTEXT_VERSIONS.getMinimumRequiredVersion(fedVersion),
            staticArgumentTransform: (subgraph, args) => {
                const subgraphName = subgraph.name;
                return {
                    name: `${subgraphName}__${args.name}`,
                };
            },
        });
        this.fromContextDirectiveSpec = (0, directiveAndTypeSpecification_1.createDirectiveSpecification)({
            name: ContextDirectiveName.FROM_CONTEXT,
            locations: [graphql_1.DirectiveLocation.ARGUMENT_DEFINITION],
            args: [{ name: 'field', type: (schema, feature) => {
                        (0, utils_1.assert)(feature, "Shouldn't be added without being attached to a @link spec");
                        const fieldValue = feature.typeNameInSchema(fieldValueScalar);
                        const fieldValueType = schema.type(fieldValue);
                        (0, utils_1.assert)(fieldValueType, () => `Expected "${fieldValue}" to be defined`);
                        (0, utils_1.assert)((0, definitions_1.isInputType)(fieldValueType), `Expected "${fieldValue}" to be an input type`);
                        return fieldValueType;
                    } }],
            composes: false,
        });
        this.registerDirective(this.contextDirectiveSpec);
        this.registerDirective(this.fromContextDirectiveSpec);
    }
    get defaultCorePurpose() {
        return 'SECURITY';
    }
    contextDirective(schema) {
        return this.directive(schema, ContextSpecDefinition.directiveName);
    }
}
exports.ContextSpecDefinition = ContextSpecDefinition;
ContextSpecDefinition.directiveName = 'context';
ContextSpecDefinition.identity = `https://specs.apollo.dev/${ContextSpecDefinition.directiveName}`;
exports.CONTEXT_VERSIONS = new coreSpec_1.FeatureDefinitions(ContextSpecDefinition.identity).add(new ContextSpecDefinition(new coreSpec_1.FeatureVersion(0, 1)));
(0, knownCoreFeatures_1.registerKnownFeature)(exports.CONTEXT_VERSIONS);
//# sourceMappingURL=contextSpec.js.map