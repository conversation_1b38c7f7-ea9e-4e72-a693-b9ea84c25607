{"version": 3, "file": "precompute.js", "sourceRoot": "", "sources": ["../src/precompute.ts"], "names": [], "mappings": ";;;AAAA,wBASW;AAEX,SAAgB,iBAAiB,CAAC,MAAc;IAC9C,MAAM,QAAQ,GAAG,IAAA,qBAAkB,EAAC,MAAM,CAAC,CAAC;IAC5C,IAAA,SAAM,EAAC,QAAQ,EAAE,wCAAwC,CAAC,CAAC;IAE3D,MAAM,YAAY,GAAG,QAAQ,CAAC,YAAY,EAAE,CAAC;IAC7C,MAAM,iBAAiB,GAAG,QAAQ,CAAC,iBAAiB,EAAE,CAAC;IAIvD,MAAM,kBAAkB,GAAG,QAAQ,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;IAE/F,MAAM,eAAe,GAAgB,IAAI,GAAG,EAAE,CAAC;IAC/C,MAAM,YAAY,GAAG,CAAC,IAAmB,EAAE,EAAE;QAC3C,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,EAAE,CAAC;YACzD,IAAA,sBAAmB,EAAC;gBAClB,UAAU,EAAE,IAAI;gBAChB,SAAS,EAAE,GAAG;gBACd,qCAAqC,EAAE,IAAI;gBAC3C,QAAQ,EAAE,KAAK;aAChB,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;QACvD,CAAC;IACH,CAAC,CAAC;IAEF,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,WAAW,EAAE,EAAE,CAAC;QACxC,YAAY,CAAC,IAAI,CAAC,CAAC;QACnB,MAAM,gBAAgB,GAAG,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAChG,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;YAClC,MAAM,gBAAgB,GAAG,kBAAkB,IAAI,KAAK,CAAC,mBAAmB,CAAC,kBAAkB,CAAC;mBACvF,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,IAAI,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;YAC5G,IAAI,gBAAgB,EAAE,CAAC;gBACrB,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YACxC,CAAC;YACD,KAAK,MAAM,QAAQ,IAAI,KAAK,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,EAAE,CAAC;gBACpE,MAAM,UAAU,GAAG,IAAA,WAAQ,EAAC,KAAK,CAAC,IAAK,CAAC,CAAC;gBAGzC,IAAI,IAAA,kBAAe,EAAC,UAAU,CAAC,EAAE,CAAC;oBAChC,IAAA,sBAAmB,EAAC;wBAClB,UAAU;wBACV,SAAS,EAAE,QAAQ;wBACnB,qCAAqC,EAAE,IAAI;wBAC3C,QAAQ,EAAE,KAAK;qBAChB,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;wBAGf,IAAI,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC;4BAChC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;wBACpC,CAAC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,cAAc,EAAE,EAAE,CAAC;QAC3C,YAAY,CAAC,IAAI,CAAC,CAAC;IACrB,CAAC;IAED,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;AAC1D,CAAC;AA3DD,8CA2DC"}