{"version": 3, "file": "RemoteGraphQLDataSource.js", "sourceRoot": "", "sources": ["../../src/datasources/RemoteGraphQLDataSource.ts"], "names": [], "mappings": ";;;;;;AAAA,wDAAmD;AACnD,mCAA2G;AAC3G,+DAAsD;AAEtD,uEAAoE;AACpE,0EAAwC;AACxC,2CAAsF;AAEtF,qCAA+D;AAG/D,MAAa,uBAAuB;IAMlC,YACE,MAE6C;QAsC/C,QAAG,GAAY,KAAK,CAAC;QAMrB,oCAA+B,GAAY,IAAI,CAAC;QA1C9C,IAAI,CAAC,OAAO,GAAG,2BAAO,CAAC,QAAQ,CAAC;YAK9B,UAAU,EAAE,QAAQ;YAIpB,KAAK,EAAE,KAAK;SACb,CAAC,CAAC;QACH,IAAI,MAAM,EAAE,CAAC;YACX,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QACrC,CAAC;IACH,CAAC;IA8BD,KAAK,CAAC,OAAO,CACX,OAAkD;;QAElD,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,eAAe,EAAE,GAAG,OAAO,CAAC;QACtD,MAAM,qBAAqB,GACzB,OAAO,CAAC,IAAI,KAAK,oCAA4B,CAAC,kBAAkB;YAC9D,CAAC,CAAC,OAAO,CAAC,qBAAqB;YAC/B,CAAC,CAAC,SAAS,CAAC;QAShB,MAAM,OAAO,GAAG,eAA2B,CAAC;QAG5C,MAAM,OAAO,GAAG,IAAI,oBAAgB,EAAE,CAAC;QACvC,IAAI,MAAA,OAAO,CAAC,IAAI,0CAAE,OAAO,EAAE,CAAC;YAC1B,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;gBACjD,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YAC9B,CAAC;QACH,CAAC;QACD,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,kBAAkB,CAAC,CAAC;QAEhD,OAAO,CAAC,IAAI,GAAG;YACb,MAAM,EAAE,MAAM;YACd,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,OAAO;SACR,CAAC;QAEF,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QACtC,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;QACnC,CAAC;QAED,MAAM,EAAE,KAAK,EAAE,GAAG,mBAAmB,EAAE,GAAG,OAAO,CAAC;QAKlD,MAAM,kBAAkB,GACtB,IAAI,CAAC,+BAA+B;YACpC,OAAO,CAAC,IAAI,KAAK,oCAA4B,CAAC,kBAAkB;YAChE,OAAO,CAAC,sBAAsB,CAAC,kBAAkB;YACjD,UAAU,IAAI,OAAO,CAAC,sBAAsB,CAAC,kBAAkB;YAC7D,CAAC,CAAC,OAAO,CAAC,sBAAsB,CAAC,kBAAkB;YACnD,CAAC,CAAC,IAAI,CAAC;QAEX,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;YACb,MAAM,OAAO,GAAG,IAAA,6BAAU,EAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAIzE,mBAAmB,CAAC,UAAU,GAAG;gBAC/B,GAAG,OAAO,CAAC,UAAU;gBACrB,cAAc,EAAE;oBACd,OAAO,EAAE,CAAC;oBACV,UAAU,EAAE,OAAO;iBACpB;aACF,CAAC;YAEF,MAAM,qBAAqB,GAAG,MAAM,IAAI,CAAC,WAAW,CAClD,mBAAmB,EACnB,OAAO,CACR,CAAC;YAIF,IACE,CAAC,qBAAqB,CAAC,MAAM;gBAC7B,CAAC,qBAAqB,CAAC,MAAM,CAAC,IAAI,CAChC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,KAAK,wBAAwB,CACtD,EACD,CAAC;gBACD,OAAO,IAAI,CAAC,OAAO,CAAC;oBAClB,QAAQ,EAAE,qBAAqB;oBAC/B,OAAO,EAAE,mBAAmB;oBAC5B,OAAO;oBACP,kBAAkB;oBAClB,qBAAqB;iBACtB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAKD,MAAM,gBAAgB,GAA0B;YAC9C,KAAK;YACL,GAAG,mBAAmB;SACvB,CAAC;QACF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;QACnE,OAAO,IAAI,CAAC,OAAO,CAAC;YAClB,QAAQ;YACR,OAAO,EAAE,gBAAgB;YACzB,OAAO;YACP,kBAAkB;YAClB,qBAAqB;SACtB,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,WAAW,CACvB,OAA8B,EAC9B,OAAiB;QAIjB,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;QACzE,CAAC;QAKD,MAAM,EAAE,IAAI,EAAE,GAAG,kBAAkB,EAAE,GAAG,OAAO,CAAC;QAChD,MAAM,6BAA6B,GAAG,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,CAAC;QACzE,MAAM,WAAW,GAAuB;YACtC,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,OAAO,EAAE,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC;YACzC,IAAI,EAAE,6BAA6B;SACpC,CAAC;QAMF,MAAM,YAAY,GAAG,IAAI,oBAAgB,CAAC,IAAI,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;QAEjE,IAAI,aAA0C,CAAC;QAE/C,IAAI,CAAC;YAGH,aAAa,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;YAE1D,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,CAAC;gBACtB,MAAM,MAAM,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;YACpD,CAAC;YAED,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;YAExE,IAAI,CAAC,IAAA,qBAAQ,EAAC,IAAI,CAAC,EAAE,CAAC;gBACpB,MAAM,IAAI,KAAK,CAAC,8CAA8C,IAAI,EAAE,CAAC,CAAC;YACxE,CAAC;YAED,OAAO;gBACL,GAAG,IAAI;gBACP,IAAI,EAAE,aAAa;aACpB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,YAAY,EAAE,aAAa,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;YAC7E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAMO,KAAK,CAAC,OAAO,CAAC,EACpB,QAAQ,EACR,OAAO,EACP,OAAO,EACP,kBAAkB,EAClB,qBAAqB,EAOtB;;QACC,MAAM,iBAAiB,GACrB,OAAO,IAAI,CAAC,kBAAkB,KAAK,UAAU;YAC3C,CAAC,CAAC,MAAM,IAAI,CAAC,kBAAkB,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,qBAAqB,EAAE,CAAC;YACtF,CAAC,CAAC,QAAQ,CAAC;QAEf,IAAI,kBAAkB,EAAE,CAAC;YACvB,MAAM,MAAM,GAAG,IAAA,iDAAuB,EACpC,MAAA,QAAQ,CAAC,IAAI,0CAAE,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAC5C,CAAC;YAMF,MAAM,IAAI,GAAqB,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;YAC7C,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;YACjC,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC3D,IAAI,CAAC,MAAM,GAAG,CAAC,MAAM,CAAC;YACxB,CAAC;YACD,IAAI,MAAM,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE,CAAC;gBAC/B,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;YACzB,CAAC;YACD,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,IAAI,EAAE,CAAC;gBAC9B,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;YACxB,CAAC;YACD,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACpC,CAAC;QAED,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAQM,iBAAiB,CACtB,KAAY,EACZ,aAA+B,EAC/B,cAAgC,EAChC,QAAmB,EACnB,QAAgC;QAEhC,MAAM,KAAK,CAAC;IACd,CAAC;IAEM,SAAS,CACd,aAA8B,EAC9B,aAAgC,EAChC,QAAmB;QAEnB,MAAM,WAAW,GAAG,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAC9D,IACE,WAAW;YACX,CAAC,WAAW,CAAC,UAAU,CAAC,kBAAkB,CAAC;gBACzC,WAAW,CAAC,UAAU,CAAC,mCAAmC,CAAC,CAAC,EAC9D,CAAC;YACD,OAAO,aAAa,CAAC,IAAI,EAAE,CAAC;QAC9B,CAAC;aAAM,CAAC;YACN,OAAO,aAAa,CAAC,IAAI,EAAE,CAAC;QAC9B,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,iBAAiB,CAAC,QAAyB;QACtD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAE5C,MAAM,UAAU,GAA2B;YACzC,QAAQ,EAAE;gBACR,GAAG,EAAE,QAAQ,CAAC,GAAG;gBACjB,MAAM,EAAE,QAAQ,CAAC,MAAM;gBACvB,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,IAAI;aACL;SACF,CAAC;QAEF,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;YAC5B,UAAU,CAAC,IAAI,GAAG,iBAAiB,CAAC;QACtC,CAAC;aAAM,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;YACnC,UAAU,CAAC,IAAI,GAAG,WAAW,CAAC;QAChC,CAAC;QAED,OAAO,IAAI,sBAAY,CAAC,GAAG,QAAQ,CAAC,MAAM,KAAK,QAAQ,CAAC,UAAU,EAAE,EAAE;YACpE,UAAU;SACX,CAAC,CAAC;IACL,CAAC;CACF;AA/TD,0DA+TC"}