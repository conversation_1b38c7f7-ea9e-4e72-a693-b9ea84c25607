import type { Logger } from '@apollo/utils.logger';
import { SupergraphManager, SupergraphSdlHookOptions, DynamicGatewayConfig } from '../../config';
import { Experimental_UpdateComposition } from '../..';
export interface LegacyFetcherOptions {
    pollIntervalInMs?: number;
    logger?: Logger;
    subgraphHealthCheck?: boolean;
    updateServiceDefinitions: Experimental_UpdateComposition;
    gatewayConfig: DynamicGatewayConfig;
}
export declare class LegacyFetcher implements SupergraphManager {
    private config;
    private update?;
    private healthCheck?;
    private getDataSource?;
    private timerRef;
    private state;
    private compositionId?;
    private serviceDefinitions?;
    constructor(options: LegacyFetcherOptions);
    private issueDeprecationWarnings;
    initialize({ update, healthCheck, getDataSource, }: SupergraphSdlHookOptions): Promise<{
        supergraphSdl: string;
        cleanup: () => Promise<void>;
    }>;
    private updateSupergraphSdl;
    private updateByComposition;
    private createSupergraphFromServiceList;
    private beginPolling;
    private poll;
    private logUpdateFailure;
}
//# sourceMappingURL=index.d.ts.map